# Backstage Go Client with <PERSON><PERSON>er Authentication

This Go client demonstrates how to authenticate with <PERSON><PERSON>ge using a browser-based OAuth flow and acquire a Backstage token for making authenticated API requests.

## Overview

The client implements a browser-based authentication flow that:

1. Starts a local HTTP server to receive the authentication callback
2. Opens the user's default browser to the Backstage authentication URL
3. Handles the OAuth redirect flow
4. Extracts the Backstage token from the authentication response
5. Provides methods for making authenticated API requests

## Prerequisites

- Go 1.19 or later
- A running Backstage instance with OIDC authentication configured
- Access to a web browser on the same machine

## Configuration

The client requires the following configuration:

- **Backstage URL**: The base URL of your Backstage instance (e.g., `http://localhost:3000`)
- **Auth Provider**: The authentication provider configured in Backstage (e.g., `oidc`, `google`, `github`)

## Usage

### Basic Authentication

```go
package main

import (
    "context"
    "fmt"
    "log"
    "time"
)

func main() {
    // Create the authentication client
    client := NewBackstageAuthClient("http://localhost:3000", "oidc")
    
    // Authenticate with Backstage
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
    defer cancel()

    if err := client.Authenticate(ctx); err != nil {
        log.Fatalf("Authentication failed: %v", err)
    }

    fmt.Printf("Successfully authenticated as: %s\n", client.Identity.UserEntityRef)
    
    // Make authenticated API requests
    resp, err := client.MakeAuthenticatedRequest("GET", "/api/catalog/entities", nil)
    if err != nil {
        log.Fatalf("API request failed: %v", err)
    }
    defer resp.Body.Close()
    
    // Process response...
}
```

### Environment Variables

You can configure the client using environment variables:

```bash
export BACKSTAGE_URL="https://your-backstage-instance.com"
export BACKSTAGE_AUTH_PROVIDER="oidc"
```

### Making API Requests

The client provides a `MakeAuthenticatedRequest` method for making authenticated requests to Backstage APIs:

```go
// Get all catalog entities
resp, err := client.MakeAuthenticatedRequest("GET", "/api/catalog/entities", nil)

// Get a specific entity
resp, err := client.MakeAuthenticatedRequest("GET", "/api/catalog/entities/by-name/component/default/my-service", nil)

// Search entities by kind
resp, err := client.MakeAuthenticatedRequest("GET", "/api/catalog/entities?filter=kind=Component", nil)
```

## Authentication Flow Details

The authentication flow works as follows:

1. **Local Server**: The client starts a local HTTP server on a random port to receive the OAuth callback
2. **Browser Launch**: The client opens the user's default browser to the Backstage authentication URL
3. **User Authentication**: The user authenticates with the configured OIDC provider
4. **Callback Handling**: After successful authentication, the browser is redirected to the local server
5. **Token Extraction**: The client extracts the Backstage token from the authentication response
6. **Session Storage**: The token and user identity are stored for subsequent API requests

## Security Considerations

- The local HTTP server only runs during the authentication process and is shut down immediately after
- The authentication uses the standard OAuth2 redirect flow with CSRF protection
- Tokens are stored in memory and not persisted to disk
- The client forwards authentication cookies to extract the session information

## Limitations and Notes

1. **Browser Requirement**: This approach requires a web browser on the same machine where the Go client is running
2. **Token Refresh**: The current implementation doesn't handle automatic token refresh. You'll need to re-authenticate when tokens expire
3. **Cookie Handling**: The actual token extraction may require additional cookie handling depending on your Backstage configuration
4. **Platform Support**: Browser opening is supported on Windows, macOS, and Linux

## Alternative Authentication Methods

For production use cases or environments without browser access, consider these alternatives:

### Static Tokens

Configure static tokens in your Backstage configuration:

```yaml
backend:
  auth:
    externalAccess:
      - type: static
        options:
          token: ${YOUR_STATIC_TOKEN}
          subject: go-client
        accessRestrictions:
          - plugin: catalog
```

Then use the token directly:

```go
client := &http.Client{}
req, _ := http.NewRequest("GET", "http://localhost:3000/api/catalog/entities", nil)
req.Header.Set("Authorization", "Bearer YOUR_STATIC_TOKEN")
resp, err := client.Do(req)
```

### JWKS Authentication

For more sophisticated setups, you can use JWKS-based authentication with your existing identity provider.

## Example Applications

See `example_usage.go` for a complete example that demonstrates:

- Listing catalog entities
- Fetching specific entities
- Searching entities by kind
- Error handling and response parsing

## Troubleshooting

### Browser Doesn't Open
If the browser doesn't open automatically, the client will print the authentication URL. Copy and paste it into your browser manually.

### Authentication Timeout
The default timeout is 5 minutes. If authentication takes longer, increase the timeout in the context:

```go
ctx, cancel := context.WithTimeout(context.Background(), 15*time.Minute)
```

### Token Extraction Issues
The token extraction logic may need to be adapted based on your specific Backstage configuration and authentication provider setup.

## Contributing

This is a demonstration client. For production use, consider:

- Adding proper error handling and retry logic
- Implementing token refresh functionality
- Adding configuration file support
- Supporting additional authentication providers
- Adding comprehensive logging

## License

This example code is provided as-is for demonstration purposes.
