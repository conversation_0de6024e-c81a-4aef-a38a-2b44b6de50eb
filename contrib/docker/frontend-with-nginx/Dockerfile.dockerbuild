# The purpose of this image is to serve the frontend app content separately.
# By default the Backstage backend uses the app-backend plugin to serve the
# app from the backend itself, but it may be desirable to move the frontend
# content serving to a separate deployment, in which case this image can be
# used.

# This dockerfile also performs the build first inside docker. This may come
# with a build time impact, but is sometimes desirable. If you want to run the
# build on the host instead, use the file simply named <PERSON><PERSON><PERSON><PERSON> in this folder
# instead.

# USAGE:
#
# - Copy this file and the "docker" folder from this directory to your project
#   root
#
# - Update your .dockerignore, make sure that the source folders are not
#   excluded, but do exclude node_modules and build artifacts:
#
#       .git
#       node_modules
#       packages/*/dist
#       packages/*/node_modules
#       plugins/*/dist
#       plugins/*/node_modules
#
# - Update the copy of this file to add configuration arguments to the "build"
#   command, for example:
#
#       RUN yarn workspace app build --config <config1> --config <config2> ...
#
# - In your project root, run:
#
#       docker build -t backstage-frontend -f Dockerfile.dockerbuild .



FROM node:20-buster AS build

RUN mkdir /app
COPY . /app
WORKDIR /app

RUN yarn install
RUN yarn workspace app build



FROM nginx:mainline

RUN apt-get update && apt-get -y install jq && rm -rf /var/lib/apt/lists/*

COPY --from=build /app/packages/app/dist /usr/share/nginx/html
COPY docker/default.conf.template /etc/nginx/templates/default.conf.template

COPY docker/inject-config.sh /docker-entrypoint.d/40-inject-config.sh

ENV PORT=80
