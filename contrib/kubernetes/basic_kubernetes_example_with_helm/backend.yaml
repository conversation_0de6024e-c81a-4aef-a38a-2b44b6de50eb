apiVersion: apps/v1
kind: Deployment
metadata:
  name: backstage-backend
  labels:
    app: backstage
    component: backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backstage
      component: backend
  template:
    metadata:
      labels:
        app: backstage
        component: backend
    spec:
      containers:
        - name: backend
          image: spotify/backstage-backend:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 7007
              name: backend
              protocol: TCP
