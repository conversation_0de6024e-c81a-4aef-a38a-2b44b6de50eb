app:
  enabled: true
  nameOverride: ''
  fullnameOverride: ''
  replicaCount: 1
  serviceAccount:
    create: false
    Name: ''
  image:
    repository: spotify/backstage
    tag: latest
    pullPolicy: Always
  service:
    type: ClusterIP
    port: 80
  ingress:
    enabled: false
    annotations:
      {}
      # kubernetes.io/ingress.class: "nginx"
    hosts:
      - host: backstage.local
        paths:
          - /
    tls: []
    #  - secretName: chart-example-tls
    #    hosts:
    #      - chart-example.local
  imagePullSecrets: []
  podSecurityContext:
    {}
    # fsGroup: 2000
  securityContext:
    {}
    # capabilities:
    #   drop:
    #   - ALL
    # readOnlyRootFilesystem: true
    # runAsNonRoot: true
    # runAsUser: 1000
  resources: {}
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi
  nodeSelector: {}
  tolerations: []
  affinity: {}

backend:
  enabled: false
  nameOverride: ''
  fullnameOverride: ''
  replicaCount: 1
  serviceAccount:
    create: false
    Name: ''
  image:
    repository: spotify/backstage-backend
    tag: latest
    pullPolicy: IfNotPresent
  service:
    type: ClusterIP
    port: 7007
  ingress:
    enabled: false
    annotations:
      {}
      # kubernetes.io/ingress.class: "nginx"
    hosts:
      - host: backstage.local
        paths:
          - /backend
    tls: []
    #  - secretName: chart-example-tls
    #    hosts:
    #      - chart-example.local
  imagePullSecrets: []
  podSecurityContext:
    {}
    # fsGroup: 2000
  securityContext:
    {}
    # capabilities:
    #   drop:
    #   - ALL
    # readOnlyRootFilesystem: true
    # runAsNonRoot: true
    # runAsUser: 1000
  resources: {}
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi
  nodeSelector: {}
  tolerations: []
  affinity: {}
