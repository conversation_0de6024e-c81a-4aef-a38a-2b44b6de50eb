{{- if .Values.app.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "backstage.fullname" . }}-app
  labels:
{{ include "backstage.app.labels" . | indent 4 }}
spec:
  type: {{ .Values.app.service.type }}
  ports:
    - port: {{ .Values.app.service.port }}
      targetPort: app
      protocol: TCP
      name: app
  selector:
    app.kubernetes.io/name: {{ include "backstage.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}
{{- if .Values.backend.enabled }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "backstage.fullname" . }}-backend
  labels:
{{ include "backstage.backend.labels" . | indent 4 }}
spec:
  type: {{ .Values.backend.service.type }}
  ports:
    - port: {{ .Values.backend.service.port }}
      targetPort: backend
      protocol: TCP
      name: backend 
  selector:
    app.kubernetes.io/name: {{ include "backstage.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}
