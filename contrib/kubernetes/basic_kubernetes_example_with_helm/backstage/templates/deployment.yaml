{{- if .Values.app.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "backstage.fullname" . }}-app
  labels:
{{ include "backstage.app.labels" . | indent 4 }}
spec:
  replicas: {{ .Values.app.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "backstage.name" . }}-app
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "backstage.name" . }}-app
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
    {{- with .Values.app.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      serviceAccountName: {{ template "backstage.app.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.app.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}-app
          securityContext:
            {{- toYaml .Values.app.securityContext | nindent 12 }}
          image: "{{ .Values.app.image.repository }}:{{ .Values.app.image.tag }}"
          imagePullPolicy: {{ .Values.app.image.pullPolicy }}
          ports:
            - name: app
              containerPort: 80
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
          readinessProbe:
            httpGet:
              path: /
              port: http
          resources:
            {{- toYaml .Values.app.resources | nindent 12 }}
      {{- with .Values.app.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.app.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.app.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
{{- end }}
{{- if .Values.backend.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "backstage.fullname" . }}-backend
  labels:
{{ include "backstage.backend.labels" . | indent 4 }}
spec:
  replicas: {{ .Values.backend.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "backstage.name" . }}-backend
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "backstage.name" . }}-backend
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
    {{- with .Values.backend.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      serviceAccountName: {{ template "backstage.backend.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.backend.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}-backend
          securityContext:
            {{- toYaml .Values.backend.securityContext | nindent 12 }}
          image: "{{ .Values.backend.image.repository }}:{{ .Values.backend.image.tag }}"
          imagePullPolicy: {{ .Values.backend.image.pullPolicy }}
          ports:
            - name: backend
              containerPort: 80
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: backend
          readinessProbe:
            httpGet:
              path: /
              port: backend
          resources:
            {{- toYaml .Values.backend.resources | nindent 12 }}
      {{- with .Values.backend.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.backend.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.backend.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
{{- end }}
