apiVersion: v1
kind: Service
metadata:
  name: backstage
  labels:
    app: backstage
    component: frontend
spec:
  type: ClusterIP
  selector:
    app: backstage
    component: frontend
  ports:
    - name: frontend
      port: 80
      protocol: TCP
      targetPort: app
---
apiVersion: v1
kind: Service
metadata:
  name: backstage-backend
  labels:
    app: backstage
    component: backend
spec:
  type: ClusterIP
  selector:
    app: backstage
    component: backend
  ports:
    - name: backend
      port: 7007
      protocol: TCP
      targetPort: backend
