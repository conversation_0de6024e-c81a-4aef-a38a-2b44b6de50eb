apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: backstage
  labels:
    app: backstage
    component: ingress
spec:
  rules:
    - host: <HOSTNAME>
      http:
        paths:
          - backend:
              serviceName: backstage
              servicePort: frontend
            path: /
          - backend:
              serviceName: backstage-backend
              servicePort: backend
            path: /backend
