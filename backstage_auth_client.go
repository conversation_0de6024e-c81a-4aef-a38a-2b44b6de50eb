package main

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"time"
)

// BackstageIdentity represents the user identity returned by Backstage
type BackstageIdentity struct {
	Type                string   `json:"type"`
	UserEntityRef       string   `json:"userEntityRef"`
	OwnershipEntityRefs []string `json:"ownershipEntityRefs"`
}

// BackstageTokenResponse represents the complete authentication response
type BackstageTokenResponse struct {
	Token    string            `json:"token"`
	Identity BackstageIdentity `json:"identity"`
}

// BackstageAuthClient handles authentication with Backstage
type BackstageAuthClient struct {
	BackstageURL string
	Provider     string // e.g., "oidc", "google", "github"
	Token        string
	Identity     *BackstageIdentity
}

// NewBackstageAuthClient creates a new authentication client
func NewBackstageAuthClient(backstageURL, provider string) *BackstageAuthClient {
	return &BackstageAuthClient{
		BackstageURL: strings.TrimSuffix(backstageURL, "/"),
		Provider:     provider,
	}
}

// Authenticate performs browser-based authentication to acquire a Backstage token
func (c *BackstageAuthClient) Authenticate(ctx context.Context) error {
	// Start local HTTP server to receive the callback
	listener, err := net.Listen("tcp", "127.0.0.1:0")
	if err != nil {
		return fmt.Errorf("failed to start local server: %w", err)
	}
	defer listener.Close()

	port := listener.Addr().(*net.TCPAddr).Port
	callbackURL := fmt.Sprintf("http://localhost:%d/callback", port)

	// Generate state parameter for CSRF protection
	state, err := generateRandomString(32)
	if err != nil {
		return fmt.Errorf("failed to generate state: %w", err)
	}

	// Channel to receive the authentication result
	resultChan := make(chan *BackstageTokenResponse, 1)
	errorChan := make(chan error, 1)

	// Set up HTTP server to handle the callback
	mux := http.NewServeMux()
	mux.HandleFunc("/callback", func(w http.ResponseWriter, r *http.Request) {
		c.handleCallback(w, r, state, resultChan, errorChan)
	})

	server := &http.Server{Handler: mux}
	go func() {
		if err := server.Serve(listener); err != nil && err != http.ErrServerClosed {
			errorChan <- fmt.Errorf("server error: %w", err)
		}
	}()

	// Build the authentication URL
	authURL := c.buildAuthURL(callbackURL, state)

	// Open the browser
	fmt.Printf("Opening browser for authentication...\n")
	fmt.Printf("If the browser doesn't open automatically, visit: %s\n", authURL)

	if err := openBrowser(authURL); err != nil {
		fmt.Printf("Failed to open browser automatically: %v\n", err)
		fmt.Printf("Please manually open: %s\n", authURL)
	}

	// Wait for authentication result or timeout
	select {
	case result := <-resultChan:
		c.Token = result.Token
		c.Identity = &result.Identity
		fmt.Printf("Authentication successful! Logged in as: %s\n", result.Identity.UserEntityRef)

	case err := <-errorChan:
		return fmt.Errorf("authentication failed: %w", err)

	case <-time.After(5 * time.Minute):
		return fmt.Errorf("authentication timed out after 5 minutes")

	case <-ctx.Done():
		return ctx.Err()
	}

	// Shutdown the local server
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	server.Shutdown(shutdownCtx)

	return nil
}

// buildAuthURL constructs the authentication URL for the redirect flow
func (c *BackstageAuthClient) buildAuthURL(callbackURL, state string) string {
	params := url.Values{
		"origin":      {callbackURL},
		"redirectUrl": {callbackURL},
		"flow":        {"redirect"},
		"scope":       {"openid profile email"},
		"state":       {state},
	}

	return fmt.Sprintf("%s/api/auth/%s/start?%s",
		c.BackstageURL, c.Provider, params.Encode())
}

// handleCallback processes the authentication callback
func (c *BackstageAuthClient) handleCallback(w http.ResponseWriter, r *http.Request,
	expectedState string, resultChan chan<- *BackstageTokenResponse, errorChan chan<- error) {

	// Check for error in callback
	if errMsg := r.URL.Query().Get("error"); errMsg != "" {
		errorChan <- fmt.Errorf("authentication error: %s", errMsg)
		http.Error(w, "Authentication failed", http.StatusBadRequest)
		return
	}

	// In the actual Backstage redirect flow, the browser will be redirected back
	// to our callback URL after successful authentication. However, the token
	// is typically stored in HTTP-only cookies, not URL parameters.
	//
	// For a real implementation, you would need to:
	// 1. Make a request to the refresh endpoint to get the token
	// 2. Or implement a custom endpoint that returns the token
	//
	// This is a simplified example showing the concept

	// Make a request to get the current session/token
	token, identity, err := c.getCurrentSession(r)
	if err != nil {
		errorChan <- fmt.Errorf("failed to get session: %w", err)
		http.Error(w, "Failed to get authentication session", http.StatusInternalServerError)
		return
	}

	result := &BackstageTokenResponse{
		Token:    token,
		Identity: *identity,
	}

	resultChan <- result

	// Send success response to browser
	w.Header().Set("Content-Type", "text/html")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`
		<html>
		<head><title>Authentication Successful</title></head>
		<body>
			<h1>Authentication Successful!</h1>
			<p>You can now close this window and return to your application.</p>
			<script>window.close();</script>
		</body>
		</html>
	`))
}

// getCurrentSession attempts to get the current authentication session
// This is a simplified implementation - in practice, you might need to
// handle cookies or make additional requests to Backstage endpoints
func (c *BackstageAuthClient) getCurrentSession(r *http.Request) (string, *BackstageIdentity, error) {
	// Create a new HTTP client that will forward cookies from the original request
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Make a request to the refresh endpoint to get current session
	refreshURL := fmt.Sprintf("%s/api/auth/%s/refresh", c.BackstageURL, c.Provider)
	req, err := http.NewRequest("GET", refreshURL, nil)
	if err != nil {
		return "", nil, fmt.Errorf("failed to create refresh request: %w", err)
	}

	// Forward cookies from the original request
	for _, cookie := range r.Cookies() {
		req.AddCookie(cookie)
	}
	req.Header.Set("X-Requested-With", "XMLHttpRequest")

	resp, err := client.Do(req)
	if err != nil {
		return "", nil, fmt.Errorf("refresh request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", nil, fmt.Errorf("refresh request returned status %d", resp.StatusCode)
	}

	// Parse the response to extract token and identity
	var sessionResp struct {
		BackstageIdentity struct {
			Token    string            `json:"token"`
			Identity BackstageIdentity `json:"identity"`
		} `json:"backstageIdentity"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&sessionResp); err != nil {
		return "", nil, fmt.Errorf("failed to parse session response: %w", err)
	}

	return sessionResp.BackstageIdentity.Token, &sessionResp.BackstageIdentity.Identity, nil
}

// MakeAuthenticatedRequest makes an HTTP request with the Backstage token
func (c *BackstageAuthClient) MakeAuthenticatedRequest(method, endpoint string, body io.Reader) (*http.Response, error) {
	if c.Token == "" {
		return nil, fmt.Errorf("not authenticated - call Authenticate() first")
	}

	url := fmt.Sprintf("%s%s", c.BackstageURL, endpoint)
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.Token)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	return client.Do(req)
}

// Helper functions

func generateRandomString(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes)[:length], nil
}

func openBrowser(url string) error {
	var cmd string
	var args []string

	switch runtime.GOOS {
	case "windows":
		cmd = "cmd"
		args = []string{"/c", "start"}
	case "darwin":
		cmd = "open"
	default: // "linux", "freebsd", "openbsd", "netbsd"
		cmd = "xdg-open"
	}
	args = append(args, url)
	return exec.Command(cmd, args...).Start()
}

// Example usage
func main() {
	client := NewBackstageAuthClient("http://localhost:3000", "oidc")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// Authenticate with Backstage
	if err := client.Authenticate(ctx); err != nil {
		fmt.Printf("Authentication failed: %v\n", err)
		os.Exit(1)
	}

	// Example: Make an authenticated request to the catalog API
	resp, err := client.MakeAuthenticatedRequest("GET", "/api/catalog/entities", nil)
	if err != nil {
		fmt.Printf("API request failed: %v\n", err)
		os.Exit(1)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		fmt.Println("Successfully made authenticated request to Backstage!")
		// Process response...
	} else {
		fmt.Printf("API request returned status: %d\n", resp.StatusCode)
	}
}
