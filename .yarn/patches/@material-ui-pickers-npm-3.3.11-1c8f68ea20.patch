diff --git a/DateTimePicker/DateTimePickerTabs.d.ts b/DateTimePicker/DateTimePickerTabs.d.ts
index 52396cccbb66861dd6519459141e7744aa25aaf8..75c5a9053a88abf2454b7783c0101a1a8c9375f1 100644
--- a/DateTimePicker/DateTimePickerTabs.d.ts
+++ b/DateTimePicker/DateTimePickerTabs.d.ts
@@ -7,5 +7,5 @@ export interface DateTimePickerTabsProps {
     timeIcon?: React.ReactNode;
 }
 export declare const useStyles: (props?: any) => Record<"tabs", string>;
-export declare const DateTimePickerTabs: React.SFC<DateTimePickerTabsProps>;
+export declare const DateTimePickerTabs: React.FC<DateTimePickerTabsProps>;
 export default DateTimePickerTabs;
diff --git a/_shared/ModalDialog.d.ts b/_shared/ModalDialog.d.ts
index 067070067ccf6e7699d17f422934daf14370d225..c45a583adef09a2091865db91b2687e66af81b47 100644
--- a/_shared/ModalDialog.d.ts
+++ b/_shared/ModalDialog.d.ts
@@ -15,7 +15,7 @@ export interface ModalDialogProps extends DialogProps {
     showTabs?: boolean;
     wider?: boolean;
 }
-export declare const ModalDialog: React.SFC<ModalDialogProps & WithStyles<typeof styles>>;
+export declare const ModalDialog: React.FC<ModalDialogProps & WithStyles<typeof styles>>;
 export declare const styles: Record<"dialog" | "dialogRoot" | "dialogRootWider" | "withAdditionalAction", import("@material-ui/core/styles/withStyles").CSSProperties | import("@material-ui/core/styles/withStyles").CreateCSSProperties<{}> | ((props: {}) => import("@material-ui/core/styles/withStyles").CreateCSSProperties<{}>)>;
 declare const _default: React.ComponentType<Pick<React.PropsWithChildren<ModalDialogProps & {
     classes: Record<"dialog" | "dialogRoot" | "dialogRootWider" | "withAdditionalAction", string>;
diff --git a/_shared/PickerToolbar.d.ts b/_shared/PickerToolbar.d.ts
index f1ccd368bf9ab853cf0356d03fce0390becad8cd..e75ef734788cb26cc07dc079c714f759f525c447 100644
--- a/_shared/PickerToolbar.d.ts
+++ b/_shared/PickerToolbar.d.ts
@@ -5,5 +5,5 @@ export declare const useStyles: (props?: any) => Record<"toolbar" | "toolbarLand
 interface PickerToolbarProps extends ExtendMui<ToolbarProps> {
     isLandscape: boolean;
 }
-declare const PickerToolbar: React.SFC<PickerToolbarProps>;
+declare const PickerToolbar: React.FC<PickerToolbarProps>;
 export default PickerToolbar;
diff --git a/_shared/WithUtils.d.ts b/_shared/WithUtils.d.ts
index 22fe0425817be182a6c9a1a21a0f9eefb61696e7..b5b2a966ec0ea8427c4355227d8425b9c1af3790 100644
--- a/_shared/WithUtils.d.ts
+++ b/_shared/WithUtils.d.ts
@@ -4,4 +4,4 @@ import { MaterialUiPickersDate } from '../typings/date';
 export interface WithUtilsProps {
     utils: IUtils<MaterialUiPickersDate>;
 }
-export declare const withUtils: () => <P extends WithUtilsProps>(Component: React.ComponentType<P>) => React.SFC<Pick<P, Exclude<keyof P, "utils">>>;
+export declare const withUtils: () => <P extends WithUtilsProps>(Component: React.ComponentType<P>) => React.FC<Pick<P, Exclude<keyof P, "utils">>>;
diff --git a/_shared/icons/ArrowLeftIcon.d.ts b/_shared/icons/ArrowLeftIcon.d.ts
index ce0f208a2aa6dae03a77dfe830a10a95c2085030..95a516da22e02b9a4167ee4a52bf2cd0a1b4aec6 100644
--- a/_shared/icons/ArrowLeftIcon.d.ts
+++ b/_shared/icons/ArrowLeftIcon.d.ts
@@ -1,3 +1,3 @@
 import React from 'react';
 import { SvgIconProps } from '@material-ui/core/SvgIcon';
-export declare const ArrowLeftIcon: React.SFC<SvgIconProps>;
+export declare const ArrowLeftIcon: React.FC<SvgIconProps>;
diff --git a/_shared/icons/ArrowRightIcon.d.ts b/_shared/icons/ArrowRightIcon.d.ts
index 71443a34f7bbd2cef8c2af487c817b7ea788dd7a..a96314aa8750180773f1d006ed766e5a7e8e071d 100644
--- a/_shared/icons/ArrowRightIcon.d.ts
+++ b/_shared/icons/ArrowRightIcon.d.ts
@@ -1,3 +1,3 @@
 import React from 'react';
 import { SvgIconProps } from '@material-ui/core/SvgIcon';
-export declare const ArrowRightIcon: React.SFC<SvgIconProps>;
+export declare const ArrowRightIcon: React.FC<SvgIconProps>;
diff --git a/_shared/icons/DateRangeIcon.d.ts b/_shared/icons/DateRangeIcon.d.ts
index 722f8736d86f248d464c5af855795663d9e220d3..6018043d41861d96335f060ea1dc78336892ce66 100644
--- a/_shared/icons/DateRangeIcon.d.ts
+++ b/_shared/icons/DateRangeIcon.d.ts
@@ -1,3 +1,3 @@
 import React from 'react';
 import { SvgIconProps } from '@material-ui/core/SvgIcon';
-export declare const DateRangeIcon: React.SFC<SvgIconProps>;
+export declare const DateRangeIcon: React.FC<SvgIconProps>;
diff --git a/_shared/icons/KeyboardIcon.d.ts b/_shared/icons/KeyboardIcon.d.ts
index c1a0a111d831acc58cf19aa6d54f8324e68de9d8..8d7d1dac47815cef02215c4f11f36e73509b4219 100644
--- a/_shared/icons/KeyboardIcon.d.ts
+++ b/_shared/icons/KeyboardIcon.d.ts
@@ -1,3 +1,3 @@
 import React from 'react';
 import { SvgIconProps } from '@material-ui/core/SvgIcon';
-export declare const KeyboardIcon: React.SFC<SvgIconProps>;
+export declare const KeyboardIcon: React.FC<SvgIconProps>;
diff --git a/_shared/icons/TimeIcon.d.ts b/_shared/icons/TimeIcon.d.ts
index 49e0b627132f7d31d1b0a205548229a9f7a2c0a6..15ebc6e630992edb3b01790c0dd651651adf51de 100644
--- a/_shared/icons/TimeIcon.d.ts
+++ b/_shared/icons/TimeIcon.d.ts
@@ -1,3 +1,3 @@
 import React from 'react';
 import { SvgIconProps } from '@material-ui/core/SvgIcon';
-export declare const TimeIcon: React.SFC<SvgIconProps>;
+export declare const TimeIcon: React.FC<SvgIconProps>;
diff --git a/views/Calendar/CalendarHeader.d.ts b/views/Calendar/CalendarHeader.d.ts
index 842cbd8e021eb6d8e6553ba68033f13140485b61..7f60b4bffce1a96ade02e2671fd705c8fe87dfa2 100644
--- a/views/Calendar/CalendarHeader.d.ts
+++ b/views/Calendar/CalendarHeader.d.ts
@@ -15,5 +15,5 @@ export interface CalendarHeaderProps {
     onMonthChange: (date: MaterialUiPickersDate, direction: SlideDirection) => void | Promise<void>;
 }
 export declare const useStyles: (props?: any) => Record<"transitionContainer" | "switchHeader" | "iconButton" | "daysHeader" | "dayLabel", string>;
-export declare const CalendarHeader: React.SFC<CalendarHeaderProps>;
+export declare const CalendarHeader: React.FC<CalendarHeaderProps>;
 export default CalendarHeader;
diff --git a/views/Calendar/SlideTransition.d.ts b/views/Calendar/SlideTransition.d.ts
index f00e98a72bd0e6cfd80ab45b33b905e802b60e9b..699119009fa5ea886e7f782425aee39b7608b8c2 100644
--- a/views/Calendar/SlideTransition.d.ts
+++ b/views/Calendar/SlideTransition.d.ts
@@ -7,5 +7,5 @@ interface SlideTransitionProps {
     children: React.ReactChild;
 }
 export declare const useStyles: (props?: any) => Record<"transitionContainer" | "slideEnter-left" | "slideEnter-right" | "slideEnterActive" | "slideExit" | "slideExitActiveLeft-left" | "slideExitActiveLeft-right", string>;
-declare const SlideTransition: React.SFC<SlideTransitionProps>;
+declare const SlideTransition: React.FC<SlideTransitionProps>;
 export default SlideTransition;
