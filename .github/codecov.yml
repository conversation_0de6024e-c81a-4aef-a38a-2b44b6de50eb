comment: false # Ref: https://docs.codecov.io/docs/pull-request-comments

ignore:
  - '**/*.stories.*'

coverage:
  status:
    project:
      default:
        threshold: 0% # Ref: https://docs.codecov.io/docs/codecovyml-reference#coveragestatus
        target: auto
        informational: true # Don't block PRs

# Since Backstage is a mono repo, flags here help in getting the code coverage of individual packages.
# Documentation: https://docs.codecov.io/docs/flags
flags:
  core-app-api:
    paths:
      - packages/core-app-api/
    carryforward: true
  core-components:
    paths:
      - packages/core-components/
    carryforward: true
  core-plugin-api:
    paths:
      - packages/core-plugin-api/
    carryforward: true
