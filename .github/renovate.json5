{
  $schema: 'https://docs.renovatebot.com/renovate-schema.json',
  labels: ['dependencies'],
  extends: ['config:best-practices', ':gitSignOff'],
  // do not pin dev dependencies, which are part of the best-practices preset
  ignorePresets: [':pinDevDependencies', ':pinDigest', 'docker:pinDigests'],
  constraints: {
    // TODO(freben): Remove this later; it addresses a temporary issue in corepack
    // https://github.com/nodejs/corepack/issues/379
    // https://github.com/renovatebot/renovate/discussions/27465
    corepack: '0.24.1',
  },
  // the default limit is 10 PRs
  prConcurrentLimit: 20,
  postUpdateOptions: ['yarnDedupeHighest'],
  rangeStrategy: 'update-lockfile',
  ignoreDeps: [
    // ignored due to licensing issues. See #10992
    '@elastic/elasticsearch',
    'event-source-polyfill',
  ],
  packageRules: [
    {
      groupName: 'Spotify web-scripts monorepo packages',
      rangeStrategy: 'replace',
      matchSourceUrls: ['https://github.com/spotify/web-scripts{/,}**'],
    },
    {
      groupName: 'API Extractor / Rush Stack monorepo packages',
      rangeStrategy: 'replace',
      matchSourceUrls: ['https://github.com/microsoft/rushstack{/,}**'],
    },
    {
      groupName: 'Module-federation monorepo packages',
      rangeStrategy: 'replace',
      matchSourceUrls: ['https://github.com/module-federation/core{/,}**'],
    },
    // We update yarn packages manually as it's gzip'd and we don't want to pollute the repository too much.
    {
      matchSourceUrls: ['https://github.com/yarnpkg/berry'],
      enabled: false,
    },
    // https://github.com/backstage/backstage/issues/27123
    {
      matchPackageNames: ['browser-actions/setup-chrome'],
      enabled: false,
    },
    // ESM only majors, that we're not ready for yet
    {
      matchPackageNames: ['node-fetch'],
      allowedVersions: '<3.0.0',
    },
    {
      matchPackageNames: ['inquirer', '@types/inquirer'],
      allowedVersions: '<9.0.0',
    },
    {
      matchPackageNames: ['ora'],
      allowedVersions: '<5.0.0',
    },
    {
      matchPackageNames: ['p-limit'],
      allowedVersions: '<4.0.0',
    },
    {
      matchPackageNames: ['p-throttle'],
      allowedVersions: '<4.0.0',
    },
    {
      matchPackageNames: ['p-queue'],
      allowedVersions: '<7.0.0',
    },
    {
      matchPackageNames: ['serialize-error'],
      allowedVersions: '<9.0.0',
    },
    {
      matchPackageNames: ['yn'],
      allowedVersions: '<5.0.0',
    },
  ],
}
