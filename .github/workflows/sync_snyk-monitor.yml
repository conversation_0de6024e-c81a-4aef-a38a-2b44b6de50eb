name: Sync Snyk Monitoring
on:
  workflow_dispatch:
  push:
    branches: [master]
    paths:
      - '.github/workflows/sync_snyk-monitor.yml'
      - '**/.snyk'
      - '**/package.json'
      - 'yarn.lock'

# This workflow synchronizes the packages in this repo along with policies in
# each .snyk file with the remote state in our snyk org. It allows us to define
# ignore policies in the .snyk files and then have them show up in the snyk web
# UI, and also automatically adds any new packages that are created.

permissions:
  contents: read

jobs:
  sync:
    permissions:
      contents: read # for actions/checkout to fetch code
      security-events: write # for github/codeql-action/upload-sarif to upload SARIF results
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@ec9f2d5744a09debf3a187a3f4f675c53b671911 # v2.13.0
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Monitor and Synchronize Snyk Policies
        uses: snyk/actions/node@77490d94e966421e076e95ad8fa87aa55e5ca409 # master
        with:
          command: monitor
          args: >
            --yarn-workspaces
            --org=backstage-dgh
            --strict-out-of-sync=false
            --remote-repo-url=https://github.com/backstage/backstage
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
          NODE_OPTIONS: --max-old-space-size=7168

      # Above we run the `monitor` command, this runs the `test` command which is
      # the one that generates the SARIF report that we can upload to GitHub.
      - name: Create Snyk report
        uses: snyk/actions/node@77490d94e966421e076e95ad8fa87aa55e5ca409 # master
        continue-on-error: true # To make sure that SARIF upload gets called
        with:
          args: >
            --yarn-workspaces
            --org=backstage-dgh
            --strict-out-of-sync=false
            --sarif-file-output=snyk.sarif
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
          NODE_OPTIONS: --max-old-space-size=7168
      - name: Upload Snyk report
        uses: github/codeql-action/upload-sarif@51f77329afa6477de8c49fc9c7046c15b9a4e79d # v3.29.5
        with:
          sarif_file: snyk.sarif
