name: E2E <PERSON>docs
on:
  pull_request:
    paths:
      - 'yarn.lock'
      - '.github/workflows/verify_e2e-techdocs.yml'
      - 'packages/techdocs-cli/**'
      - 'packages/techdocs-cli-embedded-app/**'
      - 'plugins/techdocs/**'
  push:
    branches: [master]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  verify:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [20.x, 22.x]

    env:
      CI: true
      NODE_OPTIONS: --max-old-space-size=4096 --experimental-vm-modules

    name: Techdocs
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@ec9f2d5744a09debf3a187a3f4f675c53b671911 # v2.13.0
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: '3.9'

      - name: install dependencies
        run: yarn install --immutable

      - name: generate types
        run: yarn tsc

      - name: build techdocs-cli
        working-directory: packages/techdocs-cli
        run: yarn build

      - name: Install mkdocs & techdocs-core
        run: python -m pip install mkdocs-techdocs-core==1.1.7 mkdocs==1.4.0

      - name: techdocs-cli e2e test
        working-directory: packages/techdocs-cli
        run: yarn test:e2e:ci
        env:
          BACKSTAGE_TEST_DISABLE_DOCKER: 1
