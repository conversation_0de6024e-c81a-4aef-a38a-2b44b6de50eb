# NO-OP placeholder that always passes for other paths
# This is here so that we're able to set the status check as required

name: E2E Windows Void
on:
  pull_request:
    paths-ignore:
      - 'yarn.lock'
      - '.github/workflows/verify_e2e-windows.yml'
      - 'packages/cli/**'
      - 'packages/e2e-test/**'
      - 'packages/create-app/**'

permissions:
  contents: read

jobs:
  noop:
    runs-on: windows-2022

    strategy:
      matrix:
        node-version: [20.x, 22.x]

    name: E2E Windows ${{ matrix.node-version }}
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@ec9f2d5744a09debf3a187a3f4f675c53b671911 # v2.13.0
        with:
          egress-policy: audit

      - run: echo NOOP
