name: Deploy Packages
on:
  push:
    branches: [master, patch/*]

jobs:
  build:
    runs-on: ubuntu-latest

    outputs:
      needs_release: ${{ steps.release_check.outputs.needs_release }}

    strategy:
      fail-fast: false
      matrix:
        node-version: [20.x, 22.x]

    services:
      postgres17:
        image: postgres:17
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432/tcp
      postgres13:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432/tcp
      mysql8:
        image: mysql:8
        env:
          MYSQL_ROOT_PASSWORD: root
        options: >-
          --health-cmd "mysqladmin ping -h localhost"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 3306/tcp
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379/tcp

    env:
      CI: true
      NODE_OPTIONS: --max-old-space-size=8192 --no-node-snapshot --experimental-vm-modules
      INTEGRATION_TEST_GITHUB_TOKEN: ${{ secrets.INTEGRATION_TEST_GITHUB_TOKEN }}
      INTEGRATION_TEST_GITLAB_TOKEN: ${{ secrets.INTEGRATION_TEST_GITLAB_TOKEN }}
      INTEGRATION_TEST_BITBUCKET_TOKEN: ${{ secrets.INTEGRATION_TEST_BITBUCKET_TOKEN }}
      INTEGRATION_TEST_AZURE_TOKEN: ${{ secrets.INTEGRATION_TEST_AZURE_TOKEN }}

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: use node.js ${{ matrix.node-version }}
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: https://registry.npmjs.org/ # Needed for auth
      - name: yarn install
        uses: backstage/actions/yarn-install@b3c1841fd69e1658ac631afafd0fb140a2309024 # v0.6.17
        with:
          cache-prefix: ${{ runner.os }}-v${{ matrix.node-version }}

      - name: Fetch previous commit for release check
        run: git fetch origin '${{ github.event.before }}'

      - name: Check if release
        if: inputs.force_release != true
        id: release_check
        run: node scripts/check-if-release.js
        env:
          COMMIT_SHA_BEFORE: '${{ github.event.before }}'

      - name: validate config
        run: yarn backstage-cli config:check --lax

      - name: backstage-cli cache
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4
        with:
          path: .cache/backstage-cli
          key: ${{ runner.os }}-v${{ matrix.node-version }}-backstage-cli-${{ github.run_id }}
          restore-keys: |
            ${{ runner.os }}-v${{ matrix.node-version }}-backstage-cli-

      - name: lint
        run: yarn backstage-cli repo lint --successCache --successCacheDir .cache/backstage-cli

      - name: type checking and declarations
        run: yarn tsc:full

      - name: build
        run: yarn backstage-cli repo build --all

        # For now BUI has a custom build script and needs to be built separately
      - name: build BUI
        run: yarn --cwd packages/ui build

      - name: verify type dependencies
        run: yarn lint:type-deps

      - name: test (and upload coverage)
        run: |
          yarn backstage-cli repo test --maxWorkers=3 --workerIdleMemoryLimit=1300M --coverage --successCache --successCacheDir .cache/backstage-cli
        env:
          BACKSTAGE_TEST_DISABLE_DOCKER: 1
          BACKSTAGE_TEST_DATABASE_POSTGRES17_CONNECTION_STRING: postgresql://postgres:postgres@localhost:${{ job.services.postgres17.ports[5432] }}
          BACKSTAGE_TEST_DATABASE_POSTGRES13_CONNECTION_STRING: postgresql://postgres:postgres@localhost:${{ job.services.postgres13.ports[5432] }}
          BACKSTAGE_TEST_DATABASE_MYSQL8_CONNECTION_STRING: mysql://root:root@localhost:${{ job.services.mysql8.ports[3306] }}/ignored
          BACKSTAGE_TEST_CACHE_REDIS7_CONNECTION_STRING: redis://localhost:${{ job.services.redis.ports[6379] }}

      - name: Discord notification
        if: ${{ failure() }}
        uses: Ilshidur/action-discord@0c4b27844ba47cb1c7bee539c8eead5284ce9fa9 # 0.3.2
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
        with:
          args: 'Master build failed https://github.com/{{GITHUB_REPOSITORY}}/actions/runs/{{GITHUB_RUN_ID}}'

  # A separate release build that is only run for commits that are the result of merging the "Version Packages" PR
  # We can't re-use the output from the above step, but we'll have a guaranteed node_modules cache and
  # only run the build steps that are necessary for publishing
  release:
    needs: build

    if: needs.build.outputs.needs_release == 'true'

    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@ec9f2d5744a09debf3a187a3f4f675c53b671911 # v2.13.0
        with:
          egress-policy: audit

      # Notify maintainers that a new release is ready to be published
      - name: Discord notification
        uses: Ilshidur/action-discord@0c4b27844ba47cb1c7bee539c8eead5284ce9fa9 # 0.3.2
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_MAINTAINERS_WEBHOOK }}
        with:
          args: 'A new release is ready to be [published](https://github.com/backstage/publishing/actions/workflows/publish-main.yml) from {{GITHUB_SHA}}'
