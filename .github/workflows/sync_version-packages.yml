name: Sync Version Packages PR
on:
  push:
    branches:
      - master

concurrency:
  group: sync-version-packages
  cancel-in-progress: true

jobs:
  create-release-pr:
    name: Create Changeset PR
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@ec9f2d5744a09debf3a187a3f4f675c53b671911 # v2.13.0
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 20000
          fetch-tags: true
          token: ${{ secrets.GH_SERVICE_ACCOUNT_TOKEN }}

      - name: Install Dependencies
        run: yarn --immutable
      - name: Create Release Pull Request
        uses: backstage/changesets-action@a39baf18913e669734ffb00c2fd9900472cfa240 # v2.3.2
        with:
          # Calls out to `changeset version`, but also runs prettier
          version: yarn release
        env:
          GITHUB_TOKEN: ${{ secrets.GH_SERVICE_ACCOUNT_TOKEN }}
          HUSKY: '0'

      - name: Discord notification
        if: ${{ failure() }}
        uses: Ilshidur/action-discord@0c4b27844ba47cb1c7bee539c8eead5284ce9fa9 # 0.3.2
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
        with:
          args: 'Version Packages Sync Failed https://github.com/{{GITHUB_REPOSITORY}}/actions/runs/{{GITHUB_RUN_ID}}'
