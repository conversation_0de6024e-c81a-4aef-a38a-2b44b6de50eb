name: E2E Linux
on:
  # NOTE: If you change these you must update verify_e2e-linux-noop.yml as well
  pull_request:
    paths-ignore:
      - '.changeset/**'
      - 'contrib/**'
      - 'docs/**'
      - 'microsite/**'
      - 'beps/**'
  push:
    branches: [master]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: postgres
        ports:
          - 5432/tcp
        # needed because the postgres container does not provide a healthcheck
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5

    strategy:
      fail-fast: false
      matrix:
        node-version: [20.x, 22.x]

    env:
      CI: true
      NODE_OPTIONS: --max-old-space-size=8192 --no-node-snapshot --experimental-vm-modules

    name: E2E Linux ${{ matrix.node-version }}
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@ec9f2d5744a09debf3a187a3f4f675c53b671911 # v2.13.0
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Configure Git
        run: |
          git config --global user.email <EMAIL>
          git config --global user.name 'GitHub e2e user'

      - name: use node.js ${{ matrix.node-version }}
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: https://registry.npmjs.org/ # Needed for auth
      - name: yarn install
        uses: backstage/actions/yarn-install@b3c1841fd69e1658ac631afafd0fb140a2309024 # v0.6.17
        with:
          cache-prefix: ${{ runner.os }}-v${{ matrix.node-version }}

      - run: yarn tsc
      - run: yarn backstage-cli repo build
      - name: run E2E test
        run: |
          sudo sysctl fs.inotify.max_user_watches=524288
          yarn e2e-test run
        env:
          BACKSTAGE_TEST_DISABLE_DOCKER: 1
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_HOST: localhost
          POSTGRES_PORT: ${{ job.services.postgres.ports[5432] }}
