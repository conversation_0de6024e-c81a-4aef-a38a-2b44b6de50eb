# NO-OP placeholder that always passes for other paths
# This is here so that we're able to set the status check as required

name: Storybook Void
on:
  pull_request:
    paths-ignore:
      - '.github/workflows/verify_storybook.yml'
      - 'storybook/**'
      - 'packages/config/src/**'
      - 'packages/theme/src/**'
      - 'packages/types/src/**'
      - 'packages/errors/src/**'
      - 'packages/version-bridge/src/**'
      - 'packages/test-utils/src/**'
      - 'packages/core-app-api/src/**'
      - 'packages/core-plugin-api/src/**'
      - 'packages/core-components/src/**'
      - '**/*.stories.tsx'

permissions:
  contents: read

jobs:
  noop:
    runs-on: ubuntu-latest

    name: Storybook
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@ec9f2d5744a09debf3a187a3f4f675c53b671911 # v2.13.0
        with:
          egress-policy: audit

      - run: echo NOOP
