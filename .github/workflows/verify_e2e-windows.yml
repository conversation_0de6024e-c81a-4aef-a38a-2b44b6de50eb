# Building on windows is really slow, so this workflow is separate from e2e.yml and only builds on changes
# to the cli itself. They're more likely to introduce issues on windows, compared to changes to core and yarn.lock.

name: E2E Windows
on:
  # NOTE: If you change these you must update verify_e2e-windows-noop.yml as well
  pull_request:
    paths:
      - 'yarn.lock'
      - '.github/workflows/verify_e2e-windows.yml'
      - 'packages/cli/**'
      - 'packages/e2e-test/**'
      - 'packages/create-app/**'
  push:
    branches: [master]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: windows-2022

    strategy:
      matrix:
        node-version: [20.x, 22.x]

    env:
      CI: true
      NODE_OPTIONS: --max-old-space-size=8192 --no-node-snapshot --experimental-vm-modules

    name: E2E Windows ${{ matrix.node-version }}
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@ec9f2d5744a09debf3a187a3f4f675c53b671911 # v2.13.0
        with:
          egress-policy: audit

      # In order to have the create-app template function as if it was downloaded from NPM
      # we need to make sure we checkout files with LF line endings only
      - name: Set git to use LF
        run: |
          git config --global core.autocrlf false
          git config --global core.eol lf

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Configure Git
        run: |
          git config --global user.email <EMAIL>
          git config --global user.name 'GitHub e2e user'

      - name: use node.js ${{ matrix.node-version }}
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: https://registry.npmjs.org/ # Needed for auth

      - name: setup python
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: '3.10'

      - name: Add msbuild to PATH
        uses: microsoft/setup-msbuild@6fb02220983dee41ce7ae257b6f4d8f9bf5ed4ce # v2.0.0

      - name: Setup gyp env
        run: |
          echo 'GYP_MSVS_VERSION=2022' >> $Env:GITHUB_ENV
          echo 'GYP_MSVS_OVERRIDE_PATH=C:\\Dummy' >> $Env:GITHUB_ENV

      - name: Install latest gyp and set node-gyp path
        shell: powershell
        run: |
          npm prefix -g | % {npm config set dev_dir "c:\temp\.gyp2"}
          npm prefix -g | % {npm config set node_gyp "$_\node_modules\node-gyp\bin\node-gyp.js"}

      - name: setup chrome
        uses: browser-actions/setup-chrome@803ef6dfb4fdf22089c9563225d95e4a515820a0 # latest

      - name: yarn install
        uses: backstage/actions/yarn-install@b3c1841fd69e1658ac631afafd0fb140a2309024 # v0.6.17
        with:
          cache-prefix: ${{ runner.os }}-v${{ matrix.node-version }}

      - run: yarn tsc
      - run: yarn backstage-cli repo build
      - name: run E2E test
        run: yarn e2e-test run
        env:
          DEBUG: zombie
          CYPRESS_VERIFY_TIMEOUT: 600000
