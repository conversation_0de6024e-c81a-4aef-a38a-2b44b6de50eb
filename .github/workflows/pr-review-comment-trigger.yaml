# This workflow is used to trigger the "PR Review Comment" workflow. This chaining is needed
# because workflows triggered by pull_request_review_comment do not have access to secrets.

name: PR Review Comment Trigger
on:
  pull_request_review_comment:
    types:
      - created

permissions:
  contents: read

jobs:
  trigger:
    runs-on: ubuntu-latest

    # The "PR Review Comment" workflow will check the success status of this workflow and only mark
    # the PR for re-review if this workflow was successful, which is when the author leaves a review comment.
    if: github.repository == 'backstage/backstage' && github.event.comment.user.id == github.event.pull_request.user.id

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@ec9f2d5744a09debf3a187a3f4f675c53b671911 # v2.13.0
        with:
          egress-policy: audit

      - name: Save PR number
        env:
          PR_NUMBER: ${{ github.event.pull_request.number }}
        run: |
          mkdir -p ./pr
          echo $PR_NUMBER > ./pr/pr_number
      - uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: pr_number-${{ github.event.pull_request.number }}
          path: pr/
          overwrite: true
