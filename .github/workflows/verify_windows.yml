name: Verify Master Branch on Windows
on:
  workflow_dispatch:
  push:
    branches: [master]
  pull_request:
    paths:
      - '.github/workflows/verify_windows.yml'

permissions:
  contents: read

jobs:
  build:
    runs-on: windows-2022

    strategy:
      fail-fast: false
      matrix:
        node-version: [20.x, 22.x]

    env:
      CI: true
      NODE_OPTIONS: --max-old-space-size=8192 --no-node-snapshot --experimental-vm-modules
      INTEGRATION_TEST_GITHUB_TOKEN: ${{ secrets.INTEGRATION_TEST_GITHUB_TOKEN }}
      INTEGRATION_TEST_GITLAB_TOKEN: ${{ secrets.INTEGRATION_TEST_GITLAB_TOKEN }}
      INTEGRATION_TEST_BITBUCKET_TOKEN: ${{ secrets.INTEGRATION_TEST_BITBUCKET_TOKEN }}
      INTEGRATION_TEST_AZURE_TOKEN: ${{ secrets.INTEGRATION_TEST_AZURE_TOKEN }}

    steps:
      - name: Harden <PERSON>
        uses: step-security/harden-runner@ec9f2d5744a09debf3a187a3f4f675c53b671911 # v2.13.0
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: use node.js ${{ matrix.node-version }}
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: https://registry.npmjs.org/ # Needed for auth

      # Windows file operation slowness means there's no point caching this
      - name: yarn install
        run: yarn install --immutable

      - name: lint
        run: yarn backstage-cli repo lint --successCache

      - name: type checking and declarations
        run: yarn tsc:full

      - name: verify type dependencies
        run: yarn lint:type-deps

      - name: test
        run: yarn backstage-cli repo test --maxWorkers=3 --workerIdleMemoryLimit=1300M --successCache
        env:
          BACKSTAGE_TEST_DISABLE_DOCKER: 1

      # credit: https://github.com/appleboy/discord-action/issues/3#issuecomment-731426861
      - name: Discord notification
        if: ${{ failure() }}
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
        run: |
          $MESSAGE=@"
          {\"content\": \"Windows master build failed https://github.com/${{github.repository}}/actions/runs/${{github.run_id}}\"}
          "@
          C:\msys64\usr\bin\curl.exe -i -H "Accept: application/json" -H "Content-Type:application/json" -X POST $env:DISCORD_WEBHOOK --data $MESSAGE
