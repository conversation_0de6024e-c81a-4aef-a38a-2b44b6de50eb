name: Storybook
on:
  # NOTE: If you change these you must update verify_storybook-noop.yml as well
  pull_request:
    paths:
      - '.github/workflows/verify_storybook.yml'
      - '.storybook/**'
      - 'packages/ui/src/**'
      - 'packages/config/src/**'
      - 'packages/theme/src/**'
      - 'packages/types/src/**'
      - 'packages/errors/src/**'
      - 'packages/version-bridge/src/**'
      - 'packages/test-utils/src/**'
      - 'packages/core-app-api/src/**'
      - 'packages/core-plugin-api/src/**'
      - 'packages/core-components/src/**'
      - '**/*.stories.tsx'

jobs:
  chromatic:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [ubuntu-latest]
        node-version: [20.x]

    name: Storybook
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@ec9f2d5744a09debf3a187a3f4f675c53b671911 # v2.13.0
        with:
          egress-policy: audit

      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0 # Required to retrieve git history

      - name: Use node.js ${{ matrix.node-version }}
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: https://registry.npmjs.org/ # Needed for auth

      - name: Install dependencies
        uses: backstage/actions/yarn-install@b3c1841fd69e1658ac631afafd0fb140a2309024 # v0.6.17
        with:
          cache-prefix: ${{ runner.os }}-v${{ matrix.node-version }}

      - name: Build Storybook
        run: yarn build-storybook

      - name: Deploy Storybook to Chromatic
        uses: chromaui/action@1cfa065cbdab28f6ca3afaeb3d761383076a35aa # v11
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          # projectToken intentionally shared to allow collaborators to run Chromatic on forks
          # https://www.chromatic.com/docs/custom-ci-provider#run-chromatic-on-external-forks-of-open-source-projects
          projectToken: chpt_dab72dc0f97d55b
          storybookBuildDir: dist-storybook
