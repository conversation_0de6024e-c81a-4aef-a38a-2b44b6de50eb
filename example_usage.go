package main

// This file demonstrates how to use the BackstageAuthClient
// To run this example, you would typically separate it into its own package
// or rename one of the main functions

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strings"
	"time"
)

// CatalogEntity represents a Backstage catalog entity
type CatalogEntity struct {
	APIVersion string                 `json:"apiVersion"`
	Kind       string                 `json:"kind"`
	Metadata   map[string]interface{} `json:"metadata"`
	Spec       map[string]interface{} `json:"spec,omitempty"`
	Status     map[string]interface{} `json:"status,omitempty"`
}

// CatalogResponse represents the response from the catalog API
type CatalogResponse struct {
	Items []CatalogEntity `json:"items"`
}

func exampleMain() {
	// Configuration - these would typically come from environment variables or config files
	backstageURL := getEnvOrDefault("BACKSTAGE_URL", "http://localhost:3000")
	provider := getEnvOrDefault("BACKSTAGE_AUTH_PROVIDER", "oidc")

	// Create the authentication client
	client := NewBackstageAuthClient(backstageURL, provider)

	// Authenticate with Backstage
	fmt.Println("Starting Backstage authentication...")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	if err := client.Authenticate(ctx); err != nil {
		log.Fatalf("Authentication failed: %v", err)
	}

	fmt.Printf("Successfully authenticated as: %s\n", client.Identity.UserEntityRef)

	// Example 1: List all catalog entities
	fmt.Println("\n--- Fetching catalog entities ---")
	if err := listCatalogEntities(client); err != nil {
		log.Printf("Failed to list catalog entities: %v", err)
	}

	// Example 2: Get a specific entity
	fmt.Println("\n--- Fetching specific entity ---")
	if err := getSpecificEntity(client, "component", "default", "my-service"); err != nil {
		log.Printf("Failed to get specific entity: %v", err)
	}

	// Example 3: Search entities by kind
	fmt.Println("\n--- Searching for components ---")
	if err := searchEntitiesByKind(client, "Component"); err != nil {
		log.Printf("Failed to search entities: %v", err)
	}
}

// listCatalogEntities demonstrates fetching all catalog entities
func listCatalogEntities(client *BackstageAuthClient) error {
	resp, err := client.MakeAuthenticatedRequest("GET", "/api/catalog/entities", nil)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return fmt.Errorf("API returned status %d", resp.StatusCode)
	}

	var catalogResp CatalogResponse
	if err := json.NewDecoder(resp.Body).Decode(&catalogResp); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	fmt.Printf("Found %d entities in the catalog:\n", len(catalogResp.Items))
	for i, entity := range catalogResp.Items {
		if i >= 5 { // Limit output to first 5 entities
			fmt.Printf("... and %d more entities\n", len(catalogResp.Items)-5)
			break
		}
		name := "unknown"
		if metadata, ok := entity.Metadata["name"].(string); ok {
			name = metadata
		}
		fmt.Printf("  - %s (%s)\n", name, entity.Kind)
	}

	return nil
}

// getSpecificEntity demonstrates fetching a specific entity by kind, namespace, and name
func getSpecificEntity(client *BackstageAuthClient, kind, namespace, name string) error {
	endpoint := fmt.Sprintf("/api/catalog/entities/by-name/%s/%s/%s", kind, namespace, name)

	resp, err := client.MakeAuthenticatedRequest("GET", endpoint, nil)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		fmt.Printf("Entity %s/%s/%s not found\n", kind, namespace, name)
		return nil
	}

	if resp.StatusCode != 200 {
		return fmt.Errorf("API returned status %d", resp.StatusCode)
	}

	var entity CatalogEntity
	if err := json.NewDecoder(resp.Body).Decode(&entity); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	fmt.Printf("Found entity: %s (%s)\n", entity.Metadata["name"], entity.Kind)
	if description, ok := entity.Metadata["description"].(string); ok {
		fmt.Printf("  Description: %s\n", description)
	}

	return nil
}

// searchEntitiesByKind demonstrates searching for entities by kind
func searchEntitiesByKind(client *BackstageAuthClient, kind string) error {
	endpoint := fmt.Sprintf("/api/catalog/entities?filter=kind=%s", kind)

	resp, err := client.MakeAuthenticatedRequest("GET", endpoint, nil)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return fmt.Errorf("API returned status %d", resp.StatusCode)
	}

	var catalogResp CatalogResponse
	if err := json.NewDecoder(resp.Body).Decode(&catalogResp); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	fmt.Printf("Found %d entities of kind '%s':\n", len(catalogResp.Items), kind)
	for i, entity := range catalogResp.Items {
		if i >= 10 { // Limit output to first 10 entities
			fmt.Printf("... and %d more entities\n", len(catalogResp.Items)-10)
			break
		}
		name := "unknown"
		if metadata, ok := entity.Metadata["name"].(string); ok {
			name = metadata
		}
		fmt.Printf("  - %s\n", name)
	}

	return nil
}

// getEnvOrDefault returns the value of an environment variable or a default value
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// Additional helper functions for common Backstage operations

// GetUserInfo retrieves information about the current authenticated user
func GetUserInfo(client *BackstageAuthClient) error {
	if client.Identity == nil {
		return fmt.Errorf("not authenticated")
	}

	// Try to get the user entity from the catalog
	parts := strings.Split(client.Identity.UserEntityRef, "/")
	if len(parts) != 3 {
		return fmt.Errorf("invalid user entity ref format")
	}

	kind, namespace, name := parts[0], parts[1], parts[2]
	endpoint := fmt.Sprintf("/api/catalog/entities/by-name/%s/%s/%s", kind, namespace, name)

	resp, err := client.MakeAuthenticatedRequest("GET", endpoint, nil)
	if err != nil {
		return fmt.Errorf("failed to get user info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return fmt.Errorf("failed to get user info, status: %d", resp.StatusCode)
	}

	var userEntity CatalogEntity
	if err := json.NewDecoder(resp.Body).Decode(&userEntity); err != nil {
		return fmt.Errorf("failed to decode user entity: %w", err)
	}

	fmt.Printf("User Information:\n")
	fmt.Printf("  Name: %s\n", userEntity.Metadata["name"])
	if email, ok := userEntity.Spec["profile"].(map[string]interface{})["email"].(string); ok {
		fmt.Printf("  Email: %s\n", email)
	}
	if displayName, ok := userEntity.Spec["profile"].(map[string]interface{})["displayName"].(string); ok {
		fmt.Printf("  Display Name: %s\n", displayName)
	}

	return nil
}

// CreateComponent demonstrates creating a new component in the catalog
func CreateComponent(client *BackstageAuthClient, name, description string) error {
	component := CatalogEntity{
		APIVersion: "backstage.io/v1alpha1",
		Kind:       "Component",
		Metadata: map[string]interface{}{
			"name":        name,
			"description": description,
		},
		Spec: map[string]interface{}{
			"type":      "service",
			"lifecycle": "experimental",
			"owner":     client.Identity.UserEntityRef,
		},
	}

	jsonData, err := json.Marshal(component)
	if err != nil {
		return fmt.Errorf("failed to marshal component: %w", err)
	}

	resp, err := client.MakeAuthenticatedRequest("POST", "/api/catalog/entities",
		strings.NewReader(string(jsonData)))
	if err != nil {
		return fmt.Errorf("failed to create component: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 201 {
		return fmt.Errorf("failed to create component, status: %d", resp.StatusCode)
	}

	fmt.Printf("Successfully created component: %s\n", name)
	return nil
}
