---
id: getting-involved
title: Getting Involved
description: How can you help us build Backstage? We welcome contributions of all kinds, from documentation to code to design.
---

We encourage contributions of all kinds, from documentation to code to design, here's some ideas on how you can help us build and improve Backstage!

### Report bugs

No one likes bugs. Report bugs as an issue [here](https://github.com/backstage/backstage/issues/new?template=bug_template.md).

### Fix bugs or build new features

Look through the GitHub issues for [bugs](https://github.com/backstage/backstage/labels/bug), [good first issues](https://github.com/backstage/backstage/labels/good%20first%20issue) or [help wanted](https://github.com/backstage/backstage/labels/help%20wanted).

### Build a plugin

The value of Backstage grows with every new plugin that gets added. Wouldn't it be fantastic if there was a plugin for every infrastructure project out there? We think so. And we would love your help. Head over to [the community plugins repo](https://github.com/backstage/community-plugins) and get started!

A great reference example of a plugin can be found on [our blog](https://backstage.io/blog/2020/04/06/lighthouse-plugin) (thanks [@fastfrwrd](https://github.com/fastfrwrd)!)

What kind of plugins should/could be created? Some inspiration from the 120+ plugins that we have developed inside Spotify can be found [here](https://backstage.io/demos), but we will keep a running list of suggestions labeled with [[plugin]](https://github.com/backstage/community-plugins/labels/plugin) in our [community plugins repo](https://github.com/backstage/community-plugins).

### Suggesting a plugin

If you start developing a plugin that you aim to release as open source, we suggest that you create a [new Issue](https://github.com/backstage/community-plugins/issues/new/choose) in the community plugins repo. This helps the community know what plugins are in development.

You can also use this process if you have an idea for a good plugin but you hope that someone else will pick up the work.

### Adding non-code Contributions

Since there is such a large landscape of possible development, build, and deployment environments, we welcome community contributions in these areas in the [`/contrib`](https://github.com/backstage/backstage/tree/master/contrib) folder of the project. This is an excellent place to put things that help out the community at large, but which may not fit within the scope of the core product to support natively. Here, you will find Helm charts, alternative Docker images, and much more.

### Write documentation or improve the website

The current documentation is very limited. Help us make the `/docs` folder come alive.

Docs are published to [backstage.io/docs](https://backstage.io/docs). If you
contribute to the documentation, you might want to preview your changes before
submitting them. You'll find the website sources under [/microsite](https://github.com/backstage/backstage/tree/master/microsite)
with instructions for building and locally serving the website in the
[README](/microsite#readme).

For additional information and helpful guidelines on how to contribute to the documentation, check out these [Documentation Guidelines](https://github.com/backstage/backstage/blob/master/CONTRIBUTING.md#documentation-guidelines)!

### Contribute to Storybook

We think the best way to ensure different plugins provide a consistent experience is through a solid set of reusable UI/UX components. Backstage uses [Storybook](http://backstage.io/storybook).

Either help us [create new components](https://github.com/backstage/backstage/labels/help%20wanted) or improve stories for the existing ones (look for files with `*.stories.tsx`).

### Submit feedback

The best way to send feedback is to file [an issue](https://github.com/backstage/backstage/issues).

If you are proposing a feature:

- Explain in detail how it would work.
- Keep the scope as narrow as possible, to make it easier to implement.
- Use appropriate labels
- Remember that this is a volunteer-driven project, and that contributions
  are welcome :)

### Add your company to `ADOPTERS`

Have you started using Backstage? Adding your company to [ADOPTERS](https://github.com/backstage/backstage/blob/master/ADOPTERS.md) really helps the project, you can do this by filling out this [Adopter form](https://form.typeform.com/to/zcOaKikB).
