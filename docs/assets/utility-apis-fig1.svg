<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="296px" height="382px" viewBox="-0.5 -0.5 296 382" style="background-color: rgb(255, 255, 255);"><defs/><g><path d="M 121.01 214 L 154.64 214" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 159.89 214 L 152.89 217.5 L 154.64 214 L 152.89 210.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="41.01" y="194" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 214px; margin-left: 42px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">fooApiRef</div></div></div></foreignObject><text x="81" y="218" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">fooApiRef</text></switch></g><rect x="161.01" y="194" width="80" height="40" fill="#e1d5e7" stroke="#9673a6" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 214px; margin-left: 162px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">FooApi</div></div></div></foreignObject><text x="201" y="218" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">FooApi</text></switch></g><path d="M 41.01 297 L 41.01 274 L 181.01 274 L 181.01 297" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="none"/><path d="M 41.01 297 L 41.01 354 L 181.01 354 L 181.01 297" fill="none" stroke="#d6b656" stroke-miterlimit="10" pointer-events="none"/><path d="M 41.01 297 L 181.01 297" fill="none" stroke="#d6b656" stroke-miterlimit="10" pointer-events="none"/><g fill="#000000" font-family="Helvetica" font-weight="bold" text-anchor="middle" font-size="12px"><text x="110.51" y="290">Plugin</text></g><rect x="51.01" y="314" width="120" height="30" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 329px; margin-left: 52px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">Component</div></div></div></foreignObject><text x="111" y="333" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Component</text></switch></g><path d="M 81.01 314 L 81.01 240.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 81.01 235.12 L 84.51 242.12 L 81.01 240.37 L 77.51 242.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 41.01 34 L 21 34 L 21 214 L 34.64 214" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 39.89 214 L 32.89 217.5 L 34.64 214 L 32.89 210.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 101.01 34 L 191 34 L 191 67.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 191 72.88 L 187.5 65.88 L 191 67.63 L 194.5 65.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="41.01" y="14" width="60" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 34px; margin-left: 42px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">App A</div></div></div></foreignObject><text x="71" y="38" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">App A</text></switch></g><path d="M 41.01 94 L 21 94 L 21 214 L 34.64 214" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 39.89 214 L 32.89 217.5 L 34.64 214 L 32.89 210.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 101.01 94 L 134.64 94" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 139.89 94 L 132.89 97.5 L 134.64 94 L 132.89 90.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="41.01" y="74" width="60" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 94px; margin-left: 42px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">App B</div></div></div></foreignObject><text x="71" y="98" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">App B</text></switch></g><path d="M 241.01 94 L 261 94 L 261 214 L 247.38 214" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 242.13 214 L 249.13 210.5 L 247.38 214 L 249.13 217.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="141.01" y="74" width="100" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 94px; margin-left: 142px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">DefaultFooApi</div></div></div></foreignObject><text x="191" y="98" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">DefaultFooApi</text></switch></g><path d="M 241.01 154 L 261 154 L 261 214 L 247.38 214" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 242.13 214 L 249.13 210.5 L 247.38 214 L 249.13 217.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="141.01" y="134" width="100" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 154px; margin-left: 142px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">CustomFooApi</div></div></div></foreignObject><text x="191" y="158" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">CustomFooApi</text></switch></g><path d="M 141.01 314 L 185.73 239.46" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 188.43 234.96 L 187.83 242.76 L 185.73 239.46 L 181.83 239.16 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 41.01 154 L 21 154 L 21 214 L 34.64 214" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 39.89 214 L 32.89 217.5 L 34.64 214 L 32.89 210.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 101.01 154 L 134.64 154" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 139.89 154 L 132.89 157.5 L 134.64 154 L 132.89 150.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="41.01" y="134" width="60" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 154px; margin-left: 42px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: none; white-space: normal; word-wrap: normal; ">App C</div></div></div></foreignObject><text x="71" y="158" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">App C</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://desk.draw.io/support/solutions/articles/16000042487" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Viewer does not support full SVG 1.1</text></a></switch></svg>
