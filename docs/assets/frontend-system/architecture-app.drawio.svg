<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="371px" height="351px" viewBox="-0.5 -0.5 371 351" content="&lt;mxfile scale=&quot;1&quot; border=&quot;20&quot;&gt;&lt;diagram id=&quot;W2IAjyMExVh-jXkT93Bg&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs/>
    <g>
        <path d="M 115 90 L 115 50" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 185 90 L 185 80 Q 185 70 175 70 L 125 70 Q 115 70 115 60 L 115 50" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 255 90 L 255 80 Q 255 70 245 70 L 125 70 Q 115 70 115 60 L 115 50" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <rect x="100" y="100" width="30" height="10" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 130 100 L 140 100 L 150 108 L 150 110 L 120 110 L 120 108 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(90,135,105)" pointer-events="all"/>
        <path d="M 90 100 L 100 100 L 110 108 L 110 110 L 80 110 L 80 108 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(0,105)scale(1,-1)translate(0,-105)rotate(-90,95,105)" pointer-events="all"/>
        <rect x="100" y="90" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 110 90 L 115 85 L 115 85 L 120 90 L 120 90 L 120 90 L 115 90 L 110 90 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 115 120 L 115 160" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 115 120 L 115 130 Q 115 140 125 140 L 175 140 Q 185 140 185 150 L 185 160" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 115 120 L 115 130 Q 115 140 105 140 L 55 140 Q 45 140 45 150 L 45 160" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <rect x="100" y="110" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 100 120 L 100 120 L 100 120 L 110 120 L 110 120 L 110 130 L 105 125 L 100 130 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 120 120 L 120 120 L 120 120 L 130 120 L 130 120 L 130 130 L 125 125 L 120 130 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="100" y="20" width="30" height="20" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 130 30 L 140 30 L 150 38 L 150 40 L 120 40 L 120 38 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(90,135,35)" pointer-events="all"/>
        <path d="M 90 30 L 100 30 L 110 38 L 110 40 L 80 40 L 80 38 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(0,35)scale(1,-1)translate(0,-35)rotate(-90,95,35)" pointer-events="all"/>
        <rect x="100" y="40" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 110 50 L 110 50 L 110 50 L 120 50 L 120 50 L 120 60 L 115 55 L 110 60 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="170" y="100" width="30" height="20" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 200 100 L 210 100 L 220 108 L 220 110 L 190 110 L 190 108 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(90,205,105)" pointer-events="all"/>
        <path d="M 160 100 L 170 100 L 180 108 L 180 110 L 150 110 L 150 108 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(0,105)scale(1,-1)translate(0,-105)rotate(-90,165,105)" pointer-events="all"/>
        <rect x="170" y="90" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 180 90 L 185 85 L 185 85 L 190 90 L 190 90 L 190 90 L 185 90 L 180 90 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="240" y="100" width="30" height="20" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 270 100 L 280 100 L 290 108 L 290 110 L 260 110 L 260 108 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(90,275,105)" pointer-events="all"/>
        <path d="M 230 100 L 240 100 L 250 108 L 250 110 L 220 110 L 220 108 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(0,105)scale(1,-1)translate(0,-105)rotate(-90,235,105)" pointer-events="all"/>
        <rect x="240" y="90" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 250 90 L 255 85 L 255 85 L 260 90 L 260 90 L 260 90 L 255 90 L 250 90 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="100" y="170" width="30" height="10" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 130 170 L 140 170 L 150 178 L 150 180 L 120 180 L 120 178 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(90,135,175)" pointer-events="all"/>
        <path d="M 90 170 L 100 170 L 110 178 L 110 180 L 80 180 L 80 178 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(0,175)scale(1,-1)translate(0,-175)rotate(-90,95,175)" pointer-events="all"/>
        <rect x="100" y="160" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 115 190 L 115 230" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 115 190 L 115 200 Q 115 210 125 210 L 175 210 Q 185 210 185 220 L 185 230" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 115 190 L 115 200 Q 115 210 105 210 L 55 210 Q 45 210 45 220 L 45 230" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <rect x="100" y="180" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 100 190 L 100 190 L 100 190 L 110 190 L 110 190 L 110 200 L 105 195 L 100 200 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 110 190 L 110 190 L 110 190 L 120 190 L 120 190 L 120 200 L 115 195 L 110 200 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 100 160 L 105 155 L 105 155 L 110 160 L 110 160 L 110 160 L 105 160 L 100 160 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 120 160 L 125 155 L 125 155 L 130 160 L 130 160 L 130 160 L 125 160 L 120 160 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 120 190 L 120 190 L 120 190 L 130 190 L 130 190 L 130 200 L 125 195 L 120 200 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="170" y="170" width="30" height="20" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 200 170 L 210 170 L 220 178 L 220 180 L 190 180 L 190 178 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(90,205,175)" pointer-events="all"/>
        <path d="M 160 170 L 170 170 L 180 178 L 180 180 L 150 180 L 150 178 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(0,175)scale(1,-1)translate(0,-175)rotate(-90,165,175)" pointer-events="all"/>
        <rect x="170" y="160" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 170 160 L 175 155 L 175 155 L 180 160 L 180 160 L 180 160 L 175 160 L 170 160 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 180 160 L 185 155 L 185 155 L 190 160 L 190 160 L 190 160 L 185 160 L 180 160 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="240" y="170" width="30" height="10" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 270 170 L 280 170 L 290 178 L 290 180 L 260 180 L 260 178 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(90,275,175)" pointer-events="all"/>
        <path d="M 230 170 L 240 170 L 250 178 L 250 180 L 220 180 L 220 178 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(0,175)scale(1,-1)translate(0,-175)rotate(-90,235,175)" pointer-events="all"/>
        <rect x="240" y="160" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 240 160 L 245 155 L 245 155 L 250 160 L 250 160 L 250 160 L 245 160 L 240 160 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 260 160 L 265 155 L 265 155 L 270 160 L 270 160 L 270 160 L 265 160 L 260 160 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="310" y="170" width="30" height="10" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 340 170 L 350 170 L 360 178 L 360 180 L 330 180 L 330 178 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(90,345,175)" pointer-events="all"/>
        <path d="M 300 170 L 310 170 L 320 178 L 320 180 L 290 180 L 290 178 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(0,175)scale(1,-1)translate(0,-175)rotate(-90,305,175)" pointer-events="all"/>
        <rect x="310" y="160" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 310 160 L 315 155 L 315 155 L 320 160 L 320 160 L 320 160 L 315 160 L 310 160 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 330 160 L 335 155 L 335 155 L 340 160 L 340 160 L 340 160 L 335 160 L 330 160 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 115 120 L 115 130 Q 115 140 125 140 L 245 140 Q 255 140 255 150 L 255 160" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 115 120 L 115 130 Q 115 140 125 140 L 315 140 Q 325 140 325 150 L 325 160" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <rect x="100" y="240" width="30" height="20" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 130 240 L 140 240 L 150 248 L 150 250 L 120 250 L 120 248 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(90,135,245)" pointer-events="all"/>
        <path d="M 90 240 L 100 240 L 110 248 L 110 250 L 80 250 L 80 248 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(0,245)scale(1,-1)translate(0,-245)rotate(-90,95,245)" pointer-events="all"/>
        <rect x="100" y="230" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 100 230 L 105 225 L 105 225 L 110 230 L 110 230 L 110 230 L 105 230 L 100 230 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 110 230 L 115 225 L 115 225 L 120 230 L 120 230 L 120 230 L 115 230 L 110 230 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 255 190 L 255 230" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <rect x="240" y="180" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 250 190 L 250 190 L 250 190 L 260 190 L 260 190 L 260 200 L 255 195 L 250 200 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="30" y="170" width="30" height="20" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 60 170 L 70 170 L 80 178 L 80 180 L 50 180 L 50 178 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(90,65,175)" pointer-events="all"/>
        <path d="M 20 170 L 30 170 L 40 178 L 40 180 L 10 180 L 10 178 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(0,175)scale(1,-1)translate(0,-175)rotate(-90,25,175)" pointer-events="all"/>
        <rect x="30" y="160" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 30 160 L 35 155 L 35 155 L 40 160 L 40 160 L 40 160 L 35 160 L 30 160 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 50 160 L 55 155 L 55 155 L 60 160 L 60 160 L 60 160 L 55 160 L 50 160 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="240" y="240" width="30" height="20" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 270 240 L 280 240 L 290 248 L 290 250 L 260 250 L 260 248 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(90,275,245)" pointer-events="all"/>
        <path d="M 230 240 L 240 240 L 250 248 L 250 250 L 220 250 L 220 248 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(0,245)scale(1,-1)translate(0,-245)rotate(-90,235,245)" pointer-events="all"/>
        <rect x="240" y="230" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 240 230 L 245 225 L 245 225 L 250 230 L 250 230 L 250 230 L 245 230 L 240 230 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="310" y="180" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 320 190 L 320 190 L 320 190 L 330 190 L 330 190 L 330 200 L 325 195 L 320 200 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 120 230 L 125 225 L 125 225 L 130 230 L 130 230 L 130 230 L 125 230 L 120 230 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="170" y="240" width="30" height="10" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 200 240 L 210 240 L 220 248 L 220 250 L 190 250 L 190 248 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(90,205,245)" pointer-events="all"/>
        <path d="M 160 240 L 170 240 L 180 248 L 180 250 L 150 250 L 150 248 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(0,245)scale(1,-1)translate(0,-245)rotate(-90,165,245)" pointer-events="all"/>
        <rect x="170" y="230" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 170 230 L 175 225 L 175 225 L 180 230 L 180 230 L 180 230 L 175 230 L 170 230 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 180 230 L 185 225 L 185 225 L 190 230 L 190 230 L 190 230 L 185 230 L 180 230 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 190 230 L 195 225 L 195 225 L 200 230 L 200 230 L 200 230 L 195 230 L 190 230 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 185 260 L 185 300" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 185 260 L 185 270 Q 185 280 195 280 L 245 280 Q 255 280 255 290 L 255 300" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <rect x="170" y="250" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 170 260 L 170 260 L 170 260 L 180 260 L 180 260 L 180 270 L 175 265 L 170 270 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 190 260 L 190 260 L 190 260 L 200 260 L 200 260 L 200 270 L 195 265 L 190 270 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="30" y="240" width="30" height="20" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 60 240 L 70 240 L 80 248 L 80 250 L 50 250 L 50 248 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(90,65,245)" pointer-events="all"/>
        <path d="M 20 240 L 30 240 L 40 248 L 40 250 L 10 250 L 10 248 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(0,245)scale(1,-1)translate(0,-245)rotate(-90,25,245)" pointer-events="all"/>
        <rect x="30" y="230" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 30 230 L 35 225 L 35 225 L 40 230 L 40 230 L 40 230 L 35 230 L 30 230 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 40 230 L 45 225 L 45 225 L 50 230 L 50 230 L 50 230 L 45 230 L 40 230 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 50 230 L 55 225 L 55 225 L 60 230 L 60 230 L 60 230 L 55 230 L 50 230 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="170" y="310" width="30" height="20" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 200 310 L 210 310 L 220 318 L 220 320 L 190 320 L 190 318 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(90,205,315)" pointer-events="all"/>
        <path d="M 160 310 L 170 310 L 180 318 L 180 320 L 150 320 L 150 318 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(0,315)scale(1,-1)translate(0,-315)rotate(-90,165,315)" pointer-events="all"/>
        <rect x="170" y="300" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 170 300 L 175 295 L 175 295 L 180 300 L 180 300 L 180 300 L 175 300 L 170 300 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 190 300 L 195 295 L 195 295 L 200 300 L 200 300 L 200 300 L 195 300 L 190 300 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="240" y="310" width="30" height="20" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 270 310 L 280 310 L 290 318 L 290 320 L 260 320 L 260 318 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(90,275,315)" pointer-events="all"/>
        <path d="M 230 310 L 240 310 L 250 318 L 250 320 L 220 320 L 220 318 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(0,315)scale(1,-1)translate(0,-315)rotate(-90,235,315)" pointer-events="all"/>
        <rect x="240" y="300" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 240 300 L 245 295 L 245 295 L 250 300 L 250 300 L 250 300 L 245 300 L 240 300 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 260 300 L 265 295 L 265 295 L 270 300 L 270 300 L 270 300 L 265 300 L 260 300 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 30 260 L 30 260 L 30 260 L 40 260 L 40 260 L 40 270 L 35 265 L 30 270 Z" fill="#dae8fc" stroke="#6c8ebf" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 50 260 L 50 260 L 50 260 L 60 260 L 60 260 L 60 270 L 55 265 L 50 270 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 260 230 L 265 225 L 265 225 L 270 230 L 270 230 L 270 230 L 265 230 L 260 230 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 190 160 L 195 155 L 195 155 L 200 160 L 200 160 L 200 160 L 195 160 L 190 160 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
    </g>
</svg>
