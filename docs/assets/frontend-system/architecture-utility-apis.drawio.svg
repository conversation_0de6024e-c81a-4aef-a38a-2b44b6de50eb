<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="732px" height="462px" viewBox="-0.5 -0.5 732 462" content="&lt;mxfile scale=&quot;1.5&quot; border=&quot;20&quot;&gt;&lt;diagram id=&quot;gKBvn7eBFHudv9VMIRD-&quot; name=&quot;Page-1&quot;&gt;7VtZc6M4EP41foxL3PBoO8nMVu2RqtmtnTwqINuqYMTK8jW/fgVIgBC+wUlm4qmaQg1qpD6+PkQG1mSx/UJhOv+DRCgemCDaDqz7gWnajsP/zwi7guAaQUGYURwVJKMifMM/kCACQV3hCC2VBxkhMcOpSgxJkqCQKTRIKdmoj01JrL41hTOkEb6FMNap/+KIzcW2fFDRvyI8m8s3O+LGCwxfZ5SsEvG6gWlN819xewElK/H8cg4jsqmRrIeBNaGEsOJqsZ2gOJOslFox73HP3XLZFCXslAlmMWEN45XY+ShNxdLYTkoj3xDKpoCBNd7MMUPfUhhmdzdc/Zw2Z4uYjwx+OcVxPCExoflc68HN/nH6klHyimp33PyXzSAJq9GLH6frmxH7WyPK0LZGEpv7gsgCMbrjj2xVe9qpw02lU1Oqbl7Tpy1oUJjRrGRcyZJfCHG2i9bVZasJFkXc6sQwIQlqSJLL5REucJyt/iuK14jhEIobwmf8Q3JakhUNxats4UWQzpDcpfDJbBUHhUlRDBleq75xlWxsTTYpJevc59tk9Dt84fiiCAfGeJbw65BvGnHLGWdGwcUTj8SNBY6ijMeYoiX+AV9yfpn9pgQnLF+8Mx4497o898pdF3Sp5KZFltAkXqz4d5ul3oEhAJ6tmOudYHWy/AX3p2yHNdaOytVTGZDpdMlNoqm+coknaVRX6AQyGJMZJz7FqxlOBqYbc6mNXyi/mrFcnAUlk7eic/e/FZE37pa5Ykb8AQOk22KauJ8hOwzVORNu85gbhAn+RJv6o8U702It1iiUyyvWwPdXLEMurUMIBMCHALRBIACOl99pQKCIGZ1AoNOAQE/HwKAvCASaHA8jHt8l3X3PhDw0Azl+5uPMOyxJuN8KNRSjnRidApdGOe0JUcw3lEFHPvskELXcW6BoPnVEKdzVHhCotdfJNT0boKGqguOlDu5Kw6uHs/P819fct3A2itKYu9JSRwi8SGO04BrhgiMHEOSY9942hhgHrbEliIAOg4gdeGYnUcNoRA3D7iFsSIeqWdXDlqFkmasb/MVFQluzgo+bkjb8NLghHFstLvyGGamng6kUzhuDaRkkhZJMr2MwdTRNfND8V5pUJ/lvINldm/BaKnS5PSCXtN6aCv8mEXmv6S7L1/aL5brl+AbgavuaPYxh+LpkWafJBI+U7xolUX4JF2hD6OtPFNRM/2ijxehL8p5zZhRDW8y+166f84LDEaOqusgGsrhoDUvHQ1zQRfTS8M1ygqHv2jZwPS9wXRCoqRqX9NBwTMc2Lc/0fcNsFPxFwBU8rwPBoCWjuBrlCliq+geTGKOc2T8Mx5hlAhw9/XYUYM/FSphivqQCMYeiPTBcIrrGnM8+6OwfUY0XaCCzHVHdh9Fjv4hqguPtg5LWtWdL+O7DvESwvqFJZSF4mFE+bWl/eO7PlvRK88ywoaTFquJbekwixvCs1q+FGWMIDseZbNBsTVVdsXpLTESsAw2xvSpT2letka37kKWq3mlotAiaWkjS2DSbXMA/KbSdW/9Z7a/Zt6rm4x1Xi46eWn6a7qAqwW5su7Kzeq3xlnx7Nl73LOOV3cjOjNf8NN620sDU7Vk2Q9+4+bXH3DsziLYu5LUHCXpmVvWttKwMJ7wqj+PlqRnXW58eNG1et6/SybpowxlG4CkW0NExdPNAwXRUDp205Vy9LddZyfCIWDi/Uc0QEoqG0+KFv2i5YDU+3mk5KTGsnqoFt62xcXnUOnBUUgUsGaCeB1Vrak+wqo7Jy1j0rISiKwKTbGTWI5N7q0wraOYuF6ZadrNQaDLqrgfm6U2KD3qWI23+PX/LZDRCUx+H0l7bpw43dH5wkfMbXTi/dJsbfOCiO7+vJgj+hb7f5KPVax36vl7cRChFScRdH2RfMHwk//f2Rej35P9yhqxW+3D/m30mcclBVM393cAYKIVpEBwrTS//9E2iwFukBeURX7NxfC40aIxAPy0Yu3lqEnRbQ0vf+jlwp9NvSG6DO3dGo/F8CfLwYfWXHsXj1R/TWA//Aw==&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs/>
    <g>
        <rect x="19.5" y="19.5" width="375" height="60" fill="#e6e6e6" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 248px; height: 1px; padding-top: 33px; margin-left: 14px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                App
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="138" y="37" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    App
                </text>
            </switch>
        </g>
        <path d="M 214.5 289.5 L 294.95 289.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 302.82 289.5 L 292.32 294.75 L 294.95 289.5 L 292.32 284.25 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 187px; margin-left: 168px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 8px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                provides
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="168" y="190" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="8px" text-anchor="middle">
                    provides
                </text>
            </switch>
        </g>
        <rect x="79.5" y="259.5" width="135" height="60" fill="#008a00" stroke="#005700" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 193px; margin-left: 54px;">
                        <div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Catalog Plugin
                                <br/>
                                <font face="Courier New" style="font-size: 10px;">
                                    plugin:catalog
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="98" y="197" fill="#ffffff" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Catalog Plugin...
                </text>
            </switch>
        </g>
        <path d="M 214.5 169.5 L 364.5 169.5 Q 379.5 169.5 379.32 184.5 L 378.56 248.15" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 378.47 256.02 L 373.35 245.46 L 378.56 248.15 L 383.84 245.59 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 99px; margin-left: 201px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                <font style="font-size: 8px;">
                                    replaces
                                    <br/>
                                    implementation
                                    <br/>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="201" y="102" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    replaces...
                </text>
            </switch>
        </g>
        <rect x="79.5" y="139.5" width="135" height="60" fill="#e6e6e6" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 113px; margin-left: 54px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Extension Overrides
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="98" y="117" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Extension Overr...
                </text>
            </switch>
        </g>
        <path d="M 214.5 409.5 L 244.5 409.5 Q 259.5 409.5 274.5 409.5 L 294.95 409.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 302.82 409.5 L 292.32 414.75 L 294.95 409.5 L 292.32 404.25 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 267px; margin-left: 167px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 8px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                provides
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="167" y="270" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="8px" text-anchor="middle">
                    provides
                </text>
            </switch>
        </g>
        <rect x="79.5" y="379.5" width="135" height="60" fill="#008a00" stroke="#005700" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 273px; margin-left: 54px;">
                        <div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Todo Plugin
                                <br/>
                                <font face="Courier New" style="font-size: 10px;">
                                    plugin:todo
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="98" y="277" fill="#ffffff" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Todo Plugin...
                </text>
            </switch>
        </g>
        <rect x="424.5" y="19.5" width="285" height="60" fill="#e6e6e6" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 188px; height: 1px; padding-top: 33px; margin-left: 284px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Backstage Frontend Framework
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="378" y="37" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Backstage Frontend Framework
                </text>
            </switch>
        </g>
        <path d="M 559.5 289.5 L 553.85 289.59" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 545.97 289.7 L 556.39 284.3 L 553.85 289.59 L 556.55 294.79 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="304.5" y="259.5" width="255" height="60" fill="#1ba1e2" stroke="#006eaf" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 168px; height: 1px; padding-top: 193px; margin-left: 204px;">
                        <div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 10px;">
                                    Catalog Client Utility API
                                    <br/>
                                    <font face="Courier New">
                                        api:plugin.catalog.service
                                    </font>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="288" y="197" fill="#ffffff" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Catalog Client Utility API...
                </text>
            </switch>
        </g>
        <rect x="304.5" y="379.5" width="255" height="60" fill="#1ba1e2" stroke="#006eaf" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 168px; height: 1px; padding-top: 273px; margin-left: 204px;">
                        <div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 10px;">
                                    Todo Utility API
                                    <br/>
                                    <font face="Courier New">
                                        api:plugin.todo.api
                                    </font>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="288" y="277" fill="#ffffff" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Todo Utility API...
                </text>
            </switch>
        </g>
        <path d="M 49.5 79.5 L 49.5 151.5 Q 49.5 166.5 49.5 181.5 L 49.5 244.5 Q 49.5 259.5 60.11 270.11 L 72.75 282.75" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 78.31 288.31 L 67.18 284.6 L 72.75 282.75 L 74.6 277.18 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 49.5 169.5 L 49.5 241.5 Q 49.5 256.5 49.5 271.5 L 49.5 379.5 Q 49.5 394.5 60.23 399.86 L 70.96 405.23" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 78 408.75 L 66.26 408.75 L 70.96 405.23 L 70.96 399.36 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 49.5 82.5 L 49.5 154.5 Q 49.5 169.5 59.72 169.5 L 69.95 169.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 77.82 169.5 L 67.32 174.75 L 69.95 169.5 L 67.32 164.25 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 64px; margin-left: 47px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: &quot;Courier New&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                <font face="Helvetica" style="font-size: 8px;">
                                    installs
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="47" y="67" fill="rgb(0, 0, 0)" font-family="Courier New" font-size="10px" text-anchor="middle">
                    installs
                </text>
            </switch>
        </g>
        <rect x="469.5" y="139.5" width="195" height="60" fill="#1ba1e2" stroke="#006eaf" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 113px; margin-left: 314px;">
                        <div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 10px;">
                                    Fetch Utility API
                                    <br/>
                                    <font face="Courier New">
                                        api:core.fetch
                                    </font>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="378" y="117" fill="#ffffff" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Fetch Utility API...
                </text>
            </switch>
        </g>
        <path d="M 567 79.5 L 567 129.95" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 567 137.82 L 561.75 127.32 L 567 129.95 L 572.25 127.32 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 69px; margin-left: 394px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 8px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                provides
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="394" y="72" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="8px" text-anchor="middle">
                    provides
                </text>
            </switch>
        </g>
        <path d="M 432 379.5 L 432 329.05" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 432 321.18 L 437.25 331.68 L 432 329.05 L 426.75 331.68 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 235px; margin-left: 310px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 8px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                depends on
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="310" y="238" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="8px" text-anchor="middle">
                    depends on
                </text>
            </switch>
        </g>
        <path d="M 559.5 289.5 L 589.5 289.5 Q 604.5 289.5 604.46 274.5 L 604.27 208.75" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 604.25 200.88 L 609.53 211.36 L 604.27 208.75 L 599.03 211.39 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 160px; margin-left: 425px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 8px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                depends on
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="425" y="163" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="8px" text-anchor="middle">
                    depends on
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
