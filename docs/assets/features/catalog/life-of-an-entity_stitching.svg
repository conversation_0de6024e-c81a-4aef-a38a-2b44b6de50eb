<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="512px" height="542px" viewBox="-0.5 -0.5 512 542" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2021-09-04T14:02:37.071Z&quot; agent=&quot;5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36&quot; etag=&quot;lrYE7mvcvFKD3bc075jp&quot; version=&quot;15.0.4&quot; type=&quot;google&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;c7558073-3199-34d8-9f00-42111426c3f3&quot;&gt;7VtLc5s6FP41XqYD4r2sHaddtNNMvbjt6o4CMqiRkUfIsd1fXwHiZWGXxo6xiZ1FpINefN95iIMYGZPF5hODy+grDRAZAS3YjIz7EQC65gLxL5Vsc8mdZVq5JGQ4kK0qwQz/RkVXKV3hACWNhpxSwvGyKfRpHCOfN2SQMbpuNptT0px1CUOkCGY+JKr0PxzwKJe6wK7knxEOo2Jm3fbyK0/Qfw4ZXcVyvpjGKL+ygMUw8h6TCAZ0XRMZ05ExYZTyvLTYTBBJcS0Qy/s97LlaLpmhmHfpMLnjm6/uz/sH/VvwP9LI8xrM7lw3H+YFkpXEQq6WbwtwsttD6SjayBivI8zRbAn99Opa6IOQRXxBRE0XxTmNueRXF7c4fkGMYwH0R4LDWAgXOAjSgccJZ/QZTSihLJvGsLOfuBLAJMqm02XlEXKOWJxJgJaOOseE1LrOrfRPzl6T5z8hlzcp1oI2e+HTS1KEoiO6QJxtRZOigy15lDquW6YUrCuNKRtFdW1xpBBKLQ3LwSu6REEy9i/sOTf2Xsme4XZlz3wz9uwbe69kT/d6tz0bKOw9MuqjJBH4AZuIBYyfmCiFaWkac8y3R9LbBD6AyJ37rWz6LnqanwZ6oO1AX8bsOvRaC/RvZjeOo+CIAhHOZZUyHtGQxpBMK+m4QjqFsmrzhdKlFP5CnG+l/cAVp030BV5s+0MylVV+ppUPVlG939Qv3m9lbdcoURx8THcsou4TmCTYz4UPmBRT5beX3tNh0gQEdMV8dEhLDbmbgixE/FBDr10NGCKQ45fmSk5vTMb7NCZT69uYbPN9Im/ZvSNvKcg/YOGRBoS65ezuupy+UXfcW/DoHDzs6wge6j56KkBiyRVaTJktKCOEpVqM22Iw4M3clPqMORx0QUsUOC+6av5lOOjaLd7+rOgW89+cfQdn73V19lafzt4zb5SenFLP6DV+e4oPnIkNrx8hpnCdRHCZFpf5M8rfPWGVoP+24gTHaI+HtJAbmG0e0gVPRpYaO4GHLJOOB5//rLPuh3UF/O8Z6zQeQggCLYnCs4Yg9xaCOvsrB3QNQb0+b7jGJVEKjuA0e1tZJzTrvcH8R61c0xxRqyZJK+Uce94JdHzR0IsO9RrzimUO1O0aoO+dv3dJNnrhbte6CrfrqPnSIZmM2fJC+rw7lYt6snIuIaw1gtS/x7hT2mjnVGyvYc21LkmHLtztXkd23VGz60Nyu3bLcYbzut1edirv7KFgj1J0tiDZ9ZFiMXWlTMbuttfwPni1n9scMV+nHKRSG2VcfTdNVAqKkXKfoYyUKWB5f0ckWYeStGjV5SOjerspgMO2cEJlLk5s/DVuOMcm6tu109xRTsvTXqfm5Umycgu8m+Xco+aCXbitNVumDZL9Ky5PC5ZLdrSDK1M6mF6jgyjkazit1Q07FWu3nJc+a6T1wFAj7eU8p3hnirS28Uqno8RWZ1ff3jq2qkcdZwgyP7pCG1fOd5lt30Sc6HyXqFYfy+RsVF8jGdM/&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><rect x="0" y="360" width="160" height="170" fill="#f5f5f5" stroke="#666666" stroke-dasharray="1 2" pointer-events="all"/><rect x="0" y="200" width="160" height="140" fill="#f5f5f5" stroke="#666666" stroke-dasharray="1 2" pointer-events="all"/><rect x="0" y="10" width="160" height="170" fill="#f5f5f5" stroke="#666666" stroke-dasharray="1 2" pointer-events="all"/><rect x="40" y="30" width="100" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 50px; margin-left: 41px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Processed<br />Entity</div></div></div></foreignObject><text x="90" y="54" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Processed...</text></switch></g><path d="M 140 240 L 213.63 240" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 218.88 240 L 211.88 243.5 L 213.63 240 L 211.88 236.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="40" y="220" width="100" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 240px; margin-left: 41px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Processed<br />Entity</div></div></div></foreignObject><text x="90" y="244" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Processed...</text></switch></g><rect x="40" y="380" width="100" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 400px; margin-left: 41px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Processed<br />Entity</div></div></div></foreignObject><text x="90" y="404" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Processed...</text></switch></g><rect x="410" y="190" width="100" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 210px; margin-left: 411px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Final<br />Entity</div></div></div></foreignObject><text x="460" y="214" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Final...</text></switch></g><path d="M 140 280 L 170 280 Q 180 280 180 270 L 180 250 Q 180 240 190 240 L 213.63 240" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 218.88 240 L 211.88 243.5 L 213.63 240 L 211.88 236.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="60" y="270" width="80" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 280px; margin-left: 61px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Errors</div></div></div></foreignObject><text x="100" y="284" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Errors</text></switch></g><rect x="60" y="80" width="80" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 90px; margin-left: 61px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Errors</div></div></div></foreignObject><text x="100" y="94" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Errors</text></switch></g><rect x="60" y="490" width="80" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 500px; margin-left: 61px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Errors</div></div></div></foreignObject><text x="100" y="504" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Errors</text></switch></g><path d="M 370 240 L 380 240 Q 390 240 390 230 L 390 220 Q 390 210 396.82 210 L 403.63 210" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 408.88 210 L 401.88 213.5 L 403.63 210 L 401.88 206.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 370 240 L 380 240 Q 390 240 390 250 L 390 270 Q 390 280 396.82 280 L 403.63 280" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 408.88 280 L 401.88 283.5 L 403.63 280 L 401.88 276.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="220" y="220" width="150" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><path d="M 235 220 L 235 260 M 355 220 L 355 260" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 240px; margin-left: 236px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Stitcher</div></div></div></foreignObject><text x="295" y="244" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Stitcher</text></switch></g><rect x="60" y="110" width="80" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 120px; margin-left: 61px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Relation</div></div></div></foreignObject><text x="100" y="124" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Relation</text></switch></g><path d="M 140 150 L 170 150 Q 180 150 180 160 L 180 230 Q 180 240 190 240 L 213.63 240" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 218.88 240 L 211.88 243.5 L 213.63 240 L 211.88 236.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 60 150 L 30 150 Q 20 150 20 160 L 20 220 Q 20 230 30 230 L 40 230" fill="none" stroke="#666666" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><rect x="60" y="140" width="80" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 150px; margin-left: 61px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Relation</div></div></div></foreignObject><text x="100" y="154" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Relation</text></switch></g><path d="M 140 310 L 170 310 Q 180 310 180 300 L 180 250 Q 180 240 190 240 L 213.63 240" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 218.88 240 L 211.88 243.5 L 213.63 240 L 211.88 236.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="60" y="300" width="80" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 310px; margin-left: 61px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Relation</div></div></div></foreignObject><text x="100" y="314" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Relation</text></switch></g><path d="M 60 440 L 30 440 Q 20 440 20 430 L 20 260 Q 20 250 30 250 L 40 250" fill="none" stroke="#666666" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 140 440 L 170 440 Q 180 440 180 430 L 180 250 Q 180 240 190 240 L 213.63 240" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 218.88 240 L 211.88 243.5 L 213.63 240 L 211.88 236.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="60" y="430" width="80" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 440px; margin-left: 61px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Relation</div></div></div></foreignObject><text x="100" y="444" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Relation</text></switch></g><path d="M 60 120 L 30 120 Q 20 120 20 110 L 20 0" fill="none" stroke="#666666" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 40 390 L 35 390 Q 30 390 30 380 L 30 320 Q 30 310 40 310 L 60 310" fill="none" stroke="#666666" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><rect x="60" y="460" width="80" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 470px; margin-left: 61px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Relation</div></div></div></foreignObject><text x="100" y="474" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Relation</text></switch></g><path d="M 60 470 L 30 470 Q 20 470 20 480 L 20 540" fill="none" stroke="#666666" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><rect x="410" y="260" width="100" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 280px; margin-left: 411px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Search</div></div></div></foreignObject><text x="460" y="284" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Search</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Viewer does not support full SVG 1.1</text></a></switch></svg>