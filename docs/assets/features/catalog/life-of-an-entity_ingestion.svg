<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="451px" height="321px" viewBox="-0.5 -0.5 451 321" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2021-08-30T07:58:22.063Z&quot; agent=&quot;5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36&quot; etag=&quot;JQdt2U1OCyaNFYmrhq2o&quot; version=&quot;15.0.3&quot; type=&quot;google&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;c7558073-3199-34d8-9f00-42111426c3f3&quot;&gt;7ZrLcpswFIafxst0AAEhy8RxmkU66Uym08tOBgFqZOQR8i1P3yNLAnNx4rZO7NbJJuggDuj/xPklkgEaTpYfBZ7mn3hC2MBzkuUAXQ88z/UDF36pyMpEnHNPRzJBExOrAw/0idiOJjqjCSkbHSXnTNJpMxjzoiCxbMSwEHzR7JZy1rzrFGekE3iIMetGv9JE5joaeWEdvyU0y+2d3fBCnxnj+DETfFaY+xW8IPrMBNs0ZoxljhO+2Aih0QANBedSH02WQ8KUrlYxfd3NlrPVIwtSyF0uoGfRwhles9t7dPkjJI/hzd3VmRngHLOZkeJLMRU8JmVJEjgxKiSVFNDoIciVVWyRU0kepjhW7QVMiwG6yuWEQcuFwzkRkoK+l4xmBcQkVx2wacXwyERAoIQEtMjuSKqGEKocXNAnXkisMjkQSCljQ864WN8XpWnqxbG6VAr+SDbOJOE4DFQKMyR4ArLcqpVbEYBZTfiESLGCLvaCwEAzExpZiIt6dvi2T745M0ITxGZGZlXuGg0cGDq/Qcq+KhsMSAKT2DS5kDnPeIHZqI5erWcmSYyUdZ87rnisSf0kUq7MG4lnkjc5gl5i9U1d/yGwze+b566XJrlurapWcqneSwWb4bKksQ7eUGZT6+GoMTwPCYbMZyImz2hjio3EIiPymX5+P3RBGJZ03nyO/QP0jgmg808CPD8kQM/vFMs4xwUMH1SYw5i7VVKJc4fHYJcNKLYOCl04OtVyQpNE4yclfcLjdTpFZcqpug3kDq4GwXVdQU313Fr6jHGaVLUpbcJ6ZtZuLZRnzgcEPzrZzhhMus9qNBtdeJqWgL/NqbrrX6DrkFsbmxrIZ8HnsPgQHXbg11N1aOwQ1H3B8+q1wP1MMloQE285WBKQKPH7HCzyxihUGEv9Njv7MbMKtjEzz++amev1mJn/al7mdtR+9zKjDdqxFAYH9TJ0TACPy8t2BRgd1MvQvr2MrdfwR21l6CUrcysrWzVu+afGZjO7jaR20/X6tteFfEK257vHZnvd9eNoCftgKIEDL2Tq7RkDjzBTRw+6zPzmtru9WY5i0r9ZHkeB2r/uZ33hN4Wu9sAbQkdvqXPwf+rsoyPT+byjc0fH2vPdlyvJC1aivyEluMyrhHtQ1b1oferpKxN9sla1Y++6Rv+Drig6Ol3dnsJg/HCrvM7u8lZfNbeugdqWiEmU9paNMI7IOG2sibw9FWunhSXo+bLZQ+X1oHQ/QZ8elPbn5vNDQ+mW9tOD0vpsUq0nDwal6wsnBwW1ypff94eZN4Vy8Q7Fb3tKdGAoNvFJQ2mva3t2C28LxX2H4gdHVr7s1uKUoSC7L7BvysWrQYFm/Q8f+itf/R81aPQL&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><rect x="0" y="160" width="450" height="160" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)rotate(-90 7 237)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 152px; height: 1px; padding-top: 237px; margin-left: -69px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Unprocessed Entities</div></div></div></foreignObject><text x="7" y="249" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Unprocessed Entities</text></switch></g><path d="M 130 80 L 130 46.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 130 41.12 L 133.5 48.12 L 130 46.37 L 126.5 48.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 130 120 L 130 173.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 130 178.88 L 126.5 171.88 L 130 173.63 L 133.5 171.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-end; width: 1px; height: 1px; padding-top: 141px; margin-left: 125px;"><div style="box-sizing: border-box; font-size: 0; text-align: right; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">change events</div></div></div></foreignObject><text x="125" y="144" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="end">change events</text></switch></g><rect x="70" y="80" width="120" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><path d="M 70 80 L 70 120 M 190 80 L 190 120" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 100px; margin-left: 71px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Entity Provider</div></div></div></foreignObject><text x="130" y="104" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Entity Provider</text></switch></g><path d="M 320 80 L 320 46.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 320 41.12 L 323.5 48.12 L 320 46.37 L 316.5 48.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 320 120 L 320 173.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 320 178.88 L 316.5 171.88 L 320 173.63 L 323.5 171.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 141px; margin-left: 326px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">change events</div></div></div></foreignObject><text x="326" y="144" fill="#000000" font-family="Helvetica" font-size="11px">change events</text></switch></g><rect x="260" y="80" width="120" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><path d="M 260 80 L 260 120 M 380 80 L 380 120" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 100px; margin-left: 261px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Entity Provider</div></div></div></foreignObject><text x="320" y="104" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Entity Provider</text></switch></g><rect x="90" y="0" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 20px; margin-left: 91px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">External<br />Source</div></div></div></foreignObject><text x="130" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">External...</text></switch></g><rect x="280" y="0" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 20px; margin-left: 281px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">External<br />Source</div></div></div></foreignObject><text x="320" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">External...</text></switch></g><rect x="40" y="180" width="180" height="120" rx="18" ry="18" fill="#ffffff" stroke="#000000" stroke-dasharray="3 3" pointer-events="all"/><rect x="230" y="180" width="180" height="120" rx="18" ry="18" fill="#ffffff" stroke="#000000" stroke-dasharray="3 3" pointer-events="all"/><rect x="50" y="190" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 200px; margin-left: 51px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Entity</div></div></div></foreignObject><text x="70" y="204" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Entity</text></switch></g><rect x="100" y="210" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 220px; margin-left: 101px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Entity</div></div></div></foreignObject><text x="120" y="224" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Entity</text></switch></g><rect x="70" y="250" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 260px; margin-left: 71px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Entity</div></div></div></foreignObject><text x="90" y="264" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Entity</text></switch></g><rect x="150" y="260" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 270px; margin-left: 151px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Entity</div></div></div></foreignObject><text x="170" y="274" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Entity</text></switch></g><rect x="250" y="220" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 230px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Entity</div></div></div></foreignObject><text x="270" y="234" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Entity</text></switch></g><rect x="340" y="200" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 210px; margin-left: 341px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Entity</div></div></div></foreignObject><text x="360" y="214" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Entity</text></switch></g><rect x="300" y="260" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 270px; margin-left: 301px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Entity</div></div></div></foreignObject><text x="320" y="274" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Entity</text></switch></g><rect x="165" y="230" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 240px; margin-left: 166px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Entity</div></div></div></foreignObject><text x="185" y="244" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Entity</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Viewer does not support full SVG 1.1</text></a></switch></svg>