<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="442px" height="621px" viewBox="-0.5 -0.5 442 621" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2021-08-30T06:48:48.032Z&quot; agent=&quot;5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36&quot; etag=&quot;hHhhmKG2AaxT-2zBWbgZ&quot; version=&quot;15.0.3&quot; type=&quot;google&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;c7558073-3199-34d8-9f00-42111426c3f3&quot;&gt;7Vxbc6M2FP41eWwGcfdjknW2md7SejrtPioggzYYeYQc2/31FSCuwhgHG2Ovk4eggyTE+c5dInfa02LzlcKl/xtxUXCnKu7mTvtyp6pANwD/E1O2ggLUSUrxKHYFrSDM8H9IEBVBXWEXRZWOjJCA4WWV6JAwRA6r0CClZF3tNidB9alL6CGJMHNgIFP/wS7zU6qtmgX9Z4Q9P3syMMX7vUHn3aNkFYrnhSRE6Z0FzKYR7xj50CXrEkmb3mlPlBCWXi02TyiI+ZpxLB33vONuvmSKQtZlgAN/Cf/Ufn9HrxHzXxZouiCPP4EMgYhtM2Ygl/NGNAllPvFICINpQX1MXhjF8yq8VfT5lZAlJwJO/I4Y2wqg4YoRTvLZIhB3+ZLp9t94/L2RNb+J6ZLGl02ltRUt+Z0FGyKyog5qedGJgJJB6iHWxhFFT3vGbCg9QzD1KyILxJfEO1AUQIY/qiIEhSR6eb986CvBfNWqItRGA0Y6RCiNaivVKdKlilEFsPyitIyClMB9APQZRz5gsBLv8He4pMRBUcSxVZVpyDDDXC3rAlKFf+1jhmZLmLB/zQ1EFeo5DoInEhCajNXm87nqOJweMUreUemOa76ZhtkG8geiDG1aMRF3VV2p8lYR7XWh3cAQNL+k2bqyG8YKAIdyGwCrl6KBYRUNhe5DbFd50wlgFGEnJT7jIJu6hy5anXXROLYu9gIxW3hJZV6vU2E08/wKY1+ZwqANZqWZeetb6U4xb9w4gsOzz6dkyVDODrgtdVjGjiza7Q91reoPda0Wz+zxn7X+/CJdwXGdpi1ZgL8SrpDwEtVeV/arvTWs1mtniUcz1SwppnKvGq26mZsKUDEUYlg3W5HkC2VD0VPpJx2VPgv+RuJYAdDHA7v1OditG+wHW9OJZE1jfK/BkjZlHPagllQBEnOfMdeO6wpUDe3sgaqiSoyeIUgd/wLZWxfjJvYOK8ZqvwLV+NKAz5t5oGgd7fxYYnqjPUYfR0yfs7XshjiI9BLto26PLaJXLqrw1Uc79a5RmDWqKAwol1VpGbSMcgCq9shQnVwSqkMglBv6U2/t1N2eVreuO7Z2DnWvdXeZPWeXe921rtO6V7O9pIOCN7LeLYPfV4tl1pUH1QdIZTIxb8bOEzs88dkhqNU0/oAs/hQRXmdrc/RM/lMSmO/gZ2mv2S6Bar3gOEyAp0sBnti3aQryIh8u40uxF7o/0CsOAfyxYgGOSztNAaBrINvVmwJAW33TzFNtfdpnT5Bz8b+IMwYnVnCjs7MC44onVPWS4onRoKiOwk7XIwUDtNtpvV5mA4PYaUOy0zOGmeMjeuVWWjfObqXBRVnpPsrb9SjYyHZLgGmMMpBuzMfzFLx9D62GOjivVe98RtA8+hnBnrm+fJYv2eOJl8Aj3Q/soqsPdPOdgvOZUFPOM6Ybhmiy62YGLGYh92SmF1/NEnlsqzGDw2vMtoOaa8xvthH7mONw3qpV6c+9R2S2l7puhrGnYTS7nyUbWdJite8e3iRjOMkYSSJk1RKbySAlUPnY4BNkMCCe7BceXl+uwCeYDd54WKdgyedfZFsgHclyYeTnjG5hqhH/NjHVTH7amLpXA0osa4pfMlrPTYp6KRYoNShS7Zc2KaSJzHogVp/oxB+yAEu9IX0I0lmRvjfS0kQnR1o+qXFDugVpXT0S0tJEJ0daTqVuSLcgbUwm95PSj30c3PdMe3IpkAvCYuMOh54kDzxUYVXcq/gKIYEB9kLepCkoj1mM/iDoC+y6STLQFGdVC55zErJyeUOJf48TUOmTHbZ2X3mjrqlHjKjkKlNanv/hwNC71ppOCIb8LeJL6KEo/hTpxwIDdP249xNg8GbxhX5q1op/gaBN/wc=&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><path d="M 115 200 L 115 233.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 115 238.88 L 111.5 231.88 L 115 233.63 L 118.5 231.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="40" y="160" width="150" height="40" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 180px; margin-left: 41px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Unprocessed Entities</div></div></div></foreignObject><text x="115" y="184" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Unprocessed Entities</text></switch></g><path d="M 115 360 L 115 403.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 115 408.88 L 111.5 401.88 L 115 403.63 L 118.5 401.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="40" y="320" width="150" height="40" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 340px; margin-left: 41px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Processed Entities</div></div></div></foreignObject><text x="115" y="344" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Processed Entities</text></switch></g><path d="M 235 360 L 235 380 Q 235 390 225 390 L 125 390 Q 115 390 115 396.82 L 115 403.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 115 408.88 L 111.5 401.88 L 115 403.63 L 118.5 401.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="200" y="320" width="70" height="40" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 340px; margin-left: 201px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Relations</div></div></div></foreignObject><text x="235" y="344" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Relations</text></switch></g><path d="M 200 170 L 190 170" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 200 190 L 190 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><rect x="200" y="160" width="80" height="40" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 180px; margin-left: 201px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Edges</div></div></div></foreignObject><text x="240" y="184" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Edges</text></switch></g><rect x="40" y="490" width="150" height="40" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 510px; margin-left: 41px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Final Entities</div></div></div></foreignObject><text x="115" y="514" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Final Entities</text></switch></g><rect x="200" y="490" width="80" height="40" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 510px; margin-left: 201px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Search</div></div></div></foreignObject><text x="240" y="514" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Search</text></switch></g><path d="M 315 360 L 315 380 Q 315 390 305 390 L 125 390 Q 115 390 115 396.82 L 115 403.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 115 408.88 L 111.5 401.88 L 115 403.63 L 118.5 401.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="280" y="320" width="70" height="40" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 340px; margin-left: 281px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Errors</div></div></div></foreignObject><text x="315" y="344" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Errors</text></switch></g><path d="M 115 280 L 115 313.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 115 318.88 L 111.5 311.88 L 115 313.63 L 118.5 311.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 115 280 L 115 290 Q 115 300 125 300 L 225 300 Q 235 300 235 306.82 L 235 313.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 235 318.88 L 231.5 311.88 L 235 313.63 L 238.5 311.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 115 280 L 115 290 Q 115 300 125 300 L 305 300 Q 315 300 315 306.82 L 315 313.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 315 318.88 L 311.5 311.88 L 315 313.63 L 318.5 311.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 40 260 L 20 260 Q 10 260 10 250 L 10 190 Q 10 180 20 180 L 33.63 180" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 38.88 180 L 31.88 183.5 L 33.63 180 L 31.88 176.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="40" y="240" width="150" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><path d="M 55 240 L 55 280 M 175 240 L 175 280" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 260px; margin-left: 56px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Processors</div></div></div></foreignObject><text x="115" y="264" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Processors</text></switch></g><path d="M 115 450 L 115 483.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 115 488.88 L 111.5 481.88 L 115 483.63 L 118.5 481.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 115 450 L 115 460 Q 115 470 125 470 L 230 470 Q 240 470 240 476.82 L 240 483.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 240 488.88 L 236.5 481.88 L 240 483.63 L 243.5 481.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="40" y="410" width="150" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><path d="M 55 410 L 55 450 M 175 410 L 175 450" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 430px; margin-left: 56px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Stitcher</div></div></div></foreignObject><text x="115" y="434" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Stitcher</text></switch></g><path d="M 115 120 L 115 153.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 115 158.88 L 111.5 151.88 L 115 153.63 L 118.5 151.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 115 80 L 115 46.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 115 41.12 L 118.5 48.12 L 115 46.37 L 111.5 48.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="40" y="80" width="150" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><path d="M 55 80 L 55 120 M 175 80 L 175 120" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 100px; margin-left: 56px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Entity Providers</div></div></div></foreignObject><text x="115" y="104" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Entity Providers</text></switch></g><rect x="75" y="0" width="80" height="40" rx="6" ry="6" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 20px; margin-left: 76px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">External<br />Sources</div></div></div></foreignObject><text x="115" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">External...</text></switch></g><path d="M 115 580 L 115 536.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 115 531.12 L 118.5 538.12 L 115 536.37 L 111.5 538.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 115 580 L 115 560 Q 115 550 125 550 L 230 550 Q 240 550 240 543.18 L 240 536.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 240 531.12 L 243.5 538.12 L 240 536.37 L 236.5 538.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="75" y="580" width="80" height="40" rx="6" ry="6" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 600px; margin-left: 76px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Catalog<br />API</div></div></div></foreignObject><text x="115" y="604" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Catalog...</text></switch></g><path d="M 0 60 L 440 60" fill="none" stroke="#666666" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 0 220 L 440 220" fill="none" stroke="#666666" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 0 380 L 440 380" fill="none" stroke="#666666" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 0 560 L 440 560" fill="none" stroke="#666666" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><rect x="290" y="220" width="150" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-end; width: 148px; height: 1px; padding-top: 230px; margin-left: 290px;"><div style="box-sizing: border-box; font-size: 0; text-align: right; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #808080; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Processing</div></div></div></foreignObject><text x="438" y="234" fill="#808080" font-family="Helvetica" font-size="12px" text-anchor="end">Processing</text></switch></g><rect x="290" y="380" width="150" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-end; width: 148px; height: 1px; padding-top: 390px; margin-left: 290px;"><div style="box-sizing: border-box; font-size: 0; text-align: right; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #808080; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Stitching</div></div></div></foreignObject><text x="438" y="394" fill="#808080" font-family="Helvetica" font-size="12px" text-anchor="end">Stitching</text></switch></g><rect x="290" y="60" width="150" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-end; width: 148px; height: 1px; padding-top: 70px; margin-left: 290px;"><div style="box-sizing: border-box; font-size: 0; text-align: right; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #808080; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Ingestion</div></div></div></foreignObject><text x="438" y="74" fill="#808080" font-family="Helvetica" font-size="12px" text-anchor="end">Ingestion</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Viewer does not support full SVG 1.1</text></a></switch></svg>