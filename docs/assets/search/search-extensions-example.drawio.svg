<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1160px" height="365px" viewBox="-0.5 -0.5 1160 365" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2023-10-04T12:57:48.635Z&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36&quot; version=&quot;22.0.2&quot; etag=&quot;WMkzAK-eGOpDuwkrBF6a&quot; type=&quot;google&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;E66WZAFE3fXKv47I5-gu&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><rect x="-0.5" y="0" width="1159.5" height="365" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/><rect x="889" y="105" width="160" height="150" fill="none" stroke="none" pointer-events="all"/><path d="M 889.3 104.65 C 889.3 104.65 889.3 104.65 889.3 104.65 M 889.3 104.65 C 889.3 104.65 889.3 104.65 889.3 104.65 M 888.78 117.45 C 892.07 113.24 896.65 108.97 899.28 105.37 M 888.78 117.45 C 892.12 114.61 893.11 111.09 899.28 105.37 M 888.91 129.49 C 897.51 122.28 898.24 118.48 909.91 105.34 M 888.91 129.49 C 894.14 123.66 902.92 114.2 909.91 105.34 M 889.05 141.53 C 902.87 125.19 909.68 116.25 920.54 105.3 M 889.05 141.53 C 898.82 128.85 912.14 116.42 920.54 105.3 M 889.18 153.57 C 906.37 141.66 913.46 126.75 931.17 105.27 M 889.18 153.57 C 899.31 143.07 905.16 134.88 931.17 105.27 M 889.31 165.61 C 906.31 144.71 924.58 131.47 941.8 105.23 M 889.31 165.61 C 902.41 154.03 913.77 137.74 941.8 105.23 M 888.79 178.4 C 905.83 159.84 921.15 141.87 952.43 105.2 M 888.79 178.4 C 910.33 153.42 935.17 125.76 952.43 105.2 M 888.92 190.45 C 909.27 167.81 930.2 144.25 963.06 105.16 M 888.92 190.45 C 905.42 171.92 921.23 153.82 963.06 105.16 M 889.06 202.49 C 911.1 178.99 933.6 151.01 973.69 105.13 M 889.06 202.49 C 912.1 176.55 930.96 151.94 973.69 105.13 M 889.19 214.53 C 918.6 181.35 942.49 146.87 984.32 105.09 M 889.19 214.53 C 923.42 176.07 957.74 137.19 984.32 105.09 M 889.32 226.57 C 922.18 188.83 951.44 150.9 994.95 105.06 M 889.32 226.57 C 920.82 194.94 949.29 164.11 994.95 105.06 M 888.8 239.36 C 931.03 187.31 976.33 138.46 1005.58 105.03 M 888.8 239.36 C 916.8 210.98 943.48 179.14 1005.58 105.03 M 888.93 251.4 C 935.95 195.75 978.74 148.34 1016.21 104.99 M 888.93 251.4 C 929.94 200.89 974.74 151.58 1016.21 104.99 M 893 258.92 C 925.64 218.11 962.33 179.64 1026.84 104.96 M 893 258.92 C 943.37 202.7 993.69 143.86 1026.84 104.96 M 903.63 258.88 C 949.67 206.44 995.16 153.51 1037.47 104.92 M 903.63 258.88 C 942.82 212.57 983.07 168.18 1037.47 104.92 M 914.26 258.85 C 942.28 229.35 966.98 196.26 1048.1 104.89 M 914.26 258.85 C 950.84 212.12 991.64 166.15 1048.1 104.89 M 924.89 258.81 C 962.74 221.32 993.54 188.99 1054.79 109.38 M 924.89 258.81 C 968.73 207.82 1010.66 157.57 1054.79 109.38 M 935.52 258.78 C 971.84 220.45 1008.24 179.93 1054.27 122.18 M 935.52 258.78 C 977.17 213.62 1014.6 167.62 1054.27 122.18 M 946.15 258.74 C 987.88 211.21 1027.3 162.76 1054.4 134.22 M 946.15 258.74 C 984.08 214.98 1019.93 172.57 1054.4 134.22 M 956.78 258.71 C 988.62 223.37 1017.53 187.24 1054.54 146.26 M 956.78 258.71 C 989.73 222.35 1023.08 184.59 1054.54 146.26 M 967.41 258.68 C 1002.8 222.59 1027.67 190.17 1054.67 158.3 M 967.41 258.68 C 995.83 221.1 1028.83 183.8 1054.67 158.3 M 978.04 258.64 C 1003.25 226.53 1031.32 192.85 1054.15 171.1 M 978.04 258.64 C 1006.03 224.48 1035.65 193.03 1054.15 171.1 M 988.67 258.61 C 1008.09 236.9 1020.36 224.09 1054.28 183.14 M 988.67 258.61 C 1010.22 232.87 1032.94 209.8 1054.28 183.14 M 999.3 258.57 C 1016.18 232.93 1035.31 215.72 1054.41 195.18 M 999.3 258.57 C 1011.32 242.65 1024.67 226.63 1054.41 195.18 M 1009.93 258.54 C 1018.57 243.44 1035.8 227.96 1054.55 207.22 M 1009.93 258.54 C 1024.74 240.14 1042.79 222.32 1054.55 207.22 M 1020.56 258.5 C 1029.96 244.77 1041.24 231.4 1054.68 219.26 M 1020.56 258.5 C 1028.91 250.97 1035.77 242.8 1054.68 219.26 M 1031.19 258.47 C 1040.27 248.46 1048.35 240.58 1054.16 232.05 M 1031.19 258.47 C 1042.23 249.06 1050.44 239.14 1054.16 232.05 M 1041.82 258.43 C 1044.51 256.7 1049.81 249.31 1054.29 244.09 M 1041.82 258.43 C 1043.42 257.41 1045.89 254.3 1054.29 244.09" fill="none" stroke="#b0e3e6" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 889 105 C 941.47 108.03 998.79 103.99 1049 105 M 889 105 C 924.85 104.21 957.25 106.01 1049 105 M 1049 105 C 1043.37 156.03 1047.82 205.37 1049 255 M 1049 105 C 1048.72 148.29 1046.58 195.22 1049 255 M 1049 255 C 988.25 250.12 927.51 254.05 889 255 M 1049 255 C 1007.1 257.87 963.28 258.16 889 255 M 889 255 C 890.06 221.46 886.72 184.54 889 105 M 889 255 C 889.72 215.64 890.7 175.71 889 105" fill="none" stroke="#0e8088" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 180px; margin-left: 890px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><b style="font-size: 16px;">Core Routes<br /></b>EXTENSION</div></div></div></foreignObject><text x="969" y="184" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Core Routes...</text></switch></g><rect x="889" y="65" width="160" height="40" fill="none" stroke="none" pointer-events="all"/><path d="M 889.18 64.79 C 889.18 64.79 889.18 64.79 889.18 64.79 M 889.18 64.79 C 889.18 64.79 889.18 64.79 889.18 64.79 M 889.31 76.83 C 893.32 70.4 894.03 72.09 899.81 64.76 M 889.31 76.83 C 891.59 72.86 897.39 68.42 899.81 64.76 M 888.79 89.63 C 895.68 79.87 905.05 69.88 910.44 64.72 M 888.79 89.63 C 896.36 78.82 905.31 72.72 910.44 64.72 M 888.92 101.67 C 901.03 90.2 905.74 81.73 921.07 64.69 M 888.92 101.67 C 902.76 86.67 913.64 74.52 921.07 64.69 M 893.65 108.43 C 905.02 96.11 909.45 87.79 931.7 64.65 M 893.65 108.43 C 908.64 92.5 921.82 73.27 931.7 64.65 M 903.62 109.15 C 914.68 96.43 921.4 84.28 941.67 65.37 M 903.62 109.15 C 913.72 96.12 925.14 85.5 941.67 65.37 M 914.25 109.11 C 922.04 98.82 934.59 84.78 952.3 65.34 M 914.25 109.11 C 920.63 97.68 930.04 88.66 952.3 65.34 M 924.88 109.08 C 933.25 94.22 950.64 84.91 962.93 65.31 M 924.88 109.08 C 938.53 91.6 953.67 73.18 962.93 65.31 M 935.51 109.04 C 953.04 91.24 965.86 79.72 973.57 65.27 M 935.51 109.04 C 941.77 101.83 950.9 90.5 973.57 65.27 M 946.14 109.01 C 959.75 93.03 974.27 77.66 984.2 65.24 M 946.14 109.01 C 956.81 95.45 969.43 82.06 984.2 65.24 M 956.77 108.98 C 969.85 97.77 975.19 84.35 994.83 65.2 M 956.77 108.98 C 965.1 98.58 977.64 87.74 994.83 65.2 M 967.4 108.94 C 976.67 99.53 987.6 91.91 1005.46 65.17 M 967.4 108.94 C 982.03 92.59 996.42 74.98 1005.46 65.17 M 978.03 108.91 C 987.29 92.23 998.84 75.62 1016.09 65.13 M 978.03 108.91 C 992.61 90.3 1005.88 75.73 1016.09 65.13 M 988.66 108.87 C 998.47 92.53 1009.05 78.79 1026.72 65.1 M 988.66 108.87 C 997.38 102.22 1005.75 92.67 1026.72 65.1 M 999.29 108.84 C 1012.1 91.99 1020.42 81.93 1037.35 65.06 M 999.29 108.84 C 1009.21 98.88 1020.46 84.92 1037.35 65.06 M 1009.92 108.8 C 1015.45 96.03 1030.71 89.1 1047.98 65.03 M 1009.92 108.8 C 1021.35 94.75 1033.57 83.56 1047.98 65.03 M 1020.55 108.77 C 1033.19 95.59 1042.36 90.66 1054.67 69.52 M 1020.55 108.77 C 1034.7 92.49 1047.81 77.2 1054.67 69.52 M 1031.18 108.73 C 1037.03 100.64 1043.58 93.71 1054.15 82.32 M 1031.18 108.73 C 1038.79 101.39 1043.62 93.26 1054.15 82.32 M 1041.81 108.7 C 1047.86 107.63 1050.86 104.21 1054.28 94.36 M 1041.81 108.7 C 1046.21 103.71 1046.67 101.36 1054.28 94.36" fill="none" stroke="#b0e3e6" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 889 65 C 944.95 68.63 1001.79 71.71 1049 65 M 889 65 C 951.71 68.23 1014.98 67.33 1049 65 M 1049 65 C 1051.34 83.81 1051.63 94.2 1049 105 M 1049 65 C 1047.91 75.47 1049.85 87.06 1049 105 M 1049 105 C 994.71 104.61 936.51 106.41 889 105 M 1049 105 C 1011.54 106.95 974.81 106.18 889 105 M 889 105 C 887.49 97.49 889.7 90.81 889 65 M 889 105 C 889.41 93.68 889.71 84.82 889 65" fill="none" stroke="#0e8088" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 85px; margin-left: 890px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><p style="line-height: 100%;"><b>Routes</b></p></div></div></div></foreignObject><text x="969" y="90" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">Routes</text></switch></g><path d="M 1030.35 88.89 L 1060 55" fill="none" stroke="none" pointer-events="stroke"/><path d="M 1025.74 94.16 L 1027.71 86.59 L 1032.98 91.2 Z" fill="none" stroke="none" pointer-events="all"/><path d="M 1030.35 88.89 M 1030.35 88.89 C 1035.46 79.08 1047.4 66.77 1060 55 M 1030.35 88.89 C 1037.6 81.69 1047.85 71.75 1060 55" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1027.43 86.92 C 1027.43 86.92 1027.43 86.92 1027.43 86.92 M 1027.43 86.92 C 1027.43 86.92 1027.43 86.92 1027.43 86.92 M 1025.85 94.82 C 1027.55 92.62 1027.43 93.37 1030.44 89.54 M 1025.85 94.82 C 1026.45 92.97 1027.5 92.87 1030.44 89.54" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 1025.74 94.16 M 1025.74 94.16 C 1027.87 92.33 1026.63 86.96 1027.71 86.59 M 1025.74 94.16 C 1027.13 91.92 1026.86 88.24 1027.71 86.59 M 1027.71 86.59 C 1030.26 88.01 1031.71 89.61 1032.98 91.2 M 1027.71 86.59 C 1029.06 86.92 1029.28 88.74 1032.98 91.2 M 1032.98 91.2 C 1030.49 91.83 1028.85 92.18 1025.74 94.16 M 1032.98 91.2 C 1030.18 92.36 1027.67 93.34 1025.74 94.16" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 46px; margin-left: 1090px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">Attachment<br />Point</div></div></div></foreignObject><text x="1090" y="49" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">Attachment...</text></switch></g><rect x="699" y="218.75" width="130" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 234px; margin-left: 700px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Route Artifact</div></div></div></foreignObject><text x="764" y="237" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Route Artifact</text></switch></g><rect x="699" y="141.25" width="130" height="77.5" fill="none" stroke="none" pointer-events="all"/><path d="M 699.31 140.89 C 699.31 140.89 699.31 140.89 699.31 140.89 M 699.31 140.89 C 699.31 140.89 699.31 140.89 699.31 140.89 M 699.05 147.29 C 700 144.48 701.9 143.28 704.3 141.25 M 699.05 147.29 C 700.25 145.67 702.78 142.85 704.3 141.25 M 698.79 153.69 C 701.48 150.13 704.43 147.39 709.28 141.61 M 698.79 153.69 C 703.69 151.04 704.3 147.15 709.28 141.61 M 699.18 159.33 C 706.49 157.13 706.81 150.91 714.93 141.22 M 699.18 159.33 C 704.13 154.33 706.97 146.64 714.93 141.22 M 698.92 165.73 C 705.7 161.68 712.37 150.36 719.91 141.58 M 698.92 165.73 C 702.66 160.7 708.34 156.5 719.91 141.58 M 699.31 171.37 C 707.25 159.38 720.88 152.35 725.56 141.18 M 699.31 171.37 C 708.91 160.22 719.02 149.62 725.56 141.18 M 699.05 177.77 C 705.62 169.64 713.9 157.48 730.54 141.54 M 699.05 177.77 C 705.45 167.6 713.75 160.58 730.54 141.54 M 698.79 184.17 C 710.35 177.21 719.97 161.62 736.19 141.15 M 698.79 184.17 C 712.85 167.64 729.8 150.52 736.19 141.15 M 699.19 189.81 C 711.8 179.43 722.08 165.07 741.17 141.51 M 699.19 189.81 C 714.7 170.51 727.22 156.04 741.17 141.51 M 698.93 196.21 C 710.53 181.88 730.06 166.58 746.82 141.12 M 698.93 196.21 C 715.94 177.86 734.62 155.15 746.82 141.12 M 699.32 201.85 C 715.68 184.51 731.71 164.06 751.8 141.48 M 699.32 201.85 C 721.04 177 740.75 153.63 751.8 141.48 M 699.06 208.25 C 708.39 191.71 724.38 176.4 757.45 141.08 M 699.06 208.25 C 711.55 193.85 725.37 180.08 757.45 141.08 M 698.8 214.65 C 722.38 183.24 751.65 158.72 762.43 141.44 M 698.8 214.65 C 719.96 191.29 743.34 163.49 762.43 141.44 M 699.85 219.54 C 715.7 198.73 736.67 176.49 768.08 141.05 M 699.85 219.54 C 727.13 189.01 753.56 161.88 768.08 141.05 M 705.49 219.14 C 726.62 187.28 752.97 155.46 773.06 141.41 M 705.49 219.14 C 716.98 205.74 732.04 186.34 773.06 141.41 M 710.48 219.5 C 727.62 198.08 744.13 175.86 778.71 141.01 M 710.48 219.5 C 732.81 192.41 756.82 166.6 778.71 141.01 M 715.46 219.86 C 733.74 206.05 746.27 184.48 783.69 141.37 M 715.46 219.86 C 734.4 199.46 754.37 176.29 783.69 141.37 M 721.11 219.47 C 745.53 196.75 766.73 170.16 789.34 140.98 M 721.11 219.47 C 740.08 196.05 761.32 172.5 789.34 140.98 M 726.09 219.83 C 740.45 204.56 756.48 180.84 794.32 141.34 M 726.09 219.83 C 750.95 190.07 773.17 163.53 794.32 141.34 M 731.74 219.43 C 760.44 193.07 781.69 166.19 799.97 140.94 M 731.74 219.43 C 746.05 203.92 761.47 181.39 799.97 140.94 M 736.72 219.79 C 757.69 189.25 788.4 167.07 804.95 141.3 M 736.72 219.79 C 749.48 201.79 766.61 187.14 804.95 141.3 M 742.37 219.4 C 763.42 196.25 780.59 175.24 810.6 140.91 M 742.37 219.4 C 765.6 194.05 786.66 170.65 810.6 140.91 M 747.35 219.76 C 765.02 200.74 780.46 181.41 815.59 141.27 M 747.35 219.76 C 769.29 199.17 786.72 173.27 815.59 141.27 M 753 219.36 C 769.87 203.32 790.11 182.53 821.23 140.87 M 753 219.36 C 773.46 198.51 793.22 172.52 821.23 140.87 M 757.99 219.72 C 780.27 190.28 808.05 163.38 826.22 141.23 M 757.99 219.72 C 778.89 192.25 805.94 165.81 826.22 141.23 M 763.63 219.33 C 782.92 198.31 800.96 178.05 830.55 142.35 M 763.63 219.33 C 777.78 202.58 793.44 182.66 830.55 142.35 M 768.62 219.69 C 791.95 194.01 812.93 168.58 830.28 148.75 M 768.62 219.69 C 788.87 197.36 805.4 174.88 830.28 148.75 M 774.26 219.29 C 789.46 200.12 808.9 174.69 830.68 154.39 M 774.26 219.29 C 787.41 203.75 801.21 192.05 830.68 154.39 M 779.25 219.65 C 796.43 195.9 816.05 172.14 830.42 160.79 M 779.25 219.65 C 798.57 199.93 816.27 175.37 830.42 160.79 M 784.89 219.26 C 791.98 211.52 807.01 194.32 830.16 167.19 M 784.89 219.26 C 799.11 202.3 811.95 189.39 830.16 167.19 M 789.88 219.62 C 802.36 205.34 809.17 195.6 830.55 172.83 M 789.88 219.62 C 803.62 205.39 814.72 193.15 830.55 172.83 M 795.52 219.23 C 804.6 208.71 810.7 195.63 830.29 179.23 M 795.52 219.23 C 806.25 209.88 817.56 196.2 830.29 179.23 M 800.51 219.59 C 806.44 214.38 815.97 202.83 830.68 184.87 M 800.51 219.59 C 810.02 207.3 819.02 199.03 830.68 184.87 M 806.15 219.19 C 815.22 207.73 824.71 195.75 830.42 191.27 M 806.15 219.19 C 813.79 211.79 824.71 198.36 830.42 191.27 M 811.14 219.55 C 821.48 208.86 825.23 206.22 830.16 197.66 M 811.14 219.55 C 815.75 216.55 817.99 209.34 830.16 197.66 M 816.78 219.16 C 823.78 211.67 827.14 206.28 830.56 203.31 M 816.78 219.16 C 818.76 216.91 821.55 209.68 830.56 203.31 M 821.77 219.52 C 825.02 218.23 829.7 213.8 830.29 209.71 M 821.77 219.52 C 823.2 216.8 826.41 213.55 830.29 209.71 M 826.75 219.88 C 827.46 217.22 828.73 216.74 830.69 215.35 M 826.75 219.88 C 828.28 218.12 828.55 217.18 830.69 215.35" fill="none" stroke="#bac8d3" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 699 141.25 C 747.83 139.09 789.39 142.97 829 141.25 M 699 141.25 C 743.45 141.77 785.98 141.75 829 141.25 M 829 141.25 C 830.96 156.99 826.69 182.28 829 218.75 M 829 141.25 C 829.27 156.81 830.8 173.55 829 218.75 M 829 218.75 C 792.48 216.13 762.4 215.93 699 218.75 M 829 218.75 C 789 222.07 744.85 220.85 699 218.75 M 699 218.75 C 700.86 197.17 697.19 175.76 699 141.25 M 699 218.75 C 698.37 189.65 699.33 162.71 699 141.25" fill="none" stroke="#23445d" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 180px; margin-left: 700px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><b>PATH<br />+<br />ELEMENT<br /></b></div></div></div></foreignObject><text x="764" y="184" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PATH...</text></switch></g><path d="M 829 180 Q 859 180 859 132.5 Q 859 85 880.88 85" fill="none" stroke="none" pointer-events="stroke"/><path d="M 887.88 85 L 880.88 88.5 L 880.88 81.5 Z" fill="none" stroke="none" pointer-events="all"/><path d="M 829 180 M 829 180 C 851.07 180.03 860.89 166.08 859 132.5 M 829 180 C 847.42 180.7 862.43 163.63 859 132.5 M 859 132.5 C 855 104.48 865.56 86.25 880.88 85 M 859 132.5 C 860.34 102.8 869.91 86.4 880.88 85" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 880.92 81.46 C 880.92 81.46 880.92 81.46 880.92 81.46 M 880.92 81.46 C 880.92 81.46 880.92 81.46 880.92 81.46 M 880.65 87.86 C 881.07 87.26 883.21 84.1 884.59 83.33 M 880.65 87.86 C 881.44 86.17 882.4 85.56 884.59 83.33" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 887.88 85 M 887.88 85 C 884.66 86.21 883.42 86.96 880.88 88.5 M 887.88 85 C 885.32 86.51 883.42 86.82 880.88 88.5 M 880.88 88.5 C 880.8 86.89 880 82.58 880.88 81.5 M 880.88 88.5 C 880.92 87.02 881.38 85.3 880.88 81.5 M 880.88 81.5 C 884.03 82.68 885.36 84.79 887.88 85 M 880.88 81.5 C 882.33 81.72 884.95 82.53 887.88 85" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 134px; margin-left: 860px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">Input</div></div></div></foreignObject><text x="860" y="137" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">Input</text></switch></g><rect x="279" y="141.25" width="130" height="77.5" fill="none" stroke="none" pointer-events="all"/><path d="M 279.01 141.24 C 279.01 141.24 279.01 141.24 279.01 141.24 M 279.01 141.24 C 279.01 141.24 279.01 141.24 279.01 141.24 M 278.75 147.63 C 278.77 146.31 280.67 145.8 284 141.6 M 278.75 147.63 C 280.76 146.84 281.39 143.71 284 141.6 M 279.14 153.28 C 281 150.45 282.2 150.93 289.64 141.2 M 279.14 153.28 C 281.43 147.69 286.89 145.45 289.64 141.2 M 278.88 159.68 C 280.18 154.76 289.58 153.36 294.63 141.56 M 278.88 159.68 C 283.81 153.32 287.94 148.35 294.63 141.56 M 279.28 165.32 C 285.96 158.03 293.71 154.36 300.27 141.17 M 279.28 165.32 C 286.75 156.34 293.77 148.15 300.27 141.17 M 279.02 171.72 C 283.34 162.86 295.44 157.23 305.26 141.53 M 279.02 171.72 C 289.48 159.25 298.55 148.95 305.26 141.53 M 278.75 178.11 C 292.7 163.16 306.18 151.57 310.9 141.13 M 278.75 178.11 C 286.58 167.24 297.41 157.92 310.9 141.13 M 279.15 183.76 C 286.6 171.13 296.04 166 315.89 141.49 M 279.15 183.76 C 287.04 174.98 295.93 165.95 315.89 141.49 M 278.89 190.15 C 289.25 179.09 297.54 164.95 321.53 141.1 M 278.89 190.15 C 290.01 179.96 299.66 167.91 321.53 141.1 M 279.28 195.8 C 288.06 186.86 297.5 174.9 326.52 141.46 M 279.28 195.8 C 297.08 177.15 309.9 162.15 326.52 141.46 M 279.02 202.2 C 298.07 176.38 314.64 158.36 332.16 141.06 M 279.02 202.2 C 289.3 188.27 302.96 175.27 332.16 141.06 M 278.76 208.59 C 294.34 189.09 320.62 168.15 337.15 141.42 M 278.76 208.59 C 303.44 183.04 323.15 155.13 337.15 141.42 M 279.15 214.24 C 299.34 198.58 314.67 174.16 342.79 141.03 M 279.15 214.24 C 302.5 186.41 328.21 155.51 342.79 141.03 M 279.55 219.88 C 301.35 196.84 326.29 166.01 347.78 141.39 M 279.55 219.88 C 305.08 190.33 331.51 159.89 347.78 141.39 M 285.19 219.49 C 308.73 188.51 330.28 163.21 353.42 141 M 285.19 219.49 C 312.68 189.77 336.6 159.41 353.42 141 M 290.18 219.85 C 306.74 197.6 321.23 184.84 358.41 141.36 M 290.18 219.85 C 315.08 192.74 338.51 166.06 358.41 141.36 M 295.82 219.45 C 313.57 194.59 338.95 175.5 364.05 140.96 M 295.82 219.45 C 319.23 193.22 343.26 166.35 364.05 140.96 M 300.81 219.81 C 325.11 192 346.97 158.24 369.04 141.32 M 300.81 219.81 C 326.05 189.95 354.07 159.93 369.04 141.32 M 306.45 219.42 C 327.43 194.56 348.49 173.09 374.68 140.93 M 306.45 219.42 C 328.69 195.14 352.01 170.54 374.68 140.93 M 311.44 219.78 C 333.48 194.16 357.35 164.53 379.67 141.29 M 311.44 219.78 C 323.1 202.5 337.35 188.59 379.67 141.29 M 317.08 219.38 C 333.09 195.2 356.1 169.03 385.31 140.89 M 317.08 219.38 C 337.34 197.58 358.95 172.02 385.31 140.89 M 322.07 219.74 C 342.68 193.92 371.27 163.6 390.3 141.25 M 322.07 219.74 C 342.3 198.64 361.92 175.97 390.3 141.25 M 327.71 219.35 C 338.86 201.9 356.48 179.88 395.29 141.61 M 327.71 219.35 C 352.04 191.83 377.63 160.54 395.29 141.61 M 332.7 219.71 C 360.35 187.36 382.38 162.11 400.93 141.22 M 332.7 219.71 C 351.28 200.65 366.09 183.01 400.93 141.22 M 338.34 219.31 C 358.46 189.64 388.38 156.88 405.92 141.58 M 338.34 219.31 C 360.51 192.14 381.94 167.72 405.92 141.58 M 343.33 219.67 C 369.44 195.9 383.92 169.23 410.25 142.69 M 343.33 219.67 C 360.91 201.08 376.95 182.17 410.25 142.69 M 348.97 219.28 C 375.41 192.23 397.05 164.75 410.64 148.34 M 348.97 219.28 C 361.19 201.98 379.09 185.44 410.64 148.34 M 353.96 219.64 C 372.23 195.57 396.45 169.45 410.38 154.73 M 353.96 219.64 C 367.83 200.57 383.46 184.17 410.38 154.73 M 359.6 219.24 C 372.26 203.33 393.22 188.81 410.12 161.13 M 359.6 219.24 C 378.37 196.51 395.58 174.27 410.12 161.13 M 364.59 219.6 C 375.88 209.92 389.1 191.04 410.51 166.77 M 364.59 219.6 C 374.41 210.04 383.4 194.85 410.51 166.77 M 370.23 219.21 C 376.71 211.21 385.89 198.76 410.25 173.17 M 370.23 219.21 C 381 210.44 386.53 200.22 410.25 173.17 M 375.22 219.57 C 386.9 201.8 401.1 189.27 410.65 178.81 M 375.22 219.57 C 387.52 202.54 403.42 187.26 410.65 178.81 M 380.86 219.17 C 384.98 209.39 396.09 208.18 410.39 185.21 M 380.86 219.17 C 387.25 211.09 394.63 204.45 410.39 185.21 M 385.85 219.53 C 390.94 210.88 394.5 210.97 410.12 191.61 M 385.85 219.53 C 393.25 209.06 402.57 199.66 410.12 191.61 M 391.49 219.14 C 400.17 211.63 401.54 204.58 410.52 197.25 M 391.49 219.14 C 398.34 212.93 403.15 206.21 410.52 197.25 M 396.48 219.5 C 401.17 215.7 403.64 207.8 410.26 203.65 M 396.48 219.5 C 402.85 215.57 406.12 208.3 410.26 203.65 M 401.47 219.86 C 406.79 216.11 407.49 211.81 410.65 209.29 M 401.47 219.86 C 403.01 216.54 406.57 214.22 410.65 209.29 M 407.11 219.47 C 406.92 219.6 407.98 217.54 410.39 215.69 M 407.11 219.47 C 408.48 218.83 409.15 217.75 410.39 215.69" fill="none" stroke="#bac8d3" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 279 141.25 C 328.28 138.06 379.24 138.03 409 141.25 M 279 141.25 C 318.14 141.37 362.01 141.8 409 141.25 M 409 141.25 C 407.61 158.51 411.3 183.72 409 218.75 M 409 141.25 C 409.22 169.6 406.52 196.91 409 218.75 M 409 218.75 C 362.51 218.79 317.09 219.69 279 218.75 M 409 218.75 C 374.27 216.84 342.2 216.66 279 218.75 M 279 218.75 C 278.5 192.67 278.92 163.05 279 141.25 M 279 218.75 C 278.37 194.23 278.61 172.5 279 141.25" fill="none" stroke="#23445d" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 180px; margin-left: 280px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><b>COMPONENT</b></div></div></div></foreignObject><text x="344" y="184" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">COMPONENT</text></switch></g><rect x="280" y="218.75" width="130" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 234px; margin-left: 281px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Item Artifact</div></div></div></foreignObject><text x="345" y="237" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Item Artifact</text></switch></g><rect x="469" y="105" width="160" height="150" fill="none" stroke="none" pointer-events="all"/><path d="M 469.01 104.99 C 469.01 104.99 469.01 104.99 469.01 104.99 M 469.01 104.99 C 469.01 104.99 469.01 104.99 469.01 104.99 M 469.14 117.03 C 474.72 109.58 478.02 109.4 479.64 104.96 M 469.14 117.03 C 471.49 115.08 474.73 111.03 479.64 104.96 M 469.27 129.08 C 475.35 119.51 486.42 109.69 490.27 104.92 M 469.27 129.08 C 476.71 119.17 484.97 110.21 490.27 104.92 M 468.75 141.87 C 474.11 129.93 483.93 124.84 500.9 104.89 M 468.75 141.87 C 479.49 132.06 490.09 118.05 500.9 104.89 M 468.88 153.91 C 487.74 137.29 505.02 114.85 511.53 104.86 M 468.88 153.91 C 478.02 140.55 488.62 129.33 511.53 104.86 M 469.01 165.95 C 489.51 140.17 505.29 123.19 522.16 104.82 M 469.01 165.95 C 488.1 144.95 507.21 126.16 522.16 104.82 M 469.15 177.99 C 490.62 154.03 517.6 124.42 532.79 104.79 M 469.15 177.99 C 486.01 159.56 505.1 138.38 532.79 104.79 M 469.28 190.03 C 490.46 164.48 514.13 142.6 543.42 104.75 M 469.28 190.03 C 485.36 174.21 501.37 155.3 543.42 104.75 M 468.76 202.83 C 484.95 185.46 508.18 162.23 554.05 104.72 M 468.76 202.83 C 497.74 164.73 529.73 132.59 554.05 104.72 M 468.89 214.87 C 499.41 178.51 528.79 140.7 564.68 104.68 M 468.89 214.87 C 499.9 184.61 525.27 152.33 564.68 104.68 M 469.02 226.91 C 507.52 184.24 547.1 138.31 575.31 104.65 M 469.02 226.91 C 510.03 186.9 547.49 142.06 575.31 104.65 M 469.16 238.95 C 496.14 210.27 520.56 179.21 585.28 105.37 M 469.16 238.95 C 498.53 204.48 532.71 167.24 585.28 105.37 M 469.29 250.99 C 511.77 204.03 553.09 160.08 595.91 105.33 M 469.29 250.99 C 495.54 220.36 526.32 189.04 595.91 105.33 M 473.36 258.51 C 520.13 202.88 567.45 152.15 606.54 105.3 M 473.36 258.51 C 514.94 213.21 555.16 165.49 606.54 105.3 M 483.99 258.47 C 528.47 216.02 569.7 165.57 617.17 105.27 M 483.99 258.47 C 513.65 226.11 541.89 195.39 617.17 105.27 M 494.62 258.44 C 534.2 213.91 579.3 160.98 627.8 105.23 M 494.62 258.44 C 535 211.26 575.5 161.53 627.8 105.23 M 505.25 258.4 C 545.36 204.71 585.5 157.83 634.49 109.72 M 505.25 258.4 C 544.36 211.74 585.15 167.88 634.49 109.72 M 515.88 258.37 C 548.27 227.21 572.66 199.94 634.63 121.77 M 515.88 258.37 C 544.28 221.35 578.85 188.38 634.63 121.77 M 526.51 258.33 C 552.88 231.39 573.2 208.84 634.76 133.81 M 526.51 258.33 C 565.83 212.04 603.06 164.38 634.76 133.81 M 537.14 258.3 C 566.12 228.05 601.51 189.28 634.24 146.6 M 537.14 258.3 C 565.94 225.16 598.36 188.83 634.24 146.6 M 547.11 259.02 C 574.32 228.15 599.89 205.73 634.37 158.64 M 547.11 259.02 C 563.22 240.86 582.32 220.89 634.37 158.64 M 557.75 258.98 C 589.97 227.05 618.58 189.68 634.5 170.68 M 557.75 258.98 C 578.09 237.2 592.74 217.14 634.5 170.68 M 568.38 258.95 C 586.27 239.98 606.97 221.99 634.64 182.72 M 568.38 258.95 C 587.72 235.72 606.73 212.67 634.64 182.72 M 579.01 258.92 C 601.23 235.98 621.76 217.9 634.77 194.77 M 579.01 258.92 C 599.32 235.99 621.41 212.11 634.77 194.77 M 589.64 258.88 C 601.66 245.57 618.16 228.46 634.25 207.56 M 589.64 258.88 C 602.6 245.83 615.74 231.44 634.25 207.56 M 600.27 258.85 C 610.39 249.13 621.37 240.07 634.38 219.6 M 600.27 258.85 C 611.87 245.77 622.76 231.81 634.38 219.6 M 610.9 258.81 C 616.92 254.8 621.92 241.42 634.51 231.64 M 610.9 258.81 C 618.11 250.75 623.64 244.55 634.51 231.64 M 621.53 258.78 C 623.67 258.37 629.18 255.2 634.65 243.68 M 621.53 258.78 C 625.41 253.54 632.09 249.04 634.65 243.68" fill="none" stroke="#b0e3e6" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 469 105 C 531.74 107.17 588.24 106.74 629 105 M 469 105 C 507.71 106.45 546.38 104.44 629 105 M 629 105 C 625.46 140.35 623.42 174.66 629 255 M 629 105 C 625.36 163.94 624.86 221.82 629 255 M 629 255 C 566.86 252.33 500.62 258.23 469 255 M 629 255 C 594.4 251.83 558.91 253.88 469 255 M 469 255 C 469.91 225.35 469.28 196.49 469 105 M 469 255 C 469 201.68 468.91 151.3 469 105" fill="none" stroke="#0e8088" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 180px; margin-left: 470px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><p style="line-height: 100%;"><b>Search Page<br style="font-size: 16px;" /></b><font style="font-size: 12px;">EXTENSION</font></p></div></div></div></foreignObject><text x="549" y="185" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">Search Page...</text></switch></g><rect x="469" y="65" width="160" height="40" fill="none" stroke="none" pointer-events="all"/><path d="M 468.88 65.14 C 468.88 65.14 468.88 65.14 468.88 65.14 M 468.88 65.14 C 468.88 65.14 468.88 65.14 468.88 65.14 M 469.01 77.18 C 469.59 74.74 474.09 73.29 479.51 65.1 M 469.01 77.18 C 470.73 75.25 473.2 72.24 479.51 65.1 M 469.15 89.22 C 474.39 80.43 479.13 75.16 490.14 65.07 M 469.15 89.22 C 478.3 78.98 485.65 70.22 490.14 65.07 M 469.28 101.26 C 480.57 86.79 488.99 80.04 500.77 65.03 M 469.28 101.26 C 480.89 88.49 493.03 73.27 500.77 65.03 M 473.35 108.77 C 490.8 94.07 498.83 73.8 511.4 65 M 473.35 108.77 C 482.57 96.84 492.08 82.92 511.4 65 M 483.98 108.74 C 494.43 89.49 511.48 76.74 522.03 64.96 M 483.98 108.74 C 493.13 97.71 505.23 85.35 522.03 64.96 M 494.61 108.7 C 506.02 93.73 519.73 83.85 532.66 64.93 M 494.61 108.7 C 503.21 99.53 512.46 88.32 532.66 64.93 M 505.24 108.67 C 516.82 94.71 530 80.63 543.29 64.89 M 505.24 108.67 C 512.69 101.62 523.18 91.44 543.29 64.89 M 515.87 108.63 C 529.45 94.91 540.24 76.6 553.92 64.86 M 515.87 108.63 C 526.68 92.35 542.41 79.03 553.92 64.86 M 526.5 108.6 C 537.85 93.68 547.55 80.93 564.55 64.83 M 526.5 108.6 C 535.87 95.45 547.27 84.64 564.55 64.83 M 537.13 108.56 C 548.96 95.4 551.24 90.88 575.18 64.79 M 537.13 108.56 C 550.07 91.37 565.41 72.84 575.18 64.79 M 547.76 108.53 C 558.15 92.49 572.71 77.63 585.81 64.76 M 547.76 108.53 C 559.97 98.02 571.23 82.71 585.81 64.76 M 558.39 108.5 C 566.87 102.54 574.81 92.45 596.44 64.72 M 558.39 108.5 C 566.36 100.78 575.24 88.66 596.44 64.72 M 569.02 108.46 C 580.05 93.04 589.84 78.49 607.07 64.69 M 569.02 108.46 C 580.86 96.62 593.61 80.43 607.07 64.69 M 579.65 108.43 C 593.75 93.16 602.77 87.41 617.7 64.65 M 579.65 108.43 C 589.52 94.16 603.91 81.43 617.7 64.65 M 589.63 109.15 C 605.75 92.6 612.1 80.44 627.68 65.37 M 589.63 109.15 C 598.89 98.81 608.41 86.94 627.68 65.37 M 600.26 109.11 C 606.95 99.96 618.44 82.76 634.37 69.87 M 600.26 109.11 C 612.32 93.88 625.19 81.14 634.37 69.87 M 610.89 109.08 C 614.24 104.99 623.15 96.75 634.5 81.91 M 610.89 109.08 C 619.62 97.79 627.38 90.86 634.5 81.91 M 621.52 109.04 C 622.03 109.23 627.92 101.76 634.64 93.95 M 621.52 109.04 C 624.3 102.81 629.42 101.12 634.64 93.95" fill="none" stroke="#b0e3e6" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 469 65 C 527.24 68.15 578.42 62.83 629 65 M 469 65 C 521.8 63.33 570.4 64 629 65 M 629 65 C 631.08 78.06 625.64 89.71 629 105 M 629 65 C 629.32 76.92 629.46 89.94 629 105 M 629 105 C 569.77 101.78 509.45 100.58 469 105 M 629 105 C 563.6 106.03 503.37 107.48 469 105 M 469 105 C 471.53 95.16 472.67 82.5 469 65 M 469 105 C 467.27 91.9 467.24 81.42 469 65" fill="none" stroke="#0e8088" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 85px; margin-left: 470px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><p style="line-height: 100%;"><b>Items</b></p></div></div></div></foreignObject><text x="549" y="90" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">Items</text></switch></g><path d="M 614.35 88.89 L 644 55" fill="none" stroke="none" pointer-events="stroke"/><path d="M 609.74 94.16 L 611.71 86.59 L 616.98 91.2 Z" fill="none" stroke="none" pointer-events="all"/><path d="M 614.35 88.89 M 614.35 88.89 C 623.03 77.28 632.57 62.02 644 55 M 614.35 88.89 C 624.74 78.25 630.2 69.62 644 55" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 612.03 86.22 C 612.03 86.22 612.03 86.22 612.03 86.22 M 612.03 86.22 C 612.03 86.22 612.03 86.22 612.03 86.22 M 610.46 94.13 C 611.49 91.73 612.51 90.42 614.39 89.6 M 610.46 94.13 C 612.09 91.89 613.85 91.24 614.39 89.6" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 609.74 94.16 M 609.74 94.16 C 611.71 92.13 610.93 88.35 611.71 86.59 M 609.74 94.16 C 609.4 93.39 610.38 91.21 611.71 86.59 M 611.71 86.59 C 614.89 88.56 616.28 90.24 616.98 91.2 M 611.71 86.59 C 613.49 87.07 614.02 88.25 616.98 91.2 M 616.98 91.2 C 615.63 91.63 610.32 93.59 609.74 94.16 M 616.98 91.2 C 615.39 92.52 612.82 93.16 609.74 94.16" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 46px; margin-left: 674px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">Attachment<br />Point</div></div></div></foreignObject><text x="674" y="49" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">Attachment...</text></switch></g><path d="M 629 180 Q 629 180 690.88 180" fill="none" stroke="none" pointer-events="stroke"/><path d="M 697.88 180 L 690.88 183.5 L 690.88 176.5 Z" fill="none" stroke="none" pointer-events="all"/><path d="M 629 180 M 629 180 C 631.07 183.27 649.84 181.91 690.88 180 M 629 180 C 625.7 181.25 648.13 183.88 690.88 180" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 691.14 176.2 C 691.14 176.2 691.14 176.2 691.14 176.2 M 691.14 176.2 C 691.14 176.2 691.14 176.2 691.14 176.2 M 690.88 182.59 C 692.76 180.29 694.71 180.53 694.82 178.07 M 690.88 182.59 C 692.33 181.99 693.02 180.01 694.82 178.07" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 697.88 180 M 697.88 180 C 695.7 180.92 693.93 182.39 690.88 183.5 M 697.88 180 C 697.01 180.24 694.81 181.65 690.88 183.5 M 690.88 183.5 C 689.57 183.29 691.77 179.82 690.88 176.5 M 690.88 183.5 C 690.67 181.45 690.32 178.12 690.88 176.5 M 690.88 176.5 C 694.51 177.37 694.97 179.42 697.88 180 M 690.88 176.5 C 693.04 177.21 696.02 179.64 697.88 180" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 181px; margin-left: 663px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">Output</div></div></div></foreignObject><text x="663" y="184" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">Output</text></switch></g><path d="M 409 180 Q 439 180 439 132.5 Q 439 85 460.88 85" fill="none" stroke="none" pointer-events="stroke"/><path d="M 467.88 85 L 460.88 88.5 L 460.88 81.5 Z" fill="none" stroke="none" pointer-events="all"/><path d="M 409 180 M 409 180 C 431.91 183.06 436.35 160.43 439 132.5 M 409 180 C 432.55 182.39 443.43 162.32 439 132.5 M 439 132.5 C 435.43 98.84 444.02 88.49 460.88 85 M 439 132.5 C 438.93 98.64 448.23 81.08 460.88 85" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 460.62 81.8 C 460.62 81.8 460.62 81.8 460.62 81.8 M 460.62 81.8 C 460.62 81.8 460.62 81.8 460.62 81.8 M 461.01 87.45 C 461.24 85.33 464.14 85.74 464.29 83.67 M 461.01 87.45 C 461.58 86.09 462.84 85.44 464.29 83.67" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 467.88 85 M 467.88 85 C 464.91 84.5 463.3 88.18 460.88 88.5 M 467.88 85 C 465.73 86.24 462.67 87.2 460.88 88.5 M 460.88 88.5 C 461.22 87.04 462.12 84.09 460.88 81.5 M 460.88 88.5 C 460.96 86.51 461.28 85.38 460.88 81.5 M 460.88 81.5 C 461.86 82.56 466.52 82.21 467.88 85 M 460.88 81.5 C 462.31 82.39 465.67 83.39 467.88 85" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 136px; margin-left: 440px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">Input</div></div></div></foreignObject><text x="440" y="139" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">Input</text></switch></g><rect x="49" y="105" width="160" height="150" fill="none" stroke="none" pointer-events="all"/><path d="M 48.71 105.34 C 48.71 105.34 48.71 105.34 48.71 105.34 M 48.71 105.34 C 48.71 105.34 48.71 105.34 48.71 105.34 M 48.84 117.38 C 50.98 113.92 58.08 110.59 59.34 105.3 M 48.84 117.38 C 53.21 115.06 55.22 110.02 59.34 105.3 M 48.97 129.42 C 53.61 120.58 60.26 115.23 69.97 105.27 M 48.97 129.42 C 57.27 119.8 64.16 111.91 69.97 105.27 M 49.11 141.46 C 55.11 134.51 68.65 123.13 80.6 105.23 M 49.11 141.46 C 61.66 129.84 73.54 116.74 80.6 105.23 M 49.24 153.5 C 67.95 137.41 80.77 120.32 91.23 105.2 M 49.24 153.5 C 64.37 139.19 79.11 120.3 91.23 105.2 M 48.72 166.3 C 61.43 152.88 77.96 138.33 101.86 105.16 M 48.72 166.3 C 64.19 147.83 79.6 127.74 101.86 105.16 M 48.85 178.34 C 76.73 149.19 97.04 123.58 112.49 105.13 M 48.85 178.34 C 65.54 158.29 80.36 139.06 112.49 105.13 M 48.98 190.38 C 75.57 164.48 96.62 136 123.12 105.1 M 48.98 190.38 C 64.5 169.29 83.73 149.58 123.12 105.1 M 49.12 202.42 C 80.86 164.35 108.82 133.35 133.75 105.06 M 49.12 202.42 C 72.19 174.47 95.61 149.2 133.75 105.06 M 49.25 214.46 C 76.79 183.29 96.95 161.16 144.38 105.03 M 49.25 214.46 C 76.88 186.68 98.92 159.4 144.38 105.03 M 48.73 227.26 C 82.29 182.13 117.2 146.95 155.01 104.99 M 48.73 227.26 C 78.26 194.22 110.68 155.32 155.01 104.99 M 48.86 239.3 C 83.43 195.94 119.14 156.81 165.64 104.96 M 48.86 239.3 C 92.93 190.49 134.87 138.42 165.64 104.96 M 48.99 251.34 C 99.09 192.1 142.8 138.56 176.27 104.92 M 48.99 251.34 C 78.6 212.55 110.82 176.37 176.27 104.92 M 53.06 258.85 C 101.85 203.59 149.28 151.17 186.9 104.89 M 53.06 258.85 C 80.82 226.29 109.68 193.79 186.9 104.89 M 63.69 258.82 C 99.53 218.27 138.17 175.36 197.53 104.85 M 63.69 258.82 C 100.33 220.56 131.08 183.59 197.53 104.85 M 74.32 258.78 C 100.25 227.79 134.03 190.51 208.16 104.82 M 74.32 258.78 C 112.89 213.31 151.69 169.85 208.16 104.82 M 84.95 258.75 C 130.54 210.83 175.23 157.11 214.2 110.07 M 84.95 258.75 C 119.46 220.41 156.01 179.73 214.2 110.07 M 95.58 258.71 C 128.29 226 159.94 183.79 214.33 122.11 M 95.58 258.71 C 141.18 208.3 183.13 158.81 214.33 122.11 M 106.21 258.68 C 139.23 213.78 176.86 170.58 214.46 134.15 M 106.21 258.68 C 128.85 233.98 153.46 206.96 214.46 134.15 M 116.84 258.64 C 142.97 222.68 172.36 193.72 214.6 146.19 M 116.84 258.64 C 137.92 234.61 159.41 208.76 214.6 146.19 M 127.47 258.61 C 148.41 235.54 170.85 207.41 214.73 158.23 M 127.47 258.61 C 154.68 230.57 178.8 198.93 214.73 158.23 M 138.1 258.57 C 155.51 238.7 173.73 223.48 214.21 171.03 M 138.1 258.57 C 163.68 228.5 194.16 196.33 214.21 171.03 M 148.73 258.54 C 170.42 237.9 186.3 215.82 214.34 183.07 M 148.73 258.54 C 164.36 243.21 177.13 224.02 214.34 183.07 M 159.36 258.5 C 178.06 242.09 193.73 217.81 214.47 195.11 M 159.36 258.5 C 179.63 237.48 196.49 217.15 214.47 195.11 M 169.99 258.47 C 182.11 243.33 193.9 231.87 214.6 207.15 M 169.99 258.47 C 181.31 246.71 189.55 233.87 214.6 207.15 M 180.62 258.44 C 185.71 249.07 196.06 237.11 214.74 219.19 M 180.62 258.44 C 189.51 246.12 200.32 235.88 214.74 219.19 M 191.25 258.4 C 202.82 247.74 210.38 242.36 214.22 231.99 M 191.25 258.4 C 196.86 252.76 203.01 246.55 214.22 231.99 M 201.88 258.37 C 206.19 251.81 204.54 249.33 214.35 244.03 M 201.88 258.37 C 204.28 255.55 207.28 252.27 214.35 244.03" fill="none" stroke="#b0e3e6" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 49 105 C 106.03 106.69 164.88 105.86 209 105 M 49 105 C 109.79 107.95 169.8 107.51 209 105 M 209 105 C 208.41 138.74 208.65 162.44 209 255 M 209 105 C 206.38 138.11 208.09 170.14 209 255 M 209 255 C 173.92 257.49 137.57 252.39 49 255 M 209 255 C 146.47 250.91 87.47 251.18 49 255 M 49 255 C 48.93 216.5 55.23 175.12 49 105 M 49 255 C 50.45 194.57 50.03 137.24 49 105" fill="none" stroke="#0e8088" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 180px; margin-left: 50px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><p style="line-height: 120%;"><font style=""><b><span style="font-size: 16px;">Search Result Item</span><br /></b><font style="font-size: 12px;">EXTENSION</font></font></p></div></div></div></foreignObject><text x="129" y="184" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Search Result Item...</text></switch></g><rect x="49" y="65" width="160" height="40" fill="none" stroke="none" pointer-events="all"/><path d="M 49.24 64.72 C 49.24 64.72 49.24 64.72 49.24 64.72 M 49.24 64.72 C 49.24 64.72 49.24 64.72 49.24 64.72 M 48.72 77.52 C 55.39 71.17 56.56 71.7 59.87 64.69 M 48.72 77.52 C 54.01 74.06 55.35 69.99 59.87 64.69 M 48.85 89.56 C 53.44 86.03 57.88 74.3 70.5 64.66 M 48.85 89.56 C 55.43 80.57 64.98 74.57 70.5 64.66 M 48.98 101.6 C 55.36 93.67 61.53 78.72 80.47 65.38 M 48.98 101.6 C 57.93 92.23 68.14 82.91 80.47 65.38 M 53.05 109.11 C 66.73 94.04 84.88 72.32 91.1 65.34 M 53.05 109.11 C 64.33 96.5 73.11 88.13 91.1 65.34 M 63.68 109.08 C 78.08 95.62 91.67 77.61 101.73 65.31 M 63.68 109.08 C 70.55 97.15 82.79 89.69 101.73 65.31 M 74.31 109.05 C 80.76 102.36 86.75 88.56 112.36 65.27 M 74.31 109.05 C 84 98.4 93.56 84.34 112.36 65.27 M 84.94 109.01 C 90.93 103.93 98.2 94.52 122.99 65.24 M 84.94 109.01 C 96.65 94.16 106.68 81.44 122.99 65.24 M 95.57 108.98 C 111.23 94.3 124.19 77.49 133.62 65.2 M 95.57 108.98 C 101.97 98.41 113.15 89.86 133.62 65.2 M 106.2 108.94 C 118.24 95.09 130.73 81.54 144.25 65.17 M 106.2 108.94 C 115.01 99.61 122.91 88.94 144.25 65.17 M 116.83 108.91 C 128.72 93.33 137.94 84.48 154.88 65.13 M 116.83 108.91 C 124.82 100.12 136.09 87.98 154.88 65.13 M 127.46 108.87 C 137.88 93.35 154.15 74.78 165.51 65.1 M 127.46 108.87 C 143.06 92.14 156.75 74.73 165.51 65.1 M 138.09 108.84 C 155.62 92.98 170.1 77.49 176.14 65.07 M 138.09 108.84 C 148.59 93.82 160.65 80.42 176.14 65.07 M 148.72 108.8 C 158.81 97.38 169.09 85.36 186.77 65.03 M 148.72 108.8 C 158.22 97.66 165.64 89.63 186.77 65.03 M 159.35 108.77 C 173.37 97.17 182.68 77.82 197.4 65 M 159.35 108.77 C 169.43 99.02 177.93 86.37 197.4 65 M 169.98 108.74 C 180.19 96.28 191.97 81.28 208.03 64.96 M 169.98 108.74 C 185.88 92.59 199.15 76.02 208.03 64.96 M 180.61 108.7 C 197.83 89.95 205.29 75.41 214.73 69.46 M 180.61 108.7 C 191.75 95.7 201.97 85.05 214.73 69.46 M 191.24 108.67 C 198.73 101.78 212.21 86.11 214.21 82.25 M 191.24 108.67 C 199.46 97.78 206.41 92.32 214.21 82.25 M 201.87 108.63 C 204.02 106.38 210.4 98.26 214.34 94.29 M 201.87 108.63 C 204.12 106.11 206.95 104.14 214.34 94.29" fill="none" stroke="#b0e3e6" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 49 65 C 84.87 62.05 125.95 67.03 209 65 M 49 65 C 109.24 63.08 171.74 65.53 209 65 M 209 65 C 204.31 80.64 204.75 99.34 209 105 M 209 65 C 210.15 79.4 209.17 94.42 209 105 M 209 105 C 169.45 101.85 125.57 103.82 49 105 M 209 105 C 158.11 102.77 107.64 100.92 49 105 M 49 105 C 52.28 89.17 50.9 77.09 49 65 M 49 105 C 47.29 91.79 48.4 80.44 49 65" fill="none" stroke="#0e8088" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 85px; margin-left: 50px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><p style="line-height: 100%;"><br /></p></div></div></div></foreignObject><text x="129" y="90" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle"></text></switch></g><path d="M 209 180 Q 244 180 270.88 180.08" fill="none" stroke="none" pointer-events="stroke"/><path d="M 277.88 180.1 L 270.87 183.58 L 270.89 176.58 Z" fill="none" stroke="none" pointer-events="all"/><path d="M 209 180 M 209 180 C 235.24 182.38 253.5 178.58 270.88 180.08 M 209 180 C 229.06 178.53 250.12 184.07 270.88 180.08" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 270.89 176.58 C 270.89 176.58 270.89 176.58 270.89 176.58 M 270.89 176.58 C 270.89 176.58 270.89 176.58 270.89 176.58 M 270.63 182.98 C 270.92 181.8 272.55 180.86 274.57 178.45 M 270.63 182.98 C 271.89 181.26 272.96 179.31 274.57 178.45" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 277.88 180.1 M 277.88 180.1 C 275.12 181.95 274.82 181.45 270.87 183.58 M 277.88 180.1 C 274.95 181.05 272.94 182.74 270.87 183.58 M 270.87 183.58 C 269.64 181.72 270.17 179.89 270.89 176.58 M 270.87 183.58 C 270.64 181.3 270.85 179.95 270.89 176.58 M 270.89 176.58 C 273.04 175.96 273.4 177.09 277.88 180.1 M 270.89 176.58 C 273.91 177.91 276.36 178.56 277.88 180.1" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 181px; margin-left: 247px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">Output</div></div></div></foreignObject><text x="247" y="184" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">Output</text></switch></g><rect x="469" y="265" width="160" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 280px; margin-left: 470px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">id: plugin.search.page</div></div></div></foreignObject><text x="549" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">id: plugin.search.page</text></switch></g><rect x="249" y="265" width="190" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 188px; height: 1px; padding-top: 280px; margin-left: 250px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">at: <font color="#009999">plugin.search.page</font>/<b style=""><font color="#006666">items</font></b></div></div></div></foreignObject><text x="344" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">at: plugin.search.page/items</text></switch></g><rect x="889" y="265" width="160" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 280px; margin-left: 890px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">id: core.router</div></div></div></foreignObject><text x="969" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">id: core.router</text></switch></g><rect x="49" y="265" width="160" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 280px; margin-left: 50px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">id: plugin.search.result.item</div></div></div></foreignObject><text x="129" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">id: plugin.search.result.i...</text></switch></g><rect x="669" y="265" width="190" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 188px; height: 1px; padding-top: 280px; margin-left: 670px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">at: <font color="#009999">core.router</font>/<b style=""><font color="#006666">routes</font></b></div></div></div></foreignObject><text x="764" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">at: core.router/routes</text></switch></g><path d="M 294.53 292.88 L 289 315" fill="none" stroke="none" pointer-events="stroke"/><path d="M 296.23 286.08 L 297.93 293.72 L 291.14 292.03 Z" fill="none" stroke="none" pointer-events="all"/><path d="M 294.53 292.88 M 294.53 292.88 C 296.63 301.02 293.89 301.57 289 315 M 294.53 292.88 C 294.16 299.44 291.22 309.83 289 315" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 296.24 286.07 C 296.24 286.07 296.24 286.07 296.24 286.07 M 296.24 286.07 C 296.24 286.07 296.24 286.07 296.24 286.07 M 294.01 294.73 C 295.09 292.91 295.52 292.75 297.29 290.95 M 294.01 294.73 C 295.23 293.2 296.11 293 297.29 290.95" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 296.23 286.08 M 296.23 286.08 C 295.74 288.32 297.53 289.55 297.93 293.72 M 296.23 286.08 C 297.2 288.42 298.3 292.16 297.93 293.72 M 297.93 293.72 C 295.56 294.48 291.74 292.22 291.14 292.03 M 297.93 293.72 C 295.27 293.43 293.19 291.96 291.14 292.03 M 291.14 292.03 C 293.17 290.72 295.64 287.97 296.23 286.08 M 291.14 292.03 C 292.53 290.79 293.55 289.03 296.23 286.08" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 329px; margin-left: 290px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">Target Extension<br />Identification</div></div></div></foreignObject><text x="290" y="332" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">Target Extension...</text></switch></g><path d="M 400.97 292.88 L 406.5 315" fill="none" stroke="none" pointer-events="stroke"/><path d="M 399.27 286.08 L 404.36 292.03 L 397.57 293.72 Z" fill="none" stroke="none" pointer-events="all"/><path d="M 400.97 292.88 M 400.97 292.88 C 401.27 296.95 404.43 306.3 406.5 315 M 400.97 292.88 C 401.31 299.14 404.48 305.17 406.5 315" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 399.54 285.76 C 399.54 285.76 399.54 285.76 399.54 285.76 M 399.54 285.76 C 399.54 285.76 399.54 285.76 399.54 285.76 M 397.97 293.67 C 399.23 291.46 401.05 291.76 401.91 289.14 M 397.97 293.67 C 398.46 292.9 400.4 291.42 401.91 289.14" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 399.27 286.08 M 399.27 286.08 C 402.47 287.21 402.63 291.07 404.36 292.03 M 399.27 286.08 C 401.12 287.22 401.39 289.43 404.36 292.03 M 404.36 292.03 C 403.54 293.95 401.28 292.57 397.57 293.72 M 404.36 292.03 C 401.85 292.74 400.66 293.51 397.57 293.72 M 397.57 293.72 C 398.1 292.01 397.67 288.79 399.27 286.08 M 397.57 293.72 C 397.98 291.63 398.34 290.94 399.27 286.08" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 329px; margin-left: 411px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">Target Extension<br />Input Attachment Point</div></div></div></foreignObject><text x="411" y="332" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">Target Extension...</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>