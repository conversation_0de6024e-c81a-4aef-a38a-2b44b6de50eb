<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="661px" height="259px" viewBox="-0.5 -0.5 661 259" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2023-08-26T22:43:32.067Z&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36&quot; etag=&quot;1Jp8IrwoE_BMXggvxxVp&quot; version=&quot;21.6.9&quot; type=&quot;device&quot;&gt;&lt;diagram id=&quot;TTnOLjRJzGgt66t77H9m&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><rect x="40" y="31" width="80" height="40" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 51px; margin-left: 41px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">User</div></div></div></foreignObject><text x="80" y="55" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">User</text></switch></g><path d="M 220 181 L 80 181 L 80 77.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 80 72.12 L 83.5 79.12 L 80 77.37 L 76.5 79.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="220" y="31" width="120" height="170" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 38px; margin-left: 221px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Plugin</div></div></div></foreignObject><text x="280" y="50" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">Plugin</text></switch></g><path d="M 500 179.07 L 420.5 179.1 L 346.37 179.47" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 341.12 179.49 L 348.1 175.96 L 346.37 179.47 L 348.14 182.96 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 180px; margin-left: 421px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">Permission Response</div></div></div></foreignObject><text x="421" y="183" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">Permission Response</text></switch></g><rect x="500" y="31" width="150" height="170" fill="rgb(255, 255, 255)" stroke="#009900" stroke-width="2" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 38px; margin-left: 501px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Permission Framework</div></div></div></foreignObject><text x="575" y="50" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">Permission Framework</text></switch></g><path d="M 120 51 L 213.63 51" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 218.88 51 L 211.88 54.5 L 213.63 51 L 211.88 47.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 51px; margin-left: 166px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">Action<br style="font-size: 9px;" />(Read request)</div></div></div></foreignObject><text x="166" y="53" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="9px" text-anchor="middle">Action...</text></switch></g><rect x="240" y="61" width="80" height="50" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 86px; margin-left: 241px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Resources</div></div></div></foreignObject><text x="280" y="90" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Resources</text></switch></g><path d="M 240 146 L 166 146 L 166 67.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 166 62.12 L 169.5 69.12 L 166 67.37 L 162.5 69.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 320 146 L 420.5 146 L 420.04 67.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 420.01 62.12 L 423.55 69.1 L 420.04 67.37 L 416.55 69.14 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="240" y="121" width="80" height="50" fill="rgb(255, 255, 255)" stroke="#0000cc" stroke-width="2" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 146px; margin-left: 241px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 10px">Resource read permission</font></div></div></div></foreignObject><text x="280" y="150" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Resource read...</text></switch></g><rect x="530" y="61" width="90" height="20" fill="rgb(255, 255, 255)" stroke="#cc0000" stroke-width="2" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 71px; margin-left: 531px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Permission Policy</div></div></div></foreignObject><text x="575" y="74" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">Permission Policy</text></switch></g><path d="M 340 50 L 491.83 50.04" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 497.08 50.04 L 490.08 53.54 L 491.83 50.04 L 490.08 46.54 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 49px; margin-left: 420px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">Permission Request</div></div></div></foreignObject><text x="420" y="52" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">Permission Request</text></switch></g><rect x="10" y="194" width="20" height="10" fill="#009900" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 199px; margin-left: 42px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">Provided by Backstage</div></div></div></foreignObject><text x="42" y="202" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px">Provided by Backstage</text></switch></g><rect x="10" y="214" width="20" height="10" fill="#cc0000" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 219px; margin-left: 42px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">Configured by integrators</div></div></div></foreignObject><text x="42" y="222" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px">Configured by integrators</text></switch></g><rect x="10" y="234" width="20" height="10" fill="#0000cc" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 239px; margin-left: 42px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">Configured by plugin authors</div></div></div></foreignObject><text x="42" y="242" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px">Configured by plugin authors</text></switch></g><rect x="515" y="98" width="120" height="60" fill="rgb(255, 255, 255)" stroke="#009900" stroke-width="2" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 128px; margin-left: 516px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Permission Backend<br />Permission hooks<br />Integration helpers<br />...</div></div></div></foreignObject><text x="575" y="131" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">Permission Backend...</text></switch></g><ellipse cx="165" cy="21" rx="10" ry="10" fill="rgb(255, 255, 255)" stroke="#000000" stroke-width="2" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 21px; margin-left: 156px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">1</div></div></div></foreignObject><text x="165" y="25" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">1</text></switch></g><ellipse cx="420" cy="21" rx="10" ry="10" fill="rgb(255, 255, 255)" stroke="#000000" stroke-width="2" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 21px; margin-left: 411px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">2</div></div></div></foreignObject><text x="420" y="25" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">2</text></switch></g><ellipse cx="635" cy="71" rx="10" ry="10" fill="rgb(255, 255, 255)" stroke="#000000" stroke-width="2" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 71px; margin-left: 626px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">3</div></div></div></foreignObject><text x="635" y="75" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">3</text></switch></g><ellipse cx="420" cy="201" rx="10" ry="10" fill="rgb(255, 255, 255)" stroke="#000000" stroke-width="2" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 201px; margin-left: 411px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">4</div></div></div></foreignObject><text x="420" y="205" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">4</text></switch></g><ellipse cx="65" cy="166" rx="10" ry="10" fill="rgb(255, 255, 255)" stroke="#000000" stroke-width="2" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 166px; margin-left: 56px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">5</div></div></div></foreignObject><text x="65" y="170" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">5</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 171px; margin-left: 134px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">User gets a response</div></div></div></foreignObject><text x="134" y="173" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="9px" text-anchor="middle">User gets a response</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>