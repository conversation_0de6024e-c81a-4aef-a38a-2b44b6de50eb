<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1105px" height="666px" viewBox="-0.5 -0.5 1105 666" content="&lt;mxfile host=&quot;46bd9516-dfce-4ce2-b246-4bbf59f0ff2c&quot; modified=&quot;2020-11-19T20:13:59.658Z&quot; agent=&quot;5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Code-Insiders/1.52.0-insider Chrome/83.0.4103.122 Electron/9.3.3 Safari/537.36&quot; etag=&quot;SEe7AI8Fi28HXIC8dJwB&quot; version=&quot;13.6.5&quot;&gt;&lt;diagram id=&quot;BTONK6ejGRyM9EUH_zET&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(241, 250, 238);">
    <defs>
        <linearGradient x1="0%" y1="0%" x2="0%" y2="100%" id="mx-gradient-a8dadc-100-a8dadc-100-s-0">
            <stop offset="0%" style="stop-color:#A8DADC"/>
            <stop offset="100%" style="stop-color:#A8DADC"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="955" y="4" width="117" height="233" fill="none" stroke="#457b9d" stroke-dasharray="3 3" pointer-events="all"/>
        <rect x="102" y="39" width="678" height="574" fill="#a8dadc" stroke="#457b9d" pointer-events="all"/>
        <rect x="978" y="81" width="71" height="70" fill="#a8dadc" stroke="#457b9d" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 69px; height: 1px; padding-top: 116px; margin-left: 979px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Repo 1
                                <br/>
                                (with docs)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1014" y="120" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Repo 1...
                </text>
            </switch>
        </g>
        <rect x="978" y="154" width="71" height="70" fill="#a8dadc" stroke="#457b9d" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 69px; height: 1px; padding-top: 189px; margin-left: 979px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Repo 2
                                <br/>
                                (with docs)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1014" y="193" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Repo 2...
                </text>
            </switch>
        </g>
        <rect x="108" y="308.5" width="103" height="25" fill="#a8dadc" stroke="#457b9d" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 101px; height: 1px; padding-top: 321px; margin-left: 109px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                TechDocs plugin
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="160" y="325" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    TechDocs plugin
                </text>
            </switch>
        </g>
        <rect x="378" y="52" width="365" height="538" fill="#a8dadc" stroke="#457b9d" pointer-events="all"/>
        <rect x="236" y="298.5" width="137" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 135px; height: 1px; padding-top: 309px; margin-left: 237px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Request TechDocs site
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="305" y="312" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Request TechDocs site
                </text>
            </switch>
        </g>
        <path d="M 213.24 321 L 392.76 321" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 219.12 317.5 L 212.12 321 L 219.12 324.5" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 386.88 324.5 L 393.88 321 L 386.88 317.5" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/>
        <image x="112.5" y="43.5" width="65.5" height="64.5" xlink:href="data:image/svg+xml;base64,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" preserveAspectRatio="none"/>
        <path d="M 0 0 L 1103 0" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1 664 L 1103 664" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 0 0 L 0 664" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1103 0 L 1103 664" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 989.44 72 C 979.12 72 971.9 63.83 971.9 55.13 C 971.9 44.73 980.52 38 989.57 38 C 998.29 38 1006.9 44.95 1006.9 54.99 C 1006.9 63.99 999.53 72 989.44 72 Z" fill="#0d2636" stroke="none" pointer-events="all"/>
        <path d="M 985.66 68.28 C 985.66 69.01 985.08 69.32 984.23 69.03 C 979.05 67.27 974.07 61.82 974.07 55.02 C 974.07 45.82 982.16 40.02 989.18 40.02 C 998.3 40.02 1004.78 47.28 1004.78 54.93 C 1004.78 61.25 1000.59 67.07 994.32 69.11 C 993.61 69.29 993.17 68.92 993.17 68.32 L 993.17 63.89 C 993.17 63.04 992.81 62.12 992.15 61.49 C 994.7 61.2 996.24 60.59 997.42 59.4 C 998.59 58.28 999.11 56.64 999.2 54.61 C 999.27 52.99 998.82 51.42 997.62 50.22 C 998.04 49.23 998.11 47.91 997.48 46.29 C 996.22 46.2 994.78 46.93 993.31 47.81 C 990.73 47.16 988.14 47.08 985.56 47.85 C 984.4 47.12 983.35 46.3 981.35 46.29 C 980.82 47.72 980.74 49.03 981.19 50.19 C 979.76 51.74 979.6 53.25 979.62 54.76 C 979.78 57.67 980.88 59.14 982.1 60.02 C 983.11 60.75 984.53 61.21 986.69 61.52 C 986.11 62.08 985.78 62.74 985.73 63.52 C 984.46 64.08 982.64 64.37 981.39 62.65 C 980.84 61.79 980.08 60.82 978.64 60.83 C 978.4 60.82 978.17 60.91 978.14 61 C 978.1 61.11 978.24 61.34 978.4 61.43 C 979.66 62.21 979.86 62.65 980.38 63.71 C 980.85 64.96 981.68 65.44 982.58 65.77 C 983.49 66.09 984.97 66 985.66 65.77 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <path d="M 1018.15 75.4 C 1015.69 75.4 1013.7 73.44 1013.7 71.03 L 1013.7 39.37 C 1013.7 36.96 1015.69 35 1018.15 35 L 1050.65 35 C 1051.83 35 1052.96 35.46 1053.8 36.28 C 1054.63 37.1 1055.1 38.21 1055.1 39.37 L 1055.1 71.03 C 1055.1 72.19 1054.63 73.3 1053.8 74.12 C 1052.96 74.94 1051.83 75.4 1050.65 75.4 Z" fill="#a8dadc" stroke="none" pointer-events="all"/>
        <path d="M 1021.18 44.86 C 1021.29 44.08 1021.64 43.32 1022.85 42.61 C 1024.34 41.91 1026.02 41.31 1029.22 40.77 C 1032.21 40.49 1036.49 40.38 1039.85 40.8 C 1043.05 41.38 1044.4 41.91 1045.61 42.54 C 1046.72 43.15 1047.46 43.85 1047.54 44.73 L 1045.39 57.89 C 1045.09 58.67 1044.5 59.37 1042.47 60.29 C 1037.58 62.29 1031.24 62.43 1026.07 60.25 C 1024.7 59.67 1023.86 58.89 1023.47 58.13 Z M 1024.57 61.86 C 1024.59 61.48 1024.92 61.27 1025.3 61.45 C 1027.7 63.24 1031.11 64.09 1034.38 64.14 C 1037.93 64.13 1041.34 63.11 1043.56 61.53 C 1043.95 61.22 1044.24 61.44 1044.2 61.81 L 1043.24 66.88 C 1043.02 67.56 1042.53 68.08 1041.74 68.58 C 1039.41 69.75 1037.21 70.12 1033.85 70.12 C 1031.97 70.12 1030.33 69.95 1028.56 69.28 C 1027.3 68.79 1026.1 68.27 1025.5 67.01 Z" fill="#205081" stroke="none" pointer-events="all"/>
        <path d="M 1026.89 45.11 C 1026.44 44.91 1025.91 44.6 1025.91 44.25 C 1025.91 43.82 1026.43 43.48 1027.13 43.23 C 1029.35 42.56 1032.16 42.32 1034.51 42.32 C 1037.16 42.32 1039.69 42.66 1041.28 43.11 C 1042.08 43.38 1042.82 43.74 1042.82 44.21 C 1042.82 44.65 1042.19 44.98 1041.43 45.27 C 1039.2 46.01 1036.7 46.06 1034.09 46.06 C 1031.76 46.06 1028.62 45.87 1026.89 45.11 Z M 1034.43 56.8 C 1035.55 56.8 1036.44 55.7 1036.44 54.81 C 1036.44 53.87 1035.71 52.74 1034.4 52.74 C 1033.28 52.74 1032.29 53.76 1032.29 54.83 C 1032.29 55.76 1033.39 56.8 1034.43 56.8 Z M 1034.39 59.01 C 1032.16 59.01 1030.17 57.17 1030.17 55.15 C 1030.17 51.89 1032.77 50.7 1034.41 50.7 C 1036.47 50.7 1038.59 52.54 1038.59 54.92 C 1038.59 57.01 1036.75 59.01 1034.39 59.01 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <path d="M 1013.7 39.37 C 1013.7 36.96 1015.69 35 1018.15 35 L 1050.65 35 C 1051.83 35 1052.96 35.46 1053.8 36.28 C 1054.63 37.1 1055.1 38.21 1055.1 39.37 L 1055.1 50.01 C 1042.04 56.27 1026.76 56.27 1013.7 50.01 Z" fill-opacity="0.2" fill="#ffffff" stroke="none" pointer-events="all"/>
        <rect x="956.5" y="8" width="114" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 112px; height: 1px; padding-top: 18px; margin-left: 958px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Source code hosting
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1014" y="22" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Source code hosting
                </text>
            </switch>
        </g>
        <rect x="382" y="61" width="154" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 152px; height: 1px; padding-top: 71px; margin-left: 383px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                TechDocs Backend Plugin
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="459" y="75" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    TechDocs Backend Plugin
                </text>
            </switch>
        </g>
        <rect x="568" y="146" width="56" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 54px; height: 1px; padding-top: 156px; margin-left: 569px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                (Stages)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="596" y="160" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    (Stages)
                </text>
            </switch>
        </g>
        <path d="M 631.5 243 L 631.5 263 L 631.5 253.5 L 631.5 271.26" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 628 265.38 L 631.5 272.38 L 635 265.38" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="596" y="173" width="71" height="70" fill="#a8dadc" stroke="#457b9d" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 69px; height: 1px; padding-top: 208px; margin-left: 597px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Prepare
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="632" y="212" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Prepare
                </text>
            </switch>
        </g>
        <path d="M 631.5 343.5 L 631.5 363.5 L 631.5 355 L 631.5 372.76" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 628 366.88 L 631.5 373.88 L 635 366.88" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="596" y="273.5" width="71" height="70" fill="#a8dadc" stroke="#457b9d" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 69px; height: 1px; padding-top: 309px; margin-left: 597px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Generate
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="632" y="312" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Generate
                </text>
            </switch>
        </g>
        <rect x="596" y="375" width="71" height="70" fill="#a8dadc" stroke="#457b9d" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 69px; height: 1px; padding-top: 410px; margin-left: 597px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Publish
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="632" y="414" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Publish
                </text>
            </switch>
        </g>
        <path d="M 601.5 499 C 601.5 477.67 661.5 477.67 661.5 499 L 661.5 547 C 661.5 568.33 601.5 568.33 601.5 547 Z" fill="#a8dadc" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 601.5 499 C 601.5 515 661.5 515 661.5 499" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 535px; margin-left: 603px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Local Filesystem
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="632" y="539" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Local File...
                </text>
            </switch>
        </g>
        <rect x="672" y="196" width="62" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 60px; height: 1px; padding-top: 206px; margin-left: 673px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Fetch markdown files
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="703" y="210" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Fetch mark...
                </text>
            </switch>
        </g>
        <rect x="674" y="298.5" width="62" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 60px; height: 1px; padding-top: 309px; margin-left: 675px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Build doc files using mkdocs
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="705" y="312" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Build doc...
                </text>
            </switch>
        </g>
        <rect x="672" y="416" width="62" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 60px; height: 1px; padding-top: 426px; margin-left: 673px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Store generated static content
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="703" y="430" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Store gene...
                </text>
            </switch>
        </g>
        <rect x="8" y="9" width="287" height="21" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 20px; margin-left: 152px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                TechDocs Basic (out-of-the-box) architecture
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="152" y="24" fill="#1D3557" font-family="Helvetica" font-size="14px" text-anchor="middle">
                    TechDocs Basic (out-of-the-box) architect...
                </text>
            </switch>
        </g>
        <path d="M 464 312.38 L 536 312.41 Q 546 312.42 546 302.42 L 546 210.17 Q 546 200.17 556 200.17 L 588.99 200.16" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 594.24 200.16 L 587.24 203.66 L 588.99 200.16 L 587.24 196.66 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 470.37 329.62 L 539 329.59 Q 549 329.58 549 339.58 L 549 504.42 Q 549 514.42 559 514.4 L 572 514.38" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 465.12 329.62 L 472.12 326.12 L 470.37 329.62 L 472.12 333.12 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="395" y="303.75" width="69" height="34.5" fill="#a8dadc" stroke="#457b9d" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 67px; height: 1px; padding-top: 321px; margin-left: 396px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                <font style="font-size: 12px">
                                    Route handler
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="430" y="325" fill="#1D3557" font-family="Helvetica" font-size="14px" text-anchor="middle">
                    Route hand...
                </text>
            </switch>
        </g>
        <rect x="493" y="310.5" width="40" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 321px; margin-left: 494px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                or
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="513" y="324" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    or
                </text>
            </switch>
        </g>
        <rect x="433" y="237" width="111" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 109px; height: 1px; padding-top: 247px; margin-left: 434px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Trigger new build if docs D.N.E.
                                <br/>
                                or are outdated
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="489" y="251" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Trigger new build...
                </text>
            </switch>
        </g>
        <rect x="472" y="403" width="72" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 70px; height: 1px; padding-top: 413px; margin-left: 473px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Fetch files to render
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="508" y="417" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Fetch files...
                </text>
            </switch>
        </g>
        <path d="M 631.5 445 L 632.75 469.82" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 633.02 475.07 L 629.17 468.25 L 632.75 469.82 L 636.16 467.9 Z" fill="#457b9d" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="876" y="541" width="110" height="60" rx="1" ry="1" fill="none" stroke="#000000" transform="translate(2,3)" opacity="0.25"/>
        <rect x="876" y="541" width="110" height="60" rx="1" ry="1" fill="none" stroke="#457b9d" pointer-events="all"/>
        <path d="M 897.19 584.26 C 896.2 584.26 895.26 583.72 894.77 582.7 L 889 572.3 C 888.48 571.39 888.56 570.33 889 569.58 L 894.82 559.17 C 895.31 558.23 896.2 557.74 897.09 557.74 L 908.78 557.74 C 909.65 557.74 910.51 558.18 911.02 559.07 L 916.82 569.46 C 917.52 570.56 917.28 571.73 916.88 572.38 L 911.12 582.72 C 910.73 583.55 909.86 584.26 908.74 584.26 Z" fill="#a8dadc" stroke="none" pointer-events="all"/>
        <path d="M 907.88 584.26 L 898.08 574.15 L 899.64 571.76 L 898.08 570.36 L 907.84 567.86 L 915.18 575.43 L 911.12 582.72 C 910.73 583.55 909.86 584.26 908.74 584.26 Z" fill-opacity="0.07" fill="#000000" stroke="none" pointer-events="all"/>
        <rect x="888.48" y="557.74" width="0" height="0" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 906.19 569.6 C 906.46 569.6 906.64 569.37 906.64 569.1 C 906.64 568.7 906.41 568.57 906.2 568.57 C 905.84 568.57 905.66 568.8 905.66 569.07 C 905.66 569.38 905.89 569.6 906.19 569.6 Z M 898.26 570.45 C 898.14 570.45 897.98 570.32 897.98 570.18 L 897.98 568.09 C 897.98 567.85 898.13 567.77 898.39 567.77 L 907.56 567.77 C 907.78 567.77 907.93 567.82 907.93 568.14 L 907.93 570.06 C 907.93 570.27 907.81 570.45 907.56 570.45 Z M 906.17 573.44 C 906.44 573.44 906.67 573.17 906.67 572.91 C 906.67 572.61 906.49 572.4 906.17 572.4 C 905.87 572.4 905.66 572.57 905.66 572.92 C 905.66 573.16 905.87 573.44 906.17 573.44 Z M 898.36 574.24 C 898.12 574.24 897.98 574.11 897.98 573.92 L 897.98 571.89 C 897.98 571.69 898.1 571.56 898.35 571.56 L 907.48 571.56 C 907.8 571.56 907.93 571.65 907.93 571.97 L 907.93 573.74 C 907.93 574.06 907.8 574.24 907.51 574.24 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 571px; margin-left: 932px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: left; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                <font color="#000000">
                                    GCP
                                    <br/>
                                    Bucket
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="932" y="575" fill="#1D3557" font-family="Helvetica" font-size="12px">
                    GCP...
                </text>
            </switch>
        </g>
        <path d="M 1008 563.5 L 1020.49 541 L 1045.46 541 L 1058 563.5 L 1045.46 586 L 1020.49 586 Z M 1020.44 573.83 C 1020.56 575.49 1021.79 576.84 1023.41 577.09 L 1042.69 577.09 C 1044.27 576.79 1045.45 575.45 1045.56 573.83 L 1045.56 555.3 L 1040.33 549.91 L 1023.71 549.91 C 1022.01 550.02 1020.63 551.35 1020.44 553.06 Z M 1022.3 553.88 C 1022.18 552.94 1022.79 552.07 1023.71 551.89 L 1039.47 551.89 L 1039.47 556.27 L 1043.7 556.27 L 1043.7 573.43 C 1043.65 574.37 1042.87 575.11 1041.94 575.11 L 1023.71 575.11 C 1022.93 574.95 1022.35 574.28 1022.3 573.48 Z M 1027.94 556.73 L 1029.5 555.86 L 1029.5 560.9 L 1027.99 560.9 L 1027.99 562.07 L 1032.37 562.07 L 1032.37 560.85 L 1031.01 560.85 L 1031.01 554.39 L 1029.7 554.39 L 1027.94 555.66 Z M 1036 554.24 C 1034.89 554.36 1033.95 555.08 1033.53 556.12 C 1033.1 557.58 1033.12 559.14 1033.58 560.6 C 1034.14 561.64 1035.04 562.27 1036 562.28 C 1036.9 562.2 1037.73 561.59 1038.26 560.6 C 1038.73 559.16 1038.76 557.62 1038.36 556.17 C 1037.98 555.14 1037.07 554.4 1036 554.24 Z M 1035.9 555.56 C 1036.38 555.54 1036.81 556.06 1036.9 556.78 C 1037.05 557.79 1037.05 558.82 1036.9 559.83 C 1036.79 560.54 1036.37 561.03 1035.9 561.01 C 1035.42 561.03 1035 560.54 1034.89 559.83 C 1034.74 558.82 1034.74 557.79 1034.89 556.78 C 1034.99 556.06 1035.41 555.54 1035.9 555.56 Z M 1030.16 564.62 C 1029.03 564.7 1028.04 565.4 1027.59 566.45 C 1027.12 567.94 1027.12 569.54 1027.59 571.03 C 1028.17 572.16 1029.14 572.81 1030.16 572.76 C 1031.05 572.66 1031.86 572.03 1032.37 571.03 C 1032.83 569.54 1032.83 567.94 1032.37 566.45 C 1031.98 565.5 1031.15 564.82 1030.16 564.62 Z M 1030.05 566.05 C 1030.32 566.05 1030.58 566.21 1030.77 566.49 C 1030.96 566.78 1031.06 567.17 1031.06 567.57 C 1031.16 568.38 1031.16 569.2 1031.06 570.02 C 1031.04 570.84 1030.6 571.49 1030.05 571.49 C 1029.78 571.51 1029.52 571.37 1029.32 571.09 C 1029.12 570.81 1029.01 570.43 1029 570.02 C 1028.9 569.2 1028.9 568.38 1029 567.57 C 1029 567.15 1029.11 566.75 1029.31 566.46 C 1029.51 566.18 1029.78 566.02 1030.05 566.05 Z M 1033.88 566.05 L 1033.88 567.27 L 1035.39 566.4 L 1035.39 571.39 L 1033.83 571.39 L 1033.83 572.56 L 1038.26 572.56 L 1038.26 571.39 L 1036.9 571.39 L 1036.9 564.82 L 1035.69 564.87 Z" fill="#a8dadc" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 593px; margin-left: 1033px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                Azure storage
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1033" y="605" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Azure st...
                </text>
            </switch>
        </g>
        <path d="M 877.67 528.92 L 879.79 528.92 L 879.79 530.86 L 874.93 530.86 C 874.16 530.86 873.42 530.56 872.87 530.01 C 872.32 529.46 872.01 528.72 872.01 527.95 L 872.01 500.73 C 872.01 500.47 872.12 500.23 872.3 500.04 C 872.48 499.86 872.73 499.76 872.99 499.76 L 883.76 499.76 C 884.06 499.76 884.34 499.89 884.53 500.13 L 886.47 502.67 L 890.48 502.67 L 890.48 504.62 L 886 504.62 C 885.7 504.61 885.41 504.47 885.23 504.22 L 883.29 501.68 L 873.96 501.68 L 873.96 527.93 C 873.96 528.47 874.39 528.9 874.93 528.9 C 875.47 528.9 875.9 528.47 875.9 527.93 L 875.9 507.52 C 875.9 507.26 876.01 507.01 876.19 506.83 C 876.37 506.65 876.62 506.54 876.88 506.54 L 890.48 506.54 L 890.48 508.49 L 877.85 508.49 L 877.85 527.93 C 877.85 528.27 877.79 528.6 877.67 528.92 Z M 910.62 499.07 L 902.84 491.29 C 902.66 491.11 902.41 491 902.15 491 C 901.89 491 901.64 491.11 901.46 491.29 L 893.68 499.07 C 893.4 499.35 893.32 499.77 893.47 500.13 C 893.62 500.5 893.98 500.73 894.37 500.73 L 898.26 500.73 L 898.26 511.42 C 898.26 511.96 898.7 512.4 899.23 512.4 L 901.75 512.4 L 896.32 517.83 L 890.88 512.41 L 893.4 512.41 C 893.66 512.41 893.91 512.3 894.09 512.12 C 894.27 511.94 894.37 511.69 894.37 511.43 L 894.37 504.63 L 892.43 504.63 L 892.43 510.46 L 888.54 510.46 C 888.15 510.46 887.8 510.7 887.65 511.06 C 887.5 511.42 887.58 511.83 887.85 512.11 L 895.63 519.89 C 895.81 520.07 896.06 520.18 896.32 520.18 C 896.58 520.18 896.82 520.07 897.01 519.89 L 904.78 512.11 C 905.06 511.83 905.14 511.42 904.99 511.06 C 904.84 510.7 904.48 510.46 904.09 510.46 L 900.2 510.46 L 900.2 499.77 C 900.2 499.51 900.1 499.26 899.92 499.08 C 899.74 498.9 899.49 498.8 899.23 498.8 L 896.72 498.8 L 902.15 493.35 L 907.58 498.79 L 905.07 498.79 C 904.81 498.79 904.56 498.89 904.38 499.07 C 904.2 499.25 904.09 499.5 904.09 499.76 L 904.09 506.56 L 902.15 506.56 L 902.15 508.51 L 907.01 508.51 L 907.01 528.92 L 904.09 528.92 L 904.09 530.86 L 907.98 530.86 C 908.52 530.86 908.95 530.43 908.95 529.89 L 908.95 507.53 C 908.95 507 908.52 506.56 907.98 506.56 L 906.04 506.56 L 906.04 500.73 L 909.93 500.73 C 910.32 500.73 910.68 500.5 910.83 500.13 C 910.98 499.77 910.9 499.35 910.62 499.07 Z M 885.34 526.94 C 885.04 526.68 884.72 526.47 884.37 526.29 L 883.74 525.95 C 883.48 525.82 883.24 525.65 883.05 525.43 C 882.92 525.26 882.85 525.05 882.86 524.84 C 882.85 524.58 882.96 524.33 883.15 524.16 C 883.36 523.99 883.64 523.9 883.91 523.91 C 884.49 523.93 885.05 524.05 885.58 524.26 L 885.58 523.03 C 885.01 522.76 884.38 522.62 883.75 522.61 C 883.3 522.6 882.85 522.7 882.45 522.89 C 882.08 523.08 881.78 523.36 881.56 523.71 C 881.33 524.08 881.22 524.52 881.23 524.95 C 881.22 525.42 881.34 525.87 881.6 526.26 C 881.93 526.67 882.36 527 882.84 527.23 L 883.47 527.56 C 883.73 527.69 883.96 527.86 884.16 528.07 C 884.29 528.25 884.36 528.47 884.36 528.69 C 884.38 528.97 884.27 529.24 884.07 529.44 C 883.83 529.63 883.52 529.72 883.21 529.7 C 882.9 529.69 882.59 529.65 882.29 529.59 C 881.96 529.53 881.63 529.43 881.32 529.31 L 881.32 530.54 C 881.61 530.7 881.92 530.81 882.24 530.87 C 882.6 530.96 882.97 531 883.34 531 C 884.04 531.03 884.74 530.81 885.28 530.36 C 885.77 529.91 886.04 529.26 886 528.6 C 886.01 528.27 885.95 527.94 885.84 527.63 C 885.72 527.37 885.55 527.13 885.33 526.94 Z M 887.24 530.87 L 888.87 530.87 L 888.87 527.46 L 890.87 527.46 L 890.87 526.22 L 888.86 526.22 L 888.86 524.03 L 891.46 524.03 L 891.46 522.79 L 887.23 522.79 Z M 892.1 524.07 L 894.04 524.07 L 894.04 530.87 L 895.68 530.87 L 895.68 524.07 L 897.62 524.07 L 897.62 522.79 L 892.12 522.79 Z M 902.79 523.46 C 903.65 524.55 903.65 526.09 902.79 527.18 C 902.35 527.64 901.73 527.89 901.09 527.86 L 900.12 527.86 L 900.12 530.86 L 898.54 530.86 L 898.54 522.79 L 901.1 522.79 C 901.74 522.76 902.36 523 902.8 523.47 Z M 901.82 525.31 C 901.84 524.97 901.75 524.64 901.59 524.34 C 901.42 524.12 901.16 524 900.89 524.01 L 900.13 524.01 L 900.13 526.67 L 900.89 526.67 C 901.16 526.68 901.43 526.55 901.59 526.33 C 901.77 526.02 901.85 525.67 901.83 525.32 Z" fill="#116d5b" stroke="none" pointer-events="all"/>
        <rect x="985" y="477" width="99" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 97px; height: 1px; padding-top: 487px; margin-left: 986px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Storage solutions
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1035" y="491" fill="#1D3557" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Storage solutions
                </text>
            </switch>
        </g>
        <path d="M 631.52 170.76 L 631.9 131 Q 632 121 642 120.98 L 955 120.5" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 628.08 164.85 L 631.51 171.88 L 635.08 164.92" fill="none" stroke="#457b9d" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 796 524.25 L 859 524.25 L 859 602.75 L 796 602.75 Z" fill="#457b9d" stroke="none" pointer-events="all"/>
        <path d="M 797 525.25 L 858 525.25 L 858 586.25 L 797 586.25 Z" fill="url(#mx-gradient-a8dadc-100-a8dadc-100-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 826.27 534.72 C 821.91 534.7 817.63 535.36 814.23 536.4 C 812.42 536.96 810.87 537.62 809.67 538.37 C 808.48 539.13 807.59 539.97 807.32 541.08 C 807.31 541.15 807.3 541.22 807.3 541.29 L 807.3 541.29 C 807.3 541.29 807.3 541.29 807.3 541.29 C 807.3 541.32 807.3 541.34 807.3 541.37 C 807.32 541.52 807.33 541.65 807.36 541.79 L 811.55 572.35 C 811.55 572.37 811.55 572.4 811.56 572.42 C 811.77 573.35 812.51 573.97 813.42 574.48 C 814.32 574.99 815.47 575.4 816.77 575.74 C 819.37 576.41 822.58 576.79 825.5 576.77 C 829.22 576.8 832.57 576.49 835.22 575.89 C 837.87 575.3 839.84 574.51 840.81 573.13 C 840.89 573.02 840.94 572.9 840.96 572.77 L 842.81 559.58 C 843.24 559.68 843.66 559.78 844.07 559.85 C 844.81 559.98 845.5 560.05 846.16 559.93 C 846.48 559.87 846.82 559.75 847.11 559.52 C 847.39 559.27 847.6 558.91 847.67 558.52 C 847.68 558.48 847.68 558.44 847.68 558.4 C 847.7 557.27 846.88 556.51 846.02 555.83 C 845.26 555.23 844.37 554.72 843.55 554.31 L 845.37 541.33 C 845.38 541.23 845.38 541.12 845.35 541.02 C 845.06 539.89 844.14 539.05 842.94 538.32 C 841.74 537.59 840.21 536.97 838.48 536.45 C 835.04 535.4 830.83 534.75 827.14 534.73 L 827.14 534.73 C 826.85 534.73 826.56 534.72 826.28 534.72 Z M 826.28 536.36 C 826.55 536.36 826.83 536.37 827.11 536.37 L 827.11 536.37 C 827.12 536.37 827.12 536.37 827.13 536.37 C 830.63 536.39 834.73 537.02 838 538.02 C 839.64 538.52 841.06 539.11 842.08 539.72 C 843.03 540.3 843.54 540.89 843.69 541.32 C 843.55 541.79 843.13 542.29 842.33 542.81 C 841.42 543.4 840.07 543.95 838.46 544.39 C 835.23 545.27 830.93 545.75 826.72 545.75 C 826.72 545.75 826.71 545.75 826.71 545.75 C 822.82 545.82 818.24 545.39 814.68 544.52 C 812.91 544.08 811.39 543.53 810.38 542.92 C 809.52 542.4 809.1 541.91 808.98 541.48 L 808.97 541.38 C 809.1 540.97 809.59 540.37 810.55 539.76 C 811.56 539.12 813 538.5 814.71 537.97 C 817.92 536.99 822.08 536.34 826.27 536.36 Z M 843.32 544.12 L 841.44 557.49 C 841 557.36 840.56 557.22 840.17 557.11 C 836.68 555.87 831.54 553.67 828.19 552.02 C 828.19 552.02 828.19 552 828.19 552 C 828.19 550.96 827.33 550.11 826.29 550.11 C 825.26 550.11 824.4 550.96 824.4 552 C 824.4 553.03 825.26 553.89 826.29 553.89 C 826.72 553.89 827.12 553.74 827.44 553.49 C 830.88 555.18 836.04 557.39 839.64 558.66 C 839.65 558.67 839.66 558.67 839.68 558.68 C 840.13 558.81 840.66 558.97 841.21 559.14 L 839.37 572.26 C 838.84 572.92 837.25 573.76 834.85 574.3 C 832.38 574.86 829.13 575.16 825.5 575.13 C 825.5 575.13 825.49 575.13 825.49 575.13 C 822.72 575.15 819.6 574.78 817.18 574.15 C 815.97 573.84 814.93 573.45 814.22 573.05 C 813.51 572.65 813.2 572.25 813.16 572.07 L 809.35 544.22 C 809.41 544.25 809.47 544.29 809.53 544.33 C 810.78 545.07 812.42 545.65 814.29 546.11 C 818.03 547.03 822.7 547.47 826.73 547.39 C 831.06 547.39 835.46 546.91 838.89 545.97 C 840.6 545.5 842.09 544.92 843.22 544.19 C 843.25 544.17 843.28 544.14 843.32 544.12 Z M 826.29 551.75 C 826.45 551.75 826.54 551.85 826.54 552 C 826.54 552.14 826.45 552.25 826.29 552.25 C 826.15 552.25 826.05 552.14 826.05 552 C 826.05 551.85 826.15 551.75 826.29 551.75 Z M 843.3 556.04 C 843.91 556.36 844.51 556.74 845 557.12 C 845.67 557.64 845.92 558.13 845.96 558.28 C 845.93 558.29 845.93 558.31 845.85 558.32 C 845.56 558.37 845 558.35 844.35 558.24 C 843.95 558.16 843.49 558.06 843.04 557.94 Z" fill="#457b9d" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 59px; height: 1px; padding-top: 599px; margin-left: 798px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: #1D3557; line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                <font color="#f5f5f5">
                                    Amazon S3
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="828" y="599" fill="#1D3557" font-family="Helvetica" font-size="10px" text-anchor="middle" font-weight="bold">
                    Amazon S3
                </text>
            </switch>
        </g>
        <rect x="572" y="475" width="522" height="169" fill="none" stroke="#000000" stroke-dasharray="3 3" pointer-events="all"/>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://desk.draw.io/support/solutions/articles/16000042487" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Viewer does not support full SVG 1.1
            </text>
        </a>
    </switch>
</svg>
