<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="642px" height="327px" viewBox="-0.5 -0.5 642 327" content="&lt;mxfile scale=&quot;1.5&quot; border=&quot;20&quot;&gt;&lt;diagram id=&quot;gKBvn7eBFHudv9VMIRD-&quot; name=&quot;Page-1&quot;&gt;3Vldb9owFP01PBblCyiPHaXdHiZVYtPaRzcxiVVjI8dA6K+fHTuJEzOahZQiUqmKj79y7zn3+iYM/Nkqe2RgnfykEcQDz4mygX8/8Lxg6on/EtgrYORNFBAzFCnIrYAFeocadDS6QRFMawM5pZijdR0MKSEw5DUMMEZ39WFLiuu7rkEMLWARAmyjf1DEE4X6gVPh3yGKk2LnW93xCsK3mNEN0dsNPH+ZX6p7BYql9Pg0ARHdGZA/H/gzRilXd6tsBrH0bOE1Ne/hH73lYzNIeJsJmqYtwBtt+TdhASRRqp+P7wuX5FZBOc8Z+N92CeJwsQah7N0JDQgs4SssWq64XSKMZxRTls/152P5J/CUM/oGjZ5xfskZlHADV5fAbYu0kVvIOMwMSFv4COkKcrYXQ3Tvja+9rcV4U3h/V1EbOBpLDFoLuoFWU1wuXblU3GivHvawb3l4AdkWhfCaPOw2PDyxHXz7Sf6dWv59YnQrsoflXhiJENdNQgms+xNmiD8b9y+ShuFIt+4zzUre2BcNEt3JZCOar5iGb78SRBT8gHC5MCnyW5DTAxg32nXOJUW6c3qMmZRuWKjNCnR6BCyGetRIQdLgo+wxiAFH23rOO0SGnvpEkXiQaghdLlOxZ5OtcodWBLp2DvqddmXPGU5GJoEf0Cce8tlsGKTLZjUtb30a7TLQkDh87jCKxUr3KxRFOLeQMvQuNAHKqO9HIH5LgejwdoaO492qORehmcAOeryJEbmmnNo8tcoce4akOrL8O884JCmiRMA5pdfk6ubxdU5XFxVvbweYY+a/Kh1+zQlWpli3nmInH+XYVpltfIajrzWPdqF3yjHmdTjGGj4++zHW5XA6QGHbw6l3CscWg+K9doOvqlL3pl+X6sqnrRz8gwhJyUEdw8SZTo04cYdO0LLiq6Ljxey7+IpPmvMEGRLOh+x/cqVnB1rQd6C1LvmqMVldXoUqm3JTluhJR9Zxmws5jYWU/dZCHWrQA99OOqr5KkSpDGhEo3c8GnvU8vhytOw3qsmuWvY+iIkepWzXLh1r0CuSslmAuUd1HIE0yXd2TxBw71XPCQJuJFFf1OtT4xqNugm6ue5nKtr+QjA7oc6ovVJ5l/lK5bV7pWoIrPM7Vn/KLNThNNQxkZ+dqsvtJjr19cpapt0mnQUpmtWPOGp49TuZP/8L&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs/>
    <g>
        <rect x="19.5" y="19.5" width="600" height="60" fill="#e6e6e6" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 398px; height: 1px; padding-top: 33px; margin-left: 14px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Backends
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="213" y="37" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Backends
                </text>
            </switch>
        </g>
        <rect x="259.5" y="154.5" width="120" height="60" fill="#e6e6e6" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 123px; margin-left: 174px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Services
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="213" y="127" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Services
                </text>
            </switch>
        </g>
        <path d="M 139.5 274.5 L 250.32 274.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 257.82 274.5 L 250.32 277 L 250.32 272 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 183px; margin-left: 133px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Provide
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="133" y="186" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="9px" text-anchor="middle">
                    Provide
                </text>
            </switch>
        </g>
        <path d="M 109.5 244.5 L 250.98 187.91" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 257.94 185.12 L 251.91 190.23 L 250.05 185.59 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 143px; margin-left: 123px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Use
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="123" y="146" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="9px" text-anchor="middle">
                    Use
                </text>
            </switch>
        </g>
        <rect x="19.5" y="244.5" width="120" height="60" fill="#e6e6e6" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 183px; margin-left: 14px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Plugins
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="53" y="187" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Plugins
                </text>
            </switch>
        </g>
        <rect x="259.5" y="244.5" width="120" height="60" fill="#e6e6e6" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 183px; margin-left: 174px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Extension Points
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="213" y="187" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Extension Poi...
                </text>
            </switch>
        </g>
        <path d="M 499.5 289.5 L 388.68 289.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 381.18 289.5 L 388.68 287 L 388.68 292 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 193px; margin-left: 293px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Provide
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="293" y="196" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="9px" text-anchor="middle">
                    Provide
                </text>
            </switch>
        </g>
        <path d="M 529.5 244.5 L 388.02 187.91" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 381.06 185.12 L 388.95 185.59 L 387.09 190.23 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 143px; margin-left: 303px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Use
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="303" y="146" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="9px" text-anchor="middle">
                    Use
                </text>
            </switch>
        </g>
        <rect x="499.5" y="244.5" width="120" height="60" fill="#e6e6e6" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 183px; margin-left: 334px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Modules
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="373" y="187" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Modules
                </text>
            </switch>
        </g>
        <path d="M 78.9 81.9 L 79.47 235.32" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 79.49 242.82 L 76.97 235.33 L 81.97 235.31 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 109px; margin-left: 53px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Install
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="53" y="111" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="9px" text-anchor="middle">
                    Install
                </text>
            </switch>
        </g>
        <path d="M 559.5 80.7 L 559.5 235.32" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 559.5 242.82 L 557 235.32 L 562 235.32 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 109px; margin-left: 373px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Install
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="373" y="111" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="9px" text-anchor="middle">
                    Install
                </text>
            </switch>
        </g>
        <path d="M 319.5 79.5 L 319.5 145.32" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" stroke-dasharray="4.5 4.5" pointer-events="stroke"/>
        <path d="M 319.5 152.82 L 317 145.32 L 322 145.32 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 78px; margin-left: 213px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Provide
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="213" y="81" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="9px" text-anchor="middle">
                    Provide
                </text>
            </switch>
        </g>
        <path d="M 499.5 259.5 L 388.68 259.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 381.18 259.5 L 388.68 257 L 388.68 262 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 173px; margin-left: 293px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Call
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="293" y="176" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="9px" text-anchor="middle">
                    Call
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
