<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="897px" height="646px" viewBox="-0.5 -0.5 897 646" content="&lt;mxfile scale=&quot;1.5&quot; border=&quot;20&quot;&gt;&lt;diagram id=&quot;gKBvn7eBFHudv9VMIRD-&quot; name=&quot;Page-1&quot;&gt;7V1tb6M4EP41kXZPahVwIPCxabe3J3V11fWku/3oECdBS3HOkG2zv/5ssHkzJIQQ0xCyUjcM2JiZeWbG4zEZgfvX998J3Ky/4QXyRvp48T4CDyNdN0xA/zLCLibYUzMmrIi7iElaSnhxfyFOHHPq1l2gIHdhiLEXups80cG+j5wwR4OE4Lf8ZUvs5e+6gSskEV4c6MnUf9xFuI6pYDJO6V+Ru1qLO1v8xBw6P1YEb31+u5EOltEnPv0KRVf8+mANF/gtQwJfRuCeYBzG317f75HHOCu4Frd7rDibDJsgP6zTwOAtfkJvi8SQo4GFO8GL6HEQazAegdnb2g3RywY67OwbFT6lrcNXjx5p9OvS9bx77GEStQVLg/2j9CAk+AfKnDGjD2uB/TBDjz+UzgeGSIjeK59OS3hGNRHhVxSSHb1ENNCNuAnXwhshv7dUptqY09YZeYrrIFejVdJ1ykv6hbOzgrV6r1mrdcjaid1n1t4I49CJ1o57zVrQIWsnVq9Zq5ld8nbab95Ou+TtWGIlWtBoiR/62Ed53qF3N/yXsflWN/jhd8519v3hPXuwEwc+HVbcyhCH30WH7CBtFh2l7RZ3LOCjh3MPOz/+Xrt+TH50PTGiIIQk5FHmJD6bOTpe1jFDGBf2S5QyDW+Jw6/iKknHskL8KlAheII8GLo/892fJEStsRCPkeFFy0JXJQvZNtWVxXQA1AEhThUJUY7uZ3TuR/lAiQ9o4+HdKxu6Pr77WI6ohKWSb9rjiKwuHZHE8kdCnzXm+d1m0ydGFwJVzZD5bJ2JzSWqfay9OqfpyZuaoiHKi5wx3HWgd+e5K9rTw6u7WHjRYDFxf1HxwUT69IB3Yzf0fk2t3gEjN5GNnEhrFOzlCVaPN33GbmSzxCV4uQzoTTPKk17DNRUUNLWogfGj8EZ7+ilMerViP/HzS/1Eypw8cT39tveqMybhGq+wD70vKfWSw1tnS35GiGhm5Rq6ZqNEbRW5ZsOQDNhddAEO14iwQRG4XLpOqRo8wTny8gKH3Ho49IFp+z1mhaDA/QXnUX9MehumrtGTGLOR8bBvNsgz2bzxKMFDls+x5lY7jfGtZml8JtwU+omnz+f39AIcZcvQAIlg/8zyCCReRlz8cZBoqoKiHLPRJnDjsh6msxA56wV2gpF+HzgUk9hbUIBN6WWPv10cOsG4nKm8J+ourCnIgepGawerZt512meAqi7b1E93W2pPfcbr0PVXn+nZvxDlXYDot2eC33eSCC84BTfJxyeWynmPsddMDgm42qbQlE0hMBSZwonZWIqX4d06laIqhyZufjjvc98j65eYtk7SPmAwf+0AZ1oCHKDK/E0G83c+KU5UmT95nbXC/M16ZP6KwZ9S82fujxvKJ8m5meb1rOoBUSuYwYapChvi5hlswI+2KHFqYVIBB4nSZ4AwKcEBaAMH0wEHtXGgyzgwLFU40GUcbKl29AgIyXJcJ0Bosn5xrUAAJQ6hIk/XPhCABAQHhtDDqz5hoQ4UjDNBYdpkAeFaoVCykGyqKj8D8jpAgCBx+uQVimXbSoGwv7xvAEIWCCULYqauCgjyOkq6CNYbKNg1oGCeCwr6AIXaUChJpZuqMoJATqVnFoH7AwapeF8lGkxrQENtNJRkVo2Kkpv20SBnVjd9W0OXlpFUTpuBvP3q2duuXF92vMEabthXigNvNyMswR0e5nUqGHbksUqVZxy4oYtZFYqHlqwPUZzyVDidFKmIchYSP311NcsCBuvkbm1IRy9ujtM0WTqgRDpGG9KpsaVzEEtuwn1AKmYLUjFkF93AnVx9ZfjHqfs7dXdMoQDtiDrxYn23VVDPpnXiRdfQXp24UbanNK1dTFJ59zyTcaFli0ZF6UxStjixRMZ5l+u8faVpICS78/iWHjwj4tIxMzGeWIVAj+TOLiaANizZ5Fiqll+NGtvrkwhi57nUGRBwOHyYx27jaZ4Qkle8/LkNaTdIiIDz+9aYahPd4n+tffyvirUPyeXEKLxYzaCJDROZiEI/UxRud754NeA1xavdJV6PifivGa9SMlUhXJMJ+oDXD4BXMaXL4VXVhndx8wGvh7ZuF96MpRavnS8EDnhN8VpSjqgOr3I54oDXErx2C9cm+bQBrmeCqy7D1VZVLCZuPsD1wGqq1mk4vH8vUld4vRyInVpcUJ4Ult49KKWX20sLm3JV5wDUMqB2OW+1O980M/jVFPQllafK0kxm2RsoBriWpIW7xKspl0VWS+m6l/8TX3dg/b+NvYfTMl9neowDwQb6OfmY/23ZW+tnuW8r9v/XZEndY08WvWZ/IfqhQ4i7iq8V5Dkp7XyOCcXnjRNj4C4aGvl0c5Olfy4dRINxp/tZg8rRFnSUCjrM618etHybcxb5nFRbw8r0PV/b0IbageIiEZCNQRKKt17CK9vsJ7xi3KDEBxjCOQyQXLbVX+aXmeKzcd+uEeAyT7epfNQ6ZQfHLlLq43reSJS9nMIBa4gZwqwuyGISlSCyKyqTidaGTI6JEK5ZJvVg0opIalQHXrdIFAjBrmGrzmatOzHONX7q4Uq0rmIO3YFxHmqv6slEoXEeymsOiESFca4RtJw7lJbeVaHSWIuc0qCFdsV7JtUba3soDKgnE3XG2tYHkewXiQIhaONjpv3XnYTWNf1Whse50tDaWPajD7NvL5Tyhx+E0HeuKhuoj6clzG8rHzhiv6ojfn82Xh9Pf+IXfPkf&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs/>
    <g>
        <rect x="717" y="279" width="150" height="60" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <rect x="702" y="279" width="150" height="60" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <rect x="499.5" y="279" width="150" height="60" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <rect x="484.5" y="279" width="150" height="60" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <rect x="289.5" y="279" width="150" height="60" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <rect x="274.5" y="279" width="150" height="60" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <path d="M 297 279 L 265.37 241.05" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 260.57 235.29 L 267.3 239.45 L 263.45 242.65 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 334.5 279 L 334.5 243.18" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 334.5 235.68 L 337 243.18 L 332 243.18 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 372 279 L 403.63 241.05" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 408.43 235.29 L 405.55 242.65 L 401.7 239.45 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="259.5" y="279" width="150" height="60" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 206px; margin-left: 174px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Backend Deployment A
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="223" y="210" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Backend Deployme...
                </text>
            </switch>
        </g>
        <rect x="484.5" y="564" width="120" height="60" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 396px; margin-left: 324px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Frontend App
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="363" y="400" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Frontend App
                </text>
            </switch>
        </g>
        <path d="M 544.5 564 L 544.5 528.18" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 544.5 520.68 L 547 528.18 L 542 528.18 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 507 459 Q 507 399 420.75 399 Q 334.5 399 334.5 348.18" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 334.5 340.68 L 337 348.18 L 332 348.18 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 286px; margin-left: 284px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                All other traffic
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="284" y="290" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    All other traffic
                </text>
            </switch>
        </g>
        <path d="M 582 459 Q 582 399 668.25 399 Q 754.5 399 754.5 348.18" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 754.5 340.68 L 757 348.18 L 752 348.18 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 286px; margin-left: 468px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                /api/{techdocs,scaffolder}/*
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="468" y="290" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    /api/{techdocs,scaffolder}/*
                </text>
            </switch>
        </g>
        <rect x="469.5" y="459" width="150" height="60" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 326px; margin-left: 314px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                (Authenticating) Reverse Proxy
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="363" y="330" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    (Authenticating)...
                </text>
            </switch>
        </g>
        <path d="M 717 279 L 711.01 243.05" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 709.78 235.65 L 713.47 242.64 L 708.54 243.46 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 792 279 L 823.63 241.05" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 828.43 235.29 L 825.55 242.65 L 821.7 239.45 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="679.5" y="279" width="150" height="60" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 206px; margin-left: 454px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Backend Deployment C
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="503" y="210" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Backend Deployme...
                </text>
            </switch>
        </g>
        <path d="M 507 279 L 494.9 242.71" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 492.53 235.59 L 497.27 241.92 L 492.53 243.5 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 582 279 L 594.1 242.71" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 596.47 235.59 L 596.47 243.5 L 591.73 241.92 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="469.5" y="279" width="150" height="60" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 206px; margin-left: 314px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Backend Deployment B
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="363" y="210" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Backend Deployme...
                </text>
            </switch>
        </g>
        <path d="M 259.5 189 Q 259.5 189 259.5 160.68" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 259.5 153.18 L 262 160.68 L 257 160.68 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="229.5" y="189" width="60" height="45" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 141px; margin-left: 154px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                app
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="173" y="145" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    app
                </text>
            </switch>
        </g>
        <path d="M 334.5 189 Q 334.5 189 334.5 160.68" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 334.5 153.18 L 337 160.68 L 332 160.68 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="304.5" y="189" width="60" height="45" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 141px; margin-left: 204px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                auth
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="223" y="145" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    auth
                </text>
            </switch>
        </g>
        <path d="M 492 189 Q 492 189 492 160.68" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 492 153.18 L 494.5 160.68 L 489.5 160.68 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="454.5" y="189" width="75" height="45" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 48px; height: 1px; padding-top: 141px; margin-left: 304px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                catalog
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="328" y="145" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    catalog
                </text>
            </switch>
        </g>
        <path d="M 597 189 Q 597 189 597 160.68" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 597 153.18 L 599.5 160.68 L 594.5 160.68 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="559.5" y="189" width="75" height="45" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 48px; height: 1px; padding-top: 141px; margin-left: 374px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                search
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="398" y="145" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    search
                </text>
            </switch>
        </g>
        <path d="M 709.5 189 Q 709.5 189 709.5 160.68" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 709.5 153.18 L 712 160.68 L 707 160.68 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="664.5" y="189" width="90" height="45" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 141px; margin-left: 444px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                techdocs
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="473" y="145" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    techdocs
                </text>
            </switch>
        </g>
        <path d="M 829.5 189 Q 829.5 189 829.5 160.68" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 829.5 153.18 L 832 160.68 L 827 160.68 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="784.5" y="189" width="90" height="45" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 141px; margin-left: 524px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                scaffolder
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="553" y="145" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    scaffolder
                </text>
            </switch>
        </g>
        <path d="M 409.5 189 Q 409.5 189 409.5 160.68" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 409.5 153.18 L 412 160.68 L 407 160.68 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="379.5" y="189" width="60" height="45" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 141px; margin-left: 254px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                proxy
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="273" y="145" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    proxy
                </text>
            </switch>
        </g>
        <path d="M 229.5 174 L 218.25 174 Q 207 174 207 189 L 207 200.25 Q 207 211.5 195.75 211.5 L 190.13 211.5 Q 184.5 211.5 195.75 211.5 L 201.38 211.5 Q 207 211.5 207 226.5 L 207 237.75 Q 207 249 218.25 249 L 229.5 249" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" stroke-dasharray="4.5 4.5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-end; width: 32px; height: 1px; padding-top: 141px; margin-left: 89px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: right;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Plugins
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="121" y="145" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="end">
                    Plugi...
                </text>
            </switch>
        </g>
        <path d="M 229.5 264 L 218.25 264 Q 207 264 207 279 L 207 294 Q 207 309 195.75 309 L 190.13 309 Q 184.5 309 195.75 309 L 201.38 309 Q 207 309 207 324 L 207 339 Q 207 354 218.25 354 L 229.5 354" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" stroke-dasharray="4.5 4.5" pointer-events="all"/>
        <path d="M 544.5 459 Q 544.5 459 544.5 348.18" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 544.5 340.68 L 547 348.18 L 542 348.18 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 362px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                /api/{catalog,search}/*
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="362" y="250" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    /api/{catalog,search}/*
                </text>
            </switch>
        </g>
        <path d="M 334.5 106.5 Q 334.5 106.5 334.5 78.18" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 334.5 70.68 L 337 78.18 L 332 78.18 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 319.5 113.36 C 319.5 109.57 326.22 106.5 334.5 106.5 C 338.48 106.5 342.29 107.22 345.11 108.51 C 347.92 109.79 349.5 111.54 349.5 113.36 L 349.5 144.64 C 349.5 148.43 342.78 151.5 334.5 151.5 C 326.22 151.5 319.5 148.43 319.5 144.64 Z" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 349.5 113.36 C 349.5 117.14 342.78 120.21 334.5 120.21 C 326.22 120.21 319.5 117.14 319.5 113.36" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 409.5 106.5 Q 409.5 87.78 372 87.78 Q 334.5 87.78 334.5 78.18" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 334.5 70.68 L 337 78.18 L 332 78.18 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 394.5 113.36 C 394.5 109.57 401.22 106.5 409.5 106.5 C 413.48 106.5 417.29 107.22 420.11 108.51 C 422.92 109.79 424.5 111.54 424.5 113.36 L 424.5 144.64 C 424.5 148.43 417.78 151.5 409.5 151.5 C 401.22 151.5 394.5 148.43 394.5 144.64 Z" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 424.5 113.36 C 424.5 117.14 417.78 120.21 409.5 120.21 C 401.22 120.21 394.5 117.14 394.5 113.36" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 492 106.5 Q 492 87.78 518.25 87.78 Q 544.5 87.78 544.5 78.18" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 544.5 70.68 L 547 78.18 L 542 78.18 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 477 113.36 C 477 109.57 483.72 106.5 492 106.5 C 495.98 106.5 499.79 107.22 502.61 108.51 C 505.42 109.79 507 111.54 507 113.36 L 507 144.64 C 507 148.43 500.28 151.5 492 151.5 C 483.72 151.5 477 148.43 477 144.64 Z" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 507 113.36 C 507 117.14 500.28 120.21 492 120.21 C 483.72 120.21 477 117.14 477 113.36" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 597 106.5 Q 597 87.78 570.75 87.78 Q 544.5 87.78 544.5 78.18" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 544.5 70.68 L 547 78.18 L 542 78.18 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 582 113.36 C 582 109.57 588.72 106.5 597 106.5 C 600.98 106.5 604.79 107.22 607.61 108.51 C 610.42 109.79 612 111.54 612 113.36 L 612 144.64 C 612 148.43 605.28 151.5 597 151.5 C 588.72 151.5 582 148.43 582 144.64 Z" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 612 113.36 C 612 117.14 605.28 120.21 597 120.21 C 588.72 120.21 582 117.14 582 113.36" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 709.5 106.5 Q 709.5 87.78 739.5 87.78 Q 769.5 87.78 769.5 78.18" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 769.5 70.68 L 772 78.18 L 767 78.18 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 694.5 113.36 C 694.5 109.57 701.22 106.5 709.5 106.5 C 713.48 106.5 717.29 107.22 720.11 108.51 C 722.92 109.79 724.5 111.54 724.5 113.36 L 724.5 144.64 C 724.5 148.43 717.78 151.5 709.5 151.5 C 701.22 151.5 694.5 148.43 694.5 144.64 Z" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 724.5 113.36 C 724.5 117.14 717.78 120.21 709.5 120.21 C 701.22 120.21 694.5 117.14 694.5 113.36" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 829.5 106.5 Q 829.5 87.78 799.5 87.78 Q 769.5 87.78 769.5 78.18" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 769.5 70.68 L 772 78.18 L 767 78.18 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 814.5 113.36 C 814.5 109.57 821.22 106.5 829.5 106.5 C 833.48 106.5 837.29 107.22 840.11 108.51 C 842.92 109.79 844.5 111.54 844.5 113.36 L 844.5 144.64 C 844.5 148.43 837.78 151.5 829.5 151.5 C 821.22 151.5 814.5 148.43 814.5 144.64 Z" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 844.5 113.36 C 844.5 117.14 837.78 120.21 829.5 120.21 C 821.22 120.21 814.5 117.14 814.5 113.36" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 259.5 106.5 Q 259.5 87.78 297 87.78 Q 334.5 87.78 334.5 78.18" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 334.5 70.68 L 337 78.18 L 332 78.18 Z" fill="#666666" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 244.5 113.36 C 244.5 109.57 251.22 106.5 259.5 106.5 C 263.48 106.5 267.29 107.22 270.11 108.51 C 272.92 109.79 274.5 111.54 274.5 113.36 L 274.5 144.64 C 274.5 148.43 267.78 151.5 259.5 151.5 C 251.22 151.5 244.5 148.43 244.5 144.64 Z" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 274.5 113.36 C 274.5 117.14 267.78 120.21 259.5 120.21 C 251.22 120.21 244.5 117.14 244.5 113.36" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 229.5 99 L 218.25 99 Q 207 99 207 114 L 207 121.5 Q 207 129 195.75 129 L 190.13 129 Q 184.5 129 195.75 129 L 201.38 129 Q 207 129 207 144 L 207 151.5 Q 207 159 218.25 159 L 229.5 159" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" stroke-dasharray="4.5 4.5" pointer-events="all"/>
        <rect x="19.5" y="286.5" width="165" height="45" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-end; width: 108px; height: 1px; padding-top: 206px; margin-left: 13px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: right;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="">
                                    Horizontally Scaled
                                </span>
                                <br style="border-color: var(--border-color);"/>
                                <span style="">
                                    Deployments
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="121" y="210" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="end">
                    Horizontally Scale...
                </text>
            </switch>
        </g>
        <rect x="19.5" y="106.5" width="165" height="45" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-end; width: 108px; height: 1px; padding-top: 86px; margin-left: 13px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: right;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Logical Databases
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="121" y="90" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="end">
                    Logical Databases
                </text>
            </switch>
        </g>
        <path d="M 319.5 53.36 C 319.5 49.57 326.22 46.5 334.5 46.5 C 338.48 46.5 342.29 47.22 345.11 48.51 C 347.92 49.79 349.5 51.54 349.5 53.36 L 349.5 62.14 C 349.5 65.93 342.78 69 334.5 69 C 326.22 69 319.5 65.93 319.5 62.14 Z" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 349.5 53.36 C 349.5 57.14 342.78 60.21 334.5 60.21 C 326.22 60.21 319.5 57.14 319.5 53.36" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 319.5 45.86 C 319.5 42.07 326.22 39 334.5 39 C 338.48 39 342.29 39.72 345.11 41.01 C 347.92 42.29 349.5 44.04 349.5 45.86 L 349.5 54.64 C 349.5 58.43 342.78 61.5 334.5 61.5 C 326.22 61.5 319.5 58.43 319.5 54.64 Z" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 349.5 45.86 C 349.5 49.64 342.78 52.71 334.5 52.71 C 326.22 52.71 319.5 49.64 319.5 45.86" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 319.5 38.36 C 319.5 34.57 326.22 31.5 334.5 31.5 C 338.48 31.5 342.29 32.22 345.11 33.51 C 347.92 34.79 349.5 36.54 349.5 38.36 L 349.5 47.14 C 349.5 50.93 342.78 54 334.5 54 C 326.22 54 319.5 50.93 319.5 47.14 Z" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 349.5 38.36 C 349.5 42.14 342.78 45.21 334.5 45.21 C 326.22 45.21 319.5 42.14 319.5 38.36" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 529.5 53.36 C 529.5 49.57 536.22 46.5 544.5 46.5 C 548.48 46.5 552.29 47.22 555.11 48.51 C 557.92 49.79 559.5 51.54 559.5 53.36 L 559.5 62.14 C 559.5 65.93 552.78 69 544.5 69 C 536.22 69 529.5 65.93 529.5 62.14 Z" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 559.5 53.36 C 559.5 57.14 552.78 60.21 544.5 60.21 C 536.22 60.21 529.5 57.14 529.5 53.36" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 529.5 45.86 C 529.5 42.07 536.22 39 544.5 39 C 548.48 39 552.29 39.72 555.11 41.01 C 557.92 42.29 559.5 44.04 559.5 45.86 L 559.5 54.64 C 559.5 58.43 552.78 61.5 544.5 61.5 C 536.22 61.5 529.5 58.43 529.5 54.64 Z" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 559.5 45.86 C 559.5 49.64 552.78 52.71 544.5 52.71 C 536.22 52.71 529.5 49.64 529.5 45.86" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 529.5 38.36 C 529.5 34.57 536.22 31.5 544.5 31.5 C 548.48 31.5 552.29 32.22 555.11 33.51 C 557.92 34.79 559.5 36.54 559.5 38.36 L 559.5 47.14 C 559.5 50.93 552.78 54 544.5 54 C 536.22 54 529.5 50.93 529.5 47.14 Z" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 559.5 38.36 C 559.5 42.14 552.78 45.21 544.5 45.21 C 536.22 45.21 529.5 42.14 529.5 38.36" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 754.5 53.36 C 754.5 49.57 761.22 46.5 769.5 46.5 C 773.48 46.5 777.29 47.22 780.11 48.51 C 782.92 49.79 784.5 51.54 784.5 53.36 L 784.5 62.14 C 784.5 65.93 777.78 69 769.5 69 C 761.22 69 754.5 65.93 754.5 62.14 Z" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 784.5 53.36 C 784.5 57.14 777.78 60.21 769.5 60.21 C 761.22 60.21 754.5 57.14 754.5 53.36" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 754.5 45.86 C 754.5 42.07 761.22 39 769.5 39 C 773.48 39 777.29 39.72 780.11 41.01 C 782.92 42.29 784.5 44.04 784.5 45.86 L 784.5 54.64 C 784.5 58.43 777.78 61.5 769.5 61.5 C 761.22 61.5 754.5 58.43 754.5 54.64 Z" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 784.5 45.86 C 784.5 49.64 777.78 52.71 769.5 52.71 C 761.22 52.71 754.5 49.64 754.5 45.86" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 754.5 38.36 C 754.5 34.57 761.22 31.5 769.5 31.5 C 773.48 31.5 777.29 32.22 780.11 33.51 C 782.92 34.79 784.5 36.54 784.5 38.36 L 784.5 47.14 C 784.5 50.93 777.78 54 769.5 54 C 761.22 54 754.5 50.93 754.5 47.14 Z" fill="#f5f5f5" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 784.5 38.36 C 784.5 42.14 777.78 45.21 769.5 45.21 C 761.22 45.21 754.5 42.14 754.5 38.36" fill="none" stroke="#666666" stroke-width="1.5" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 229.5 20.25 L 218.25 20.25 Q 207 20.25 207 35.25 L 207 42.75 Q 207 50.25 195.75 50.25 L 190.13 50.25 Q 184.5 50.25 195.75 50.25 L 201.38 50.25 Q 207 50.25 207 65.25 L 207 72.75 Q 207 80.25 218.25 80.25 L 229.5 80.25" fill="none" stroke="rgb(0, 0, 0)" stroke-width="1.5" stroke-miterlimit="10" stroke-dasharray="4.5 4.5" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)scale(1.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-end; width: 108px; height: 1px; padding-top: 34px; margin-left: 13px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: right;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                DBMS Instances
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="121" y="37" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="end">
                    DBMS Instances
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
