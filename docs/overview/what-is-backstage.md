---
id: what-is-backstage
title: What is Backstage?
description: Backstage is an open source framework for building developer portals. Powered by a centralized software catalog, Backstage restores order to your microservices and infrastructure
---

![software-catalog](../assets/header.png)

[Backstage](https://backstage.io/) is an open source framework for building developer
portals. Powered by a centralized software catalog, Backstage restores order to
your microservices and infrastructure and enables your product teams to ship
high-quality code quickly — without compromising autonomy.

Backstage unifies all your infrastructure tooling, services, and documentation
to create a streamlined development environment from end to end.

<iframe width="672" height="378" src="https://www.youtube.com/embed/85TQEpNCaU0" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

Out of the box, Backstage includes:

- [Backstage Software Catalog](../features/software-catalog/index.md) for
  managing all your software (microservices, libraries, data pipelines,
  websites, ML models, etc.)

- [Backstage Software Templates](../features/software-templates/index.md) for
  quickly spinning up new projects and standardizing your tooling with your
  organization’s best practices

- [Backstage TechDocs](../features/techdocs/README.md) for making it easy to
  create, maintain, find, and use technical documentation, using a "docs like
  code" approach

- Plus, a growing ecosystem of
  [open source plugins](https://github.com/backstage/backstage/tree/master/plugins)
  that further expand Backstage’s customizability and functionality

## Backstage and the CNCF

Backstage is a CNCF Incubation project after graduating from Sandbox. Read the announcement
[here](https://backstage.io/blog/2022/03/16/backstage-turns-two#out-of-the-sandbox-and-into-incubation).

<img src="https://backstage.io/img/cncf-white.svg" alt="CNCF logo" width="400" />

## Benefits

- For _engineering managers_, it allows you to maintain standards and best
  practices across the organization, and can help you manage your whole tech
  ecosystem, from migrations to test certification.

- For _end users_ (developers), it makes it fast and simple to build software
  components in a standardized way, and it provides a central place to manage
  all projects and documentation.

- For _platform engineers_, it enables extensibility and scalability by letting
  you easily integrate new tools and services (via plugins), as well as
  extending the functionality of existing ones.

- For _everyone_, it’s a single, consistent experience that ties all your
  infrastructure tooling, resources, standards, owners, contributors, and
  administrators together in one place.

If you have questions or want support, please join our
[Discord chatroom](https://discord.gg/backstage-687207715902193673).
