---
id: roadmap
title: Roadmap
description: Roadmap of Backstage
---

## The Backstage Roadmap

Backstage is still under rapid development, and this page details the project's
public roadmap. This not a complete list of all work happening in and around the
project, it only highlights the highest priority initiatives worked on by the
core maintainers.

## 2025 Spring Roadmap

The initiatives listed below are planned for release within the next half-year, starting in November 2024. The roadmap is updated every 6 months, and the next update is planned for April 2024.

### New Frontend System - Ready for Adoption

The [new frontend system](../frontend-system/index.md) still needs more work, and
the next milestone is to improve it to the point where there is enough
confidence in the design to start encouraging adoption in the community. You can
follow along with this work in the [meta issue](https://github.com/backstage/backstage/issues/19545).
This milestone also includes reaching and executing [rollout phase 2](https://github.com/backstage/backstage/issues/19545#issuecomment-1766069146).

Once the initial milestone is reached, the goal is to also build out broader
support for the new frontend system in the core plugins.

### Rework Pull Request & Issue Process

Our current review and issue triage process is centered around either core- or
project area maintainers. The goal of this initiative is to make it simpler for
more members of the community to be involved and contribute to this process.

### Catalog Performance

The goal of this initiative is to improve the performance of the Software
Catalog, both in terms of speeding up the read API and improving the ingestion
process.

## How to influence the roadmap

As we evolve Backstage, we want you to contribute actively in the journey to
define the most effective developer experience in the world.

A roadmap is only useful if it captures real needs. If you have success stories,
feedback, or ideas, we want to hear from you! If you plan to work (or are
already working) on a new or existing feature, please let us know, so that we
can update the roadmap accordingly. We are also happy to share knowledge and
context that will help your feature land successfully.

You can also head over to the
[CONTRIBUTING](https://github.com/backstage/backstage/blob/master/CONTRIBUTING.md)
guidelines to get started.

If you have specific questions about the roadmap, please create an
[issue](https://github.com/backstage/backstage/issues/new/choose), ping us on
[Discord](https://discord.gg/backstage-687207715902193673), or [book time](https://info.backstage.spotify.com/office-hours) with the Spotify team.
