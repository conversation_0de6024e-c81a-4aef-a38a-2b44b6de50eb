---
id: adrs-adr000
title: 'ADR000: [TITLE]'
description: Architecture Decision Record (ADR) for [TITLE] [DESCRIPTION]
---

<!-- These documents have names that are short noun phrases. For example, "ADR001: Deployment on Ruby on Rails 3.0.10" or "ADR009: LDAP for Multitenant Integration" -->

## Context

<!--
This section describes the forces at play, including technological, political, social, and project local. These forces are probably in tension, and should be called out as such. The language in this section is value-neutral. It is simply describing facts. -->

## Decision

<!-- This section describes our response to these forces. It is stated in full sentences, with active voice. "We will ..." -->

## Consequences

<!-- This section describes the resulting context, after applying the decision. All consequences should be listed here, not just the "positive" ones. A particular decision may have positive, negative, and neutral consequences, but all of them affect the team and project in the future. -->

<!-- This template is taken from a blog post by <PERSON> http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions -->
