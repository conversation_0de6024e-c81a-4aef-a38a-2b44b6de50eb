---
id: adrs-adr001
title: 'ADR001: Architecture Decision Record (ADR) log'
description: Architecture Decision Record (ADR) logs as a reference point for the team
---

## Decision

A decision was made to store ADRs in a log in the project repository.

## Discussion

There is a need to store big decisions made in a log as a reference point for
the team, help with onboarding new members, and give context to others interested
in the project.

## Risks

People stop adding ADRs to the log and context gets lost.
