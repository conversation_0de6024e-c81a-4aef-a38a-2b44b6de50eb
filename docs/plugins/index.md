---
id: index
title: Introduction to Plugins
description: Learn about integrating various infrastructure and software development tools into Backstage through plugins.
---

Backstage orchestrates a cohesive single-page application by seamlessly integrating various plugins.

Our vision for the plugin ecosystem champions flexibility, empowering you to incorporate a broad spectrum of infrastructure and software development tools into Backstage as plugins. Adherence to stringent [design guidelines](../dls/design.md) guarantees a consistent and intuitive user experience across the entire plugin landscape.

![Plugin Screenshot](../assets/plugins/my-plugin_screenshot.png)

## Creating a Plugin

Embark on your plugin development journey by following the detailed steps provided in the [Create a Plugin](create-a-plugin.md) documentation.

## Suggesting a Plugin

If you're developing an open-source plugin, we encourage you to create a [new issue in the community plugins repository](https://github.com/backstage/community-plugins/issues/new/choose). This informs the community about upcoming plugins and invites collaboration and feedback.

This approach is equally beneficial if you conceive an idea for a potentially impactful plugin but prefer if another contributor undertakes its development.

## Integration with the Software Catalog

Should your plugin complement the Software Catalog rather than exist as a standalone feature (for instance, as an additional tab or a card within an "Overview" tab), you'll find comprehensive guidance on achieving this integration in the [Integrating Plugin into Software Catalog guide](integrating-plugin-into-software-catalog.md).
