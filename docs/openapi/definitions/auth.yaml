openapi: 3.0.1
info:
  title: Auth API
  description: |
    # Backstage's auth-provider API.

    **Provided by `@backstage/auth-backend`.**

    The purpose of the Auth APIs in Backstage are to identify the user, and to provide a way for plugins
    to request access to 3rd party services on behalf of that user.

    The API is supplied with a list of providers - such as `Google` or `GitHub` - and will add the endpoints
    described below to each of those providers.

    Read more about [User Authentication and Authorization in Backstage](https://github.com/backstage/backstage/blob/master/docs/auth/overview.md).
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  version: 0.1.1-alpha.8
externalDocs:
  description: Backstage official documentation
  url: https://backstage.io/docs
servers:
  - url: http://localhost:7007/api/auth/
tags:
  - name: provider
    description: List of endpoints per provider
paths:
  /start:
    get:
      tags:
        - provider
      summary:
        Initializes the authorization flow and redirects to a consent screen for
        the provider
      description:
        The popup window is pointed to the `/start` endpoint of the auth
        provider in the auth-backend plugin, which then redirects to the OAuth
        consent screen of the provider.
      parameters:
        - name: scope
          in: query
          description:
            <PERSON><PERSON> requested for the auth session. The scope format and handling
            is specific to each provider
          required: true
          schema:
            type: string
            example: email
        - name: env
          in: query
          description: Parameter to indicate runtime environment
          required: true
          schema:
            type: string
            example: development
      responses:
        302:
          description: Redirect to the consent popup
          headers:
            Location:
              description: The url to the consent popup
              schema:
                type: string
            Set-Cookie:
              description: Nonce cookie
              schema:
                $ref: '#/components/schemas/Nonce-Cookie'
        default:
          description: An error occurred
  /frame/handler:
    get:
      tags:
        - provider
      summary: Handles the response from the consent popup for the provider
      description:
        If the login request is accepted, the popup window will be redirected
        back to the `/handler/frame` endpoint of the auth backend.
      parameters:
        - name: scope
          in: query
          description: Scope requested for the auth session
          required: true
          schema:
            type: string
            example: email
        - name: code
          in: query
          description: Short-term authorization code used to be exchanged for access tokens
          required: true
          schema:
            type: string
        - name: state
          in: query
          description: Nonce value stored in state
          required: true
          schema:
            type: string
        - name: provider-nonce
          in: cookie
          description: Nonce value stored in cookie
          required: true
          schema:
            type: string
        - name: env
          in: query
          description: Parameter to indicate runtime environment
          required: true
          schema:
            type: string
            example: development
      responses:
        200:
          description: Message received from consent popup
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/AuthResponse'
                  - $ref: '#/components/schemas/AuthError'
              examples:
                AuthResponse:
                  summary: AuthResponse
                  value: |
                    {
                      type: "auth-result",
                      payload: {
                          accessToken: "access-token",
                          idToken: "id-token",
                          expiresInSecond: 3600,
                          scope: "email"
                        }
                    }
                AuthError:
                  summary: AuthError
                  value: |
                    {
                      type: "auth-result",
                      error: {
                        name: "error",
                        message: "message",
                        stack: "stack"
                      }
                    }
          headers:
            Set-Cookie:
              description: Refresh cookie
              schema:
                $ref: '#/components/schemas/Refresh-Cookie'
  /refresh:
    get:
      tags:
        - provider
      summary: Handles refreshing of tokens due to reload or expiry
      description:
        If supported by the provider, the `/refresh` endpoint is responsible for
        refreshing the tokens using a refresh-token stored in a cookie
      parameters:
        - name: scope
          in: query
          description: Scope requested for the auth session
          required: true
          schema:
            type: string
            example: email
        - name: optional
          in: query
          description: Prevent the popup from being displayed
          schema:
            type: string
        - name: x-requested-with
          in: header
          description: X-Requested-With header preventing CSRF
          required: true
          schema:
            type: string
            example: 'x-requested-with: XMLHttpRequest'
        - name: provider-refresh-token
          in: cookie
          description: Refresh token used to refresh the current session
          required: true
          schema:
            type: string
      responses:
        200:
          description: Successfully refreshed the session
        401:
          description: Validation error
  /logout:
    post:
      tags:
        - provider
      summary: Logs user out of the current provider
      description:
        When logging out the current session is removed and if present the
        cookie containing the refresh token is also removed
      parameters:
        - name: x-requested-with
          in: header
          description: X-Requested-With header preventing CSRF
          required: true
          schema:
            type: string
            example: 'x-requested-with: XMLHttpRequest'
      responses:
        200:
          description: Successfully logged out
          headers:
            Set-Cookie:
              description: Refresh cookie
              schema:
                $ref: '#/components/schemas/Refresh-Cookie'
        401:
          description: Validation error
components:
  schemas:
    Nonce-Cookie:
      type: object
      properties:
        provider-nonce:
          type: string
        maxAge:
          type: integer
        secure:
          type: boolean
        sameSite:
          type: string
        domain:
          type: string
        path:
          type: string
        httpOnly:
          type: boolean
    Refresh-Cookie:
      type: object
      properties:
        provider-refresh-token:
          type: string
        maxAge:
          type: integer
        secure:
          type: boolean
        sameSite:
          type: string
        domain:
          type: string
        path:
          type: string
        httpOnly:
          type: boolean
    AuthResponse:
      type: object
      properties:
        type:
          type: string
        payload:
          type: object
          properties:
            accessToken:
              type: string
            idToken:
              type: string
            expiresInSeconds:
              type: number
            scope:
              type: string
    AuthError:
      type: object
      properties:
        type:
          type: string
        error:
          type: object
          properties:
            name:
              type: string
            message:
              type: string
            stack:
              type: string
