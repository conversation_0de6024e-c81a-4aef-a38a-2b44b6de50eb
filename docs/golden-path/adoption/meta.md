<!-- THIS FILE IS NOT INTENDED TO BE DISPLAYED ON THE DOCSITE -->

# Glossary

- Page: A single `md` file.
- Guide: A number of pages grouped under the same folder.

# Overall Writing Guidelines

The goal of these docs is to provide a comprehensive set of guides that technical champions and non-technical users can use to know what to expect on their Backstage adoption journey. This should include a host of resources for how to quantify impact of Backstage, how to present those finding to leadership, focusing on the right things for your company and tips and tricks for engaging with your developers.

When writing guide pages, keep it light! These should be instructional docs, and at the same time conversational and a joy to read. Guides should build on each other, when reading through a progression, the reader should feel more comfortable and confident with concepts as they pop up across progression levels. Guides should be standalone but can link across.

# Sections

Users should already have read through the summary section of the docs, "What is Backstage", "The Spotify story", etc.

## Getting started

We recommend you poke around the demo site to get a feel for what Backstage can provide. If you're technical, or working with someone technical, you can run through the steps in `golden-path/create-app` and `golden-path/deploying-backstage` to get something running for just your company.

## First round of stakeholder feedback

If you think Backstage is a good fit for your company, it's likely there are others that do or will think the same. You may have already identified them. For this initial round of feedback, share recommendations for what that group should look like, is there any required number of technical or non-technical members, do you need leadership involved at this point, etc.

For non-technical users, it's recommended to find a technical partner to help stand up a unique instance.

## Customizing your instance

At this point, we're assuming you already have an instance created through `golden-path/create-app` and `golden-path/deploying-backstage`. You should now start customizing it to your company's needs. We recommend you start small, write some catalog-info YAML files and start to build a personalized catalog.

## Second round of stakeholder feedback

## Getting leadership buy-in

## Plugin ownership and inner source mentality

## Driving to full catalog completion
