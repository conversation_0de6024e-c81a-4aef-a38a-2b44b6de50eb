# Release v1.6.0-next.0

## @backstage/plugin-azure-devops@0.2.0-next.0

### Minor Changes

- 6c1c59b96e: Added README card `EntityAzureReadmeCard` for Azure Devops.

  To get the README component working you'll need to do the following two steps:

  1.  First we need to add the @backstage/plugin-azure-devops package to your frontend app:

      ```bash title="From your Backstage root directory"
      yarn add --cwd packages/app @backstage/plugin-azure-devops
      ```

  2.  Second we need to add the `EntityAzureReadmeCard` extension to the entity page in your app:

      ```tsx
      // In packages/app/src/components/catalog/EntityPage.tsx
      import {
        EntityAzureReadmeCard,
        isAzureDevOpsAvailable,
      } from '@backstage/plugin-azure-devops';

      // As it is a card, you can customize it the way you prefer
      // For example in the Service section

      const overviewContent = (
        <Grid container spacing={3} alignItems="stretch">
          <EntitySwitch>
            <EntitySwitch.Case if={isAzureDevOpsAvailable}>
              <Grid item md={6}>
                ...
              </Grid>
              <Grid item md={6}>
                <EntityAzureReadmeCard maxHeight={350} />
              </Grid>
            </EntitySwitch.Case>
          </EntitySwitch>
        </Grid>
      );
      ```

  **Notes:**

  - You'll need to add the `EntitySwitch.Case` above from step 2 to all the entity sections you want to see Readme in. For example if you wanted to see Readme when looking at Website entities then you would need to add this to the `websiteEntityPage` section.
  - The `if` prop is optional on the `EntitySwitch.Case`, you can remove it if you always want to see the tab even if the entity being viewed does not have the needed annotation
  - The `maxHeight` property on the `EntityAzureReadmeCard` will set the maximum screen size you would like to see, if not set it will default to 100%

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/plugin-azure-devops-common@0.3.0-next.0
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-azure-devops-common@0.3.0-next.0

### Minor Changes

- 6c1c59b96e: Added README card `EntityAzureReadmeCard` for Azure Devops.

  To get the README component working you'll need to do the following two steps:

  1.  First we need to add the @backstage/plugin-azure-devops package to your frontend app:

      ```bash title="From your Backstage root directory"
      yarn add --cwd packages/app @backstage/plugin-azure-devops
      ```

  2.  Second we need to add the `EntityAzureReadmeCard` extension to the entity page in your app:

      ```tsx
      // In packages/app/src/components/catalog/EntityPage.tsx
      import {
        EntityAzureReadmeCard,
        isAzureDevOpsAvailable,
      } from '@backstage/plugin-azure-devops';

      // As it is a card, you can customize it the way you prefer
      // For example in the Service section

      const overviewContent = (
        <Grid container spacing={3} alignItems="stretch">
          <EntitySwitch>
            <EntitySwitch.Case if={isAzureDevOpsAvailable}>
              <Grid item md={6}>
                ...
              </Grid>
              <Grid item md={6}>
                <EntityAzureReadmeCard maxHeight={350} />
              </Grid>
            </EntitySwitch.Case>
          </EntitySwitch>
        </Grid>
      );
      ```

  **Notes:**

  - You'll need to add the `EntitySwitch.Case` above from step 2 to all the entity sections you want to see Readme in. For example if you wanted to see Readme when looking at Website entities then you would need to add this to the `websiteEntityPage` section.
  - The `if` prop is optional on the `EntitySwitch.Case`, you can remove it if you always want to see the tab even if the entity being viewed does not have the needed annotation
  - The `maxHeight` property on the `EntityAzureReadmeCard` will set the maximum screen size you would like to see, if not set it will default to 100%

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup

## @backstage/plugin-dynatrace@0.2.0-next.0

### Minor Changes

- e44c0b3811: New features:

  - Some visual improvements to the table that displays Problems
  - Added support for viewing recent Synthetics results using
  - Added some additional linking to the configured Dynatrace instance

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-scaffolder@1.6.0-next.0

### Minor Changes

- 3424a8075d: Added support for `async` validation for the `next` version of the plugin
- 192d856495: Implementing review step for the scaffolder under `create/next`

### Patch Changes

- 9ffb75616d: Fix bug with empty strings in `EntityPicker`
- 3f739be9d9: Minor API signatures cleanup
- 9097278df2: Updated dependency `json-schema-library` to `^7.0.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/catalog-client@1.0.5-next.0
  - @backstage/integration-react@1.1.4-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-scaffolder-common@1.2.0-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0
  - @backstage/plugin-permission-react@0.4.5-next.0
  - @backstage/plugin-catalog-common@1.0.6-next.0

## @backstage/plugin-scaffolder-backend@1.6.0-next.0

### Minor Changes

- ea2eee9e6a: Add the option for a homepage when using the `github:publish` action

- 8872cc735d: Fixed a bug in plugin-scaffolder-backend where it ignores the skip migration database options.

  To use this new implementation you need to create the instance of `DatabaseTaskStore` using the `PluginDatabaseManager` instead of `Knex`;

      import { DatabaseManager, getRootLogger, loadBackendConfig } from '@backstage/backend-common';
      import { DatabaseTaskStore } from '@backstage/plugin-scaffolder-backend';

      const config = await loadBackendConfig({ argv: process.argv, logger: getRootLogger() });
      const databaseManager = DatabaseManager.fromConfig(config, { migrations: { skip: true } });
      const databaseTaskStore = await DatabaseTaskStore.create(databaseManager);

- 1ff817b3f0: add entity metadata to the template info type

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 2df9955f4a: Removed the depreacated `publish:file` action, use the template editor to test templates instead.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/plugin-catalog-backend@1.3.2-next.0
  - @backstage/backend-plugin-api@0.1.2-next.0
  - @backstage/catalog-client@1.0.5-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-scaffolder-common@1.2.0-next.0
  - @backstage/plugin-catalog-node@1.0.2-next.0

## @backstage/plugin-scaffolder-common@1.2.0-next.0

### Minor Changes

- 1ff817b3f0: add entity metadata to the template info type

## @backstage/plugin-search-backend-module-pg@0.4.0-next.0

### Minor Changes

- 8872cc735d: Fixed a bug in search-backend-module-pg where it ignores the skip migration database options when using the database.

  To use this new implementation you need to create the instance of `DatabaseDocumentStore` using the `PluginDatabaseManager` instead of `Knex`;

      import { DatabaseManager, getRootLogger, loadBackendConfig } from '@backstage/backend-common';
      import { DatabaseDocumentStore } from '@backstage/plugin-search-backend-module-pg';

      const config = await loadBackendConfig({ argv: process.argv, logger: getRootLogger() });
      const databaseManager = DatabaseManager.fromConfig(config, { migrations: { skip: true } });
      const databaseDocumentStore = await DatabaseDocumentStore.create(databaseManager);

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/plugin-search-backend-node@1.0.2-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/app-defaults@1.0.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.0.6-next.0
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-permission-react@0.4.5-next.0

## @backstage/backend-app-api@0.2.1-next.0

### Patch Changes

- de3347ca74: Updated usages of `ServiceFactory`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/backend-tasks@0.3.5-next.0
  - @backstage/backend-plugin-api@0.1.2-next.0
  - @backstage/plugin-permission-node@0.6.5-next.0

## @backstage/backend-common@0.15.1-next.0

### Patch Changes

- 399286d7dd: Workaround support for `swc` instead of `sucrase`
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/config-loader@1.1.4-next.0
  - @backstage/integration@1.3.1-next.0

## @backstage/backend-defaults@0.1.1-next.0

### Patch Changes

- de3347ca74: Updated usages of `ServiceFactory`.
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.2-next.0
  - @backstage/backend-app-api@0.2.1-next.0

## @backstage/backend-plugin-api@0.1.2-next.0

### Patch Changes

- eef91a2558: Simplified the `ServiceFactory` type and removed `AnyServiceFactory`.
- 68513f169a: When defining a new `ServiceRef` you can now also include a `defaultFactory`, which will be used to construct instances of the service in case there is no explicit factory defined.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/backend-tasks@0.3.5-next.0
  - @backstage/plugin-permission-common@0.6.4-next.0

## @backstage/backend-tasks@0.3.5-next.0

### Patch Changes

- 243533ecdc: Added support to mysql on some raw queries
- 8872cc735d: Fixed a bug where the database option to skip migrations was ignored.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0

## @backstage/backend-test-utils@0.1.28-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- de3347ca74: Updated usages of `ServiceFactory`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/cli@0.18.2-next.0
  - @backstage/backend-plugin-api@0.1.2-next.0
  - @backstage/backend-app-api@0.2.1-next.0

## @backstage/catalog-client@1.0.5-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup

## @backstage/cli@0.18.2-next.0

### Patch Changes

- 6ae0f6a719: Switch out `sucrase` for `swc` for transpilation.

  `sucrase` is a little more relaxed when it comes to supporting the ways of mocking in `jest`. You might have to make some changes to your tests to meet the `jest` standard and spec if your tests seems to start failing.

  Mocks that look like this are invalid, and they will throw a reference error in line with the `jest` documentation [here on example 3](https://jestjs.io/docs/es6-class-mocks#calling-jestmock-with-the-module-factory-parameter)

  ```ts
  const mockCommandExists = jest.fn();
  jest.mock('command-exists', () => mockCommandExists);
  ```

  You might need to update these mocks to look a little like the following to defer the call to the `jest.fn()` spy until the mock is called.

  ```ts
  const mockCommandExists = jest.fn();
  jest.mock(
    'command-exists',
    () =>
      (...args: any[]) =>
        commandExists(...args),
  );
  ```

  Also, imports are immutable. So it means that you might get some errors when trying to use `jest.spyOn` with starred imports. You might see an error like this:

  ```log
  TypeError: Cannot redefine property: executeFrameHandlerStrategy
          at Function.defineProperty (<anonymous>)

        20 | import { AuthResolverContext } from '../types';
        21 |
      > 22 | const mockFrameHandler = jest.spyOn(
           |                               ^
        23 |   helpers,
        24 |   'executeFrameHandlerStrategy',
        25 | ) as unknown as jest.MockedFunction<
  ```

  This happens when you try to do `import * as something from './something'` and then `jest.spyOn(something, 'test)`. You will need to add a `jest.mock` call to mock out the required starred import to return `jest.fn()` functions from the start. Something like this fixes the above test:

  ```ts
  jest.mock('../../helpers', () => ({
    executeFrameHandlerStrategy: jest.fn(),
  }));
  ```

  You can also remove any occurrence of `hot(App)` and any import of `react-hot-loader` if you're using the that package locally, as all this has now been replaced with [React Refresh](https://www.npmjs.com/package/react-refresh) which you will get out of the box with the new CLI.

  **Note** If you're experiencing difficulties with running tests after the migration, please reach out to us on Discord to see if we can help, or raise an issue. But in the meantime you can switch back to the existing behaviour by using the following config in your root `package.json`.

  ```json
  "jest": {
    "transform": {
      "\\.(js|jsx|ts|tsx|mjs|cjs)$": "@backstage/cli/config/jestSucraseTransform.js",
      "\\.(bmp|gif|jpg|jpeg|png|frag|xml|svg|eot|woff|woff2|ttf)$": "@backstage/cli/config/jestFileTransform.js",
      "\\.(yaml)$": "jest-transform-yaml"
    }
  }
  ```

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.

- Updated dependencies
  - @backstage/config-loader@1.1.4-next.0
  - @backstage/release-manifests@0.0.6-next.0

## @backstage/config-loader@1.1.4-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.

## @backstage/core-app-api@1.0.6-next.0

### Patch Changes

- 744fea158b: Added `getSystemIcons()` function to the `AppContext` available through `useApp` that will pull a list of all the icons that have been registered in the App.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0

## @backstage/core-components@0.11.1-next.0

### Patch Changes

- b29c44d895: Adds code to generate ids for headers parsed through the MarkdownContent component.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- c8bb0ff8ce: Minor cleanup of the public API surface to reduce the number of warnings
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0

## @backstage/core-plugin-api@1.0.6-next.0

### Patch Changes

- 744fea158b: Added `getSystemIcons()` function to the `AppContext` available through `useApp` that will pull a list of all the icons that have been registered in the App.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup

## @backstage/create-app@0.4.31-next.0

### Patch Changes

- e83de28e36: Fix typo in the documentation

- 208d6780c9: The `packages/backend/Dockerfile` received a couple of updates, it now looks as follows:

  ```Dockerfile
  FROM node:16-bullseye-slim

  # Install sqlite3 dependencies. You can skip this if you don't use sqlite3 in the image,
  # in which case you should also move better-sqlite3 to "devDependencies" in package.json.
  RUN apt-get update && \
      apt-get install -y --no-install-recommends libsqlite3-dev python3 build-essential && \
      rm -rf /var/lib/apt/lists/* && \
      yarn config set python /usr/bin/python3

  # From here on we use the least-privileged `node` user to run the backend.
  USER node
  WORKDIR /app

  # This switches many Node.js dependencies to production mode.
  ENV NODE_ENV production

  # Copy repo skeleton first, to avoid unnecessary docker cache invalidation.
  # The skeleton contains the package.json of each package in the monorepo,
  # and along with yarn.lock and the root package.json, that's enough to run yarn install.
  COPY --chown=node:node yarn.lock package.json packages/backend/dist/skeleton.tar.gz ./
  RUN tar xzf skeleton.tar.gz && rm skeleton.tar.gz

  RUN yarn install --frozen-lockfile --production --network-timeout 300000 && rm -rf "$(yarn cache dir)"

  # Then copy the rest of the backend bundle, along with any other files we might want.
  COPY --chown=node:node packages/backend/dist/bundle.tar.gz app-config*.yaml ./
  RUN tar xzf bundle.tar.gz && rm bundle.tar.gz

  CMD ["node", "packages/backend", "--config", "app-config.yaml", "--config", "app-config.production.yaml"]
  ```

  The two notable changes are that a `USER node` instruction has been added and the ordering of instructions has been changed accordingly. This means that the app will now be running using the least-privileged `node` user. In order for this to work we now need to make sure that all app files are owned by the `node` user, which we do by adding the `--chown=node:node` option to the `COPY` instructions.

  The second change is the addition of `ENV NODE_ENV production`, which ensured that all Node.js modules run in production mode. If you apply this change to an existing app, note that one of the more significant changes is that this switches the log formatting to use the default production format, JSON. Rather than your log lines looking like this:

  ```log
  2022-08-10T11:36:05.478Z catalog info Performing database migration type=plugin
  ```

  They will now look like this:

  ```log
  {"level":"info","message":"Performing database migration","plugin":"catalog","service":"backstage","type":"plugin"}
  ```

  If you wish to keep the existing format, you can override this change by applying the following change to `packages/backend/src/index.ts`:

  ```diff
     getRootLogger,
  +  setRootLogger,
  +  createRootLogger,
  +  coloredFormat,
     useHotMemoize,
   ...
     ServerTokenManager,
   } from '@backstage/backend-common';

   ...

   async function main() {
  +  setRootLogger(createRootLogger({ format: coloredFormat }));
  +
     const config = await loadBackendConfig({
  ```

- c0a08fd08c: Added `EntityLinksCard` to the system `EntityPage`.

  For an existing installation where you want to display the links card for entity pages of kind `system` you should make the following adjustment to `packages/app/src/components/catalog/EntityPage.tsx`

  ```diff
  const systemPage = (
    ...
          <Grid item md={6} xs={12}>
            <EntityCatalogGraphCard variant="gridItem" height={400} />
          </Grid>
  +       <Grid item md={4} xs={12}>
  +         <EntityLinksCard />
  +       </Grid>
  -      <Grid item md={6}>
  +      <Grid item md={8}>
            <EntityHasComponentsCard variant="gridItem" />
          </Grid>
    ...
  );
  ```

## @backstage/dev-utils@1.0.6-next.0

### Patch Changes

- 4c5e85ddf3: Removed the dependency and setup of `react-hot-loader`, since the `@backstage/cli` now uses `swc` with `React Refresh` instead.
- Updated dependencies
  - @backstage/core-app-api@1.0.6-next.0
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/integration-react@1.1.4-next.0
  - @backstage/test-utils@1.1.4-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0
  - @backstage/app-defaults@1.0.6-next.0

## @backstage/integration@1.3.1-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.

## @backstage/integration-react@1.1.4-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/integration@1.3.1-next.0

## @backstage/release-manifests@0.0.6-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.

## @techdocs/cli@1.2.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/plugin-techdocs-node@1.3.1-next.0

## @backstage/test-utils@1.1.4-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-app-api@1.0.6-next.0
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/plugin-permission-common@0.6.4-next.0
  - @backstage/plugin-permission-react@0.4.5-next.0

## @backstage/plugin-adr@0.2.1-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- c8bb0ff8ce: Minor cleanup of the public API surface to reduce the number of warnings
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/integration-react@1.1.4-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0
  - @backstage/plugin-search-react@1.0.2-next.0
  - @backstage/plugin-adr-common@0.2.1-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/plugin-adr-backend@0.2.1-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/catalog-client@1.0.5-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-adr-common@0.2.1-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/plugin-adr-common@0.2.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/plugin-airbrake@0.3.9-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/test-utils@1.1.4-next.0
  - @backstage/dev-utils@1.0.6-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-airbrake-backend@0.2.9-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0

## @backstage/plugin-allure@0.1.25-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-analytics-module-ga@0.1.20-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## @backstage/plugin-apache-airflow@0.2.2-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- c8bb0ff8ce: Minor cleanup of the public API surface to reduce the number of warnings
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## @backstage/plugin-api-docs@0.8.9-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup
- c8bb0ff8ce: Minor cleanup of the public API surface to reduce the number of warnings
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0
  - @backstage/plugin-catalog@1.5.1-next.0

## @backstage/plugin-apollo-explorer@0.1.2-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## @backstage/plugin-app-backend@0.3.36-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 8872cc735d: Fixed a bug where the database option to skip migrations was ignored.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/config-loader@1.1.4-next.0

## @backstage/plugin-auth-backend@0.15.2-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/catalog-client@1.0.5-next.0
  - @backstage/plugin-auth-node@0.2.5-next.0

## @backstage/plugin-auth-node@0.2.5-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0

## @backstage/plugin-azure-devops-backend@0.3.15-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.

- cb1cfc018b: `createRouter` now requires an additional reader: `UrlReader` argument

  ```diff
  export default async function createPlugin(
    env: PluginEnvironment,
  ): Promise<Router> {
    return createRouter({
      logger: env.logger,
      config: env.config,
  +   reader: env.reader,
    });
  }
  ```

  Remember to check if you have already provided these settings previously.

  #### [Azure DevOps]

  ```yaml
  # app-config.yaml
  azureDevOps:
    host: dev.azure.com
    token: my-token
    organization: my-company
  ```

  #### [Azure Integrations]

  ```yaml
  # app-config.yaml
  integrations:
    azure:
      - host: dev.azure.com
        token: ${AZURE_TOKEN}
  ```

- ef9ab322de: Minor API signatures cleanup

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/plugin-azure-devops-common@0.3.0-next.0

## @backstage/plugin-badges@0.2.33-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-badges-backend@0.1.30-next.0

### Patch Changes

- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/catalog-client@1.0.5-next.0

## @backstage/plugin-bazaar@0.1.24-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/cli@0.18.2-next.0
  - @backstage/catalog-client@1.0.5-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0
  - @backstage/plugin-catalog@1.5.1-next.0

## @backstage/plugin-bazaar-backend@0.1.20-next.0

### Patch Changes

- 8872cc735d: Fixed a bug where the database option to skip migrations was ignored.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/backend-test-utils@0.1.28-next.0

## @backstage/plugin-bitbucket-cloud-common@0.1.3-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/integration@1.3.1-next.0

## @backstage/plugin-bitrise@0.1.36-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-catalog@1.5.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/catalog-client@1.0.5-next.0
  - @backstage/integration-react@1.1.4-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0
  - @backstage/plugin-search-react@1.0.2-next.0
  - @backstage/plugin-catalog-common@1.0.6-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/plugin-catalog-backend@1.3.2-next.0

### Patch Changes

- 243533ecdc: Added support to mysql on some raw queries
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 62788b2ee8: The experimental `CatalogProcessingExtensionPoint` now accepts multiple providers and processors at once.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/backend-plugin-api@0.1.2-next.0
  - @backstage/catalog-client@1.0.5-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-permission-common@0.6.4-next.0
  - @backstage/plugin-permission-node@0.6.5-next.0
  - @backstage/plugin-scaffolder-common@1.2.0-next.0
  - @backstage/plugin-catalog-node@1.0.2-next.0
  - @backstage/plugin-catalog-common@1.0.6-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/plugin-catalog-backend-module-aws@0.1.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/backend-tasks@0.3.5-next.0
  - @backstage/plugin-catalog-backend@1.3.2-next.0
  - @backstage/integration@1.3.1-next.0

## @backstage/plugin-catalog-backend-module-azure@0.1.7-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/backend-tasks@0.3.5-next.0
  - @backstage/plugin-catalog-backend@1.3.2-next.0
  - @backstage/integration@1.3.1-next.0

## @backstage/plugin-catalog-backend-module-bitbucket@0.2.3-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/plugin-catalog-backend@1.3.2-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-bitbucket-cloud-common@0.1.3-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.1.3-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-tasks@0.3.5-next.0
  - @backstage/plugin-catalog-backend@1.3.2-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-bitbucket-cloud-common@0.1.3-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.1.1-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/backend-tasks@0.3.5-next.0
  - @backstage/plugin-catalog-backend@1.3.2-next.0
  - @backstage/integration@1.3.1-next.0

## @backstage/plugin-catalog-backend-module-gerrit@0.1.4-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/backend-tasks@0.3.5-next.0
  - @backstage/plugin-catalog-backend@1.3.2-next.0
  - @backstage/integration@1.3.1-next.0

## @backstage/plugin-catalog-backend-module-github@0.1.7-next.0

### Patch Changes

- 3c4a388537: New experimental alpha exports for use with the upcoming backend system.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/backend-tasks@0.3.5-next.0
  - @backstage/plugin-catalog-backend@1.3.2-next.0
  - @backstage/backend-plugin-api@0.1.2-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-catalog-node@1.0.2-next.0

## @backstage/plugin-catalog-backend-module-gitlab@0.1.7-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/backend-tasks@0.3.5-next.0
  - @backstage/plugin-catalog-backend@1.3.2-next.0
  - @backstage/integration@1.3.1-next.0

## @backstage/plugin-catalog-backend-module-ldap@0.5.3-next.0

### Patch Changes

- c54fcea9af: Now the `searchStream` method in LDAP client awaits the callbacks
- Updated dependencies
  - @backstage/backend-tasks@0.3.5-next.0
  - @backstage/plugin-catalog-backend@1.3.2-next.0

## @backstage/plugin-catalog-backend-module-msgraph@0.4.2-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 4c82b955fc: Fix typo
- Updated dependencies
  - @backstage/backend-tasks@0.3.5-next.0
  - @backstage/plugin-catalog-backend@1.3.2-next.0

## @backstage/plugin-catalog-backend-module-openapi@0.1.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/plugin-catalog-backend@1.3.2-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-catalog-node@1.0.2-next.0

## @backstage/plugin-catalog-common@1.0.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.6.4-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/plugin-catalog-graph@0.2.21-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/catalog-client@1.0.5-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-catalog-graphql@0.3.13-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.

## @backstage/plugin-catalog-import@0.8.12-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/catalog-client@1.0.5-next.0
  - @backstage/integration-react@1.1.4-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-catalog-node@1.0.2-next.0

### Patch Changes

- 62788b2ee8: The experimental `CatalogProcessingExtensionPoint` now accepts multiple providers and processors at once.
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.2-next.0

## @backstage/plugin-catalog-react@1.1.4-next.0

### Patch Changes

- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/catalog-client@1.0.5-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-permission-common@0.6.4-next.0
  - @backstage/plugin-permission-react@0.4.5-next.0
  - @backstage/plugin-catalog-common@1.0.6-next.0

## @backstage/plugin-cicd-statistics@0.1.11-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-cicd-statistics-module-gitlab@0.1.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/plugin-cicd-statistics@0.1.11-next.0

## @backstage/plugin-circleci@0.3.9-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-cloudbuild@0.3.9-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-code-climate@0.1.9-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-code-coverage@0.2.2-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-code-coverage-backend@0.2.2-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 8872cc735d: Fixed a bug where the database option to skip migrations was ignored.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/catalog-client@1.0.5-next.0
  - @backstage/integration@1.3.1-next.0

## @backstage/plugin-codescene@0.1.4-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## @backstage/plugin-config-schema@0.1.32-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## @backstage/plugin-cost-insights@0.11.31-next.0

### Patch Changes

- 9318bf15d4: The `CostInsightsHeader`component now uses group names if available
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## @backstage/plugin-explore@0.3.40-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-explore-react@0.0.21-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-explore-react@0.0.21-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0

## @backstage/plugin-firehydrant@0.1.26-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-fossa@0.2.41-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-gcalendar@0.3.5-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## @backstage/plugin-gcp-projects@0.3.28-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## @backstage/plugin-git-release-manager@0.3.22-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/integration@1.3.1-next.0

## @backstage/plugin-github-actions@0.5.9-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-github-deployments@0.1.40-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/integration-react@1.1.4-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-github-issues@0.1.1-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-github-pull-requests-board@0.1.3-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-gitops-profiles@0.3.27-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## @backstage/plugin-gocd@0.1.15-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-graphiql@0.2.41-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## @backstage/plugin-graphql-backend@0.1.26-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/plugin-catalog-graphql@0.3.13-next.0

## @backstage/plugin-home@0.4.25-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-stack-overflow@0.1.5-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-ilert@0.1.35-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-jenkins@0.7.8-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0
  - @backstage/plugin-jenkins-common@0.1.8-next.0

## @backstage/plugin-jenkins-backend@0.1.26-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/catalog-client@1.0.5-next.0
  - @backstage/plugin-auth-node@0.2.5-next.0
  - @backstage/plugin-permission-common@0.6.4-next.0
  - @backstage/plugin-jenkins-common@0.1.8-next.0

## @backstage/plugin-jenkins-common@0.1.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.6.4-next.0
  - @backstage/plugin-catalog-common@1.0.6-next.0

## @backstage/plugin-kafka@0.3.9-next.0

### Patch Changes

- f6be17460d: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-kafka-backend@0.2.29-next.0

### Patch Changes

- f6be17460d: Minor API signatures cleanup
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0

## @backstage/plugin-kubernetes@0.7.2-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-kubernetes-backend@0.7.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/catalog-client@1.0.5-next.0
  - @backstage/plugin-auth-node@0.2.5-next.0

## @backstage/plugin-lighthouse@0.3.9-next.0

### Patch Changes

- f6be17460d: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-newrelic@0.3.27-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## @backstage/plugin-newrelic-dashboard@0.2.2-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-org@0.5.9-next.0

### Patch Changes

- ab6650ede9: Added an edit button to the `UserProfileCard` that is enabled when the `backstage.io/edit-url` is present, this matches how the `GroupProfileCard` works
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-pagerduty@0.5.2-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-periskop@0.1.7-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-periskop-backend@0.1.7-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0

## @backstage/plugin-permission-backend@0.5.11-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/plugin-auth-node@0.2.5-next.0
  - @backstage/plugin-permission-common@0.6.4-next.0
  - @backstage/plugin-permission-node@0.6.5-next.0

## @backstage/plugin-permission-common@0.6.4-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.

## @backstage/plugin-permission-node@0.6.5-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/plugin-auth-node@0.2.5-next.0
  - @backstage/plugin-permission-common@0.6.4-next.0

## @backstage/plugin-permission-react@0.4.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/plugin-permission-common@0.6.4-next.0

## @backstage/plugin-proxy-backend@0.2.30-next.0

### Patch Changes

- f6be17460d: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0

## @backstage/plugin-rollbar@0.4.9-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-rollbar-backend@0.1.33-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.2.11-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/plugin-scaffolder-backend@1.6.0-next.0
  - @backstage/integration@1.3.1-next.0

## @backstage/plugin-scaffolder-backend-module-rails@0.4.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/plugin-scaffolder-backend@1.6.0-next.0
  - @backstage/integration@1.3.1-next.0

## @backstage/plugin-scaffolder-backend-module-yeoman@0.2.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.6.0-next.0

## @backstage/plugin-search@1.0.2-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0
  - @backstage/plugin-search-react@1.0.2-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/plugin-search-backend@1.0.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/plugin-search-backend-node@1.0.2-next.0
  - @backstage/plugin-auth-node@0.2.5-next.0
  - @backstage/plugin-permission-common@0.6.4-next.0
  - @backstage/plugin-permission-node@0.6.5-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/plugin-search-backend-module-elasticsearch@1.0.2-next.0

### Patch Changes

- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/plugin-search-backend-node@1.0.2-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/plugin-search-backend-node@1.0.2-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/backend-tasks@0.3.5-next.0
  - @backstage/plugin-permission-common@0.6.4-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/plugin-search-common@1.0.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.6.4-next.0

## @backstage/plugin-search-react@1.0.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/plugin-sentry@0.4.2-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-shortcuts@0.3.1-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## @backstage/plugin-sonarqube@0.4.1-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-sonarqube-backend@0.1.1-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0

## @backstage/plugin-splunk-on-call@0.3.33-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-stack-overflow@0.1.5-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-home@0.4.25-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/plugin-stack-overflow-backend@0.1.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/plugin-tech-insights@0.2.5-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-tech-insights-backend@0.5.2-next.0

### Patch Changes

- 8872cc735d: Fixed a bug where the database option to skip migrations was ignored.
- 2e0689e536: Support for timeout in FactRetrieverRegistrationOptions
- 9e8e9f5243: Modify Tech insight initialization to expose FactRetrieverEngine. Enables users to trigger fact retrieval manually or reschedule retrievers on runtime.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/plugin-tech-insights-node@0.3.4-next.0
  - @backstage/backend-tasks@0.3.5-next.0
  - @backstage/catalog-client@1.0.5-next.0

## @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.20-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/plugin-tech-insights-node@0.3.4-next.0

## @backstage/plugin-tech-insights-node@0.3.4-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- 2e0689e536: Support for timeout in FactRetrieverRegistrationOptions
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/backend-tasks@0.3.5-next.0

## @backstage/plugin-tech-radar@0.5.16-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## @backstage/plugin-techdocs@1.3.2-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 829f14a9b0: Always update the title and sub-title when the location changes on a `TechDocs` reader page.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/integration-react@1.1.4-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0
  - @backstage/plugin-search-react@1.0.2-next.0
  - @backstage/plugin-techdocs-react@1.0.4-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/plugin-techdocs-addons-test-utils@1.0.4-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/plugin-techdocs@1.3.2-next.0
  - @backstage/core-app-api@1.0.6-next.0
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/integration-react@1.1.4-next.0
  - @backstage/test-utils@1.1.4-next.0
  - @backstage/plugin-catalog@1.5.1-next.0
  - @backstage/plugin-search-react@1.0.2-next.0
  - @backstage/plugin-techdocs-react@1.0.4-next.0

## @backstage/plugin-techdocs-backend@1.2.2-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/catalog-client@1.0.5-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-permission-common@0.6.4-next.0
  - @backstage/plugin-techdocs-node@1.3.1-next.0
  - @backstage/plugin-catalog-common@1.0.6-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/plugin-techdocs-module-addons-contrib@1.0.4-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/integration-react@1.1.4-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-techdocs-react@1.0.4-next.0

## @backstage/plugin-techdocs-node@1.3.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## @backstage/plugin-techdocs-react@1.0.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## @backstage/plugin-todo@0.2.11-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-todo-backend@0.1.33-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/catalog-client@1.0.5-next.0
  - @backstage/integration@1.3.1-next.0

## @backstage/plugin-user-settings@0.4.8-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## @backstage/plugin-vault@0.1.3-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0

## @backstage/plugin-vault-backend@0.2.2-next.0

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/backend-tasks@0.3.5-next.0
  - @backstage/backend-test-utils@0.1.28-next.0

## @backstage/plugin-xcmetrics@0.2.29-next.0

### Patch Changes

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## example-app@0.2.75-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-kafka@0.3.9-next.0
  - @backstage/plugin-lighthouse@0.3.9-next.0
  - @backstage/plugin-scaffolder@1.6.0-next.0
  - @backstage/plugin-azure-devops@0.2.0-next.0
  - @backstage/plugin-cloudbuild@0.3.9-next.0
  - @backstage/plugin-explore@0.3.40-next.0
  - @backstage/plugin-github-actions@0.5.9-next.0
  - @backstage/plugin-jenkins@0.7.8-next.0
  - @backstage/plugin-newrelic-dashboard@0.2.2-next.0
  - @backstage/plugin-pagerduty@0.5.2-next.0
  - @backstage/plugin-sentry@0.4.2-next.0
  - @backstage/plugin-tech-insights@0.2.5-next.0
  - @backstage/plugin-techdocs@1.3.2-next.0
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.4-next.0
  - @backstage/plugin-user-settings@0.4.8-next.0
  - @backstage/plugin-dynatrace@0.2.0-next.0
  - @backstage/plugin-cost-insights@0.11.31-next.0
  - @backstage/core-app-api@1.0.6-next.0
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/plugin-org@0.5.9-next.0
  - @backstage/cli@0.18.2-next.0
  - @backstage/integration-react@1.1.4-next.0
  - @backstage/plugin-airbrake@0.3.9-next.0
  - @backstage/plugin-apache-airflow@0.2.2-next.0
  - @backstage/plugin-api-docs@0.8.9-next.0
  - @backstage/plugin-badges@0.2.33-next.0
  - @backstage/plugin-catalog-import@0.8.12-next.0
  - @backstage/plugin-circleci@0.3.9-next.0
  - @backstage/plugin-code-coverage@0.2.2-next.0
  - @backstage/plugin-gcalendar@0.3.5-next.0
  - @backstage/plugin-gcp-projects@0.3.28-next.0
  - @backstage/plugin-gocd@0.1.15-next.0
  - @backstage/plugin-graphiql@0.2.41-next.0
  - @backstage/plugin-home@0.4.25-next.0
  - @backstage/plugin-kubernetes@0.7.2-next.0
  - @backstage/plugin-newrelic@0.3.27-next.0
  - @backstage/plugin-rollbar@0.4.9-next.0
  - @backstage/plugin-search@1.0.2-next.0
  - @backstage/plugin-shortcuts@0.3.1-next.0
  - @backstage/plugin-stack-overflow@0.1.5-next.0
  - @backstage/plugin-tech-radar@0.5.16-next.0
  - @backstage/plugin-todo@0.2.11-next.0
  - @backstage/plugin-catalog-react@1.1.4-next.0
  - @backstage/app-defaults@1.0.6-next.0
  - @backstage/plugin-catalog-graph@0.2.21-next.0
  - @backstage/plugin-search-react@1.0.2-next.0
  - @backstage/plugin-permission-react@0.4.5-next.0
  - @backstage/plugin-techdocs-react@1.0.4-next.0
  - @backstage/plugin-catalog-common@1.0.6-next.0
  - @backstage/plugin-search-common@1.0.1-next.0
  - @internal/plugin-catalog-customized@0.0.2-next.0

## example-backend@0.2.75-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/plugin-scaffolder-backend@1.6.0-next.0
  - @backstage/plugin-kafka-backend@0.2.29-next.0
  - @backstage/plugin-proxy-backend@0.2.30-next.0
  - @backstage/plugin-azure-devops-backend@0.3.15-next.0
  - @backstage/plugin-search-backend-node@1.0.2-next.0
  - @backstage/plugin-tech-insights-node@0.3.4-next.0
  - @backstage/backend-tasks@0.3.5-next.0
  - @backstage/plugin-catalog-backend@1.3.2-next.0
  - @backstage/plugin-search-backend-module-pg@0.4.0-next.0
  - @backstage/catalog-client@1.0.5-next.0
  - @backstage/integration@1.3.1-next.0
  - @backstage/plugin-app-backend@0.3.36-next.0
  - @backstage/plugin-auth-backend@0.15.2-next.0
  - @backstage/plugin-auth-node@0.2.5-next.0
  - @backstage/plugin-code-coverage-backend@0.2.2-next.0
  - @backstage/plugin-graphql-backend@0.1.26-next.0
  - @backstage/plugin-jenkins-backend@0.1.26-next.0
  - @backstage/plugin-permission-backend@0.5.11-next.0
  - @backstage/plugin-permission-common@0.6.4-next.0
  - @backstage/plugin-permission-node@0.6.5-next.0
  - @backstage/plugin-rollbar-backend@0.1.33-next.0
  - @backstage/plugin-techdocs-backend@1.2.2-next.0
  - @backstage/plugin-todo-backend@0.1.33-next.0
  - @backstage/plugin-tech-insights-backend@0.5.2-next.0
  - @backstage/plugin-badges-backend@0.1.30-next.0
  - @backstage/plugin-search-backend-module-elasticsearch@1.0.2-next.0
  - @backstage/plugin-kubernetes-backend@0.7.2-next.0
  - @backstage/plugin-scaffolder-backend-module-rails@0.4.4-next.0
  - @backstage/plugin-search-backend@1.0.2-next.0
  - @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.20-next.0
  - example-app@0.2.75-next.0
  - @backstage/plugin-search-common@1.0.1-next.0

## example-backend-next@0.0.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.6.0-next.0
  - @backstage/plugin-catalog-backend@1.3.2-next.0
  - @backstage/backend-defaults@0.1.1-next.0

## techdocs-cli-embedded-app@0.2.74-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs@1.3.2-next.0
  - @backstage/core-app-api@1.0.6-next.0
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0
  - @backstage/cli@0.18.2-next.0
  - @backstage/integration-react@1.1.4-next.0
  - @backstage/test-utils@1.1.4-next.0
  - @backstage/app-defaults@1.0.6-next.0
  - @backstage/plugin-catalog@1.5.1-next.0
  - @backstage/plugin-techdocs-react@1.0.4-next.0

## @internal/plugin-catalog-customized@0.0.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.0
  - @backstage/plugin-catalog@1.5.1-next.0

## @internal/plugin-todo-list@1.0.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.0
  - @backstage/core-components@0.11.1-next.0

## @internal/plugin-todo-list-backend@1.0.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.0
  - @backstage/plugin-auth-node@0.2.5-next.0

## @internal/plugin-todo-list-common@1.0.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.6.4-next.0
