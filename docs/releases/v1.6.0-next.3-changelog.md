# Release v1.6.0-next.3

## @backstage/catalog-client@1.1.0-next.2

### Minor Changes

- 65d1d4343f: Adding `validateEntity` method that calls `/validate-entity` endpoint.

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-auth-backend@0.16.0-next.3

### Minor Changes

- 8600855fbf: The auth0 integration is updated to use the `passport-auth0` library. The configuration under `auth.providers.auth0.\*` now supports an optional `audience` parameter; providing that allows you to connect to the correct API to get permissions, access tokens, and full profile information.

  [What is an Audience](https://community.auth0.com/t/what-is-the-audience/71414)

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-auth-node@0.2.5-next.3

## @backstage/plugin-catalog-backend@1.4.0-next.3

### Minor Changes

- 6e63bc43f2: Added the `refresh` function to the Connection of the entity providers.

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.1.0-next.2
  - @backstage/backend-plugin-api@0.1.2-next.2
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/plugin-permission-common@0.6.4-next.2
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-scaffolder-common@1.2.0-next.1
  - @backstage/plugin-permission-node@0.6.5-next.3

## @backstage/plugin-catalog-node@1.1.0-next.2

### Minor Changes

- 9743bc788c: Added refresh function to the `EntityProviderConnection` to be able to schedule refreshes from entity providers.

### Patch Changes

- 409ed984e8: Updated usage of experimental backend service APIs.
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.2-next.2
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/errors@1.1.1-next.0

## @backstage/app-defaults@1.0.6-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- d9e39544be: Add missing peer dependencies
- Updated dependencies
  - @backstage/core-app-api@1.1.0-next.3
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/plugin-permission-react@0.4.5-next.2

## @backstage/backend-app-api@0.2.1-next.2

### Patch Changes

- 854ba37357: Updated to support new `ServiceFactory` formats.
- 409ed984e8: Updated service implementations and backend wiring to support scoped service.
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.2-next.2
  - @backstage/errors@1.1.1-next.0
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/backend-tasks@0.3.5-next.1
  - @backstage/plugin-permission-node@0.6.5-next.3

## @backstage/backend-common@0.15.1-next.3

### Patch Changes

- 96689fbdcb: Workaround for a rare race condition in tests.
- Updated dependencies
  - @backstage/config-loader@1.1.4-next.2
  - @backstage/cli-common@0.1.10-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2

## @backstage/backend-defaults@0.1.1-next.1

### Patch Changes

- 854ba37357: Updated to support new `ServiceFactory` formats.
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.2-next.2
  - @backstage/backend-app-api@0.2.1-next.2

## @backstage/backend-plugin-api@0.1.2-next.2

### Patch Changes

- 409ed984e8: Service are now scoped to either `'plugin'` or `'root'` scope. Service factories have been updated to provide dependency instances directly rather than factory functions.
- 854ba37357: The `createServiceFactory` method has been updated to return a higher-order factory that can accept options.
- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/plugin-permission-common@0.6.4-next.2
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/backend-tasks@0.3.5-next.1

## @backstage/backend-tasks@0.3.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/backend-common@0.15.1-next.3

## @backstage/backend-test-utils@0.1.28-next.3

### Patch Changes

- 854ba37357: Updated to support new `ServiceFactory` formats.
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.2-next.2
  - @backstage/config@1.0.2-next.0
  - @backstage/backend-app-api@0.2.1-next.2
  - @backstage/cli@0.19.0-next.3
  - @backstage/backend-common@0.15.1-next.3

## @backstage/catalog-model@1.1.1-next.0

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0

## @backstage/cli@0.19.0-next.3

### Patch Changes

- 7d47def9c4: Added dependency on `@types/jest` v27. The `@types/jest` dependency has also been removed from the plugin template and should be removed from any of your own internal packages. If you wish to override the version of `@types/jest` or `jest`, use Yarn resolutions.
- a7e82c9b01: Updated `versions:bump` command to be compatible with Yarn 3.
- Updated dependencies
  - @backstage/config-loader@1.1.4-next.2
  - @backstage/cli-common@0.1.10-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/release-manifests@0.0.6-next.2

## @backstage/cli-common@0.1.10-next.0

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.

## @backstage/codemods@0.1.39-next.0

### Patch Changes

- Updated dependencies
  - @backstage/cli-common@0.1.10-next.0

## @backstage/config@1.0.2-next.0

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.

## @backstage/config-loader@1.1.4-next.2

### Patch Changes

- 5ecca7e44b: No longer log when reloading remote config.
- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/cli-common@0.1.10-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0

## @backstage/core-app-api@1.1.0-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/core-components@0.11.1-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/core-plugin-api@1.0.6-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/config@1.0.2-next.0

## @backstage/create-app@0.4.31-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/cli-common@0.1.10-next.0

## @backstage/dev-utils@1.0.6-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 329ed2b9c7: Fixed routing when using React Router v6 stable.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/app-defaults@1.0.6-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-app-api@1.1.0-next.3
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/integration-react@1.1.4-next.2
  - @backstage/test-utils@1.2.0-next.3

## @backstage/errors@1.1.1-next.0

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.

## @backstage/integration@1.3.1-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- f76f22c649: Improved caching around github app tokens.
  Tokens are now cached for 50 minutes, not 10.
  Calls to get app installations are also included in this cache.
  If you have more than one github app configured, consider adding `allowedInstallationOwners` to your apps configuration to gain the most benefit from these performance changes.
- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0

## @backstage/integration-react@1.1.4-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/integration@1.3.1-next.2

## @backstage/release-manifests@0.0.6-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.

## @techdocs/cli@1.2.1-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/cli-common@0.1.10-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-techdocs-node@1.4.0-next.2

## @backstage/test-utils@1.2.0-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- d9e39544be: Add missing peer dependencies
- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/core-app-api@1.1.0-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/plugin-permission-common@0.6.4-next.2
  - @backstage/plugin-permission-react@0.4.5-next.2

## @backstage/plugin-adr@0.2.1-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/integration-react@1.1.4-next.2
  - @backstage/plugin-adr-common@0.2.1-next.1

## @backstage/plugin-adr-backend@0.2.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-adr-common@0.2.1-next.1

## @backstage/plugin-adr-common@0.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2

## @backstage/plugin-airbrake@0.3.9-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/dev-utils@1.0.6-next.2
  - @backstage/test-utils@1.2.0-next.3

## @backstage/plugin-airbrake-backend@0.2.9-next.3

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/backend-common@0.15.1-next.3

## @backstage/plugin-allure@0.1.25-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-analytics-module-ga@0.1.20-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-apache-airflow@0.2.2-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-api-docs@0.8.9-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/plugin-catalog@1.5.1-next.3

## @backstage/plugin-apollo-explorer@0.1.2-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-app-backend@0.3.36-next.3

### Patch Changes

- Updated dependencies
  - @backstage/config-loader@1.1.4-next.2
  - @backstage/config@1.0.2-next.0
  - @backstage/backend-common@0.15.1-next.3

## @backstage/plugin-auth-node@0.2.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/backend-common@0.15.1-next.3

## @backstage/plugin-azure-devops@0.2.0-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-azure-devops-backend@0.3.15-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/backend-common@0.15.1-next.3

## @backstage/plugin-badges@0.2.33-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-badges-backend@0.1.30-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/backend-common@0.15.1-next.3

## @backstage/plugin-bazaar@0.1.24-next.2

### Patch Changes

- 1dd12349d1: Fixed broken routing by removing the wrapping `Router` from the `RoutedTabs` children.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/plugin-catalog@1.5.1-next.3
  - @backstage/cli@0.19.0-next.3

## @backstage/plugin-bazaar-backend@0.1.20-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/backend-test-utils@0.1.28-next.3
  - @backstage/backend-common@0.15.1-next.3

## @backstage/plugin-bitrise@0.1.36-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-catalog@1.5.1-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration-react@1.1.4-next.2

## @backstage/plugin-catalog-backend-module-aws@0.1.9-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/plugin-catalog-backend@1.4.0-next.3
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/backend-tasks@0.3.5-next.1

## @backstage/plugin-catalog-backend-module-azure@0.1.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/plugin-catalog-backend@1.4.0-next.3
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/backend-tasks@0.3.5-next.1

## @backstage/plugin-catalog-backend-module-bitbucket@0.2.3-next.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/plugin-catalog-backend@1.4.0-next.3
  - @backstage/backend-common@0.15.1-next.3

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.1.3-next.3

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/plugin-catalog-backend@1.4.0-next.3
  - @backstage/backend-tasks@0.3.5-next.1

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.1.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/plugin-catalog-backend@1.4.0-next.3
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/backend-tasks@0.3.5-next.1

## @backstage/plugin-catalog-backend-module-gerrit@0.1.4-next.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/plugin-catalog-backend@1.4.0-next.3
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/backend-tasks@0.3.5-next.1

## @backstage/plugin-catalog-backend-module-github@0.1.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.1.0-next.2
  - @backstage/backend-plugin-api@0.1.2-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/plugin-catalog-backend@1.4.0-next.3
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/backend-tasks@0.3.5-next.1

## @backstage/plugin-catalog-backend-module-gitlab@0.1.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/plugin-catalog-backend@1.4.0-next.3
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/backend-tasks@0.3.5-next.1

## @backstage/plugin-catalog-backend-module-ldap@0.5.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/plugin-catalog-backend@1.4.0-next.3
  - @backstage/backend-tasks@0.3.5-next.1

## @backstage/plugin-catalog-backend-module-msgraph@0.4.2-next.3

### Patch Changes

- d80aab31ae: Added $select attribute to user query
- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/plugin-catalog-backend@1.4.0-next.3
  - @backstage/backend-tasks@0.3.5-next.1

## @backstage/plugin-catalog-backend-module-openapi@0.1.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/plugin-catalog-backend@1.4.0-next.3
  - @backstage/backend-common@0.15.1-next.3

## @backstage/plugin-catalog-graph@0.2.21-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-catalog-graphql@0.3.13-next.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0

## @backstage/plugin-catalog-import@0.8.12-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/integration-react@1.1.4-next.2

## @backstage/plugin-catalog-react@1.1.4-next.2

### Patch Changes

- f6033d1121: humanizeEntityRef function can now be forced to include default namespace
- c86741a052: Support showing counts in option labels of the `EntityTagPicker`. You can enable this by adding the `showCounts` property
- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/plugin-permission-common@0.6.4-next.2
  - @backstage/plugin-permission-react@0.4.5-next.2

## @backstage/plugin-cicd-statistics@0.1.11-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-cicd-statistics-module-gitlab@0.1.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/plugin-cicd-statistics@0.1.11-next.2

## @backstage/plugin-circleci@0.3.9-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-cloudbuild@0.3.9-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-code-climate@0.1.9-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-code-coverage@0.2.2-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-code-coverage-backend@0.2.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/backend-common@0.15.1-next.3

## @backstage/plugin-codescene@0.1.4-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-config-schema@0.1.32-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-cost-insights@0.11.31-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-dynatrace@0.2.0-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-explore@0.3.40-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/plugin-explore-react@0.0.21-next.3

## @backstage/plugin-explore-react@0.0.21-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-firehydrant@0.1.26-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-fossa@0.2.41-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-gcalendar@0.3.5-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-gcp-projects@0.3.28-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-git-release-manager@0.3.22-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/integration@1.3.1-next.2

## @backstage/plugin-github-actions@0.5.9-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/integration@1.3.1-next.2

## @backstage/plugin-github-deployments@0.1.40-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/integration-react@1.1.4-next.2

## @backstage/plugin-github-issues@0.1.1-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2

## @backstage/plugin-github-pull-requests-board@0.1.3-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/integration@1.3.1-next.2

## @backstage/plugin-gitops-profiles@0.3.27-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-gocd@0.1.15-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-graphiql@0.2.41-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-graphql-backend@0.1.26-next.3

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-catalog-graphql@0.3.13-next.3

## @backstage/plugin-home@0.4.25-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/plugin-stack-overflow@0.1.5-next.3

## @backstage/plugin-ilert@0.1.35-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-jenkins@0.7.8-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-jenkins-backend@0.1.26-next.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/plugin-permission-common@0.6.4-next.2
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-auth-node@0.2.5-next.3

## @backstage/plugin-kafka@0.3.9-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-kafka-backend@0.2.29-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/backend-common@0.15.1-next.3

## @backstage/plugin-kubernetes@0.7.2-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 19a27929fb: Reset error state on success
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/plugin-kubernetes-common@0.4.2-next.1

## @backstage/plugin-kubernetes-backend@0.7.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-kubernetes-common@0.4.2-next.1
  - @backstage/plugin-auth-node@0.2.5-next.3

## @backstage/plugin-kubernetes-common@0.4.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0

## @backstage/plugin-lighthouse@0.3.9-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-newrelic@0.3.27-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-newrelic-dashboard@0.2.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-org@0.5.9-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-pagerduty@0.5.2-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-periskop@0.1.7-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-periskop-backend@0.1.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/backend-common@0.15.1-next.3

## @backstage/plugin-permission-backend@0.5.11-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/plugin-permission-common@0.6.4-next.2
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-auth-node@0.2.5-next.3
  - @backstage/plugin-permission-node@0.6.5-next.3

## @backstage/plugin-permission-common@0.6.4-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-permission-node@0.6.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/plugin-permission-common@0.6.4-next.2
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-auth-node@0.2.5-next.3

## @backstage/plugin-permission-react@0.4.5-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/plugin-permission-common@0.6.4-next.2

## @backstage/plugin-proxy-backend@0.2.30-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/backend-common@0.15.1-next.3

## @backstage/plugin-rollbar@0.4.9-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-rollbar-backend@0.1.33-next.3

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/backend-common@0.15.1-next.3

## @backstage/plugin-scaffolder@1.6.0-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 6522e459aa: Support displaying and ordering by counts in `EntityTagPicker` field. Add the `showCounts` option to enable this. Also support configuring `helperText`.
- f0510a20b5: Addition of a dismissible Error Banner in Scaffolder page
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/integration-react@1.1.4-next.2
  - @backstage/plugin-permission-react@0.4.5-next.2
  - @backstage/plugin-scaffolder-common@1.2.0-next.1

## @backstage/plugin-scaffolder-backend@1.6.0-next.3

### Patch Changes

- 50467bc15b: The number of task workers used to execute templates now default to 3, rather than 1.
- Updated dependencies
  - @backstage/plugin-catalog-node@1.1.0-next.2
  - @backstage/backend-plugin-api@0.1.2-next.2
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/plugin-catalog-backend@1.4.0-next.3
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-scaffolder-common@1.2.0-next.1
  - @backstage/plugin-auth-node@0.2.5-next.3

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.2.11-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-scaffolder-backend@1.6.0-next.3

## @backstage/plugin-scaffolder-backend-module-rails@0.4.4-next.1

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-scaffolder-backend@1.6.0-next.3

## @backstage/plugin-scaffolder-backend-module-yeoman@0.2.9-next.1

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/plugin-scaffolder-backend@1.6.0-next.3

## @backstage/plugin-scaffolder-common@1.2.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0

## @backstage/plugin-search@1.0.2-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-search-backend@1.0.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/plugin-permission-common@0.6.4-next.2
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-auth-node@0.2.5-next.3
  - @backstage/plugin-permission-node@0.6.5-next.3
  - @backstage/plugin-search-backend-node@1.0.2-next.2

## @backstage/plugin-search-backend-module-elasticsearch@1.0.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/plugin-search-backend-node@1.0.2-next.2

## @backstage/plugin-search-backend-module-pg@0.4.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-search-backend-node@1.0.2-next.2

## @backstage/plugin-search-backend-node@1.0.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/plugin-permission-common@0.6.4-next.2
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/backend-tasks@0.3.5-next.1

## @backstage/plugin-sentry@0.4.2-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-shortcuts@0.3.1-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-sonarqube@0.4.1-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-sonarqube-backend@0.1.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/backend-common@0.15.1-next.3

## @backstage/plugin-splunk-on-call@0.3.33-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-stack-overflow@0.1.5-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/plugin-home@0.4.25-next.3

## @backstage/plugin-stack-overflow-backend@0.1.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/cli@0.19.0-next.3

## @backstage/plugin-tech-insights@0.3.0-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-tech-insights-backend@0.5.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/backend-tasks@0.3.5-next.1
  - @backstage/plugin-tech-insights-node@0.3.4-next.1

## @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.20-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-tech-insights-node@0.3.4-next.1

## @backstage/plugin-tech-insights-node@0.3.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/backend-tasks@0.3.5-next.1

## @backstage/plugin-tech-radar@0.5.16-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-techdocs@1.3.2-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/integration-react@1.1.4-next.2
  - @backstage/plugin-techdocs-react@1.0.4-next.2

## @backstage/plugin-techdocs-addons-test-utils@1.0.4-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/core-app-api@1.1.0-next.3
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/integration-react@1.1.4-next.2
  - @backstage/test-utils@1.2.0-next.3
  - @backstage/plugin-catalog@1.5.1-next.3
  - @backstage/plugin-techdocs@1.3.2-next.3
  - @backstage/plugin-techdocs-react@1.0.4-next.2

## @backstage/plugin-techdocs-backend@1.3.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/plugin-permission-common@0.6.4-next.2
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-techdocs-node@1.4.0-next.2

## @backstage/plugin-techdocs-module-addons-contrib@1.0.4-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/integration@1.3.1-next.2
  - @backstage/integration-react@1.1.4-next.2
  - @backstage/plugin-techdocs-react@1.0.4-next.2

## @backstage/plugin-techdocs-node@1.4.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/backend-common@0.15.1-next.3

## @backstage/plugin-techdocs-react@1.0.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-todo@0.2.11-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-todo-backend@0.1.33-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/backend-common@0.15.1-next.3

## @backstage/plugin-user-settings@0.4.8-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3

## @backstage/plugin-vault@0.1.3-next.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## @backstage/plugin-vault-backend@0.2.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/backend-test-utils@0.1.28-next.3
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/backend-tasks@0.3.5-next.1

## @backstage/plugin-xcmetrics@0.2.29-next.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/errors@1.1.1-next.0

## example-app@0.2.75-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4-next.2
  - @backstage/app-defaults@1.0.6-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/core-app-api@1.1.0-next.3
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/integration-react@1.1.4-next.2
  - @backstage/plugin-airbrake@0.3.9-next.3
  - @backstage/plugin-apache-airflow@0.2.2-next.3
  - @backstage/plugin-api-docs@0.8.9-next.3
  - @backstage/plugin-azure-devops@0.2.0-next.3
  - @backstage/plugin-badges@0.2.33-next.3
  - @backstage/plugin-catalog-graph@0.2.21-next.2
  - @backstage/plugin-catalog-import@0.8.12-next.3
  - @backstage/plugin-circleci@0.3.9-next.3
  - @backstage/plugin-cloudbuild@0.3.9-next.3
  - @backstage/plugin-code-coverage@0.2.2-next.3
  - @backstage/plugin-cost-insights@0.11.31-next.3
  - @backstage/plugin-dynatrace@0.2.0-next.3
  - @backstage/plugin-explore@0.3.40-next.3
  - @backstage/plugin-gcalendar@0.3.5-next.3
  - @backstage/plugin-gcp-projects@0.3.28-next.3
  - @backstage/plugin-github-actions@0.5.9-next.3
  - @backstage/plugin-gocd@0.1.15-next.2
  - @backstage/plugin-graphiql@0.2.41-next.3
  - @backstage/plugin-home@0.4.25-next.3
  - @backstage/plugin-jenkins@0.7.8-next.3
  - @backstage/plugin-kafka@0.3.9-next.3
  - @backstage/plugin-kubernetes@0.7.2-next.3
  - @backstage/plugin-lighthouse@0.3.9-next.3
  - @backstage/plugin-newrelic@0.3.27-next.3
  - @backstage/plugin-org@0.5.9-next.3
  - @backstage/plugin-pagerduty@0.5.2-next.3
  - @backstage/plugin-permission-react@0.4.5-next.2
  - @backstage/plugin-rollbar@0.4.9-next.3
  - @backstage/plugin-scaffolder@1.6.0-next.3
  - @backstage/plugin-search@1.0.2-next.3
  - @backstage/plugin-sentry@0.4.2-next.3
  - @backstage/plugin-shortcuts@0.3.1-next.3
  - @backstage/plugin-stack-overflow@0.1.5-next.3
  - @backstage/plugin-tech-insights@0.3.0-next.3
  - @backstage/plugin-tech-radar@0.5.16-next.3
  - @backstage/plugin-techdocs@1.3.2-next.3
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.4-next.2
  - @backstage/plugin-todo@0.2.11-next.3
  - @backstage/plugin-user-settings@0.4.8-next.3
  - @backstage/cli@0.19.0-next.3
  - @backstage/plugin-newrelic-dashboard@0.2.2-next.2
  - @backstage/plugin-techdocs-react@1.0.4-next.2

## example-backend@0.2.75-next.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.0-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/integration@1.3.1-next.2
  - @backstage/plugin-permission-common@0.6.4-next.2
  - @backstage/plugin-scaffolder-backend-module-rails@0.4.4-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.3
  - @backstage/plugin-auth-backend@0.16.0-next.3
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-scaffolder-backend@1.6.0-next.3
  - @backstage/plugin-badges-backend@0.1.30-next.1
  - @backstage/plugin-code-coverage-backend@0.2.2-next.2
  - @backstage/plugin-jenkins-backend@0.1.26-next.3
  - @backstage/plugin-kubernetes-backend@0.7.2-next.3
  - @backstage/plugin-tech-insights-backend@0.5.2-next.2
  - @backstage/plugin-techdocs-backend@1.3.0-next.2
  - @backstage/plugin-todo-backend@0.1.33-next.2
  - example-app@0.2.75-next.3
  - @backstage/plugin-kafka-backend@0.2.29-next.1
  - @backstage/backend-tasks@0.3.5-next.1
  - @backstage/plugin-app-backend@0.3.36-next.3
  - @backstage/plugin-auth-node@0.2.5-next.3
  - @backstage/plugin-azure-devops-backend@0.3.15-next.2
  - @backstage/plugin-graphql-backend@0.1.26-next.3
  - @backstage/plugin-permission-backend@0.5.11-next.2
  - @backstage/plugin-permission-node@0.6.5-next.3
  - @backstage/plugin-proxy-backend@0.2.30-next.2
  - @backstage/plugin-rollbar-backend@0.1.33-next.3
  - @backstage/plugin-search-backend@1.0.2-next.1
  - @backstage/plugin-search-backend-module-elasticsearch@1.0.2-next.2
  - @backstage/plugin-search-backend-module-pg@0.4.0-next.2
  - @backstage/plugin-search-backend-node@1.0.2-next.2
  - @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.20-next.1
  - @backstage/plugin-tech-insights-node@0.3.4-next.1

## techdocs-cli-embedded-app@0.2.74-next.2

### Patch Changes

- Updated dependencies
  - @backstage/app-defaults@1.0.6-next.2
  - @backstage/catalog-model@1.1.1-next.0
  - @backstage/config@1.0.2-next.0
  - @backstage/core-app-api@1.1.0-next.3
  - @backstage/core-components@0.11.1-next.3
  - @backstage/core-plugin-api@1.0.6-next.3
  - @backstage/integration-react@1.1.4-next.2
  - @backstage/test-utils@1.2.0-next.3
  - @backstage/plugin-catalog@1.5.1-next.3
  - @backstage/plugin-techdocs@1.3.2-next.3
  - @backstage/cli@0.19.0-next.3
  - @backstage/plugin-techdocs-react@1.0.4-next.2

## @internal/plugin-todo-list-backend@1.0.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.2-next.0
  - @backstage/errors@1.1.1-next.0
  - @backstage/backend-common@0.15.1-next.3
  - @backstage/plugin-auth-node@0.2.5-next.3
