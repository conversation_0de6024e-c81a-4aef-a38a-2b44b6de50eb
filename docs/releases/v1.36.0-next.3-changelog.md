# Release v1.36.0-next.3

Upgrade Helper: [https://backstage.github.io/upgrade-helper/?to=1.36.0-next.3](https://backstage.github.io/upgrade-helper/?to=1.36.0-next.3)

## @backstage/canon@0.1.0-next.2

### Minor Changes

- 8309bdb: Updated core CSS tokens and fixing the Button component accordingly.

### Patch Changes

- f44e5cf: Fix spacing props not being applied for custom values.

## @backstage/cli@0.30.0-next.3

### Minor Changes

- b30e788: The `new` command is now powered by a new template system that allows you to define your own templates in a declarative way, as well as import existing templates from external sources. See the [CLI templates documentation](https://backstage.io/docs/tooling/cli/templates) for more information.

  The following flags for the `new` command have been deprecated and will be removed in a future release:

  - `--license=<license>`: Configure the global `license` instead.
  - `--no-private`: Configure the global `private` instead.
  - `--baseVersion=<version>`: Configure the global `version` instead.
  - `--npmRegistry=<url>`: Configure the global `publishRegistry` instead.
  - `--scope=<scope>`: Configure the global `namePrefix` and/or `namePluginInfix` instead.

  As part of this change the template IDs and their options have changed. The following backwards compatibility mappings for the `--select` and `--option` flags are enabled when using the default set of templates, but they will also be removed in the future:

  - `--select=plugin` is mapped to `--select=frontend-plugin` instead.
  - `--option=id=<id>` is mapped to `--option=pluginId=<id>` instead.

### Patch Changes

- Updated dependencies
  - @backstage/cli-node@0.2.13-next.1
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/eslint-plugin@0.1.10
  - @backstage/integration@1.16.1
  - @backstage/release-manifests@0.0.12
  - @backstage/types@1.2.1

## @backstage/plugin-catalog-backend@1.31.0-next.3

### Minor Changes

- 5aebc13: The `UrlReaderProccessor` accepts a new config flag `catalog.useUrlReadersSearch` to always call the `search` method of `UrlReaders`.

  This flag currently defaults to `false`, but adopters are encouraged to enable it as this behavior will be the default in a future release.

  Previous behavior was to call the `search` method only if the parsed Git URL's filename contained a wildcard and use `readUrl` otherwise. `UrlReaderService` must implement this logic in the `search` method instead.

  This allows each `UrlReaderService` implementation to check whether it's a search URL (that contains a wildcard pattern) or not using logic that is specific to each provider.

### Patch Changes

- ef73f97: Updated permission integration to use new permission resource ref.
- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/plugin-permission-node@0.8.8-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-backend-module-catalog@0.3.1-next.3
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/backend-openapi-utils@0.5.0-next.3
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-catalog-node@1.16.0-next.3

### Minor Changes

- b4183ad: Added new `catalogEntityPermissionResourceRef` export via the `/alpha` sub-path.

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-scaffolder@1.28.0-next.3

### Minor Changes

- 5d469c9: Added support for autocompletion of GitHub branches in scaffolder
- 8e67e4a: Added support for autocompletion to GithubRepoPicker component

### Patch Changes

- 1e935f0: Added conditional rendering of `oneOf` output schemas on the Installed Actions page for scaffolder actions
- 65d7020: Use template title for ongoing task page header
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-compat-api@0.3.6-next.3
  - @backstage/plugin-scaffolder-react@1.14.5-next.3
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-react@1.15.2-next.3
  - @backstage/plugin-permission-react@0.4.31-next.0
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-scaffolder-backend@1.30.0-next.3

### Minor Changes

- dc8dd4b: Added new `createTemplateFilter`, `createTemplateGlobalFunction`, `createTemplateGlobalValue` for template extensions.
- 3d6ef79: Support file globbing in fs:delete

### Patch Changes

- 1e935f0: Added conditional rendering of `oneOf` output schemas on the Installed Actions page for scaffolder actions
- Updated dependencies
  - @backstage/plugin-scaffolder-backend-module-github@0.6.0-next.2
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/plugin-permission-node@0.8.8-next.2
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-defaults@0.8.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.5-next.3
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.7-next.2
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.6-next.2
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.6-next.2
  - @backstage/plugin-scaffolder-backend-module-gerrit@0.2.6-next.2
  - @backstage/plugin-scaffolder-backend-module-gitea@0.2.6-next.2
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.8.0-next.3
  - @backstage/plugin-scaffolder-backend-module-azure@0.2.6-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.27
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-scaffolder-backend-module-github@0.6.0-next.2

### Minor Changes

- bb8302b: **BREAKING**: The `remoteUrl` output is no longer required, it can be empty only when using the new `createWhenEmpty` boolean flag.

### Patch Changes

- 5c187f9: **DEPRECATION**: The `getOctokitOptions` function signature with `repoUrl` option has been deprecated in favour of a function signature with individual `host`, `owner`, and `repo` parameters:

  ```diff
    const octokitOptions = await getOctokitOptions({
      integrations,
      credentialsProvider,
      token,
  -   repoUrl,
  +   host,
  +   owner,
  +   repo,
    });
  ```

- 5d469c9: Added support for autocompletion of GitHub branches in scaffolder

- 8e67e4a: Added support for autocompletion to GithubRepoPicker component

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-node@0.7.0-next.2

### Minor Changes

- dc8dd4b: Added new `createTemplateFilter`, `createTemplateGlobalFunction`, `createTemplateGlobalValue` for template extensions.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/backend-app-api@1.2.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/backend-defaults@0.8.0-next.3

### Patch Changes

- 3740229: In the different `UrlReadersService`, the `search` method have been updated to use the `readUrl` if the given URL doesn't contain a pattern.
  For `UrlReaders` that didn't implement the `search` method, `readUrl` is now called internally and throws if the given URL doesn't contain a pattern.
- 72cddf2: Updated `PermissionsRegistryService` to use `PermissionResourceRef`s and added the `getPermissionRuleset` method.
- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/backend-app-api@1.2.0-next.3
  - @backstage/cli-node@0.2.13-next.1
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/backend-dev-utils@0.1.5
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1

## @backstage/backend-dynamic-feature-service@0.6.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.2
  - @backstage/plugin-catalog-backend@1.31.0-next.3
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-defaults@0.8.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/cli-node@0.2.13-next.1
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/plugin-events-backend@0.4.2-next.3
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/plugin-search-backend-node@1.3.8-next.2
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-app-node@0.1.30-next.2
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-common@1.2.17

## @backstage/backend-openapi-utils@0.5.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/backend-plugin-api@1.2.0-next.2

### Patch Changes

- 72cddf2: Updated `PermissionsRegistryService` to use `PermissionResourceRef`s and added the `getPermissionRuleset` method.
- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4

## @backstage/backend-test-utils@1.3.0-next.3

### Patch Changes

- 72cddf2: Added the new `getPermissionRuleset` method to `mockServices.permissionsRegistry`.
- Updated dependencies
  - @backstage/backend-defaults@0.8.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/backend-app-api@1.2.0-next.3
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/core-compat-api@0.3.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/version-bridge@1.0.11-next.0

## @backstage/create-app@0.5.25-next.3

### Patch Changes

- bfa4ea9: Enable `catalog.useUrlReadersSearch` configuration by default in newly created Backstage installations.

  This parameter makes `UrlReaderProcessor` always use the search method.
  New adopters are encouraged to enable it as this behavior will be the default in a future release.

- Updated dependencies
  - @backstage/cli-common@0.1.15

## @backstage/dev-utils@1.1.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/app-defaults@1.5.17-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.3

## @backstage/frontend-app-api@0.10.5-next.3

### Patch Changes

- f1efb47: Add support for defining multiple attachment points for extensions and blueprints.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/config@1.3.2
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/errors@1.2.7
  - @backstage/frontend-defaults@0.1.6-next.3
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11-next.0

## @backstage/frontend-defaults@0.1.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/frontend-app-api@0.10.5-next.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-app@0.1.6-next.3

## @backstage/frontend-plugin-api@0.9.5-next.3

### Patch Changes

- f1efb47: Add support for defining multiple attachment points for extensions and blueprints.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11-next.0

## @backstage/frontend-test-utils@0.2.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/frontend-app-api@0.10.5-next.3
  - @backstage/config@1.3.2
  - @backstage/test-utils@1.7.5-next.0
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/plugin-app@0.1.6-next.3

## @backstage/repo-tools@0.12.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/cli-node@0.2.13-next.1
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/errors@1.2.7

## @techdocs/cli@1.9.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.0-next.3
  - @backstage/plugin-techdocs-node@1.13.0-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2

## @backstage/plugin-api-docs@0.12.4-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.27.0-next.3
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-compat-api@0.3.6-next.3
  - @backstage/catalog-model@1.7.3
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-react@1.15.2-next.3
  - @backstage/plugin-permission-react@0.4.31-next.0

## @backstage/plugin-app@0.1.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/plugin-permission-react@0.4.31-next.0

## @backstage/plugin-app-backend@0.4.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-app-node@0.1.30-next.2

## @backstage/plugin-app-node@0.1.30-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config-loader@1.9.6-next.0

## @backstage/plugin-app-visualizer@0.1.16-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0

## @backstage/plugin-auth-backend@0.24.3-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-backend-module-atlassian-provider@0.4.0-next.2
  - @backstage/plugin-auth-backend-module-auth0-provider@0.2.0-next.2
  - @backstage/plugin-auth-backend-module-aws-alb-provider@0.4.0-next.3
  - @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.5-next.2
  - @backstage/plugin-auth-backend-module-bitbucket-provider@0.3.0-next.2
  - @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.2.0-next.2
  - @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.4.0-next.2
  - @backstage/plugin-auth-backend-module-gcp-iap-provider@0.4.0-next.2
  - @backstage/plugin-auth-backend-module-github-provider@0.3.0-next.2
  - @backstage/plugin-auth-backend-module-gitlab-provider@0.3.0-next.2
  - @backstage/plugin-auth-backend-module-google-provider@0.3.0-next.2
  - @backstage/plugin-auth-backend-module-microsoft-provider@0.3.0-next.2
  - @backstage/plugin-auth-backend-module-oauth2-provider@0.4.0-next.2
  - @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.5-next.2
  - @backstage/plugin-auth-backend-module-oidc-provider@0.4.0-next.3
  - @backstage/plugin-auth-backend-module-okta-provider@0.2.0-next.2
  - @backstage/plugin-auth-backend-module-onelogin-provider@0.3.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-auth-backend-module-atlassian-provider@0.4.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2

## @backstage/plugin-auth-backend-module-auth0-provider@0.2.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2

## @backstage/plugin-auth-backend-module-aws-alb-provider@0.4.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-backend@0.24.3-next.3
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/errors@1.2.7

## @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7

## @backstage/plugin-auth-backend-module-bitbucket-provider@0.3.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2

## @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.2.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2

## @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.4.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-auth-backend-module-gcp-iap-provider@0.4.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-auth-backend-module-github-provider@0.3.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2

## @backstage/plugin-auth-backend-module-gitlab-provider@0.3.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2

## @backstage/plugin-auth-backend-module-google-provider@0.3.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2

## @backstage/plugin-auth-backend-module-guest-provider@0.2.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7

## @backstage/plugin-auth-backend-module-microsoft-provider@0.3.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2

## @backstage/plugin-auth-backend-module-oauth2-provider@0.4.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2

## @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/errors@1.2.7

## @backstage/plugin-auth-backend-module-oidc-provider@0.4.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-backend@0.24.3-next.3
  - @backstage/plugin-auth-node@0.6.0-next.2

## @backstage/plugin-auth-backend-module-okta-provider@0.2.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2

## @backstage/plugin-auth-backend-module-onelogin-provider@0.3.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2

## @backstage/plugin-auth-backend-module-pinniped-provider@0.3.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1

## @backstage/plugin-auth-backend-module-vmware-cloud-provider@0.5.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/catalog-model@1.7.3

## @backstage/plugin-auth-node@0.6.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-catalog@1.27.0-next.3

### Patch Changes

- 7a15cff: Revert client side paginated catalog table to using built in Material Table toolbar component
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-compat-api@0.3.6-next.3
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/errors@1.2.7
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-react@1.15.2-next.3
  - @backstage/plugin-permission-react@0.4.31-next.0
  - @backstage/plugin-scaffolder-common@1.5.9
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-search-react@1.8.6-next.3

## @backstage/plugin-catalog-backend-module-aws@0.4.8-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-defaults@0.8.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-aws-node@0.1.15
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-kubernetes-common@0.9.3-next.1

## @backstage/plugin-catalog-backend-module-azure@0.3.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-backstage-openapi@0.4.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/backend-openapi-utils@0.5.0-next.3
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.4.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.27
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.3.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-catalog-backend-module-gcp@0.3.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-kubernetes-common@0.9.3-next.1

## @backstage/plugin-catalog-backend-module-gerrit@0.2.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-catalog-backend-module-github@0.7.10-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/plugin-catalog-backend@1.31.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-github-org@0.3.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-catalog-backend-module-github@0.7.10-next.3
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/config@1.3.2

## @backstage/plugin-catalog-backend-module-gitlab@0.6.3-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-defaults@0.8.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-gitlab-org@0.2.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-catalog-backend-module-gitlab@0.6.3-next.3
  - @backstage/plugin-events-node@0.4.8-next.2

## @backstage/plugin-catalog-backend-module-incremental-ingestion@0.6.3-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/plugin-catalog-backend@1.31.0-next.3
  - @backstage/backend-defaults@0.8.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-catalog-backend-module-ldap@0.11.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-logs@0.1.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.31.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2

## @backstage/plugin-catalog-backend-module-msgraph@0.6.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-openapi@0.2.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-puppetdb@0.2.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-catalog-backend-module-unprocessed@0.5.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/plugin-catalog-unprocessed-entities-common@0.0.7
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-catalog-graph@0.4.16-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-compat-api@0.3.6-next.3
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-react@1.15.2-next.3

## @backstage/plugin-catalog-import@0.12.10-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-compat-api@0.3.6-next.3
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-react@1.15.2-next.3

## @backstage/plugin-catalog-react@1.15.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-compat-api@0.3.6-next.3
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/errors@1.2.7
  - @backstage/frontend-test-utils@0.2.6-next.3
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-react@0.4.31-next.0

## @backstage/plugin-devtools@0.1.24-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-compat-api@0.3.6-next.3
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/errors@1.2.7
  - @backstage/plugin-devtools-common@0.1.15
  - @backstage/plugin-permission-react@0.4.31-next.0

## @backstage/plugin-devtools-backend@0.5.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.2
  - @backstage/backend-defaults@0.8.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-devtools-common@0.1.15
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-events-backend@0.4.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/backend-openapi-utils@0.5.0-next.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-events-backend-module-aws-sqs@0.4.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1

## @backstage/plugin-events-backend-module-azure@0.2.17-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2

## @backstage/plugin-events-backend-module-bitbucket-cloud@0.2.17-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2

## @backstage/plugin-events-backend-module-gerrit@0.2.17-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2

## @backstage/plugin-events-backend-module-github@0.2.17-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/config@1.3.2

## @backstage/plugin-events-backend-module-gitlab@0.2.17-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/config@1.3.2

## @backstage/plugin-events-backend-test-utils@0.1.41-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.8-next.2

## @backstage/plugin-events-node@0.4.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-home@0.8.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-compat-api@0.3.6-next.3
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.3
  - @backstage/plugin-home-react@0.1.23-next.1

## @backstage/plugin-kubernetes@0.12.4-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-compat-api@0.3.6-next.3
  - @backstage/catalog-model@1.7.3
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.3
  - @backstage/plugin-kubernetes-common@0.9.3-next.1
  - @backstage/plugin-kubernetes-react@0.5.4-next.2
  - @backstage/plugin-permission-react@0.4.31-next.0

## @backstage/plugin-kubernetes-backend@0.19.3-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/plugin-permission-node@0.8.8-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/plugin-kubernetes-node@0.2.3-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1
  - @backstage/plugin-kubernetes-common@0.9.3-next.1
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-kubernetes-cluster@0.0.22-next.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.3
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.3
  - @backstage/plugin-kubernetes-common@0.9.3-next.1
  - @backstage/plugin-kubernetes-react@0.5.4-next.2
  - @backstage/plugin-permission-react@0.4.31-next.0

## @backstage/plugin-kubernetes-node@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/types@1.2.1
  - @backstage/plugin-kubernetes-common@0.9.3-next.1

## @backstage/plugin-notifications-backend@0.5.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/plugin-notifications-node@0.2.12-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-signals-node@0.1.17-next.2

## @backstage/plugin-notifications-backend-module-email@0.3.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-notifications-node@0.2.12-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1
  - @backstage/plugin-notifications-common@0.0.8

## @backstage/plugin-notifications-node@0.2.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-signals-node@0.1.17-next.2

## @backstage/plugin-org@0.6.36-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-compat-api@0.3.6-next.3
  - @backstage/catalog-model@1.7.3
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-react@1.15.2-next.3

## @backstage/plugin-org-react@0.1.35-next.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.3

## @backstage/plugin-permission-backend@0.5.54-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-permission-backend-module-allow-all-policy@0.2.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-permission-node@0.8.8-next.2

### Patch Changes

- b71f634: Added a new `PermissionRuleset` type that encapsulates a lookup function for permission rules, which can be created by the new `PermissionsRegistryService` via the `getPermissionRuleset` method. The `createConditionTransformer` and `createConditionAuthorizer` functions have been adapted to receive these accessors as arguments, with their older counterparts being deprecated.
- a9621de: Added a new `createPermissionResourceRef` utility that encapsulates the constants and types related to a permission resource types. The `createConditionExports` and `createPermissionRule` functions have also been adapted to accept these references as arguments, deprecating their older counterparts.
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-proxy-backend@0.5.11-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-proxy-node@0.1.1-next.2
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1

## @backstage/plugin-proxy-node@0.1.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2

## @backstage/plugin-scaffolder-backend-module-azure@0.2.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.6-next.2
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.6-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.27

## @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.3.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-defaults@0.8.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-backend-module-gcp@0.2.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-gerrit@0.2.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-gitea@0.2.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-gitlab@0.8.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-notifications@0.1.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-notifications-node@0.2.12-next.2
  - @backstage/plugin-notifications-common@0.0.8

## @backstage/plugin-scaffolder-backend-module-rails@0.5.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-backend-module-sentry@0.2.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-scaffolder-backend-module-yeoman@0.4.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-scaffolder-node-test-utils@0.1.19-next.3
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-node-test-utils@0.1.19-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-test-utils@1.3.0-next.3
  - @backstage/plugin-scaffolder-node@0.7.0-next.2
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-react@1.14.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.3
  - @backstage/plugin-permission-react@0.4.31-next.0
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-search@1.4.23-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-compat-api@0.3.6-next.3
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.3
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-search-react@1.8.6-next.3

## @backstage/plugin-search-backend@1.8.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.2
  - @backstage/backend-defaults@0.8.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-backend-node@1.3.8-next.2
  - @backstage/backend-openapi-utils@0.5.0-next.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-catalog@0.3.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-backend-node@1.3.8-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-elasticsearch@1.6.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-backend-node@1.3.8-next.2
  - @backstage/config@1.3.2
  - @backstage/integration-aws-node@0.1.15
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-explore@0.2.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-backend-node@1.3.8-next.2
  - @backstage/config@1.3.2
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-pg@0.5.41-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-backend-node@1.3.8-next.2
  - @backstage/config@1.3.2
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-stack-overflow-collator@0.3.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-backend-node@1.3.8-next.2
  - @backstage/config@1.3.2
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-techdocs@0.3.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-backend-node@1.3.8-next.2
  - @backstage/plugin-techdocs-node@1.13.0-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-node@1.3.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-react@1.8.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-signals-backend@0.3.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/plugin-signals-node@0.1.17-next.2

## @backstage/plugin-signals-node@0.1.17-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1

## @backstage/plugin-techdocs@1.12.3-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-compat-api@0.3.6-next.3
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/plugin-auth-react@0.1.12-next.1
  - @backstage/plugin-catalog-react@1.15.2-next.3
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-search-react@1.8.6-next.3
  - @backstage/plugin-techdocs-common@0.1.0
  - @backstage/plugin-techdocs-react@1.2.14-next.1

## @backstage/plugin-techdocs-addons-test-utils@1.0.45-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.27.0-next.3
  - @backstage/plugin-techdocs@1.12.3-next.3
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/test-utils@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.3
  - @backstage/plugin-search-react@1.8.6-next.3
  - @backstage/plugin-techdocs-react@1.2.14-next.1

## @backstage/plugin-techdocs-backend@1.11.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-backend-module-techdocs@0.3.6-next.3
  - @backstage/plugin-techdocs-node@1.13.0-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-techdocs-node@1.13.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-aws-node@0.1.15
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-user-settings@0.8.19-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-compat-api@0.3.6-next.3
  - @backstage/catalog-model@1.7.3
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/errors@1.2.7
  - @backstage/theme@0.6.4-next.0
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-react@1.15.2-next.3
  - @backstage/plugin-signals-react@0.0.10-next.0
  - @backstage/plugin-user-settings-common@0.0.1

## @backstage/plugin-user-settings-backend@0.2.30-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-signals-node@0.1.17-next.2
  - @backstage/plugin-user-settings-common@0.0.1

## example-app@0.2.106-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.27.0-next.3
  - @backstage/cli@0.30.0-next.3
  - @backstage/plugin-scaffolder@1.28.0-next.3
  - @backstage/frontend-app-api@0.10.5-next.3
  - @backstage/plugin-api-docs@0.12.4-next.3
  - @backstage/plugin-catalog-graph@0.4.16-next.3
  - @backstage/plugin-catalog-import@0.12.10-next.3
  - @backstage/plugin-org@0.6.36-next.3
  - @backstage/plugin-scaffolder-react@1.14.5-next.3
  - @backstage/plugin-techdocs@1.12.3-next.3
  - @backstage/plugin-user-settings@0.8.19-next.3
  - @backstage/app-defaults@1.5.17-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/plugin-auth-react@0.1.12-next.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-react@1.15.2-next.3
  - @backstage/plugin-catalog-unprocessed-entities@0.2.14-next.1
  - @backstage/plugin-devtools@0.1.24-next.3
  - @backstage/plugin-home@0.8.5-next.3
  - @backstage/plugin-kubernetes@0.12.4-next.3
  - @backstage/plugin-kubernetes-cluster@0.0.22-next.3
  - @backstage/plugin-notifications@0.5.2-next.2
  - @backstage/plugin-permission-react@0.4.31-next.0
  - @backstage/plugin-search@1.4.23-next.3
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-search-react@1.8.6-next.3
  - @backstage/plugin-signals@0.0.16-next.1
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.21-next.1
  - @backstage/plugin-techdocs-react@1.2.14-next.1

## example-app-next@0.0.20-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.27.0-next.3
  - @backstage/cli@0.30.0-next.3
  - @backstage/plugin-scaffolder@1.28.0-next.3
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/frontend-app-api@0.10.5-next.3
  - @backstage/core-compat-api@0.3.6-next.3
  - @backstage/plugin-api-docs@0.12.4-next.3
  - @backstage/plugin-catalog-graph@0.4.16-next.3
  - @backstage/plugin-catalog-import@0.12.10-next.3
  - @backstage/plugin-org@0.6.36-next.3
  - @backstage/plugin-scaffolder-react@1.14.5-next.3
  - @backstage/plugin-techdocs@1.12.3-next.3
  - @backstage/plugin-user-settings@0.8.19-next.3
  - @backstage/app-defaults@1.5.17-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/frontend-defaults@0.1.6-next.3
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/plugin-app@0.1.6-next.3
  - @backstage/plugin-app-visualizer@0.1.16-next.3
  - @backstage/plugin-auth-react@0.1.12-next.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-react@1.15.2-next.3
  - @backstage/plugin-catalog-unprocessed-entities@0.2.14-next.1
  - @backstage/plugin-home@0.8.5-next.3
  - @backstage/plugin-kubernetes@0.12.4-next.3
  - @backstage/plugin-kubernetes-cluster@0.0.22-next.3
  - @backstage/plugin-notifications@0.5.2-next.2
  - @backstage/plugin-permission-react@0.4.31-next.0
  - @backstage/plugin-search@1.4.23-next.3
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-search-react@1.8.6-next.3
  - @backstage/plugin-signals@0.0.16-next.1
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.21-next.1
  - @backstage/plugin-techdocs-react@1.2.14-next.1

## app-next-example-plugin@0.0.20-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/core-components@0.16.4-next.1

## example-backend@0.0.35-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend-module-github@0.6.0-next.2
  - @backstage/plugin-permission-node@0.8.8-next.2
  - @backstage/plugin-catalog-backend@1.31.0-next.3
  - @backstage/plugin-scaffolder-backend@1.30.0-next.3
  - @backstage/backend-defaults@0.8.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-backend@0.24.3-next.3
  - @backstage/plugin-catalog-backend-module-backstage-openapi@0.4.5-next.3
  - @backstage/plugin-catalog-backend-module-openapi@0.2.7-next.3
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.5-next.3
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.5-next.3
  - @backstage/plugin-kubernetes-backend@0.19.3-next.3
  - @backstage/plugin-notifications-backend@0.5.2-next.3
  - @backstage/plugin-search-backend-module-catalog@0.3.1-next.3
  - @backstage/plugin-search-backend-module-techdocs@0.3.6-next.3
  - @backstage/plugin-techdocs-backend@1.11.6-next.3
  - @backstage/plugin-devtools-backend@0.5.2-next.3
  - @backstage/plugin-permission-backend@0.5.54-next.2
  - @backstage/plugin-permission-backend-module-allow-all-policy@0.2.5-next.2
  - @backstage/plugin-search-backend@1.8.2-next.3
  - @backstage/plugin-app-backend@0.4.5-next.2
  - @backstage/plugin-auth-backend-module-github-provider@0.3.0-next.2
  - @backstage/plugin-auth-backend-module-guest-provider@0.2.5-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/plugin-events-backend@0.4.2-next.3
  - @backstage/plugin-proxy-backend@0.5.11-next.2
  - @backstage/plugin-search-backend-module-explore@0.2.8-next.2
  - @backstage/plugin-search-backend-node@1.3.8-next.2
  - @backstage/plugin-signals-backend@0.3.1-next.2
  - @backstage/plugin-scaffolder-backend-module-notifications@0.1.7-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-permission-common@0.8.4

## example-backend-legacy@0.2.107-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.3
  - @backstage/plugin-permission-node@0.8.8-next.2
  - @backstage/plugin-catalog-backend@1.31.0-next.3
  - @backstage/plugin-scaffolder-backend@1.30.0-next.3
  - @backstage/backend-defaults@0.8.0-next.3
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/plugin-auth-backend@0.24.3-next.3
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.5-next.3
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.5-next.3
  - @backstage/plugin-kubernetes-backend@0.19.3-next.3
  - @backstage/plugin-search-backend-module-catalog@0.3.1-next.3
  - @backstage/plugin-search-backend-module-techdocs@0.3.6-next.3
  - @backstage/plugin-techdocs-backend@1.11.6-next.3
  - @backstage/plugin-permission-backend@0.5.54-next.2
  - @backstage/plugin-search-backend@1.8.2-next.3
  - @backstage/plugin-app-backend@0.4.5-next.2
  - @backstage/plugin-auth-node@0.6.0-next.2
  - @backstage/plugin-events-backend@0.4.2-next.3
  - @backstage/plugin-events-node@0.4.8-next.2
  - @backstage/plugin-proxy-backend@0.5.11-next.2
  - @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.6-next.2
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.8.0-next.3
  - @backstage/plugin-scaffolder-backend-module-rails@0.5.6-next.2
  - @backstage/plugin-search-backend-module-elasticsearch@1.6.5-next.2
  - @backstage/plugin-search-backend-module-explore@0.2.8-next.2
  - @backstage/plugin-search-backend-module-pg@0.5.41-next.2
  - @backstage/plugin-search-backend-node@1.3.8-next.2
  - @backstage/plugin-signals-backend@0.3.1-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-signals-node@0.1.17-next.2

## e2e-test@0.2.25-next.3

### Patch Changes

- Updated dependencies
  - @backstage/create-app@0.5.25-next.3
  - @backstage/cli-common@0.1.15
  - @backstage/errors@1.2.7

## @internal/frontend@0.0.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11-next.0

## @internal/scaffolder@0.0.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.3
  - @backstage/plugin-scaffolder-react@1.14.5-next.3

## techdocs-cli-embedded-app@0.2.105-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.27.0-next.3
  - @backstage/cli@0.30.0-next.3
  - @backstage/plugin-techdocs@1.12.3-next.3
  - @backstage/app-defaults@1.5.17-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/core-components@0.16.4-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/test-utils@1.7.5-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/plugin-techdocs-react@1.2.14-next.1

## @internal/plugin-todo-list-backend@1.0.36-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.2
  - @backstage/errors@1.2.7
