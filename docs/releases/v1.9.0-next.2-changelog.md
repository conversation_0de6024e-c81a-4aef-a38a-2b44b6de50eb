# Release v1.9.0-next.2

## @backstage/backend-plugin-api@0.2.0-next.2

### Minor Changes

- 884d749b14: **BREAKING**: All core service references are now exported via a single `coreServices` object. For example, the `loggerServiceRef` is now accessed via `coreServices.logger` instead.

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/core-plugin-api@1.2.0-next.2

### Minor Changes

- 9a1864976a: Added a new `display` property to the `AlertMessage` which can accept the values `permanent` or `transient`.

  Here's a rough example of how to trigger an alert using the new `display` property:

  ```ts
  import { alertApiRef, useApi } from '@backstage/core-plugin-api';

  const ExampleTransient = () => {
    const alertApi = useApi(alertApiRef);
    alertApi.post({
      message: 'Example of Transient Alert',
      severity: 'success',
      display: 'transient',
    });
  };
  ```

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.5-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/version-bridge@1.0.3-next.0

## @backstage/plugin-catalog@1.7.0-next.2

### Minor Changes

- 6ffa47bb0a: Fixes in kind selectors (now `OwnershipCard` works again). `EntityKindPicker` now accepts an optional `allowedKinds` prop, just like `CatalogKindHeader`.
- 462c1d012e: Removed `CatalogKindHeader` from `DefaultCatalogPage`. Deprecated `CatalogKindHeader` in favour of `EntityKindPicker`.

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-react@1.3.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/integration-react@1.1.7-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.2

## @backstage/plugin-catalog-backend@1.6.0-next.2

### Minor Changes

- 3072ebfdd7: The search table also holds the original entity value now and the facets endpoint fetches the filtered entity data from the search table.

### Patch Changes

- c507aee8a2: Ensured typescript type checks in migration files.
- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- eacc8e2b55: Make it possible for entity providers to supply only entity refs, instead of full entities, in `delta` mutation deletions.
- 20a5161f04: Adds MySQL support for the catalog-backend
- Updated dependencies
  - @backstage/plugin-catalog-node@1.3.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/plugin-permission-node@0.7.2-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.2
  - @backstage/plugin-permission-common@0.7.2-next.1
  - @backstage/plugin-scaffolder-common@1.2.3-next.1

## @backstage/plugin-catalog-node@1.3.0-next.2

### Minor Changes

- eacc8e2b55: Make it possible for entity providers to supply only entity refs, instead of full entities, in `delta` mutation deletions.

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.2

## @backstage/plugin-scaffolder-backend@1.9.0-next.2

### Minor Changes

- b32005e98a: Deprecated the `taskWorkers` option in RouterOptions in favor of `concurrentTasksLimit` which sets the limit of concurrent tasks in a single TaskWorker

  TaskWorker can now run multiple (defaults to 10) tasks concurrently using the `concurrentTasksLimit` option available in both `RouterOptions` and `CreateWorkerOptions`.

  To use the option to create a TaskWorker:

  ```diff
  const worker = await TaskWorker.create({
      taskBroker,
      actionRegistry,
      integrations,
      logger,
      workingDirectory,
      additionalTemplateFilters,
  +   concurrentTasksLimit: 10 // (1 to Infinity)
  });
  ```

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/plugin-catalog-node@1.3.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/plugin-auth-node@0.2.8-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-scaffolder-common@1.2.3-next.1

## @backstage/plugin-search-backend@1.2.0-next.2

### Minor Changes

- 29ebc43a0b: numberOfResults is now provided alongside the query result

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-backend-node@1.1.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/plugin-auth-node@0.2.8-next.2
  - @backstage/plugin-permission-node@0.7.2-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-search-backend-module-elasticsearch@1.1.0-next.2

### Minor Changes

- 29ebc43a0b: numberOfResults is now provided alongside the query result

### Patch Changes

- 45eb4d23cf: Fixed a bug that prevented indices from being cleaned up under some circumstances, which could have led to shard exhaustion.
- Updated dependencies
  - @backstage/plugin-search-backend-node@1.1.0-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-search-backend-node@1.1.0-next.2

### Minor Changes

- 29ebc43a0b: numberOfResults is now provided alongside the query result

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-search-common@1.2.0-next.2

### Minor Changes

- 29ebc43a0b: numberOfResults (total number of results for a given query) can now be provided by each search engine and consumed as part of the search results response

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-search-react@1.3.0-next.2

### Minor Changes

- 29ebc43a0b: The `value` of a search analytics event is now set as the total number of search results (when available)

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/version-bridge@1.0.3-next.0

## @backstage/app-defaults@1.0.9-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-app-api@1.2.1-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-permission-react@0.4.8-next.2
  - @backstage/theme@0.2.16

## @backstage/backend-app-api@0.2.4-next.2

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/plugin-permission-node@0.7.2-next.2
  - @backstage/errors@1.1.4-next.1

## @backstage/backend-common@0.17.0-next.2

### Patch Changes

- 98776e638a: Fixed GitlabUrlReader to include api tokens in API calls
- 20a5161f04: Adds MySQL support for the catalog-backend
- 3c1302c07d: Updated dependency `@types/http-errors` to `^2.0.0`.
- 8015ff1258: Tweaked wording to use inclusive terminology
- Updated dependencies
  - @backstage/cli-common@0.1.11-next.0
  - @backstage/config@1.0.5-next.1
  - @backstage/config-loader@1.1.7-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/backend-defaults@0.1.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-app-api@0.2.4-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2

## @backstage/backend-tasks@0.4.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/backend-test-utils@0.1.31-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-app-api@0.2.4-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/cli@0.21.2-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/config@1.0.5-next.1

## @backstage/cli@0.21.2-next.2

### Patch Changes

- 5850ef9b84: Fix webpack dev server issue where it wasn't serving `index.html` from correct endpoint on subsequent requests.
- ee14bab716: Updated dependency `minimatch` to `5.1.1` and switch version range to `^`.
- ed0cf59c59: Updated dependency `@rollup/plugin-commonjs` to `^23.0.0`.
- 16b7c2fccd: Updated dependency `@rollup/plugin-yaml` to `^4.0.0`.
- 086c0bbb45: Updated dependency `@rollup/plugin-json` to `^5.0.0`.
- 8015ff1258: Tweaked wording to use inclusive terminology
- Updated dependencies
  - @backstage/cli-common@0.1.11-next.0
  - @backstage/config@1.0.5-next.1
  - @backstage/config-loader@1.1.7-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/release-manifests@0.0.8-next.0
  - @backstage/types@1.0.2-next.1

## @backstage/cli-common@0.1.11-next.0

### Patch Changes

- 8015ff1258: Tweaked wording to use inclusive terminology

## @backstage/codemods@0.1.42-next.0

### Patch Changes

- Updated dependencies
  - @backstage/cli-common@0.1.11-next.0

## @backstage/config-loader@1.1.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/cli-common@0.1.11-next.0
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/core-app-api@1.2.1-next.2

### Patch Changes

- b4b5b02315: Tweak feature flag registration so that it happens immediately before the first rendering of the app, rather than just after.
- 203271b746: Prevent duplicate feature flag components from rendering in the settings when using <FeatureFlagged /> components
- 8015ff1258: Tweaked wording to use inclusive terminology
- 63310e3987: Apps will now rewrite the `app.baseUrl` configuration to match the current `location.origin`. The `backend.baseUrl` will also be rewritten in the same way when the `app.baseUrl` and `backend.baseUrl` have matching origins. This will reduce the need for separate frontend builds for different environments.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/version-bridge@1.0.3-next.0

## @backstage/core-components@0.12.1-next.2

### Patch Changes

- d2e3bf6737: Made AlertDisplay not crash on undefined messages

- 5fb6d5e92e: Updated dependency `@react-hookz/web` to `^19.0.0`.

- 146378c146: Updated dependency `@react-hookz/web` to `^20.0.0`.

- 8015ff1258: Tweaked wording to use inclusive terminology

- 830687539f: Sync components in @backstage/core-components with the Component Design Guidelines

- 1ae86ab5fb: Added an option to allow the `AlertMessage` to be self-closing. This is done with a new `display` property that is set to `transient` on the `AlertMessage` when triggering a message to the `AlertApi`. The length of time that these transient messages stay open for can be set using the `transientTimeoutMs` prop on the `AlertDisplay` in the `App.tsx`. Here is an example:

  ```diff
    const App = () => (
      <AppProvider>
  +     <AlertDisplay transientTimeoutMs={2500} />
        <OAuthRequestDialog />
        <AppRouter>
          <Root>{routes}</Root>
        </AppRouter>
      </AppProvider>
    );
  ```

  The above example will set the transient timeout to 2500ms from the default of 5000ms

- 16e31e690f: InfoCard - Remove subheader container when there is not a subheader or icon

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/version-bridge@1.0.3-next.0

## @backstage/create-app@0.4.35-next.2

### Patch Changes

- Bumped create-app version.
- Updated dependencies
  - @backstage/cli-common@0.1.11-next.0

## @backstage/dev-utils@1.0.9-next.2

### Patch Changes

- 8015ff1258: Tweaked wording to use inclusive terminology
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-app-api@1.2.1-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/test-utils@1.2.3-next.2
  - @backstage/app-defaults@1.0.9-next.2
  - @backstage/integration-react@1.1.7-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/integration-react@1.1.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @techdocs/cli@1.2.4-next.2

### Patch Changes

- 8015ff1258: Tweaked wording to use inclusive terminology
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/cli-common@0.1.11-next.0
  - @backstage/plugin-techdocs-node@1.4.3-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1

## @backstage/test-utils@1.2.3-next.2

### Patch Changes

- 830687539f: Sync components in @backstage/core-components with the Component Design Guidelines
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-app-api@1.2.1-next.2
  - @backstage/plugin-permission-react@0.4.8-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-adr@0.2.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-react@1.3.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/integration-react@1.1.7-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-adr-common@0.2.4-next.2

## @backstage/plugin-adr-backend@0.2.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/plugin-adr-common@0.2.4-next.2

## @backstage/plugin-adr-common@0.2.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1

## @backstage/plugin-airbrake@0.3.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/dev-utils@1.0.9-next.2
  - @backstage/test-utils@1.2.3-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-airbrake-backend@0.2.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-allure@0.1.28-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-analytics-module-ga@0.1.23-next.2

### Patch Changes

- 9516b0c355: Added support for sending virtual pageviews on `search` events in order to enable
  Site Search functionality in GA. For more information consult [README](https://github.com/backstage/backstage/blob/v1.9.0-next.2/plugins/analytics-module-ga/README.md#enabling-site-search)
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-apache-airflow@0.2.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2

## @backstage/plugin-api-docs@0.8.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.7.0-next.2
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-apollo-explorer@0.1.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-app-backend@0.3.39-next.2

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/config-loader@1.1.7-next.2
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-auth-backend@0.17.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/plugin-auth-node@0.2.8-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-auth-node@0.2.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-azure-devops@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-azure-devops-backend@0.3.18-next.2

### Patch Changes

- eaccf6d628: Updated installation documentation
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-azure-sites@0.1.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-sites-common@0.1.0

## @backstage/plugin-azure-sites-backend@0.1.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-azure-sites-common@0.1.0

## @backstage/plugin-badges@0.2.36-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-badges-backend@0.1.33-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-bazaar@0.2.1-next.2

### Patch Changes

- 312962da30: Add `title` as optional parameter to `BazaarOverviewCard`
- Updated dependencies
  - @backstage/plugin-catalog@1.7.0-next.2
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/cli@0.21.2-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-bazaar-backend@0.2.2-next.2

### Patch Changes

- c507aee8a2: Ensured typescript type checks in migration files.
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-test-utils@0.1.31-next.2
  - @backstage/plugin-auth-node@0.2.8-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-bitrise@0.1.39-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog-backend-module-aws@0.1.12-next.2

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/plugin-catalog-node@1.3.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-catalog-backend-module-azure@0.1.10-next.2

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/plugin-catalog-node@1.3.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-catalog-backend-module-bitbucket@0.2.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.2-next.1

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.1.6-next.2

### Patch Changes

- ce08e1798e: Refresh (potentially) updated catalog files on `repo:push` more efficiently.
- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/plugin-catalog-node@1.3.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.2-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.2
  - @backstage/plugin-events-node@0.2.0-next.2

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.1.4-next.2

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/plugin-catalog-node@1.3.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1

## @backstage/plugin-catalog-backend-module-gerrit@0.1.7-next.2

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/plugin-catalog-node@1.3.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1

## @backstage/plugin-catalog-backend-module-github@0.2.2-next.2

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/plugin-catalog-node@1.3.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.2
  - @backstage/plugin-events-node@0.2.0-next.2

## @backstage/plugin-catalog-backend-module-gitlab@0.1.10-next.2

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/plugin-catalog-node@1.3.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-catalog-backend-module-incremental-ingestion@0.1.0-next.1

### Patch Changes

- c507aee8a2: Ensured typescript type checks in migration files.
- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- 61d4efe978: Make incremental providers more resilient to failures
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/plugin-catalog-node@1.3.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/backend-test-utils@0.1.31-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-catalog-backend-module-ldap@0.5.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-catalog-backend-module-msgraph@0.4.5-next.2

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/plugin-catalog-node@1.3.0-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-catalog-backend-module-openapi@0.1.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/plugin-catalog-node@1.3.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-catalog-common@1.0.9-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-catalog-graph@0.2.24-next.2

### Patch Changes

- 454f2e90db: Set the default `maxDepth` prop for `EntityRelationsGraph` to a smaller value to provide better readability.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog-import@0.9.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/integration-react@1.1.7-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.2

## @backstage/plugin-catalog-react@1.2.2-next.2

### Patch Changes

- 6ffa47bb0a: Cleanup and small fixes for the kind selector
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-permission-react@0.4.8-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/version-bridge@1.0.3-next.0
  - @backstage/plugin-catalog-common@1.0.9-next.2
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-cicd-statistics@0.1.14-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1

## @backstage/plugin-cicd-statistics-module-gitlab@0.1.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/plugin-cicd-statistics@0.1.14-next.2
  - @backstage/catalog-model@1.1.4-next.1

## @backstage/plugin-circleci@0.3.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-cloudbuild@0.3.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-code-climate@0.1.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage@0.2.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage-backend@0.2.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1

## @backstage/plugin-codescene@0.1.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-config-schema@0.1.35-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-cost-insights@0.12.1-next.2

### Patch Changes

- 5960d0902e: Internal refactor to avoid usage of deprecated symbols
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/test-utils@1.2.3-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-cost-insights-common@0.1.1

## @backstage/plugin-dynatrace@1.0.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-events-backend@0.2.0-next.2

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-events-node@0.2.0-next.2

## @backstage/plugin-events-backend-module-aws-sqs@0.1.1-next.2

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-events-node@0.2.0-next.2

## @backstage/plugin-events-backend-module-azure@0.1.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/plugin-events-node@0.2.0-next.2

## @backstage/plugin-events-backend-module-bitbucket-cloud@0.1.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/plugin-events-node@0.2.0-next.2

## @backstage/plugin-events-backend-module-gerrit@0.1.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/plugin-events-node@0.2.0-next.2

## @backstage/plugin-events-backend-module-github@0.1.1-next.2

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-events-node@0.2.0-next.2

## @backstage/plugin-events-backend-module-gitlab@0.1.1-next.2

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-events-node@0.2.0-next.2

## @backstage/plugin-events-backend-test-utils@0.1.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.2.0-next.2

## @backstage/plugin-events-node@0.2.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0-next.2

## @backstage/plugin-explore@0.3.43-next.2

### Patch Changes

- c8f49ed4d0: Update search links to only have header as linkable text
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-react@1.3.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/plugin-explore-react@0.0.24-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-explore-common@0.0.1-next.0

## @backstage/plugin-explore-backend@0.0.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-explore-common@0.0.1-next.0

## @backstage/plugin-explore-react@0.0.24-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/plugin-explore-common@0.0.1-next.0

## @backstage/plugin-firehydrant@0.1.29-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-fossa@0.2.44-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-gcalendar@0.3.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-gcp-projects@0.3.31-next.2

### Patch Changes

- 5fb6d5e92e: Updated dependency `@react-hookz/web` to `^19.0.0`.
- 146378c146: Updated dependency `@react-hookz/web` to `^20.0.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-git-release-manager@0.3.25-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-actions@0.5.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-deployments@0.1.43-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/integration-react@1.1.7-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-issues@0.2.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-pull-requests-board@0.1.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-gitops-profiles@0.3.30-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-gocd@0.1.18-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-graphiql@0.2.44-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-graphql-backend@0.1.29-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-catalog-graphql@0.3.16-next.1

## @backstage/plugin-home@0.4.28-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/plugin-stack-overflow@0.1.8-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-ilert@0.2.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-jenkins@0.7.11-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-jenkins-common@0.1.11-next.2

## @backstage/plugin-jenkins-backend@0.1.29-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/plugin-auth-node@0.2.8-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-jenkins-common@0.1.11-next.2
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-jenkins-common@0.1.11-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-common@1.0.9-next.2
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-kafka@0.3.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-kafka-backend@0.2.32-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-kubernetes@0.7.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-kubernetes-common@0.4.5-next.1

## @backstage/plugin-kubernetes-backend@0.8.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-test-utils@0.1.31-next.2
  - @backstage/plugin-auth-node@0.2.8-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-kubernetes-common@0.4.5-next.1

## @backstage/plugin-lighthouse@0.3.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic@0.3.30-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic-dashboard@0.2.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-org@0.6.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-org-react@0.1.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-pagerduty@0.5.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop@0.1.10-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop-backend@0.1.10-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-permission-backend@0.5.14-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/plugin-auth-node@0.2.8-next.2
  - @backstage/plugin-permission-node@0.7.2-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-permission-node@0.7.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/plugin-auth-node@0.2.8-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-permission-react@0.4.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-playlist@0.1.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-react@1.3.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/plugin-permission-react@0.4.8-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9-next.2
  - @backstage/plugin-permission-common@0.7.2-next.1
  - @backstage/plugin-playlist-common@0.1.3-next.1

## @backstage/plugin-playlist-backend@0.2.2-next.2

### Patch Changes

- c507aee8a2: Ensured typescript type checks in migration files.
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-test-utils@0.1.31-next.2
  - @backstage/plugin-auth-node@0.2.8-next.2
  - @backstage/plugin-permission-node@0.7.2-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1
  - @backstage/plugin-playlist-common@0.1.3-next.1

## @backstage/plugin-proxy-backend@0.2.33-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-rollbar@0.4.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-rollbar-backend@0.1.36-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-scaffolder@1.9.0-next.2

### Patch Changes

- 5fb6d5e92e: Updated dependency `@react-hookz/web` to `^19.0.0`.
- 146378c146: Updated dependency `@react-hookz/web` to `^20.0.0`.
- 9b606366bf: Bump `json-schema-library` to version `^7.3.9` which does not pull in the `gson-pointer` library
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/integration-react@1.1.7-next.2
  - @backstage/plugin-permission-react@0.4.8-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.2
  - @backstage/plugin-scaffolder-common@1.2.3-next.1

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.2.14-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.9.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-scaffolder-backend-module-rails@0.4.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.9.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-scaffolder-backend-module-yeoman@0.2.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.9.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-search@1.0.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-react@1.3.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/version-bridge@1.0.3-next.0

## @backstage/plugin-search-backend-module-pg@0.4.3-next.2

### Patch Changes

- c507aee8a2: Ensured typescript type checks in migration files.
- Updated dependencies
  - @backstage/plugin-search-backend-node@1.1.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-sentry@0.4.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-shortcuts@0.3.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-sonarqube@0.5.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-sonarqube-backend@0.1.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-splunk-on-call@0.4.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-stack-overflow@0.1.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/plugin-home@0.4.28-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-stack-overflow-backend@0.1.8-next.2

### Patch Changes

- fd0ca6f447: Added option to supply API Access Token. This is required in addition to an API key when trying to access the data for a private Stack Overflow Team.
- Updated dependencies
  - @backstage/cli@0.21.2-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-tech-insights@0.3.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-tech-insights-common@0.2.9-next.1

## @backstage/plugin-tech-insights-backend@0.5.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/plugin-tech-insights-node@0.3.7-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-tech-insights-common@0.2.9-next.1

## @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.23-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/plugin-tech-insights-node@0.3.7-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-tech-insights-common@0.2.9-next.1

## @backstage/plugin-tech-insights-node@0.3.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-tech-insights-common@0.2.9-next.1

## @backstage/plugin-tech-radar@0.5.19-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs@1.4.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-react@1.3.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/integration-react@1.1.7-next.2
  - @backstage/plugin-techdocs-react@1.0.7-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-addons-test-utils@1.0.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.7.0-next.2
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-react@1.3.0-next.2
  - @backstage/core-app-api@1.2.1-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/test-utils@1.2.3-next.2
  - @backstage/integration-react@1.1.7-next.2
  - @backstage/plugin-techdocs@1.4.1-next.2
  - @backstage/plugin-techdocs-react@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-backend@1.4.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/plugin-techdocs-node@1.4.3-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.2
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-techdocs-module-addons-contrib@1.0.7-next.2

### Patch Changes

- 5fb6d5e92e: Updated dependency `@react-hookz/web` to `^19.0.0`.
- 146378c146: Updated dependency `@react-hookz/web` to `^20.0.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/integration-react@1.1.7-next.2
  - @backstage/plugin-techdocs-react@1.0.7-next.2
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-node@1.4.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1

## @backstage/plugin-techdocs-react@1.0.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/version-bridge@1.0.3-next.0

## @backstage/plugin-todo@0.2.14-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-todo-backend@0.1.36-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1

## @backstage/plugin-user-settings@0.6.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-app-api@1.2.1-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-user-settings-backend@0.1.3-next.2

### Patch Changes

- c507aee8a2: Ensured typescript type checks in migration files.
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/plugin-auth-node@0.2.8-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-vault@0.1.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-vault-backend@0.2.5-next.2

### Patch Changes

- 568ae02463: Added (optional) config `vault.publicUrl` as alternative to `vault.baseUrl` for `editUrl` and `showUrl` in case `vault.baseUrl` is internal
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-test-utils@0.1.31-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-xcmetrics@0.2.32-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## example-app@0.2.78-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/plugin-search-react@1.3.0-next.2
  - @backstage/plugin-explore@0.3.43-next.2
  - @backstage/cli@0.21.2-next.2
  - @backstage/core-app-api@1.2.1-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2
  - @backstage/plugin-cost-insights@0.12.1-next.2
  - @backstage/plugin-gcp-projects@0.3.31-next.2
  - @backstage/plugin-scaffolder@1.9.0-next.2
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.7-next.2
  - @backstage/plugin-catalog-graph@0.2.24-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/plugin-api-docs@0.8.12-next.2
  - @internal/plugin-catalog-customized@0.0.5-next.2
  - @backstage/app-defaults@1.0.9-next.2
  - @backstage/integration-react@1.1.7-next.2
  - @backstage/plugin-airbrake@0.3.12-next.2
  - @backstage/plugin-apache-airflow@0.2.5-next.2
  - @backstage/plugin-azure-devops@0.2.3-next.2
  - @backstage/plugin-azure-sites@0.1.1-next.2
  - @backstage/plugin-badges@0.2.36-next.2
  - @backstage/plugin-catalog-import@0.9.2-next.2
  - @backstage/plugin-circleci@0.3.12-next.2
  - @backstage/plugin-cloudbuild@0.3.12-next.2
  - @backstage/plugin-code-coverage@0.2.5-next.2
  - @backstage/plugin-dynatrace@1.0.2-next.2
  - @backstage/plugin-gcalendar@0.3.8-next.2
  - @backstage/plugin-github-actions@0.5.12-next.2
  - @backstage/plugin-gocd@0.1.18-next.2
  - @backstage/plugin-graphiql@0.2.44-next.2
  - @backstage/plugin-home@0.4.28-next.2
  - @backstage/plugin-jenkins@0.7.11-next.2
  - @backstage/plugin-kafka@0.3.12-next.2
  - @backstage/plugin-kubernetes@0.7.5-next.2
  - @backstage/plugin-lighthouse@0.3.12-next.2
  - @backstage/plugin-newrelic@0.3.30-next.2
  - @backstage/plugin-newrelic-dashboard@0.2.5-next.2
  - @backstage/plugin-org@0.6.2-next.2
  - @backstage/plugin-pagerduty@0.5.5-next.2
  - @backstage/plugin-permission-react@0.4.8-next.2
  - @backstage/plugin-playlist@0.1.3-next.2
  - @backstage/plugin-rollbar@0.4.12-next.2
  - @backstage/plugin-search@1.0.5-next.2
  - @backstage/plugin-sentry@0.4.5-next.2
  - @backstage/plugin-shortcuts@0.3.4-next.2
  - @backstage/plugin-stack-overflow@0.1.8-next.2
  - @backstage/plugin-tech-insights@0.3.4-next.2
  - @backstage/plugin-tech-radar@0.5.19-next.2
  - @backstage/plugin-techdocs@1.4.1-next.2
  - @backstage/plugin-techdocs-react@1.0.7-next.2
  - @backstage/plugin-todo@0.2.14-next.2
  - @backstage/plugin-user-settings@0.6.0-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9-next.2

## example-backend@0.2.78-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-azure-devops-backend@0.3.18-next.2
  - @backstage/plugin-search-backend@1.2.0-next.2
  - @backstage/plugin-search-backend-module-elasticsearch@1.1.0-next.2
  - @backstage/plugin-search-backend-node@1.1.0-next.2
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/plugin-playlist-backend@0.2.2-next.2
  - @backstage/plugin-search-backend-module-pg@0.4.3-next.2
  - @backstage/plugin-app-backend@0.3.39-next.2
  - @backstage/plugin-catalog-node@1.3.0-next.2
  - @backstage/plugin-events-backend@0.2.0-next.2
  - @backstage/plugin-scaffolder-backend@1.9.0-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - example-app@0.2.78-next.2
  - @backstage/plugin-techdocs-backend@1.4.2-next.2
  - @backstage/plugin-scaffolder-backend-module-rails@0.4.7-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/plugin-auth-backend@0.17.2-next.2
  - @backstage/plugin-auth-node@0.2.8-next.2
  - @backstage/plugin-azure-sites-backend@0.1.1-next.2
  - @backstage/plugin-badges-backend@0.1.33-next.2
  - @backstage/plugin-code-coverage-backend@0.2.5-next.2
  - @backstage/plugin-explore-backend@0.0.1-next.1
  - @backstage/plugin-graphql-backend@0.1.29-next.2
  - @backstage/plugin-jenkins-backend@0.1.29-next.2
  - @backstage/plugin-kafka-backend@0.2.32-next.2
  - @backstage/plugin-kubernetes-backend@0.8.1-next.2
  - @backstage/plugin-permission-backend@0.5.14-next.2
  - @backstage/plugin-permission-node@0.7.2-next.2
  - @backstage/plugin-proxy-backend@0.2.33-next.2
  - @backstage/plugin-rollbar-backend@0.1.36-next.2
  - @backstage/plugin-tech-insights-backend@0.5.5-next.2
  - @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.23-next.2
  - @backstage/plugin-tech-insights-node@0.3.7-next.2
  - @backstage/plugin-todo-backend@0.1.36-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/plugin-events-node@0.2.0-next.2
  - @backstage/plugin-permission-common@0.7.2-next.1

## example-backend-next@0.0.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/plugin-app-backend@0.3.39-next.2
  - @backstage/plugin-scaffolder-backend@1.9.0-next.2
  - @backstage/backend-defaults@0.1.4-next.2

## techdocs-cli-embedded-app@0.2.77-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.7.0-next.2
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/cli@0.21.2-next.2
  - @backstage/core-app-api@1.2.1-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/test-utils@1.2.3-next.2
  - @backstage/app-defaults@1.0.9-next.2
  - @backstage/integration-react@1.1.7-next.2
  - @backstage/plugin-techdocs@1.4.1-next.2
  - @backstage/plugin-techdocs-react@1.0.7-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/theme@0.2.16

## @internal/plugin-catalog-customized@0.0.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.7.0-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.2

## @internal/plugin-todo-list@1.0.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/core-components@0.12.1-next.2
  - @backstage/theme@0.2.16

## @internal/plugin-todo-list-backend@1.0.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/plugin-auth-node@0.2.8-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
