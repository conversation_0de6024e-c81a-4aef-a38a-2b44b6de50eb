# Release v1.35.0-next.2

Upgrade Helper: [https://backstage.github.io/upgrade-helper/?to=1.35.0-next.2](https://backstage.github.io/upgrade-helper/?to=1.35.0-next.2)

## @backstage/app-defaults@1.5.16-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/core-app-api@1.15.4-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/plugin-permission-react@0.4.30-next.0
  - @backstage/theme@0.6.3

## @backstage/backend-app-api@1.1.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/config-loader@1.9.5-next.1
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-permission-node@0.8.7-next.1
  - @backstage/cli-common@0.1.15

## @backstage/backend-defaults@0.7.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-app-api@1.1.1-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/cli-node@0.2.12-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/config-loader@1.9.5-next.1
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-events-node@0.4.7-next.1
  - @backstage/integration-aws-node@0.1.15-next.0
  - @backstage/plugin-permission-node@0.8.7-next.1
  - @backstage/backend-dev-utils@0.1.5
  - @backstage/cli-common@0.1.15
  - @backstage/integration@1.16.1-next.0

## @backstage/backend-dynamic-feature-service@0.5.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-app-api@1.1.1-next.1
  - @backstage/backend-defaults@0.7.0-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/cli-node@0.2.12-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/config-loader@1.9.5-next.1
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-catalog-backend@1.30.0-next.1
  - @backstage/plugin-events-backend@0.4.1-next.1
  - @backstage/plugin-events-node@0.4.7-next.1
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/plugin-scaffolder-node@0.6.3-next.1
  - @backstage/plugin-search-common@1.2.17-next.0
  - @backstage/plugin-permission-node@0.8.7-next.1
  - @backstage/plugin-search-backend-node@1.3.7-next.1
  - @backstage/plugin-app-node@0.1.29-next.1
  - @backstage/cli-common@0.1.15

## @backstage/backend-openapi-utils@0.4.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/errors@1.2.7-next.0

## @backstage/backend-plugin-api@1.1.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/cli-common@0.1.15

## @backstage/backend-test-utils@1.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-app-api@1.1.1-next.1
  - @backstage/backend-defaults@0.7.0-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-events-node@0.4.7-next.1

## @backstage/catalog-client@1.9.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/errors@1.2.7-next.0

## @backstage/catalog-model@1.7.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/errors@1.2.7-next.0

## @backstage/cli@0.29.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/cli-node@0.2.12-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/config-loader@1.9.5-next.1
  - @backstage/errors@1.2.7-next.0
  - @backstage/release-manifests@0.0.12
  - @backstage/cli-common@0.1.15
  - @backstage/eslint-plugin@0.1.10
  - @backstage/integration@1.16.1-next.0

## @backstage/cli-node@0.2.12-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/cli-common@0.1.15

## @backstage/config@1.3.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/errors@1.2.7-next.0

## @backstage/config-loader@1.9.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/cli-common@0.1.15

## @backstage/core-app-api@1.15.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/version-bridge@1.0.10

## @backstage/core-compat-api@0.3.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/version-bridge@1.0.10

## @backstage/core-components@0.16.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/theme@0.6.3
  - @backstage/version-bridge@1.0.10

## @backstage/core-plugin-api@1.10.3-next.0

### Patch Changes

- b40eb41: Move `Expand` and `ExpandRecursive` to `@backstage/types`
- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/version-bridge@1.0.10

## @backstage/create-app@0.5.24-next.2

### Patch Changes

- Bumped create-app version.
- Updated dependencies
  - @backstage/cli-common@0.1.15

## @backstage/dev-utils@1.1.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/app-defaults@1.5.16-next.0
  - @backstage/core-app-api@1.15.4-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/integration-react@1.2.3-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/theme@0.6.3

## @backstage/errors@1.2.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0

## @backstage/frontend-app-api@0.10.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/frontend-defaults@0.1.5-next.0
  - @backstage/core-app-api@1.15.4-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/version-bridge@1.0.10

## @backstage/frontend-defaults@0.1.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/frontend-app-api@0.10.4-next.0
  - @backstage/plugin-app@0.1.5-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0

## @backstage/frontend-plugin-api@0.9.4-next.0

### Patch Changes

- b40eb41: Move `Expand` and `ExpandRecursive` to `@backstage/types`
- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/version-bridge@1.0.10

## @backstage/frontend-test-utils@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/frontend-app-api@0.10.4-next.0
  - @backstage/plugin-app@0.1.5-next.0
  - @backstage/test-utils@1.7.4-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/version-bridge@1.0.10

## @backstage/integration@1.16.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0

## @backstage/integration-aws-node@0.1.15-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0

## @backstage/integration-react@1.2.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/integration@1.16.1-next.0

## @backstage/repo-tools@0.12.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/cli-node@0.2.12-next.0
  - @backstage/config-loader@1.9.5-next.1
  - @backstage/errors@1.2.7-next.0
  - @backstage/cli-common@0.1.15

## @techdocs/cli@1.8.25-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.7.0-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-techdocs-node@1.12.16-next.1
  - @backstage/cli-common@0.1.15

## @backstage/test-utils@1.7.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/core-app-api@1.15.4-next.0
  - @backstage/plugin-permission-react@0.4.30-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/theme@0.6.3

## @backstage/types@1.2.1-next.0

### Patch Changes

- b40eb41: Move `Expand` and `ExpandRecursive` to `@backstage/types`

## @backstage/plugin-api-docs@0.12.3-next.1

### Patch Changes

- dcf6e72: Fix typo in default path of api docs definition route
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/plugin-catalog@1.26.1-next.1
  - @backstage/core-compat-api@0.3.5-next.0
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/core-components@0.16.3-next.0
  - @backstage/plugin-permission-react@0.4.30-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0

## @backstage/plugin-app@0.1.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/integration-react@1.2.3-next.0
  - @backstage/plugin-permission-react@0.4.30-next.0
  - @backstage/theme@0.6.3

## @backstage/plugin-app-backend@0.4.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/config-loader@1.9.5-next.1
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-app-node@0.1.29-next.1

## @backstage/plugin-app-node@0.1.29-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config-loader@1.9.5-next.1

## @backstage/plugin-app-visualizer@0.1.15-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/core-components@0.16.3-next.0

## @backstage/plugin-auth-backend@0.24.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.3.4-next.1
  - @backstage/plugin-auth-backend-module-gcp-iap-provider@0.3.4-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-auth-backend-module-atlassian-provider@0.3.4-next.1
  - @backstage/plugin-auth-backend-module-auth0-provider@0.1.4-next.1
  - @backstage/plugin-auth-backend-module-bitbucket-provider@0.2.4-next.1
  - @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.1.4-next.1
  - @backstage/plugin-auth-backend-module-github-provider@0.2.4-next.1
  - @backstage/plugin-auth-backend-module-gitlab-provider@0.2.4-next.1
  - @backstage/plugin-auth-backend-module-microsoft-provider@0.2.4-next.1
  - @backstage/plugin-auth-backend-module-oauth2-provider@0.3.4-next.1
  - @backstage/plugin-auth-backend-module-oidc-provider@0.3.4-next.1
  - @backstage/plugin-auth-backend-module-okta-provider@0.1.4-next.1
  - @backstage/plugin-auth-backend-module-onelogin-provider@0.2.4-next.1
  - @backstage/plugin-auth-backend-module-aws-alb-provider@0.3.2-next.1
  - @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.4-next.1
  - @backstage/plugin-auth-backend-module-google-provider@0.2.4-next.1
  - @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.4-next.1
  - @backstage/catalog-client@1.9.1-next.0

## @backstage/plugin-auth-backend-module-atlassian-provider@0.3.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-auth0-provider@0.1.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-aws-alb-provider@0.3.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-backend@0.24.2-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-bitbucket-provider@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.1.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.3.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-gcp-iap-provider@0.3.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-github-provider@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-gitlab-provider@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-google-provider@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-guest-provider@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-microsoft-provider@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-oauth2-provider@0.3.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-oidc-provider@0.3.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-auth-backend@0.24.2-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-okta-provider@0.1.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-onelogin-provider@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-pinniped-provider@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-backend-module-vmware-cloud-provider@0.4.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1

## @backstage/plugin-auth-node@0.5.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/catalog-client@1.9.1-next.0

## @backstage/plugin-auth-react@0.1.11-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/errors@1.2.7-next.0

## @backstage/plugin-bitbucket-cloud-common@0.2.27-next.0

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-catalog@1.26.1-next.1

### Patch Changes

- 208e53b: Fixing spelling mistake in translation
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/core-compat-api@0.3.5-next.0
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/plugin-search-react@1.8.5-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/integration-react@1.2.3-next.0
  - @backstage/plugin-permission-react@0.4.30-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-scaffolder-common@1.5.9-next.0
  - @backstage/plugin-search-common@1.2.17-next.0
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0

## @backstage/plugin-catalog-backend@1.30.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-openapi-utils@0.4.1-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-events-node@0.4.7-next.1
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/plugin-permission-node@0.8.7-next.1
  - @backstage/plugin-search-backend-module-catalog@0.3.0-next.1
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-aws@0.4.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.7.0-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-kubernetes-common@0.9.2-next.0
  - @backstage/integration-aws-node@0.1.15-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-azure@0.3.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-catalog-common@1.1.3-next.0
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-backstage-openapi@0.4.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-openapi-utils@0.4.1-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.4.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-events-node@0.4.7-next.1
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0
  - @backstage/integration@1.16.1-next.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.27-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.3.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-gcp@0.3.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-kubernetes-common@0.9.2-next.0

## @backstage/plugin-catalog-backend-module-gerrit@0.2.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-github@0.7.9-next.1

### Patch Changes

- 4ab00e4: Fixes an issue in `GithubMultiOrgEntityProvider` that caused an error when processing teams without a parent.
- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-catalog-backend@1.30.0-next.1
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-events-node@0.4.7-next.1
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-github-org@0.3.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend-module-github@0.7.9-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-events-node@0.4.7-next.1

## @backstage/plugin-catalog-backend-module-gitlab@0.6.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.7.0-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-events-node@0.4.7-next.1
  - @backstage/plugin-catalog-common@1.1.3-next.0
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-gitlab-org@0.2.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-events-node@0.4.7-next.1
  - @backstage/plugin-catalog-backend-module-gitlab@0.6.2-next.1

## @backstage/plugin-catalog-backend-module-incremental-ingestion@0.6.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-defaults@0.7.0-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-catalog-backend@1.30.0-next.1
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-events-node@0.4.7-next.1
  - @backstage/plugin-permission-common@0.8.4-next.0

## @backstage/plugin-catalog-backend-module-ldap@0.11.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-catalog-common@1.1.3-next.0

## @backstage/plugin-catalog-backend-module-logs@0.1.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-backend@1.30.0-next.1
  - @backstage/plugin-events-node@0.4.7-next.1

## @backstage/plugin-catalog-backend-module-msgraph@0.6.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-catalog-common@1.1.3-next.0

## @backstage/plugin-catalog-backend-module-openapi@0.2.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-catalog-common@1.1.3-next.0
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-puppetdb@0.2.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1

## @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-scaffolder-common@1.5.9-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0

## @backstage/plugin-catalog-backend-module-unprocessed@0.5.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/plugin-catalog-unprocessed-entities-common@0.0.7-next.0

## @backstage/plugin-catalog-common@1.1.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/plugin-search-common@1.2.17-next.0

## @backstage/plugin-catalog-graph@0.4.15-next.1

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/core-compat-api@0.3.5-next.0
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/core-components@0.16.3-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/catalog-client@1.9.1-next.0

## @backstage/plugin-catalog-import@0.12.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/core-compat-api@0.3.5-next.0
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/core-components@0.16.3-next.0
  - @backstage/integration-react@1.2.3-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-catalog-node@1.15.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/plugin-permission-node@0.8.7-next.1
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0

## @backstage/plugin-catalog-react@1.15.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/core-compat-api@0.3.5-next.0
  - @backstage/frontend-test-utils@0.2.5-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/integration-react@1.2.3-next.0
  - @backstage/plugin-permission-react@0.4.30-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0
  - @backstage/version-bridge@1.0.10

## @backstage/plugin-catalog-unprocessed-entities@0.2.13-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/errors@1.2.7-next.0

## @backstage/plugin-catalog-unprocessed-entities-common@0.0.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.8.4-next.0

## @backstage/plugin-config-schema@0.1.64-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/errors@1.2.7-next.0

## @backstage/plugin-devtools@0.1.23-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/core-compat-api@0.3.5-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/plugin-permission-react@0.4.30-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-devtools-common@0.1.15-next.0

## @backstage/plugin-devtools-backend@0.5.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-defaults@0.7.0-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/config-loader@1.9.5-next.1
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-devtools-common@0.1.15-next.0
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/plugin-permission-node@0.8.7-next.1
  - @backstage/cli-common@0.1.15

## @backstage/plugin-devtools-common@0.1.15-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/plugin-permission-common@0.8.4-next.0

## @backstage/plugin-events-backend@0.4.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-openapi-utils@0.4.1-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-events-node@0.4.7-next.1

## @backstage/plugin-events-backend-module-aws-sqs@0.4.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-events-node@0.4.7-next.1

## @backstage/plugin-events-backend-module-azure@0.2.16-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-events-node@0.4.7-next.1

## @backstage/plugin-events-backend-module-bitbucket-cloud@0.2.16-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-events-node@0.4.7-next.1

## @backstage/plugin-events-backend-module-gerrit@0.2.16-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-events-node@0.4.7-next.1

## @backstage/plugin-events-backend-module-github@0.2.16-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-events-node@0.4.7-next.1

## @backstage/plugin-events-backend-module-gitlab@0.2.16-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-events-node@0.4.7-next.1

## @backstage/plugin-events-backend-test-utils@0.1.40-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.7-next.1

## @backstage/plugin-events-node@0.4.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/errors@1.2.7-next.0

## @backstage/plugin-home@0.8.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/core-compat-api@0.3.5-next.0
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/core-app-api@1.15.4-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/plugin-home-react@0.1.22-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/theme@0.6.3

## @backstage/plugin-home-react@0.1.22-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/core-components@0.16.3-next.0

## @backstage/plugin-kubernetes@0.12.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/core-compat-api@0.3.5-next.0
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/core-components@0.16.3-next.0
  - @backstage/plugin-kubernetes-react@0.5.3-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/plugin-kubernetes-common@0.9.2-next.0

## @backstage/plugin-kubernetes-backend@0.19.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-kubernetes-common@0.9.2-next.0
  - @backstage/plugin-kubernetes-node@0.2.2-next.1
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/integration-aws-node@0.1.15-next.0
  - @backstage/plugin-permission-node@0.8.7-next.1
  - @backstage/catalog-client@1.9.1-next.0

## @backstage/plugin-kubernetes-cluster@0.0.21-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/core-components@0.16.3-next.0
  - @backstage/plugin-kubernetes-react@0.5.3-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/plugin-kubernetes-common@0.9.2-next.0

## @backstage/plugin-kubernetes-common@0.9.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/plugin-permission-common@0.8.4-next.0

## @backstage/plugin-kubernetes-node@0.2.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/plugin-kubernetes-common@0.9.2-next.0

## @backstage/plugin-kubernetes-react@0.5.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-kubernetes-common@0.9.2-next.0

## @backstage/plugin-notifications@0.5.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/plugin-signals-react@0.0.9-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/theme@0.6.3
  - @backstage/plugin-notifications-common@0.0.8-next.0

## @backstage/plugin-notifications-backend@0.5.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-events-node@0.4.7-next.1
  - @backstage/plugin-signals-node@0.1.16-next.1
  - @backstage/plugin-notifications-node@0.2.11-next.1
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/plugin-notifications-common@0.0.8-next.0

## @backstage/plugin-notifications-backend-module-email@0.3.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/integration-aws-node@0.1.15-next.0
  - @backstage/plugin-notifications-node@0.2.11-next.1
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/plugin-notifications-common@0.0.8-next.0

## @backstage/plugin-notifications-common@0.0.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.2-next.0

## @backstage/plugin-notifications-node@0.2.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/plugin-signals-node@0.1.16-next.1
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/plugin-notifications-common@0.0.8-next.0

## @backstage/plugin-org@0.6.35-next.1

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/core-compat-api@0.3.5-next.0
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/core-components@0.16.3-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0

## @backstage/plugin-org-react@0.1.34-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/core-components@0.16.3-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/catalog-client@1.9.1-next.0

## @backstage/plugin-permission-backend@0.5.53-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/plugin-permission-node@0.8.7-next.1

## @backstage/plugin-permission-backend-module-allow-all-policy@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/plugin-permission-node@0.8.7-next.1

## @backstage/plugin-permission-common@0.8.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0

## @backstage/plugin-permission-node@0.8.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-permission-common@0.8.4-next.0

## @backstage/plugin-permission-react@0.4.30-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-permission-common@0.8.4-next.0

## @backstage/plugin-proxy-backend@0.5.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0

## @backstage/plugin-scaffolder@1.27.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-react@1.14.3-next.2
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/core-compat-api@0.3.5-next.0
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/core-components@0.16.3-next.0
  - @backstage/integration-react@1.2.3-next.0
  - @backstage/plugin-permission-react@0.4.30-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-scaffolder-common@1.5.9-next.0
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-scaffolder-backend@1.29.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-defaults@0.7.0-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-events-node@0.4.7-next.1
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/plugin-scaffolder-common@1.5.9-next.0
  - @backstage/plugin-scaffolder-node@0.6.3-next.1
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.7.1-next.1
  - @backstage/plugin-permission-node@0.8.7-next.1
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.4-next.1
  - @backstage/plugin-scaffolder-backend-module-azure@0.2.5-next.1
  - @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.6-next.1
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.5-next.1
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.5-next.1
  - @backstage/plugin-scaffolder-backend-module-gerrit@0.2.5-next.1
  - @backstage/plugin-scaffolder-backend-module-gitea@0.2.5-next.1
  - @backstage/plugin-scaffolder-backend-module-github@0.5.5-next.2
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/integration@1.16.1-next.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.27-next.0

## @backstage/plugin-scaffolder-backend-module-azure@0.2.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-scaffolder-node@0.6.3-next.1
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-scaffolder-node@0.6.3-next.1
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.5-next.1
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.5-next.1
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-scaffolder-node@0.6.3-next.1
  - @backstage/integration@1.16.1-next.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.27-next.0

## @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-scaffolder-node@0.6.3-next.1
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-scaffolder-node@0.6.3-next.1
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.3.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-defaults@0.7.0-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-scaffolder-node@0.6.3-next.1
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-scaffolder-backend-module-gcp@0.2.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-scaffolder-node@0.6.3-next.1
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-scaffolder-backend-module-gerrit@0.2.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-scaffolder-node@0.6.3-next.1
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-scaffolder-backend-module-gitea@0.2.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-scaffolder-node@0.6.3-next.1
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-scaffolder-backend-module-github@0.5.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-scaffolder-node@0.6.3-next.1
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-scaffolder-backend-module-gitlab@0.7.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-scaffolder-node@0.6.3-next.1
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-scaffolder-backend-module-notifications@0.1.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-scaffolder-node@0.6.3-next.1
  - @backstage/plugin-notifications-node@0.2.11-next.1
  - @backstage/plugin-notifications-common@0.0.8-next.0

## @backstage/plugin-scaffolder-backend-module-rails@0.5.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-scaffolder-node@0.6.3-next.1
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-scaffolder-backend-module-sentry@0.2.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-scaffolder-node@0.6.3-next.1

## @backstage/plugin-scaffolder-backend-module-yeoman@0.4.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/plugin-scaffolder-node@0.6.3-next.1
  - @backstage/plugin-scaffolder-node-test-utils@0.1.18-next.1

## @backstage/plugin-scaffolder-common@1.5.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/plugin-permission-common@0.8.4-next.0

## @backstage/plugin-scaffolder-node@0.6.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-scaffolder-common@1.5.9-next.0
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-scaffolder-node-test-utils@0.1.18-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-test-utils@1.2.1-next.1
  - @backstage/plugin-scaffolder-node@0.6.3-next.1

## @backstage/plugin-scaffolder-react@1.14.3-next.2

### Patch Changes

- 91bb99a: Fix field extension validation not working when field is in dependencies in an array field
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/core-components@0.16.3-next.0
  - @backstage/plugin-permission-react@0.4.30-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/plugin-scaffolder-common@1.5.9-next.0
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/theme@0.6.3
  - @backstage/version-bridge@1.0.10

## @backstage/plugin-search@1.4.22-next.1

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/core-compat-api@0.3.5-next.0
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/plugin-search-react@1.8.5-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-search-common@1.2.17-next.0
  - @backstage/version-bridge@1.0.10

## @backstage/plugin-search-backend@1.8.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-defaults@0.7.0-next.1
  - @backstage/backend-openapi-utils@0.4.1-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/plugin-search-common@1.2.17-next.0
  - @backstage/plugin-permission-node@0.8.7-next.1
  - @backstage/plugin-search-backend-node@1.3.7-next.1

## @backstage/plugin-search-backend-module-catalog@0.3.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/plugin-search-common@1.2.17-next.0
  - @backstage/plugin-search-backend-node@1.3.7-next.1
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0

## @backstage/plugin-search-backend-module-elasticsearch@1.6.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-search-common@1.2.17-next.0
  - @backstage/integration-aws-node@0.1.15-next.0
  - @backstage/plugin-search-backend-node@1.3.7-next.1

## @backstage/plugin-search-backend-module-explore@0.2.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-search-common@1.2.17-next.0
  - @backstage/plugin-search-backend-node@1.3.7-next.1

## @backstage/plugin-search-backend-module-pg@0.5.40-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-search-common@1.2.17-next.0
  - @backstage/plugin-search-backend-node@1.3.7-next.1

## @backstage/plugin-search-backend-module-stack-overflow-collator@0.3.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-search-common@1.2.17-next.0
  - @backstage/plugin-search-backend-node@1.3.7-next.1

## @backstage/plugin-search-backend-module-techdocs@0.3.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/plugin-search-common@1.2.17-next.0
  - @backstage/plugin-search-backend-node@1.3.7-next.1
  - @backstage/plugin-techdocs-node@1.12.16-next.1
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0

## @backstage/plugin-search-backend-node@1.3.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/plugin-search-common@1.2.17-next.0

## @backstage/plugin-search-common@1.2.17-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/plugin-permission-common@0.8.4-next.0

## @backstage/plugin-search-react@1.8.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/plugin-search-common@1.2.17-next.0
  - @backstage/theme@0.6.3
  - @backstage/version-bridge@1.0.10

## @backstage/plugin-signals@0.0.15-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/plugin-signals-react@0.0.9-next.0
  - @backstage/theme@0.6.3

## @backstage/plugin-signals-backend@0.3.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-events-node@0.4.7-next.1
  - @backstage/plugin-signals-node@0.1.16-next.1

## @backstage/plugin-signals-node@0.1.16-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-events-node@0.4.7-next.1

## @backstage/plugin-signals-react@0.0.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0

## @backstage/plugin-techdocs@1.12.1-next.1

### Patch Changes

- 3710b35: Allow passing down `withSearch` prop to `EntityTechdocsContent` component since it was `true` by default, now user can use the `EntityTechdocsContent` component _without_ showing the search field on top of the content.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/core-compat-api@0.3.5-next.0
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/plugin-search-react@1.8.5-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/integration-react@1.2.3-next.0
  - @backstage/plugin-auth-react@0.1.11-next.0
  - @backstage/plugin-techdocs-react@1.2.13-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-search-common@1.2.17-next.0
  - @backstage/integration@1.16.1-next.0
  - @backstage/theme@0.6.3
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-techdocs-addons-test-utils@1.0.44-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs@1.12.1-next.1
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/plugin-catalog@1.26.1-next.1
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/plugin-search-react@1.8.5-next.0
  - @backstage/core-app-api@1.15.4-next.0
  - @backstage/integration-react@1.2.3-next.0
  - @backstage/test-utils@1.7.4-next.0
  - @backstage/plugin-techdocs-react@1.2.13-next.0

## @backstage/plugin-techdocs-backend@1.11.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/plugin-search-backend-module-techdocs@0.3.5-next.1
  - @backstage/plugin-techdocs-node@1.12.16-next.1
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0
  - @backstage/integration@1.16.1-next.0
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-techdocs-module-addons-contrib@1.1.20-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/integration-react@1.2.3-next.0
  - @backstage/plugin-techdocs-react@1.2.13-next.0
  - @backstage/integration@1.16.1-next.0

## @backstage/plugin-techdocs-node@1.12.16-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-search-common@1.2.17-next.0
  - @backstage/integration-aws-node@0.1.15-next.0
  - @backstage/integration@1.16.1-next.0
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-techdocs-react@1.2.13-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/version-bridge@1.0.10

## @backstage/plugin-user-settings@0.8.18-next.1

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/core-compat-api@0.3.5-next.0
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/core-app-api@1.15.4-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/plugin-signals-react@0.0.9-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/theme@0.6.3
  - @backstage/plugin-user-settings-common@0.0.1

## @backstage/plugin-user-settings-backend@0.2.29-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.1-next.0
  - @backstage/backend-defaults@0.7.0-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/config@1.3.2-next.0
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-signals-node@0.1.16-next.1
  - @backstage/plugin-user-settings-common@0.0.1

## example-app@0.2.105-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs@1.12.1-next.1
  - @backstage/plugin-scaffolder-react@1.14.3-next.2
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/plugin-catalog@1.26.1-next.1
  - @backstage/plugin-api-docs@0.12.3-next.1
  - @backstage/plugin-scaffolder@1.27.4-next.2
  - @backstage/frontend-app-api@0.10.4-next.0
  - @backstage/plugin-catalog-graph@0.4.15-next.1
  - @backstage/plugin-catalog-import@0.12.9-next.1
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/plugin-devtools@0.1.23-next.0
  - @backstage/plugin-home@0.8.4-next.2
  - @backstage/plugin-kubernetes@0.12.3-next.1
  - @backstage/plugin-org@0.6.35-next.1
  - @backstage/plugin-search@1.4.22-next.1
  - @backstage/plugin-search-react@1.8.5-next.0
  - @backstage/plugin-user-settings@0.8.18-next.1
  - @backstage/app-defaults@1.5.16-next.0
  - @backstage/cli@0.29.5-next.1
  - @backstage/core-app-api@1.15.4-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/integration-react@1.2.3-next.0
  - @backstage/plugin-auth-react@0.1.11-next.0
  - @backstage/plugin-catalog-unprocessed-entities@0.2.13-next.0
  - @backstage/plugin-kubernetes-cluster@0.0.21-next.1
  - @backstage/plugin-notifications@0.5.1-next.0
  - @backstage/plugin-permission-react@0.4.30-next.0
  - @backstage/plugin-signals@0.0.15-next.0
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.20-next.1
  - @backstage/plugin-techdocs-react@1.2.13-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-search-common@1.2.17-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0
  - @backstage/theme@0.6.3

## example-app-next@0.0.19-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs@1.12.1-next.1
  - @backstage/plugin-scaffolder-react@1.14.3-next.2
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/plugin-catalog@1.26.1-next.1
  - @backstage/plugin-api-docs@0.12.3-next.1
  - @backstage/plugin-scaffolder@1.27.4-next.2
  - @backstage/core-compat-api@0.3.5-next.0
  - @backstage/frontend-app-api@0.10.4-next.0
  - @backstage/frontend-defaults@0.1.5-next.0
  - @backstage/plugin-app@0.1.5-next.0
  - @backstage/plugin-app-visualizer@0.1.15-next.0
  - @backstage/plugin-catalog-graph@0.4.15-next.1
  - @backstage/plugin-catalog-import@0.12.9-next.1
  - @backstage/plugin-catalog-react@1.15.1-next.1
  - @backstage/plugin-home@0.8.4-next.2
  - @backstage/plugin-kubernetes@0.12.3-next.1
  - @backstage/plugin-org@0.6.35-next.1
  - @backstage/plugin-search@1.4.22-next.1
  - @backstage/plugin-search-react@1.8.5-next.0
  - @backstage/plugin-user-settings@0.8.18-next.1
  - @backstage/app-defaults@1.5.16-next.0
  - @backstage/cli@0.29.5-next.1
  - @backstage/core-app-api@1.15.4-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/integration-react@1.2.3-next.0
  - @backstage/plugin-auth-react@0.1.11-next.0
  - @backstage/plugin-catalog-unprocessed-entities@0.2.13-next.0
  - @backstage/plugin-kubernetes-cluster@0.0.21-next.1
  - @backstage/plugin-notifications@0.5.1-next.0
  - @backstage/plugin-permission-react@0.4.30-next.0
  - @backstage/plugin-signals@0.0.15-next.0
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.20-next.1
  - @backstage/plugin-techdocs-react@1.2.13-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-search-common@1.2.17-next.0
  - @backstage/plugin-catalog-common@1.1.3-next.0
  - @backstage/theme@0.6.3

## app-next-example-plugin@0.0.19-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/core-components@0.16.3-next.0

## example-backend@0.0.34-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.7.0-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/plugin-app-backend@0.4.4-next.1
  - @backstage/plugin-auth-backend@0.24.2-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-catalog-backend@1.30.0-next.1
  - @backstage/plugin-catalog-backend-module-openapi@0.2.6-next.1
  - @backstage/plugin-devtools-backend@0.5.1-next.1
  - @backstage/plugin-events-backend@0.4.1-next.1
  - @backstage/plugin-kubernetes-backend@0.19.2-next.1
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/plugin-proxy-backend@0.5.10-next.1
  - @backstage/plugin-scaffolder-backend@1.29.0-next.2
  - @backstage/plugin-search-backend@1.8.1-next.1
  - @backstage/plugin-signals-backend@0.3.0-next.2
  - @backstage/plugin-auth-backend-module-github-provider@0.2.4-next.1
  - @backstage/plugin-notifications-backend@0.5.1-next.1
  - @backstage/plugin-permission-backend@0.5.53-next.1
  - @backstage/plugin-permission-node@0.8.7-next.1
  - @backstage/plugin-search-backend-node@1.3.7-next.1
  - @backstage/plugin-techdocs-backend@1.11.5-next.2
  - @backstage/plugin-catalog-backend-module-backstage-openapi@0.4.4-next.1
  - @backstage/plugin-auth-backend-module-guest-provider@0.2.4-next.1
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.4-next.1
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.4-next.1
  - @backstage/plugin-permission-backend-module-allow-all-policy@0.2.4-next.1
  - @backstage/plugin-scaffolder-backend-module-github@0.5.5-next.2
  - @backstage/plugin-scaffolder-backend-module-notifications@0.1.6-next.1
  - @backstage/plugin-search-backend-module-catalog@0.3.0-next.1
  - @backstage/plugin-search-backend-module-explore@0.2.7-next.1
  - @backstage/plugin-search-backend-module-techdocs@0.3.5-next.1

## example-backend-legacy@0.2.106-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.7.0-next.1
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/plugin-app-backend@0.4.4-next.1
  - @backstage/plugin-auth-backend@0.24.2-next.1
  - @backstage/plugin-auth-node@0.5.6-next.1
  - @backstage/plugin-catalog-backend@1.30.0-next.1
  - @backstage/plugin-catalog-node@1.15.1-next.1
  - @backstage/plugin-events-backend@0.4.1-next.1
  - @backstage/plugin-events-node@0.4.7-next.1
  - @backstage/plugin-kubernetes-backend@0.19.2-next.1
  - @backstage/plugin-permission-common@0.8.4-next.0
  - @backstage/plugin-proxy-backend@0.5.10-next.1
  - @backstage/plugin-scaffolder-backend@1.29.0-next.2
  - @backstage/plugin-scaffolder-backend-module-rails@0.5.5-next.1
  - @backstage/plugin-search-backend@1.8.1-next.1
  - @backstage/plugin-signals-backend@0.3.0-next.2
  - @backstage/plugin-signals-node@0.1.16-next.1
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.7.1-next.1
  - @backstage/plugin-permission-backend@0.5.53-next.1
  - @backstage/plugin-permission-node@0.8.7-next.1
  - @backstage/plugin-search-backend-node@1.3.7-next.1
  - @backstage/plugin-techdocs-backend@1.11.5-next.2
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.4-next.1
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.4-next.1
  - @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.5-next.1
  - @backstage/plugin-search-backend-module-catalog@0.3.0-next.1
  - @backstage/plugin-search-backend-module-elasticsearch@1.6.4-next.1
  - @backstage/plugin-search-backend-module-explore@0.2.7-next.1
  - @backstage/plugin-search-backend-module-pg@0.5.40-next.1
  - @backstage/plugin-search-backend-module-techdocs@0.3.5-next.1
  - @backstage/catalog-client@1.9.1-next.0
  - @backstage/integration@1.16.1-next.0

## e2e-test@0.2.24-next.2

### Patch Changes

- Updated dependencies
  - @backstage/create-app@0.5.24-next.2
  - @backstage/errors@1.2.7-next.0
  - @backstage/cli-common@0.1.15

## @internal/frontend@0.0.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.4-next.0
  - @backstage/types@1.2.1-next.0
  - @backstage/version-bridge@1.0.10

## @internal/scaffolder@0.0.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-react@1.14.3-next.2
  - @backstage/frontend-plugin-api@0.9.4-next.0

## techdocs-cli-embedded-app@0.2.104-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs@1.12.1-next.1
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/plugin-catalog@1.26.1-next.1
  - @backstage/app-defaults@1.5.16-next.0
  - @backstage/cli@0.29.5-next.1
  - @backstage/core-app-api@1.15.4-next.0
  - @backstage/core-components@0.16.3-next.0
  - @backstage/integration-react@1.2.3-next.0
  - @backstage/test-utils@1.7.4-next.0
  - @backstage/plugin-techdocs-react@1.2.13-next.0
  - @backstage/catalog-model@1.7.3-next.0
  - @backstage/config@1.3.2-next.0
  - @backstage/theme@0.6.3

## @internal/plugin-todo-list@1.0.35-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.3-next.0
  - @backstage/core-components@0.16.3-next.0

## @internal/plugin-todo-list-backend@1.0.35-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.1-next.1
  - @backstage/errors@1.2.7-next.0
  - @backstage/plugin-auth-node@0.5.6-next.1

## @internal/plugin-todo-list-common@1.0.24-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.8.4-next.0
