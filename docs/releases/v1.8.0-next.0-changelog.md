# Release v1.8.0-next.0

## @backstage/backend-common@0.16.0-next.0

### Minor Changes

- a7607b5413: **BREAKING CHANGE**: The `UrlReader` interface has been updated to require that `readUrl` is implemented. `readUrl` has previously been optional to implement but a warning has been logged when calling its predecessor `read`.
  The `read` method is now deprecated and will be removed in a future release.

### Patch Changes

- 55227712dd: Generated development HTTPS backend certificate is now checked for expiration date instead of file age.
- d05e1841ce: This patch adds GiteaURLReader to the available classes. It currently only reads single files via gitea's public repos api
- 210a3b5668: Small update to fix compatibility with newer versions of the `keyv` library
- c1784a4980: Replaces in-code uses of `GitHub` with `Github` and deprecates old versions.
- Updated dependencies
  - @backstage/integration@1.4.0-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.4-next.0
  - @backstage/config-loader@1.1.6-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/cli@0.21.0-next.0

### Minor Changes

- 7539b36748: Added a new ESLint rule that restricts imports of Link from @material-ui

  The rule can be can be overridden in the following way:

  ```diff
  module.exports = require('@backstage/cli/config/eslint-factory')(__dirname, {
  +  restrictedImports: [
  +    { name: '@material-ui/core', importNames: [] },
  +    { name: '@material-ui/core/Link', importNames: [] },
  +  ],
  });
  ```

### Patch Changes

- 4091c73e68: Updated `@swc/core` to version 1.3.9 which fixes a `.tsx` parser bug
- 9c767e8f45: Updated dependency `@svgr/plugin-jsx` to `6.5.x`.
  Updated dependency `@svgr/plugin-svgo` to `6.5.x`.
  Updated dependency `@svgr/rollup` to `6.5.x`.
  Updated dependency `@svgr/webpack` to `6.5.x`.
- Updated dependencies
  - @backstage/types@1.0.1-next.0
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.4-next.0
  - @backstage/config-loader@1.1.6-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/release-manifests@0.0.6

## @backstage/core-app-api@1.2.0-next.0

### Minor Changes

- 9b737e5f2e: Updated the React Router wiring to make use of the new `basename` property of the router components in React Router v6 stable. To implement this, a new optional `basename` property has been added to the `Router` app component, which can be forwarded to the concrete router implementation in order to support this new behavior. This is done by default in any app that does not have a `Router` component override.
- 127fcad26d: Deprecated the `homepage` config as the component that used it - `HomepageTimer` - has been removed and replaced by the `HeaderWorldClock` in the home plugin

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/version-bridge@1.0.1

## @backstage/core-components@0.12.0-next.0

### Minor Changes

- fb3733e446: **BREAKING**: Removed the `HomepageTimer` as it has been replaced by the `HeaderWorldClock` in the Home plugin and was deprecated over a year ago.

### Patch Changes

- 5f695c219a: Set the `searchTooltip` to "Filter" to follow how the `searchPlaceholder` is set making this more consistent
- 7573b65232: Internal refactor of imports to avoid circular dependencies
- 858986f6b6: Disable base path workaround in `Link` component when React Router v6 stable is used.
- f905853ad6: Prefer using `Link` from `@backstage/core-components` rather than material-UI.
- Updated dependencies
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/version-bridge@1.0.1

## @backstage/core-plugin-api@1.1.0-next.0

### Minor Changes

- a228f113d0: The app `Router` component now accepts an optional `basename` property.

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/version-bridge@1.0.1

## @backstage/integration@1.4.0-next.0

### Minor Changes

- d05e1841ce: This patch brings Gitea as a valid integration: target, via the ScmIntegration interface. It adds gitea to the relevant static properties (get integration by name, get integration by type) for plugins to be able to reference the same Gitea server.
- c1784a4980: Replaces in-code uses of `GitHub` with `Github` and deprecates old versions.

  Deprecates:

  - `getGitHubFileFetchUrl` replaced by `getGithubFileFetchUrl`
  - `GitHubIntegrationConfig` replaced by `GithubIntegrationConfig`
  - `GitHubIntegration` replaced by `GithubIntegration`
  - `readGitHubIntegrationConfig` replaced by `readGithubIntegrationConfig`
  - `readGitHubIntegrationConfigs` replaced by `readGithubIntegrationConfigs`
  - `replaceGitHubUrlType` replaced by `replaceGithubUrlType`

### Patch Changes

- 7573b65232: Internal refactor of imports to avoid circular dependencies

- a6d779d58a: Remove explicit default visibility at `config.d.ts` files.

  ```ts
  /**
   * @visibility backend
   */
  ```

- Updated dependencies
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-azure-sites@0.1.0-next.0

### Minor Changes

- 4a75ce761c: Azure Sites (Apps & Functions) support for a given entity. View the current status of the site, quickly jump to site's Overview page, or Log Stream page.

### Patch Changes

- f905853ad6: Prefer using `Link` from `@backstage/core-components` rather than material-UI.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-azure-sites-common@0.1.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-azure-sites-backend@0.1.0-next.0

### Minor Changes

- 4a75ce761c: Azure Sites (Apps & Functions) support for a given entity. View the current status of the site, quickly jump to site's Overview page, or Log Stream page.

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-azure-sites-common@0.1.0-next.0
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-azure-sites-common@0.1.0-next.0

### Minor Changes

- 4a75ce761c: Azure Sites (Apps & Functions) support for a given entity. View the current status of the site, quickly jump to site's Overview page, or Log Stream page.

## @backstage/plugin-bazaar@0.2.0-next.0

### Minor Changes

- 28b39e0e0e: The limit prop of BazaarOverviewCard has been removed entirely, and instead replaced with a new optional boolean prop `fullWidth`. The BazaarOverviewCard now always use full height without fixed width. Also fixed problem with link to Bazaar.

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/cli@0.21.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-catalog@1.6.1-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-catalog-backend-module-github@0.2.0-next.0

### Minor Changes

- 67fe5bc9a9: BREAKING: Support authenticated backends by including a server token for catalog requests. The constructor of `GithubLocationAnalyzer` now requires an instance of `TokenManager` to be supplied:

  ```diff
  ...
    builder.addLocationAnalyzers(
      new GitHubLocationAnalyzer({
        discovery: env.discovery,
        config: env.config,
  +     tokenManager: env.tokenManager,
      }),
    );
  ...
  ```

- f64d66a45c: Added the ability for the GitHub discovery provider to validate that catalog files exist before emitting them.

  Users can now set the `validateLocationsExist` property to `true` in their GitHub discovery configuration to opt in to this feature.
  This feature only works with `catalogPath`s that do not contain wildcards.

  When `validateLocationsExist` is set to `true`, the GitHub discovery provider will retrieve the object from the
  repository at the provided `catalogPath`.
  If this file exists and is non-empty, then it will be emitted as a location for further processing.
  If this file does not exist or is empty, then it will not be emitted.
  Not emitting locations that do not exist allows for far fewer calls to the GitHub API to validate locations that do not exist.

### Patch Changes

- 67fe5bc9a9: Properly derive Github credentials when making requests in `GithubLocationAnalyzer` to support Github App authentication
- c1784a4980: Replaces in-code uses of `GitHub` with `Github` and deprecates old versions.
- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-catalog-backend@1.5.1-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/backend-plugin-api@0.1.4-next.0
  - @backstage/plugin-catalog-node@1.2.1-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-cost-insights@0.12.0-next.0

### Minor Changes

- 43afded227: Updated recharts to v2.0.0 and fixed typing issues

### Patch Changes

- cbe11d1e23: Tweak README
- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-cost-insights-common@0.1.1

## @backstage/plugin-github-issues@0.2.0-next.0

### Minor Changes

- ead285b9e4: **BREAKING**: Changed the casing of all exported types to have a lowercase "h" in "github". E.g. "GitHubIssuesPage" was renamed to "GithubIssuesPage". Please rename your imports where necessary.

### Patch Changes

- c8dd2a8c87: Stripping specific issues URL already present to target base issues URL.
- c1784a4980: Replaces in-code uses of `GitHub` with `Github` and deprecates old versions.
- f905853ad6: Prefer using `Link` from `@backstage/core-components` rather than material-UI.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-ilert@0.2.0-next.0

### Minor Changes

- 0697af30da: Added support for multiple responders in alert list, added new tab with list to support iLert resource 'service', added new tab with list to support iLert resource 'status page'

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-kubernetes-backend@0.8.0-next.0

### Minor Changes

- cbf5d11fdf: The Kubernetes errors when fetching pod metrics are now captured and returned to the frontend.

  - **BREAKING** The method `fetchPodMetricsByNamespace` in the interface `KubernetesFetcher` is changed to `fetchPodMetricsByNamespaces`. It now accepts a set of namespace strings and returns `Promise<FetchResponseWrapper>`.
  - Add the `PodStatusFetchResponse` to the `FetchResponse` union type.
  - Add `NOT_FOUND` to the `KubernetesErrorTypes` union type, the HTTP error with status code 404 will be mapped to this error.

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-auth-node@0.2.7-next.0
  - @backstage/plugin-kubernetes-common@0.4.4-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-scaffolder@1.8.0-next.0

### Minor Changes

- edae17309e: Added props to override default Scaffolder page title, subtitle and pageTitleOverride.
  Routes like `rootRouteRef`, `selectedTemplateRouteRef`, `nextRouteRef`, `nextSelectedTemplateRouteRef` were made public and can be used in your app (e.g. in custom TemplateCard component).

### Patch Changes

- 4830a3569f: Basic analytics instrumentation is now in place:

  - As users make their way through template steps, a `click` event is fired, including the step number.
  - After a user clicks "Create" a `create` event is fired, including the name of the software that was just created. The template used at creation is set on the `entityRef` context key.

- f905853ad6: Prefer using `Link` from `@backstage/core-components` rather than material-UI.

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/integration-react@1.1.6-next.0
  - @backstage/plugin-permission-react@0.4.7-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.8-next.0
  - @backstage/plugin-scaffolder-common@1.2.2-next.0

## @backstage/plugin-scaffolder-backend@1.8.0-next.0

### Minor Changes

- ea14eb62a2: Added a set of default Prometheus metrics around scaffolding. See below for a list of metrics and an explanation of their labels:

  - `scaffolder_task_count`: Tracks successful task runs.

    Labels:

    - `template`: The entity ref of the scaffolded template
    - `user`: The entity ref of the user that invoked the template run
    - `result`: A string describing whether the task ran successfully, failed, or was skipped

  - `scaffolder_task_duration`: a histogram which tracks the duration of a task run

    Labels:

    - `template`: The entity ref of the scaffolded template
    - `result`: A boolean describing whether the task ran successfully

  - `scaffolder_step_count`: a count that tracks each step run

    Labels:

    - `template`: The entity ref of the scaffolded template
    - `step`: The name of the step that was run
    - `result`: A string describing whether the task ran successfully, failed, or was skipped

  - `scaffolder_step_duration`: a histogram which tracks the duration of each step run

    Labels:

    - `template`: The entity ref of the scaffolded template
    - `step`: The name of the step that was run
    - `result`: A string describing whether the task ran successfully, failed, or was skipped

  You can find a guide for running Prometheus metrics here: <https://github.com/backstage/backstage/blob/master/contrib/docs/tutorials/prometheus-metrics.md>

### Patch Changes

- 7573b65232: Internal refactor of imports to avoid circular dependencies
- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-catalog-backend@1.5.1-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-auth-node@0.2.7-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/backend-plugin-api@0.1.4-next.0
  - @backstage/plugin-catalog-node@1.2.1-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-scaffolder-common@1.2.2-next.0

## @backstage/plugin-techdocs@1.4.0-next.0

### Minor Changes

- 5691baea69: Add ability to configure filters when using EntityListDocsGrid

  The following example will render two sections of cards grid:

  - One section for documentations tagged as `recommended`
  - One section for documentations tagged as `runbook`

  ```js
  <EntityListDocsGrid groups={{[
    {
      title: "Recommended Documentation",
      filterPredicate: entity =>
        entity?.metadata?.tags?.includes('recommended') ?? false,
    },
    {
      title: "RunBooks Documentation",
      filterPredicate: entity =>
        entity?.metadata?.tags?.includes('runbook') ?? false,
    }
  ]}} />
  ```

### Patch Changes

- cbe11d1e23: Tweak README
- 7573b65232: Internal refactor of imports to avoid circular dependencies
- c1784a4980: Replaces in-code uses of `GitHub` with `Github` and deprecates old versions.
- 3a1a999b7b: Include query parameters when navigating to relative links in documents
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/integration-react@1.1.6-next.0
  - @backstage/plugin-search-react@1.2.1-next.0
  - @backstage/plugin-techdocs-react@1.0.6-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/app-defaults@1.0.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-app-api@1.2.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/plugin-permission-react@0.4.7-next.0
  - @backstage/theme@0.2.16

## @backstage/backend-app-api@0.2.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/backend-plugin-api@0.1.4-next.0
  - @backstage/plugin-permission-node@0.7.1-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/backend-defaults@0.1.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-app-api@0.2.3-next.0
  - @backstage/backend-plugin-api@0.1.4-next.0

## @backstage/backend-plugin-api@0.1.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/config@1.0.4-next.0

## @backstage/backend-tasks@0.3.7-next.0

### Patch Changes

- 30e43717c7: Deprecated the `HumanDuration` type, which should now instead be imported from `@backstage/types`.
- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/backend-test-utils@0.1.30-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/cli@0.21.0-next.0
  - @backstage/backend-app-api@0.2.3-next.0
  - @backstage/backend-plugin-api@0.1.4-next.0
  - @backstage/config@1.0.4-next.0

## @backstage/catalog-client@1.1.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/catalog-model@1.1.3-next.0

### Patch Changes

- 7573b65232: Internal refactor of imports to avoid circular dependencies
- Updated dependencies
  - @backstage/types@1.0.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/config@1.0.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.1-next.0

## @backstage/config-loader@1.1.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.1-next.0
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/create-app@0.4.33-next.0

### Patch Changes

- 4091c73e68: Updated `@swc/core` to `v1.3.9` which fixes a `.tsx` parser bug. You may want to run `yarn backstage-cli versions:bump` to get on latest version including the CLI itself.
- 80bfac5266: Updated the create-app command to no longer require Git to be installed and configured. A git repository will only be initialized if possible and if not already in an git repository.
- f905853ad6: Prefer using `Link` from `@backstage/core-components` rather than material-UI.
- Updated dependencies
  - @backstage/cli-common@0.1.10

## @backstage/dev-utils@1.0.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-app-api@1.2.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/app-defaults@1.0.8-next.0
  - @backstage/integration-react@1.1.6-next.0
  - @backstage/test-utils@1.2.2-next.0
  - @backstage/theme@0.2.16

## @backstage/errors@1.1.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.1-next.0

## @backstage/integration-react@1.1.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/theme@0.2.16

## @techdocs/cli@1.2.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-techdocs-node@1.4.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.4-next.0

## @backstage/test-utils@1.2.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.2.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-permission-react@0.4.7-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/theme@0.2.16

## @backstage/types@1.0.1-next.0

### Patch Changes

- 30e43717c7: Added the `HumanDuration` type, moved here from `@backstage/backend-tasks`. This type matches the `Duration.fromObject` form of `luxon`.

## @backstage/plugin-adr@0.2.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/integration-react@1.1.6-next.0
  - @backstage/plugin-search-react@1.2.1-next.0
  - @backstage/plugin-adr-common@0.2.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-adr-backend@0.2.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-adr-common@0.2.3-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-adr-common@0.2.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-airbrake@0.3.11-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/dev-utils@1.0.8-next.0
  - @backstage/test-utils@1.2.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-airbrake-backend@0.2.11-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-allure@0.1.27-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-analytics-module-ga@0.1.22-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-apache-airflow@0.2.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0

## @backstage/plugin-api-docs@0.8.11-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-catalog@1.6.1-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-apollo-explorer@0.1.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-app-backend@0.3.38-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/backend-plugin-api@0.1.4-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/config-loader@1.1.6-next.0

## @backstage/plugin-auth-backend@0.17.1-next.0

### Patch Changes

- cbe11d1e23: Tweak README
- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-auth-node@0.2.7-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-auth-node@0.2.7-next.0

### Patch Changes

- 7573b65232: Internal refactor of imports to avoid circular dependencies
- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-azure-devops@0.2.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-azure-devops-backend@0.3.17-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-badges@0.2.35-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-badges-backend@0.1.32-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-bazaar-backend@0.2.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-auth-node@0.2.7-next.0
  - @backstage/backend-test-utils@0.1.30-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-bitbucket-cloud-common@0.2.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.4.0-next.0

## @backstage/plugin-bitrise@0.1.38-next.0

### Patch Changes

- 43afded227: Updated recharts to v2.0.0 and fixed typing issues
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog@1.6.1-next.0

### Patch Changes

- f905853ad6: Prefer using `Link` from `@backstage/core-components` rather than material-UI.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/integration-react@1.1.6-next.0
  - @backstage/plugin-search-react@1.2.1-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.8-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-catalog-backend@1.5.1-next.0

### Patch Changes

- a7607b5413: Replace usage of deprecataed `UrlReader.read` with `UrlReader.readUrl`.
- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/backend-plugin-api@0.1.4-next.0
  - @backstage/plugin-catalog-node@1.2.1-next.0
  - @backstage/plugin-permission-node@0.7.1-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-catalog-common@1.0.8-next.0
  - @backstage/plugin-scaffolder-common@1.2.2-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-catalog-backend-module-aws@0.1.11-next.0

### Patch Changes

- bae3617be5: `AwsS3EntityProvider`: Add option to configure schedule via `app-config.yaml` instead of in code.

  Please find how to configure the schedule at the config at
  <https://backstage.io/docs/integrations/aws-s3/discovery>

- defb389ecd: Add `awsS3EntityProviderCatalogModule` (new backend-plugin-api, alpha).

- a6d779d58a: Remove explicit default visibility at `config.d.ts` files.

  ```ts
  /**
   * @visibility backend
   */
  ```

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-catalog-backend@1.5.1-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/backend-plugin-api@0.1.4-next.0
  - @backstage/plugin-catalog-node@1.2.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-catalog-backend-module-azure@0.1.9-next.0

### Patch Changes

- 87ff05892d: `AzureDevOpsEntityProvider`: Add option to configure schedule via `app-config.yaml` instead of in code.

  Please find how to configure the schedule at the config at
  <https://backstage.io/docs/integrations/azure/discovery>

- 0ca399b31b: Add `azureDevOpsEntityProviderCatalogModule` (new backend-plugin-api, alpha).

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-catalog-backend@1.5.1-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/backend-plugin-api@0.1.4-next.0
  - @backstage/plugin-catalog-node@1.2.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-catalog-backend-module-bitbucket@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-catalog-backend@1.5.1-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.1.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.5.1-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/backend-plugin-api@0.1.4-next.0
  - @backstage/plugin-catalog-node@1.2.1-next.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.1-next.0
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.1.3-next.0

### Patch Changes

- 68f7f5a857: `BitbucketServerEntityProvider`: Add option to configure schedule via `app-config.yaml` instead of in code.

  Please find how to configure the schedule at the config at
  <https://backstage.io/docs/integrations/bitbucketServer/discovery>

- cd48ed8370: Add `bitbucketServerEntityProviderCatalogModule` (new backend-plugin-api, alpha).

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-catalog-backend@1.5.1-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/backend-plugin-api@0.1.4-next.0
  - @backstage/plugin-catalog-node@1.2.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-catalog-backend-module-gerrit@0.1.6-next.0

### Patch Changes

- 4fba50f5d4: Add `gerritEntityProviderCatalogModule` (new backend-plugin-api, alpha).

- 134b69f478: `GerritEntityProvider`: Add option to configure schedule via `app-config.yaml` instead of in code.

  Please find how to configure the schedule at the config at
  <https://backstage.io/docs/integrations/gerrit/discovery>

- a6d779d58a: Remove explicit default visibility at `config.d.ts` files.

  ```ts
  /**
   * @visibility backend
   */
  ```

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-catalog-backend@1.5.1-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/backend-plugin-api@0.1.4-next.0
  - @backstage/plugin-catalog-node@1.2.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-catalog-backend-module-gitlab@0.1.9-next.0

### Patch Changes

- 6bb046bcbe: Add `gitlabDiscoveryEntityProviderCatalogModule` (new backend-plugin-api, alpha).

- 81cedb5033: `GitlabDiscoveryEntityProvider`: Add option to configure schedule via `app-config.yaml` instead of in code.

  Please find how to configure the schedule at the config at
  <https://backstage.io/docs/integrations/gitlab/discovery>

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-catalog-backend@1.5.1-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/backend-plugin-api@0.1.4-next.0
  - @backstage/plugin-catalog-node@1.2.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-catalog-backend-module-ldap@0.5.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.5.1-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-catalog-backend-module-msgraph@0.4.4-next.0

### Patch Changes

- 8d1a5e08ca: `MicrosoftGraphOrgEntityProvider`: Add option to configure schedule via `app-config.yaml` instead of in code.

  Please find how to configure the schedule at the config at
  <https://github.com/backstage/backstage/tree/master/plugins/catalog-backend-module-msgraph#readme>

- 384f99c276: Add `microsoftGraphOrgEntityProviderCatalogModule` (new backend-plugin-api, alpha).

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.5.1-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/backend-plugin-api@0.1.4-next.0
  - @backstage/plugin-catalog-node@1.2.1-next.0
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-catalog-backend-module-openapi@0.1.4-next.0

### Patch Changes

- 4ce887400d: Added support to use the `UrlReaders` when `$ref` pointing to a URL.
- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-catalog-backend@1.5.1-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-catalog-node@1.2.1-next.0
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-catalog-common@1.0.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-catalog-graph@0.2.23-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog-graphql@0.3.15-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-catalog-import@0.9.1-next.0

### Patch Changes

- 1e7b640518: Get rid of `this-is-undefined-in-esm` warning
- c1784a4980: Replaces in-code uses of `GitHub` with `Github` and deprecates old versions.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/integration-react@1.1.6-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-catalog-common@1.0.8-next.0

## @backstage/plugin-catalog-node@1.2.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/backend-plugin-api@0.1.4-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-catalog-common@1.0.8-next.0

## @backstage/plugin-catalog-react@1.2.1-next.0

### Patch Changes

- a889314692: Both `EntityProvider` and `AsyncEntityProvider` contexts now wrap all children with an `AnalyticsContext` containing the corresponding `entityRef`; this opens up the possibility for all events underneath these contexts to be associated with and aggregated by the corresponding entity.
- e47f466f80: Removed forced capitalization for Entity types in the catalog sidebar.
- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-permission-react@0.4.7-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/version-bridge@1.0.1
  - @backstage/plugin-catalog-common@1.0.8-next.0

## @backstage/plugin-cicd-statistics@0.1.13-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0

## @backstage/plugin-cicd-statistics-module-gitlab@0.1.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-cicd-statistics@0.1.13-next.0

## @backstage/plugin-circleci@0.3.11-next.0

### Patch Changes

- 383574c49b: Update screenshots in documentation to match latest CircleCI plugin
- f905853ad6: Prefer using `Link` from `@backstage/core-components` rather than material-UI.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-cloudbuild@0.3.11-next.0

### Patch Changes

- f905853ad6: Prefer using `Link` from `@backstage/core-components` rather than material-UI.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-code-climate@0.1.11-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage@0.2.4-next.0

### Patch Changes

- 43afded227: Updated recharts to v2.0.0 and fixed typing issues
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage-backend@0.2.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-codescene@0.1.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-config-schema@0.1.34-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-dynatrace@1.0.1-next.0

### Patch Changes

- cbe11d1e23: Tweak README
- f905853ad6: Prefer using `Link` from `@backstage/core-components` rather than material-UI.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-explore@0.3.42-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-explore-react@0.0.23-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-explore-react@0.0.23-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.1.0-next.0

## @backstage/plugin-firehydrant@0.1.28-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-fossa@0.2.43-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-gcalendar@0.3.7-next.0

### Patch Changes

- 7573b65232: Internal refactor of imports to avoid circular dependencies
- f905853ad6: Prefer using `Link` from `@backstage/core-components` rather than material-UI.
- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-gcp-projects@0.3.30-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-git-release-manager@0.3.24-next.0

### Patch Changes

- 43afded227: Updated recharts to v2.0.0 and fixed typing issues
- c1784a4980: Replaces in-code uses of `GitHub` with `Github` and deprecates old versions.
- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-github-actions@0.5.11-next.0

### Patch Changes

- c1784a4980: Replaces in-code uses of `GitHub` with `Github` and deprecates old versions.
- f905853ad6: Prefer using `Link` from `@backstage/core-components` rather than material-UI.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-github-deployments@0.1.42-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/integration-react@1.1.6-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-github-pull-requests-board@0.1.5-next.0

### Patch Changes

- cc8bfc56c3: Add a new "Team" Filter Options to the Github Pull Requests Dashboard.

  When toggling this option on, the dashboard will displays all of the PRs opened
  by the members of that team on any repositories of the organization.

- c1784a4980: Replaces in-code uses of `GitHub` with `Github` and deprecates old versions.

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-gitops-profiles@0.3.29-next.0

### Patch Changes

- f905853ad6: Prefer using `Link` from `@backstage/core-components` rather than material-UI.
- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-gocd@0.1.17-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-graphiql@0.2.43-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-graphql-backend@0.1.28-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/plugin-catalog-graphql@0.3.15-next.0

## @backstage/plugin-home@0.4.27-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-stack-overflow@0.1.7-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-jenkins@0.7.10-next.0

### Patch Changes

- f905853ad6: Prefer using `Link` from `@backstage/core-components` rather than material-UI.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-jenkins-common@0.1.10-next.0

## @backstage/plugin-jenkins-backend@0.1.28-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-auth-node@0.2.7-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-jenkins-common@0.1.10-next.0

## @backstage/plugin-jenkins-common@0.1.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/plugin-catalog-common@1.0.8-next.0

## @backstage/plugin-kafka@0.3.11-next.0

### Patch Changes

- f905853ad6: Prefer using `Link` from `@backstage/core-components` rather than material-UI.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-kafka-backend@0.2.31-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-kubernetes@0.7.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-kubernetes-common@0.4.4-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-kubernetes-common@0.4.4-next.0

### Patch Changes

- cbf5d11fdf: The Kubernetes errors when fetching pod metrics are now captured and returned to the frontend.

  - **BREAKING** The method `fetchPodMetricsByNamespace` in the interface `KubernetesFetcher` is changed to `fetchPodMetricsByNamespaces`. It now accepts a set of namespace strings and returns `Promise<FetchResponseWrapper>`.
  - Add the `PodStatusFetchResponse` to the `FetchResponse` union type.
  - Add `NOT_FOUND` to the `KubernetesErrorTypes` union type, the HTTP error with status code 404 will be mapped to this error.

- Updated dependencies
  - @backstage/catalog-model@1.1.3-next.0

## @backstage/plugin-lighthouse@0.3.11-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic@0.3.29-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic-dashboard@0.2.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-org@0.5.11-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-pagerduty@0.5.4-next.0

### Patch Changes

- f905853ad6: Prefer using `Link` from `@backstage/core-components` rather than material-UI.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop@0.1.9-next.0

### Patch Changes

- 7573b65232: Internal refactor of imports to avoid circular dependencies
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop-backend@0.1.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-permission-backend@0.5.13-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-auth-node@0.2.7-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/plugin-permission-node@0.7.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-permission-common@0.7.1-next.0

### Patch Changes

- 7573b65232: Internal refactor of imports to avoid circular dependencies
- 64848c963c: Properly handle rules that have no parameters in `PermissionClient`
- Updated dependencies
  - @backstage/types@1.0.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-permission-node@0.7.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-auth-node@0.2.7-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-permission-react@0.4.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-playlist@0.1.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/plugin-search-react@1.2.1-next.0
  - @backstage/plugin-permission-react@0.4.7-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.8-next.0
  - @backstage/plugin-playlist-common@0.1.2-next.0

## @backstage/plugin-playlist-backend@0.2.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-auth-node@0.2.7-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/backend-test-utils@0.1.30-next.0
  - @backstage/plugin-permission-node@0.7.1-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-playlist-common@0.1.2-next.0

## @backstage/plugin-playlist-common@0.1.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.1-next.0

## @backstage/plugin-proxy-backend@0.2.32-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-rollbar@0.4.11-next.0

### Patch Changes

- f905853ad6: Prefer using `Link` from `@backstage/core-components` rather than material-UI.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-rollbar-backend@0.1.35-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.2.13-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-scaffolder-backend@1.8.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-scaffolder-backend-module-rails@0.4.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-scaffolder-backend@1.8.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-scaffolder-backend-module-yeoman@0.2.11-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.8.0-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-scaffolder-common@1.2.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-search@1.0.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-search-react@1.2.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/version-bridge@1.0.1
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-search-backend@1.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-auth-node@0.2.7-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-permission-node@0.7.1-next.0
  - @backstage/plugin-search-backend-node@1.0.4-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-search-backend-module-elasticsearch@1.0.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-backend-node@1.0.4-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-search-backend-module-pg@0.4.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-search-backend-node@1.0.4-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-search-backend-node@1.0.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-search-common@1.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-search-react@1.2.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/theme@0.2.16
  - @backstage/version-bridge@1.0.1
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-sentry@0.4.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-shortcuts@0.3.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-sonarqube@0.4.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-sonarqube-backend@0.1.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-splunk-on-call@0.3.35-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-stack-overflow@0.1.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/plugin-home@0.4.27-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-stack-overflow-backend@0.1.7-next.0

### Patch Changes

- cbe11d1e23: Tweak README

- a6d779d58a: Remove explicit default visibility at `config.d.ts` files.

  ```ts
  /**
   * @visibility backend
   */
  ```

- Updated dependencies
  - @backstage/cli@0.21.0-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-tech-insights@0.3.2-next.0

### Patch Changes

- 7095e8bc03: Fixed bug when sending data by Post in `runChecks` and `runBulkChecks` functions of the `TechInsightsClient` class, the default `Content-Type` used was `plain/text`
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-tech-insights-common@0.2.8-next.0

## @backstage/plugin-tech-insights-backend@0.5.4-next.0

### Patch Changes

- 06cf8f1cf2: Add a default delay to the fact retrievers to prevent cold-start errors
- 30e43717c7: Use `HumanDuration` from `@backstage/types`
- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-tech-insights-node@0.3.6-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-tech-insights-common@0.2.8-next.0

## @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.22-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-tech-insights-node@0.3.6-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-tech-insights-common@0.2.8-next.0

## @backstage/plugin-tech-insights-common@0.2.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-tech-insights-node@0.3.6-next.0

### Patch Changes

- 06cf8f1cf2: Add a default delay to the fact retrievers to prevent cold-start errors
- 30e43717c7: Use `HumanDuration` from `@backstage/types`
- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/plugin-tech-insights-common@0.2.8-next.0

## @backstage/plugin-tech-radar@0.5.18-next.0

### Patch Changes

- 1f888af5f6: Fixed bug in Tech Radar where, on hover, the tech list quadrant would rerender and scroll top
- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-addons-test-utils@1.0.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-app-api@1.2.0-next.0
  - @backstage/plugin-techdocs@1.4.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/plugin-catalog@1.6.1-next.0
  - @backstage/integration-react@1.1.6-next.0
  - @backstage/plugin-search-react@1.2.1-next.0
  - @backstage/plugin-techdocs-react@1.0.6-next.0
  - @backstage/test-utils@1.2.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-backend@1.4.1-next.0

### Patch Changes

- a7607b5413: Replace usage of deprecataed `UrlReader.read` with `UrlReader.readUrl`.

- a6d779d58a: Remove explicit default visibility at `config.d.ts` files.

  ```ts
  /**
   * @visibility backend
   */
  ```

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-techdocs-node@1.4.2-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-catalog-common@1.0.8-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-techdocs-module-addons-contrib@1.0.6-next.0

### Patch Changes

- c1784a4980: Replaces in-code uses of `GitHub` with `Github` and deprecates old versions.
- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/integration-react@1.1.6-next.0
  - @backstage/plugin-techdocs-react@1.0.6-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-node@1.4.2-next.0

### Patch Changes

- a7607b5413: Replace usage of deprecataed `UrlReader.read` with `UrlReader.readUrl`.
- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-techdocs-react@1.0.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/version-bridge@1.0.1

## @backstage/plugin-todo@0.2.13-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-todo-backend@0.1.35-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-user-settings@0.5.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-app-api@1.2.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-user-settings-backend@0.1.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-auth-node@0.2.7-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-vault@0.1.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-vault-backend@0.2.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/backend-test-utils@0.1.30-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-xcmetrics@0.2.31-next.0

### Patch Changes

- 7573b65232: Internal refactor of imports to avoid circular dependencies
- 43afded227: Updated recharts to v2.0.0 and fixed typing issues
- dcf9e728de: Removed an unused and hidden build details route.
- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## example-app@0.2.77-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder@1.8.0-next.0
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-app-api@1.2.0-next.0
  - @backstage/plugin-tech-insights@0.3.2-next.0
  - @backstage/plugin-techdocs@1.4.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/plugin-cost-insights@0.12.0-next.0
  - @backstage/plugin-dynatrace@1.0.1-next.0
  - @backstage/cli@0.21.0-next.0
  - @backstage/plugin-catalog-import@0.9.1-next.0
  - @backstage/plugin-tech-radar@0.5.18-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-gcalendar@0.3.7-next.0
  - @backstage/plugin-code-coverage@0.2.4-next.0
  - @backstage/plugin-github-actions@0.5.11-next.0
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.6-next.0
  - @backstage/plugin-circleci@0.3.11-next.0
  - @backstage/plugin-azure-sites@0.1.0-next.0
  - @backstage/plugin-cloudbuild@0.3.11-next.0
  - @backstage/plugin-jenkins@0.7.10-next.0
  - @backstage/plugin-kafka@0.3.11-next.0
  - @backstage/plugin-pagerduty@0.5.4-next.0
  - @backstage/plugin-rollbar@0.4.11-next.0
  - @backstage/plugin-airbrake@0.3.11-next.0
  - @backstage/plugin-api-docs@0.8.11-next.0
  - @backstage/plugin-azure-devops@0.2.2-next.0
  - @backstage/plugin-badges@0.2.35-next.0
  - @internal/plugin-catalog-customized@0.0.4-next.0
  - @backstage/plugin-catalog-graph@0.2.23-next.0
  - @backstage/plugin-explore@0.3.42-next.0
  - @backstage/plugin-gocd@0.1.17-next.0
  - @backstage/plugin-home@0.4.27-next.0
  - @backstage/plugin-kubernetes@0.7.4-next.0
  - @backstage/plugin-lighthouse@0.3.11-next.0
  - @backstage/plugin-newrelic-dashboard@0.2.4-next.0
  - @backstage/plugin-org@0.5.11-next.0
  - @backstage/plugin-playlist@0.1.2-next.0
  - @backstage/plugin-search@1.0.4-next.0
  - @backstage/plugin-sentry@0.4.4-next.0
  - @backstage/plugin-todo@0.2.13-next.0
  - @backstage/app-defaults@1.0.8-next.0
  - @backstage/integration-react@1.1.6-next.0
  - @backstage/plugin-apache-airflow@0.2.4-next.0
  - @backstage/plugin-gcp-projects@0.3.30-next.0
  - @backstage/plugin-graphiql@0.2.43-next.0
  - @backstage/plugin-newrelic@0.3.29-next.0
  - @backstage/plugin-search-react@1.2.1-next.0
  - @backstage/plugin-shortcuts@0.3.3-next.0
  - @backstage/plugin-stack-overflow@0.1.7-next.0
  - @backstage/plugin-techdocs-react@1.0.6-next.0
  - @backstage/plugin-user-settings@0.5.1-next.0
  - @backstage/plugin-permission-react@0.4.7-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.8-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## example-backend@0.2.77-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-catalog-backend@1.5.1-next.0
  - @backstage/plugin-techdocs-backend@1.4.1-next.0
  - @backstage/plugin-scaffolder-backend@1.8.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/plugin-auth-backend@0.17.1-next.0
  - @backstage/backend-tasks@0.3.7-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-auth-node@0.2.7-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/plugin-tech-insights-backend@0.5.4-next.0
  - @backstage/plugin-tech-insights-node@0.3.6-next.0
  - @backstage/plugin-azure-sites-backend@0.1.0-next.0
  - @backstage/plugin-kubernetes-backend@0.8.0-next.0
  - example-app@0.2.77-next.0
  - @backstage/plugin-app-backend@0.3.38-next.0
  - @backstage/plugin-azure-devops-backend@0.3.17-next.0
  - @backstage/plugin-badges-backend@0.1.32-next.0
  - @backstage/plugin-code-coverage-backend@0.2.4-next.0
  - @backstage/plugin-graphql-backend@0.1.28-next.0
  - @backstage/plugin-jenkins-backend@0.1.28-next.0
  - @backstage/plugin-kafka-backend@0.2.31-next.0
  - @backstage/plugin-permission-backend@0.5.13-next.0
  - @backstage/plugin-permission-node@0.7.1-next.0
  - @backstage/plugin-playlist-backend@0.2.1-next.0
  - @backstage/plugin-proxy-backend@0.2.32-next.0
  - @backstage/plugin-rollbar-backend@0.1.35-next.0
  - @backstage/plugin-scaffolder-backend-module-rails@0.4.6-next.0
  - @backstage/plugin-search-backend@1.1.1-next.0
  - @backstage/plugin-search-backend-module-elasticsearch@1.0.4-next.0
  - @backstage/plugin-search-backend-module-pg@0.4.2-next.0
  - @backstage/plugin-search-backend-node@1.0.4-next.0
  - @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.22-next.0
  - @backstage/plugin-todo-backend@0.1.35-next.0
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## example-backend-next@0.0.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.5.1-next.0
  - @backstage/plugin-scaffolder-backend@1.8.0-next.0
  - @backstage/plugin-app-backend@0.3.38-next.0
  - @backstage/backend-defaults@0.1.3-next.0

## techdocs-cli-embedded-app@0.2.76-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-app-api@1.2.0-next.0
  - @backstage/plugin-techdocs@1.4.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/cli@0.21.0-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/plugin-catalog@1.6.1-next.0
  - @backstage/app-defaults@1.0.8-next.0
  - @backstage/integration-react@1.1.6-next.0
  - @backstage/plugin-techdocs-react@1.0.6-next.0
  - @backstage/test-utils@1.2.2-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/theme@0.2.16

## @internal/plugin-catalog-customized@0.0.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.1-next.0
  - @backstage/plugin-catalog@1.6.1-next.0

## @internal/plugin-todo-list@1.0.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16

## @internal/plugin-todo-list-backend@1.0.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.0
  - @backstage/plugin-auth-node@0.2.7-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @internal/plugin-todo-list-common@1.0.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.1-next.0
