# Release v1.36.0-next.0

Upgrade Helper: [https://backstage.github.io/upgrade-helper/?to=1.36.0-next.0](https://backstage.github.io/upgrade-helper/?to=1.36.0-next.0)

## @backstage/backend-defaults@0.8.0-next.0

### Minor Changes

- a4aa244: This change introduces the `auditor` service implementation details.

### Patch Changes

- f866b86: Internal refactor to use explicit `require` for lazy-loading dependency.
- a19cb2b: Added default implementation for the new `PermissionsRegistryService`.
- Updated dependencies
  - @backstage/cli-node@0.2.13-next.0
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/backend-app-api@1.1.2-next.0
  - @backstage/backend-dev-utils@0.1.5
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/backend-plugin-api@1.2.0-next.0

### Minor Changes

- a4aa244: This change introduces the `auditor` service definition.

### Patch Changes

- 9ddfd94: Added new `PermissionsRegistryService` that is used by plugins to register permissions, resource types, and rules into the permission system. This replaces the existing `createPermissionIntegrationRouter` from `@backstage/plugin-permission-node`.
- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-permission-common@0.8.4

## @backstage/backend-test-utils@1.3.0-next.0

### Minor Changes

- dd05a97: Added mocks for the new `PermissionsRegistryService`.
- a4aa244: This change introduces mocks for the `auditor` service.

### Patch Changes

- f866b86: Internal refactor to use explicit `require` for lazy-loading dependency.
- fb051f2: Sync feature installation compatibility logic with `@backstage/backend-app-api`.
- Updated dependencies
  - @backstage/backend-defaults@0.8.0-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/backend-app-api@1.1.2-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/canon@0.1.0-next.0

### Minor Changes

- 65f4acc: This is the first alpha release for Canon. As part of this release we are introducing 5 layout components and 7 components. All theming is done through CSS variables.

## @backstage/cli@0.30.0-next.0

### Minor Changes

- cb76663: **BREAKING**: Add support for native ESM in Node.js code. This changes the behavior of dynamic import expressions in Node.js code. Typically this can be fixed by replacing `import(...)` with `require(...)`, with an `as typeof import(...)` cast if needed for types. This is because dynamic imports will no longer be transformed to `require(...)` calls, but instead be left as-is. This in turn allows you to load ESM modules from CommonJS code using `import(...)`.

  This change adds support for the following in Node.js packages, across type checking, package builds, runtime transforms and Jest tests:

  - Dynamic imports that load ESM modules from CommonJS code.
  - Both `.mjs` and `.mts` files as explicit ESM files, as well as `.cjs` and `.cts` as explicit CommonJS files.
  - Support for the `"type": "module"` field in `package.json` to indicate that the package is an ESM package.

  There are a few caveats to be aware of:

  - To enable support for native ESM in tests, you need to run the tests with the `--experimental-vm-modules` flag enabled, typically via `NODE_OPTIONS='--experimental-vm-modules'`.
  - Declaring a package as `"type": "module"` in `package.json` is supported, but in tests it will cause all local transitive dependencies to also be treated as ESM, regardless of whether they declare `"type": "module"` or not.
  - Node.js has an [ESM interoperability layer with CommonJS](https://nodejs.org/docs/latest-v22.x/api/esm.html#interoperability-with-commonjs) that allows for imports from ESM to identify named exports in CommonJS packages. This interoperability layer is **only** enabled when importing packages with a `.cts` or `.cjs` extension. This is because the interoperability layer is not fully compatible with the NPM ecosystem, and would break package if it was enabled for `.js` files.
  - Dynamic imports of CommonJS packages will vary in shape depending on the runtime, i.e. test vs local development, etc. It is therefore recommended to avoid dynamic imports of CommonJS packages and instead use `require`, or to use the explicit CommonJS extensions as mentioned above. If you do need to dynamically import CommonJS packages, avoid using `default` exports, as the shape of them vary across different environments and you would otherwise need to manually unwrap the import based on the shape of the module object.

### Patch Changes

- f21b125: Ensure that both global-agent and undici agents are enabled when proxying is enabled.
- Updated dependencies
  - @backstage/cli-node@0.2.13-next.0
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/eslint-plugin@0.1.10
  - @backstage/integration@1.16.1
  - @backstage/release-manifests@0.0.12
  - @backstage/types@1.2.1

## @techdocs/cli@1.9.0-next.0

### Minor Changes

- 8de3d2d: Allow configurable optional retries for publisher AWS S3 operations.

### Patch Changes

- 69f84ac: Internal update to work with dynamic imports.
- Updated dependencies
  - @backstage/backend-defaults@0.8.0-next.0
  - @backstage/plugin-techdocs-node@1.13.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2

## @backstage/plugin-catalog-backend@1.31.0-next.0

### Minor Changes

- 8805f93: The catalog backend now supports the new `PermissionsRegistryService`, which can be used to add custom permission rules.
- a4aa244: This change integrates the `auditor` service into the Catalog plugin.

### Patch Changes

- c9139e1: Ignore benign database conflict errors during stitching, now logged with debug level instead.
- f178b12: Cleanup `refresh_state_references` for entity processors and providers that are no longer in control of a `refresh_state` row for entity
- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-search-backend-module-catalog@0.3.1-next.0
  - @backstage/backend-openapi-utils@0.4.2-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-events-node@0.4.8-next.0
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-scaffolder-backend@1.30.0-next.0

### Minor Changes

- a4aa244: This change integrates the `auditor` service into the Scaffolder plugin.

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/backend-defaults@0.8.0-next.0
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.27
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.5-next.0
  - @backstage/plugin-events-node@0.4.8-next.0
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-scaffolder-backend-module-azure@0.2.6-next.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.7-next.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.6-next.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.6-next.0
  - @backstage/plugin-scaffolder-backend-module-gerrit@0.2.6-next.0
  - @backstage/plugin-scaffolder-backend-module-gitea@0.2.6-next.0
  - @backstage/plugin-scaffolder-backend-module-github@0.5.6-next.0
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.7.2-next.0
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-scaffolder-node@0.7.0-next.0

### Minor Changes

- a4aa244: This change introduces an optional `taskId` property to `TaskContext`.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-techdocs-node@1.13.0-next.0

### Minor Changes

- 8de3d2d: Allow configurable optional retries for publisher AWS S3 operations.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-aws-node@0.1.15
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/backend-app-api@1.1.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/backend-dynamic-feature-service@0.5.4-next.0

### Patch Changes

- 96c20cd: Make sure changes are successfully tracked before starting up scanner.
- Updated dependencies
  - @backstage/cli-node@0.2.13-next.0
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/backend-defaults@0.8.0-next.0
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/plugin-catalog-backend@1.31.0-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/backend-app-api@1.1.2-next.0
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-app-node@0.1.30-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-events-backend@0.4.2-next.0
  - @backstage/plugin-events-node@0.4.8-next.0
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-backend-node@1.3.8-next.0
  - @backstage/plugin-search-common@1.2.17

## @backstage/backend-openapi-utils@0.4.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/cli-node@0.2.13-next.0

### Patch Changes

- cb76663: Added `type` field to `BackstagePackageJson` type.
- Updated dependencies
  - @backstage/cli-common@0.1.15
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/config-loader@1.9.6-next.0

### Patch Changes

- f866b86: Internal refactor to use explicit `require` for lazy-loading dependency.
- Updated dependencies
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/core-compat-api@0.3.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/core-plugin-api@1.10.3
  - @backstage/version-bridge@1.0.10

## @backstage/create-app@0.5.25-next.0

### Patch Changes

- Bumped create-app version.
- Updated dependencies
  - @backstage/cli-common@0.1.15

## @backstage/dev-utils@1.1.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/app-defaults@1.5.16
  - @backstage/catalog-model@1.7.3
  - @backstage/core-app-api@1.15.4
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/integration-react@1.2.3
  - @backstage/theme@0.6.3
  - @backstage/plugin-catalog-react@1.15.2-next.0

## @backstage/frontend-app-api@0.10.5-next.0

### Patch Changes

- 3e21b8d: Added `getNodesByRoutePath` method to the `AppTreeApi`.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/config@1.3.2
  - @backstage/core-app-api@1.15.4
  - @backstage/core-plugin-api@1.10.3
  - @backstage/errors@1.2.7
  - @backstage/frontend-defaults@0.1.6-next.0
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.10

## @backstage/frontend-defaults@0.1.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/frontend-app-api@0.10.5-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-app@0.1.6-next.0

## @backstage/frontend-plugin-api@0.9.5-next.0

### Patch Changes

- 3e21b8d: Added `getNodesByRoutePath` method to the `AppTreeApi`.
- Updated dependencies
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.10

## @backstage/frontend-test-utils@0.2.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/frontend-app-api@0.10.5-next.0
  - @backstage/config@1.3.2
  - @backstage/test-utils@1.7.4
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.10
  - @backstage/plugin-app@0.1.6-next.0

## @backstage/repo-tools@0.12.2-next.0

### Patch Changes

- 98ddf05: The `api-reports` command is now also able to generate SQL reports, enabled by the `--sql-reports` flag.
- cb76663: Internal refactor to support native ESM.
- ecd01a9: Internal refactor of API report generation.
- Updated dependencies
  - @backstage/cli-node@0.2.13-next.0
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/errors@1.2.7

## @backstage/plugin-api-docs@0.12.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/core-compat-api@0.3.6-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/plugin-catalog@1.26.2-next.0
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-react@1.15.2-next.0
  - @backstage/plugin-permission-react@0.4.30

## @backstage/plugin-app@0.1.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/integration-react@1.2.3
  - @backstage/theme@0.6.3
  - @backstage/plugin-permission-react@0.4.30

## @backstage/plugin-app-backend@0.4.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-app-node@0.1.30-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-app-node@0.1.30-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0

## @backstage/plugin-app-visualizer@0.1.16-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3

## @backstage/plugin-auth-backend@0.24.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-backend-module-atlassian-provider@0.3.5-next.0
  - @backstage/plugin-auth-backend-module-auth0-provider@0.1.5-next.0
  - @backstage/plugin-auth-backend-module-aws-alb-provider@0.3.3-next.0
  - @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.5-next.0
  - @backstage/plugin-auth-backend-module-bitbucket-provider@0.2.5-next.0
  - @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.1.5-next.0
  - @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.3.5-next.0
  - @backstage/plugin-auth-backend-module-gcp-iap-provider@0.3.5-next.0
  - @backstage/plugin-auth-backend-module-github-provider@0.2.5-next.0
  - @backstage/plugin-auth-backend-module-gitlab-provider@0.2.5-next.0
  - @backstage/plugin-auth-backend-module-google-provider@0.2.5-next.0
  - @backstage/plugin-auth-backend-module-microsoft-provider@0.2.5-next.0
  - @backstage/plugin-auth-backend-module-oauth2-provider@0.3.5-next.0
  - @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.5-next.0
  - @backstage/plugin-auth-backend-module-oidc-provider@0.3.5-next.0
  - @backstage/plugin-auth-backend-module-okta-provider@0.1.5-next.0
  - @backstage/plugin-auth-backend-module-onelogin-provider@0.2.5-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-atlassian-provider@0.3.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-auth0-provider@0.1.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-aws-alb-provider@0.3.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/errors@1.2.7
  - @backstage/plugin-auth-backend@0.24.3-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-bitbucket-provider@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.1.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.3.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-gcp-iap-provider@0.3.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-github-provider@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-gitlab-provider@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-google-provider@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-guest-provider@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-microsoft-provider@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-oauth2-provider@0.3.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/errors@1.2.7
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-oidc-provider@0.3.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-auth-backend@0.24.3-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-okta-provider@0.1.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-onelogin-provider@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-pinniped-provider@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-vmware-cloud-provider@0.4.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-node@0.5.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-catalog@1.26.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-react@1.8.6-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-compat-api@0.3.6-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/errors@1.2.7
  - @backstage/integration-react@1.2.3
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-react@1.15.2-next.0
  - @backstage/plugin-permission-react@0.4.30
  - @backstage/plugin-scaffolder-common@1.5.9
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-catalog-backend-module-aws@0.4.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.0-next.0
  - @backstage/plugin-kubernetes-common@0.9.3-next.0
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-aws-node@0.1.15
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-azure@0.3.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-backstage-openapi@0.4.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/backend-openapi-utils@0.4.2-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.4.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.27
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.3.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-catalog-backend-module-gcp@0.3.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-kubernetes-common@0.9.3-next.0
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2

## @backstage/plugin-catalog-backend-module-gerrit@0.2.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-catalog-backend-module-github@0.7.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.31.0-next.0
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-catalog-backend-module-github-org@0.3.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-backend-module-github@0.7.10-next.0
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-catalog-backend-module-gitlab@0.6.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.0-next.0
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-catalog-backend-module-gitlab-org@0.2.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-catalog-backend-module-gitlab@0.6.3-next.0
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-catalog-backend-module-incremental-ingestion@0.6.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.0-next.0
  - @backstage/plugin-catalog-backend@1.31.0-next.0
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.8-next.0
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-catalog-backend-module-ldap@0.11.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-logs@0.1.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.31.0-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-catalog-backend-module-msgraph@0.6.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-openapi@0.2.7-next.0

### Patch Changes

- 7fc6f7c: Fix `resolveUrl` for split openapi definition relative path
- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-puppetdb@0.2.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-catalog-backend-module-unprocessed@0.5.5-next.0

### Patch Changes

- 4e073c7: Use new `PermissionsRegistryService` instead of the deprecated `catalogPermissionExtensionPoint`.
- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-catalog-unprocessed-entities-common@0.0.7
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-catalog-graph@0.4.16-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-compat-api@0.3.6-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-react@1.15.2-next.0

## @backstage/plugin-catalog-import@0.12.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-compat-api@0.3.6-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-react@1.2.3
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-react@1.15.2-next.0

## @backstage/plugin-catalog-node@1.15.2-next.0

### Patch Changes

- 4a941e7: Deprecated the alpha `catalogPermissionExtensionPoint` and related types, since the same functionality is now available via the new `PermissionsRegistryService`.
- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-catalog-react@1.15.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-compat-api@0.3.6-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/errors@1.2.7
  - @backstage/frontend-test-utils@0.2.6-next.0
  - @backstage/integration-react@1.2.3
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.10
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-react@0.4.30

## @backstage/plugin-devtools@0.1.24-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/core-compat-api@0.3.6-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/errors@1.2.7
  - @backstage/plugin-devtools-common@0.1.15
  - @backstage/plugin-permission-react@0.4.30

## @backstage/plugin-devtools-backend@0.5.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/backend-defaults@0.8.0-next.0
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-devtools-common@0.1.15
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-events-backend@0.4.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/backend-openapi-utils@0.4.2-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-events-backend-module-aws-sqs@0.4.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-events-backend-module-azure@0.2.17-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-events-backend-module-bitbucket-cloud@0.2.17-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-events-backend-module-gerrit@0.2.17-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-events-backend-module-github@0.2.17-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-events-backend-module-gitlab@0.2.17-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-events-backend-test-utils@0.1.41-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-events-node@0.4.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-home@0.8.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-app-api@1.15.4
  - @backstage/core-compat-api@0.3.6-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/theme@0.6.3
  - @backstage/plugin-catalog-react@1.15.2-next.0
  - @backstage/plugin-home-react@0.1.22

## @backstage/plugin-kubernetes@0.12.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-kubernetes-react@0.5.4-next.0
  - @backstage/plugin-kubernetes-common@0.9.3-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/core-compat-api@0.3.6-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/plugin-catalog-react@1.15.2-next.0

## @backstage/plugin-kubernetes-backend@0.19.3-next.0

### Patch Changes

- ac0e1ac: Fixed the lack of `secrets` to fetch from the kubernetes api by adding option to specify additional Objects which are not part of Default Objects
- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/plugin-kubernetes-common@0.9.3-next.0
  - @backstage/plugin-kubernetes-node@0.2.3-next.0
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-kubernetes-cluster@0.0.22-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-kubernetes-react@0.5.4-next.0
  - @backstage/plugin-kubernetes-common@0.9.3-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/plugin-catalog-react@1.15.2-next.0

## @backstage/plugin-kubernetes-common@0.9.3-next.0

### Patch Changes

- ac0e1ac: Fixed the lack of `secrets` to fetch from the kubernetes api by adding option to specify additional Objects which are not part of Default Objects
- Updated dependencies
  - @backstage/catalog-model@1.7.3
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-kubernetes-node@0.2.3-next.0

### Patch Changes

- ac0e1ac: Fixed the lack of `secrets` to fetch from the kubernetes api by adding option to specify additional Objects which are not part of Default Objects
- Updated dependencies
  - @backstage/plugin-kubernetes-common@0.9.3-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/types@1.2.1

## @backstage/plugin-kubernetes-react@0.5.4-next.0

### Patch Changes

- f35a754: Fixed bug in string-to-integer conversion to properly handle decimal values with BigInt.
- Updated dependencies
  - @backstage/plugin-kubernetes-common@0.9.3-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-notifications@0.5.2-next.0

### Patch Changes

- 438c36c: added topic filter for notifications
- Updated dependencies
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/errors@1.2.7
  - @backstage/theme@0.6.3
  - @backstage/types@1.2.1
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-signals-react@0.0.9

## @backstage/plugin-notifications-backend@0.5.2-next.0

### Patch Changes

- 438c36c: added topic filter for notifications
- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-events-node@0.4.8-next.0
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-notifications-node@0.2.12-next.0
  - @backstage/plugin-signals-node@0.1.17-next.0

## @backstage/plugin-notifications-backend-module-email@0.3.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-notifications-node@0.2.12-next.0

## @backstage/plugin-notifications-node@0.2.12-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-signals-node@0.1.17-next.0

## @backstage/plugin-org@0.6.36-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/core-compat-api@0.3.6-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-react@1.15.2-next.0

## @backstage/plugin-org-react@0.1.35-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/plugin-catalog-react@1.15.2-next.0

## @backstage/plugin-permission-backend@0.5.54-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-permission-backend-module-allow-all-policy@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-permission-node@0.8.8-next.0

### Patch Changes

- 049d5d4: The returned router from `createPermissionIntegrationRouter` is now mutable, allowing for permissions and resources to be added after creation of the router.
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-proxy-backend@0.5.11-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/plugin-proxy-node@0.1.1-next.0

## @backstage/plugin-proxy-node@0.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0

## @backstage/plugin-scaffolder@1.27.5-next.0

### Patch Changes

- 3107f1f: Fixed a bug in the BitbucketRepoBranchPicker component that crashed the scaffolder
- 3edf7e7: Add schema output return type to the `makeFieldSchema` function return
- Updated dependencies
  - @backstage/plugin-scaffolder-react@1.14.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-compat-api@0.3.6-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-react@1.2.3
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-react@1.15.2-next.0
  - @backstage/plugin-permission-react@0.4.30
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-scaffolder-backend-module-azure@0.2.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.6-next.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.6-next.0

## @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.27

## @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.3.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.0-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-backend-module-gcp@0.2.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-gerrit@0.2.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-gitea@0.2.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-github@0.5.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-gitlab@0.7.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-notifications@0.1.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-notifications-node@0.2.12-next.0

## @backstage/plugin-scaffolder-backend-module-rails@0.5.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-backend-module-sentry@0.2.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-scaffolder-backend-module-yeoman@0.4.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/types@1.2.1
  - @backstage/plugin-scaffolder-node-test-utils@0.1.19-next.0

## @backstage/plugin-scaffolder-node-test-utils@0.1.19-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-test-utils@1.3.0-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-react@1.14.4-next.0

### Patch Changes

- 3edf7e7: Add schema output return type to the `makeFieldSchema` function return
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/theme@0.6.3
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.10
  - @backstage/plugin-catalog-react@1.15.2-next.0
  - @backstage/plugin-permission-react@0.4.30
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-search@1.4.23-next.0

### Patch Changes

- 63e1012: Added new extension points to extend search filters `SearchFilterBlueprint` and `SearchFilterResultTypeBlueprint`
- Updated dependencies
  - @backstage/plugin-search-react@1.8.6-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/core-compat-api@0.3.6-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.10
  - @backstage/plugin-catalog-react@1.15.2-next.0
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend@1.8.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/backend-defaults@0.8.0-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/backend-openapi-utils@0.4.2-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-backend-node@1.3.8-next.0
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-catalog@0.3.1-next.0

### Patch Changes

- eee8d76: Modified the logic for generating the location URL by encoding the entity property values with `encodeURIComponent`. This enhancement improves the safety and reliability of the URL.
- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-backend-node@1.3.8-next.0
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-elasticsearch@1.6.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/integration-aws-node@0.1.15
  - @backstage/plugin-search-backend-node@1.3.8-next.0
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-explore@0.2.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/plugin-search-backend-node@1.3.8-next.0
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-pg@0.5.41-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/plugin-search-backend-node@1.3.8-next.0
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-stack-overflow-collator@0.3.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/plugin-search-backend-node@1.3.8-next.0
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-techdocs@0.3.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/plugin-techdocs-node@1.13.0-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-backend-node@1.3.8-next.0
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-node@1.3.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-react@1.8.6-next.0

### Patch Changes

- 63e1012: Added new extension points to extend search filters `SearchFilterBlueprint` and `SearchFilterResultTypeBlueprint`
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/theme@0.6.3
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.10
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-signals-backend@0.3.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-events-node@0.4.8-next.0
  - @backstage/plugin-signals-node@0.1.17-next.0

## @backstage/plugin-signals-node@0.1.17-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-techdocs@1.12.2-next.0

### Patch Changes

- f4be934: Changed the base URL in addLinkClickListener from window.location.origin to app.baseUrl for improved path handling. This fixes an issue where Backstage, when running on a subpath, was unable to handle non-Backstage URLs of the same origin correctly.

- 1f40e6b: Add optional props to `TechDocCustomHome` to allow for more flexibility:

  ```tsx
  import { TechDocsCustomHome } from '@backstage/plugin-techdocs';
  //...

  const options = { emptyRowsWhenPaging: false };
  const linkDestination = (entity: Entity): string | undefined => {
    return entity.metadata.annotations?.['external-docs'];
  };
  const techDocsTabsConfig = [
    {
      label: 'Recommended Documentation',
      panels: [
        {
          title: 'Golden Path',
          description: 'Documentation about standards to follow',
          panelType: 'DocsCardGrid',
          panelProps: { CustomHeader: () => <ContentHeader title='Golden Path'/> },
          filterPredicate: entity =>
            entity?.metadata?.tags?.includes('golden-path') ?? false,
        },
        {
          title: 'Recommended',
          description: 'Useful documentation',
          panelType: 'InfoCardGrid',
          panelProps: {
            CustomHeader: () => <ContentHeader title='Recommended' />
            linkDestination: linkDestination,
          },
          filterPredicate: entity =>
            entity?.metadata?.tags?.includes('recommended') ?? false,
        },
      ],
    },
    {
      label: 'Browse All',
      panels: [
        {
          description: 'Browse all docs',
          filterPredicate: filterEntity,
          panelType: 'TechDocsIndexPage',
          title: 'All',
          panelProps: { PageWrapper: React.Fragment, CustomHeader: React.Fragment, options: options },
        },
      ],
    },
  ];

  const AppRoutes = () => {
    <FlatRoutes>
      <Route
        path="/docs"
        element={
          <TechDocsCustomHome
            tabsConfig={techDocsTabsConfig}
            filter={{
              kind: ['Location', 'Resource', 'Component'],
              'metadata.annotations.featured-docs': CATALOG_FILTER_EXISTS,
            }}
            CustomPageWrapper={({ children }: React.PropsWithChildren<{}>) => (<PageWithHeader title="Docs" themeId="documentation">{children}</PageWithHeader>)}
          />
        }
      />
    </FlatRoutes>;
  };
  ```

  Add new Grid option called `InfoCardGrid` which is a more customizable card option for the Docs grid.

  ```tsx
  <InfoCardGrid
    entities={entities}
    linkContent="Learn more"
    linkDestination={entity => entity.metadata['external-docs']}
  />
  ```

  Expose existing `CustomDocsPanel` so that it can be used independently if desired.

  ```tsx
  const panels: PanelConfig[] = [
    {
      description: '',
      filterPredicate: entity => {},
      panelType: 'InfoCardGrid',
      title: 'Standards',
      panelProps: {
            CustomHeader: () => <ContentHeader title='Recommended' />
            linkDestination: linkDestination,
          },
    },
    {
      description: '',
      filterPredicate: entity => {},
      panelType: 'DocsCardGrid',
      title: 'Contribute',
    },
  ];
  {
    panels.map((config, index) => (
      <CustomDocsPanel
        key={index}
        config={config}
        entities={!!entities ? entities : []}
        index={index}
      />
    ));
  }
  ```

- Updated dependencies
  - @backstage/plugin-search-react@1.8.6-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-compat-api@0.3.6-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-react@1.2.3
  - @backstage/theme@0.6.3
  - @backstage/plugin-auth-react@0.1.11
  - @backstage/plugin-catalog-react@1.15.2-next.0
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-techdocs-common@0.1.0
  - @backstage/plugin-techdocs-react@1.2.13

## @backstage/plugin-techdocs-addons-test-utils@1.0.45-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-react@1.8.6-next.0
  - @backstage/plugin-techdocs@1.12.2-next.0
  - @backstage/core-app-api@1.15.4
  - @backstage/core-plugin-api@1.10.3
  - @backstage/integration-react@1.2.3
  - @backstage/test-utils@1.7.4
  - @backstage/plugin-catalog@1.26.2-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.0
  - @backstage/plugin-techdocs-react@1.2.13

## @backstage/plugin-techdocs-backend@1.11.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/plugin-techdocs-node@1.13.0-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-backend-module-techdocs@0.3.6-next.0
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-user-settings@0.8.19-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/core-app-api@1.15.4
  - @backstage/core-compat-api@0.3.6-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/errors@1.2.7
  - @backstage/theme@0.6.3
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-react@1.15.2-next.0
  - @backstage/plugin-signals-react@0.0.9
  - @backstage/plugin-user-settings-common@0.0.1

## @backstage/plugin-user-settings-backend@0.2.30-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.0-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-signals-node@0.1.17-next.0
  - @backstage/plugin-user-settings-common@0.0.1

## example-app@0.2.106-next.0

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.30.0-next.0
  - @backstage/plugin-notifications@0.5.2-next.0
  - @backstage/plugin-search-react@1.8.6-next.0
  - @backstage/plugin-search@1.4.23-next.0
  - @backstage/plugin-techdocs@1.12.2-next.0
  - @backstage/plugin-scaffolder@1.27.5-next.0
  - @backstage/plugin-scaffolder-react@1.14.4-next.0
  - @backstage/frontend-app-api@0.10.5-next.0
  - @backstage/app-defaults@1.5.16
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-app-api@1.15.4
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/integration-react@1.2.3
  - @backstage/theme@0.6.3
  - @backstage/plugin-api-docs@0.12.4-next.0
  - @backstage/plugin-auth-react@0.1.11
  - @backstage/plugin-catalog@1.26.2-next.0
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-graph@0.4.16-next.0
  - @backstage/plugin-catalog-import@0.12.10-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.0
  - @backstage/plugin-catalog-unprocessed-entities@0.2.13
  - @backstage/plugin-devtools@0.1.24-next.0
  - @backstage/plugin-home@0.8.5-next.0
  - @backstage/plugin-kubernetes@0.12.4-next.0
  - @backstage/plugin-kubernetes-cluster@0.0.22-next.0
  - @backstage/plugin-org@0.6.36-next.0
  - @backstage/plugin-permission-react@0.4.30
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-signals@0.0.15
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.20
  - @backstage/plugin-techdocs-react@1.2.13
  - @backstage/plugin-user-settings@0.8.19-next.0

## example-app-next@0.0.20-next.0

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.30.0-next.0
  - @backstage/plugin-notifications@0.5.2-next.0
  - @backstage/plugin-search-react@1.8.6-next.0
  - @backstage/plugin-search@1.4.23-next.0
  - @backstage/plugin-techdocs@1.12.2-next.0
  - @backstage/plugin-scaffolder@1.27.5-next.0
  - @backstage/plugin-scaffolder-react@1.14.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/frontend-app-api@0.10.5-next.0
  - @backstage/app-defaults@1.5.16
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-app-api@1.15.4
  - @backstage/core-compat-api@0.3.6-next.0
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/frontend-defaults@0.1.6-next.0
  - @backstage/integration-react@1.2.3
  - @backstage/theme@0.6.3
  - @backstage/plugin-api-docs@0.12.4-next.0
  - @backstage/plugin-app@0.1.6-next.0
  - @backstage/plugin-app-visualizer@0.1.16-next.0
  - @backstage/plugin-auth-react@0.1.11
  - @backstage/plugin-catalog@1.26.2-next.0
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-graph@0.4.16-next.0
  - @backstage/plugin-catalog-import@0.12.10-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.0
  - @backstage/plugin-catalog-unprocessed-entities@0.2.13
  - @backstage/plugin-home@0.8.5-next.0
  - @backstage/plugin-kubernetes@0.12.4-next.0
  - @backstage/plugin-kubernetes-cluster@0.0.22-next.0
  - @backstage/plugin-org@0.6.36-next.0
  - @backstage/plugin-permission-react@0.4.30
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-signals@0.0.15
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.20
  - @backstage/plugin-techdocs-react@1.2.13
  - @backstage/plugin-user-settings@0.8.19-next.0

## app-next-example-plugin@0.0.20-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/core-components@0.16.3

## example-backend@0.0.35-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/plugin-notifications-backend@0.5.2-next.0
  - @backstage/backend-defaults@0.8.0-next.0
  - @backstage/plugin-catalog-backend@1.31.0-next.0
  - @backstage/plugin-kubernetes-backend@0.19.3-next.0
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.5-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-backend@1.30.0-next.0
  - @backstage/plugin-catalog-backend-module-openapi@0.2.7-next.0
  - @backstage/plugin-search-backend-module-catalog@0.3.1-next.0
  - @backstage/plugin-devtools-backend@0.5.2-next.0
  - @backstage/plugin-permission-backend@0.5.54-next.0
  - @backstage/plugin-permission-backend-module-allow-all-policy@0.2.5-next.0
  - @backstage/plugin-search-backend@1.8.2-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-app-backend@0.4.5-next.0
  - @backstage/plugin-auth-backend@0.24.3-next.0
  - @backstage/plugin-auth-backend-module-github-provider@0.2.5-next.0
  - @backstage/plugin-auth-backend-module-guest-provider@0.2.5-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-catalog-backend-module-backstage-openapi@0.4.5-next.0
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.5-next.0
  - @backstage/plugin-events-backend@0.4.2-next.0
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-proxy-backend@0.5.11-next.0
  - @backstage/plugin-scaffolder-backend-module-github@0.5.6-next.0
  - @backstage/plugin-scaffolder-backend-module-notifications@0.1.7-next.0
  - @backstage/plugin-search-backend-module-explore@0.2.8-next.0
  - @backstage/plugin-search-backend-module-techdocs@0.3.6-next.0
  - @backstage/plugin-search-backend-node@1.3.8-next.0
  - @backstage/plugin-signals-backend@0.3.1-next.0
  - @backstage/plugin-techdocs-backend@1.11.6-next.0

## example-backend-legacy@0.2.107-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/backend-defaults@0.8.0-next.0
  - @backstage/plugin-catalog-backend@1.31.0-next.0
  - @backstage/plugin-kubernetes-backend@0.19.3-next.0
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.5-next.0
  - @backstage/plugin-catalog-node@1.15.2-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-scaffolder-backend@1.30.0-next.0
  - @backstage/plugin-search-backend-module-catalog@0.3.1-next.0
  - @backstage/plugin-permission-backend@0.5.54-next.0
  - @backstage/plugin-search-backend@1.8.2-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-app-backend@0.4.5-next.0
  - @backstage/plugin-auth-backend@0.24.3-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.5-next.0
  - @backstage/plugin-events-backend@0.4.2-next.0
  - @backstage/plugin-events-node@0.4.8-next.0
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-proxy-backend@0.5.11-next.0
  - @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.6-next.0
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.7.2-next.0
  - @backstage/plugin-scaffolder-backend-module-rails@0.5.6-next.0
  - @backstage/plugin-search-backend-module-elasticsearch@1.6.5-next.0
  - @backstage/plugin-search-backend-module-explore@0.2.8-next.0
  - @backstage/plugin-search-backend-module-pg@0.5.41-next.0
  - @backstage/plugin-search-backend-module-techdocs@0.3.6-next.0
  - @backstage/plugin-search-backend-node@1.3.8-next.0
  - @backstage/plugin-signals-backend@0.3.1-next.0
  - @backstage/plugin-signals-node@0.1.17-next.0
  - @backstage/plugin-techdocs-backend@1.11.6-next.0

## e2e-test@0.2.25-next.0

### Patch Changes

- Updated dependencies
  - @backstage/create-app@0.5.25-next.0
  - @backstage/cli-common@0.1.15
  - @backstage/errors@1.2.7

## @internal/frontend@0.0.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.0
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.10

## @internal/scaffolder@0.0.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-react@1.14.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.0

## techdocs-cli-embedded-app@0.2.105-next.0

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.30.0-next.0
  - @backstage/plugin-techdocs@1.12.2-next.0
  - @backstage/app-defaults@1.5.16
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-app-api@1.15.4
  - @backstage/core-components@0.16.3
  - @backstage/core-plugin-api@1.10.3
  - @backstage/integration-react@1.2.3
  - @backstage/test-utils@1.7.4
  - @backstage/theme@0.6.3
  - @backstage/plugin-catalog@1.26.2-next.0
  - @backstage/plugin-techdocs-react@1.2.13

## @internal/plugin-todo-list-backend@1.0.36-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/errors@1.2.7
  - @backstage/plugin-auth-node@0.5.7-next.0
