# Release v1.9.0-next.1

## @backstage/backend-common@0.17.0-next.1

### Minor Changes

- de8a975911: Changed to use native `AbortController` and `AbortSignal` from Node.js, instead
  of the one from `node-abort-controller`. This is possible now that the minimum
  supported Node.js version of the project is 16.

  Note that their interfaces are very slightly different, but typically not in a
  way that matters to consumers. If you see any typescript errors as a direct
  result from this, they are compatible with each other in the ways that we
  interact with them, and should be possible to type-cast across without ill
  effects.

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- 1f2b2de3fe: exported KubernetesContainerRunner, KubernetesContainerRunnerOptions, KubernetesContainerRunnerMountBase
- 6b82598bd8: Added the ability to understand Job Artifact URLs to the GitLab integration
- 8646067e07: Fixed `SingleHostDiscovery` so that it properly handles single-string `backend.listen` configurations such as `:80`.
- Updated dependencies
  - @backstage/types@1.0.2-next.1
  - @backstage/config-loader@1.1.7-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/cli-common@0.1.10
  - @backstage/errors@1.1.4-next.1

## @backstage/backend-tasks@0.4.0-next.1

### Minor Changes

- de8a975911: Changed to use native `AbortController` and `AbortSignal` from Node.js, instead
  of the one from `node-abort-controller`. This is possible now that the minimum
  supported Node.js version of the project is 16.

  Note that their interfaces are very slightly different, but typically not in a
  way that matters to consumers. If you see any typescript errors as a direct
  result from this, they are compatible with each other in the ways that we
  interact with them, and should be possible to type-cast across without ill
  effects.

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/repo-tools@0.1.0-next.1

### Minor Changes

- 03843259b4: Api reference documentation improvements

  - breadcrumbs links semantics as code spans
  - new `@config` annotation to describe related config keys

### Patch Changes

- 71f80eb354: add the command type-deps to the repo tool package.
- Updated dependencies
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-catalog-backend@1.6.0-next.1

### Minor Changes

- c395abb5b2: The catalog no longer stops after the first processor `validateEntityKind`
  method returns `true` when validating entity kind shapes. Instead, it continues
  through all registered processors that have this method, and requires that _at
  least one_ of them returned true.

  The old behavior of stopping early made it harder to extend existing core kinds
  with additional fields, since the `BuiltinKindsEntityProcessor` is always
  present at the top of the processing chain and ensures that your additional
  validation code would never be run.

  This is technically a breaking change, although it should not affect anybody
  under normal circumstances, except if you had problematic validation code that
  you were unaware that it was not being run. That code may now start to exhibit
  those problems.

  If you need to disable this new behavior, `CatalogBuilder` as used in your
  `packages/backend/src/plugins/catalog.ts` file now has a
  `useLegacySingleProcessorValidation()` method to go back to the old behavior.

  ```diff
   const builder = await CatalogBuilder.create(env);
  +builder.useLegacySingleProcessorValidation();
  ```

### Patch Changes

- 2a8e3cc0b5: Optimize `Stitcher` process to be more memory efficient
- 5b3e2afa45: Fixed deprecated use of `substr` into `substring`.
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/plugin-catalog-node@1.2.2-next.1
  - @backstage/plugin-permission-node@0.7.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1
  - @backstage/plugin-scaffolder-common@1.2.3-next.1
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-catalog-backend-module-incremental-ingestion@0.1.0-next.0

### Minor Changes

- 98c643a1a2: Introduces incremental entity providers, which are used for streaming very large data sources into the catalog.

### Patch Changes

- de8a975911: Changed to use native `AbortController` and `AbortSignal` from Node.js, instead
  of the one from `node-abort-controller`. This is possible now that the minimum
  supported Node.js version of the project is 16.

  Note that their interfaces are very slightly different, but typically not in a
  way that matters to consumers. If you see any typescript errors as a direct
  result from this, they are compatible with each other in the ways that we
  interact with them, and should be possible to type-cast across without ill
  effects.

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-catalog-backend@1.6.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/backend-test-utils@0.1.31-next.1
  - @backstage/plugin-catalog-node@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/app-defaults@1.0.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.2.1-next.1
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-permission-react@0.4.8-next.1
  - @backstage/theme@0.2.16

## @backstage/backend-app-api@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/plugin-permission-node@0.7.2-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/backend-defaults@0.1.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-app-api@0.2.4-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1

## @backstage/backend-plugin-api@0.1.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/backend-test-utils@0.1.31-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/cli@0.21.2-next.1
  - @backstage/backend-app-api@0.2.4-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/config@1.0.5-next.1

## @backstage/catalog-client@1.2.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/catalog-model@1.1.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/cli@0.21.2-next.1

### Patch Changes

- 8fffe42708: JSX and React Fast Refresh transforms are no longer enabled when bundling backend code.
- Updated dependencies
  - @backstage/types@1.0.2-next.1
  - @backstage/config-loader@1.1.7-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/release-manifests@0.0.8-next.0
  - @backstage/cli-common@0.1.10
  - @backstage/errors@1.1.4-next.1

## @backstage/config@1.0.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2-next.1

## @backstage/config-loader@1.1.7-next.1

### Patch Changes

- 40e7e6e1a2: Updated dependency `typescript-json-schema` to `^0.55.0`.
- Updated dependencies
  - @backstage/types@1.0.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/cli-common@0.1.10
  - @backstage/errors@1.1.4-next.1

## @backstage/core-app-api@1.2.1-next.1

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/version-bridge@1.0.3-next.0
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/config@1.0.5-next.1

## @backstage/core-components@0.12.1-next.1

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/version-bridge@1.0.3-next.0
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/core-plugin-api@1.1.1-next.1

### Patch Changes

- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/version-bridge@1.0.3-next.0
  - @backstage/types@1.0.2-next.1
  - @backstage/config@1.0.5-next.1

## @backstage/create-app@0.4.35-next.1

### Patch Changes

- Bumped create-app version.
- Updated dependencies
  - @backstage/cli-common@0.1.10

## @backstage/dev-utils@1.0.9-next.1

### Patch Changes

- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/core-app-api@1.2.1-next.1
  - @backstage/core-components@0.12.1-next.1
  - @backstage/test-utils@1.2.3-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/app-defaults@1.0.9-next.1
  - @backstage/integration-react@1.1.7-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/errors@1.1.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2-next.1

## @backstage/integration@1.4.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/integration-react@1.1.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @techdocs/cli@1.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-techdocs-node@1.4.3-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/cli-common@0.1.10

## @backstage/test-utils@1.2.3-next.1

### Patch Changes

- 5e238ed56a: The test utility for the plugin context called `MockPluginProvider` has been created. It will be handy in the cases when you use
  `__experimentalConfigure` in your plugin. It is experimental and exported through `@backstage/test-utils/alpha`.
- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/core-app-api@1.2.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-permission-react@0.4.8-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/types@1.0.2-next.1

### Patch Changes

- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.

## @backstage/version-bridge@1.0.3-next.0

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals

## @backstage/plugin-adr@0.2.4-next.1

### Patch Changes

- a19cffbeed: Update search links to only have header as linkable text
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/plugin-search-react@1.2.2-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/integration-react@1.1.7-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-adr-common@0.2.4-next.1
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-adr-backend@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-adr-common@0.2.4-next.1
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-adr-common@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-airbrake@0.3.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/test-utils@1.2.3-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/dev-utils@1.0.9-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-airbrake-backend@0.2.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-allure@0.1.28-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-analytics-module-ga@0.1.23-next.1

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-apache-airflow@0.2.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1

## @backstage/plugin-api-docs@0.8.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/plugin-catalog@1.6.2-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-apollo-explorer@0.1.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-app-backend@0.3.39-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/config-loader@1.1.7-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-auth-backend@0.17.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-auth-node@0.2.8-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-auth-node@0.2.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-azure-devops@0.2.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-azure-devops-backend@0.3.18-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-azure-sites@0.1.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-sites-common@0.1.0

## @backstage/plugin-azure-sites-backend@0.1.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-azure-sites-common@0.1.0

## @backstage/plugin-badges@0.2.36-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-badges-backend@0.1.33-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-bazaar@0.2.1-next.1

### Patch Changes

- 9b1891061c: `HomePageBazaarInfoCard` is now displaying `title` instead of `name`. Title is a string that doesn't have to be URL friendly.
  The BazaarOverviewCard have the new property `fullHeight`. Link in `BazaarOverviewCard`is moved to header in card.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/plugin-catalog@1.6.2-next.1
  - @backstage/cli@0.21.2-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-bazaar-backend@0.2.2-next.1

### Patch Changes

- 9b1891061c: Column `title` has replaced column `name` for `BazaarProject` in database
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/backend-test-utils@0.1.31-next.1
  - @backstage/plugin-auth-node@0.2.8-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-bitbucket-cloud-common@0.2.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.4.1-next.1

## @backstage/plugin-bitrise@0.1.39-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog@1.6.2-next.1

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- ca04d97b09: Handle refresh entity error in `AboutCard`.
- a19cffbeed: Update search links to only have header as linkable text
- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/plugin-search-react@1.2.2-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/integration-react@1.1.7-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9-next.1
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-catalog-backend-module-aws@0.1.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-catalog-backend@1.6.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/plugin-catalog-node@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-catalog-backend-module-azure@0.1.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-catalog-backend@1.6.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/plugin-catalog-node@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-catalog-backend-module-bitbucket@0.2.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-catalog-backend@1.6.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.2-next.1

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.1.6-next.1

### Patch Changes

- 4eb0bce299: Fix repo:push topic not matching `BitbucketCloudEventRouter`.
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-catalog-backend@1.6.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/plugin-catalog-node@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.2-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.1
  - @backstage/plugin-events-node@0.2.0-next.1

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.1.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-catalog-backend@1.6.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/plugin-catalog-node@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-catalog-backend-module-gerrit@0.1.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-catalog-backend@1.6.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/plugin-catalog-node@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-catalog-backend-module-github@0.2.2-next.1

### Patch Changes

- fe93cce743: Added the githubCredentialsProvider property to the GithubLocationAnalyzerOptions to be able to override the GithubCredentialsProvider.

- a0fd4af94a: Handle GitHub `push` events at the `GithubEntityProvider` by subscribing to the topic `github.push.`

  Implements `EventSubscriber` to receive events for the topic `github.push`.

  On `github.push`, the affected repository will be refreshed.
  This includes adding new Location entities, refreshing existing ones,
  and removing obsolete ones.

  Please find more information at
  <https://backstage.io/docs/integrations/github/discovery#installation-with-events-support>

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-catalog-backend@1.6.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/plugin-catalog-node@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.1
  - @backstage/plugin-events-node@0.2.0-next.1

## @backstage/plugin-catalog-backend-module-gitlab@0.1.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-catalog-backend@1.6.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/plugin-catalog-node@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-catalog-backend-module-ldap@0.5.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-catalog-backend-module-msgraph@0.4.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/plugin-catalog-node@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-model@1.1.4-next.1

## @backstage/plugin-catalog-backend-module-openapi@0.1.5-next.1

### Patch Changes

- dd995cdc72: Enabled support of resolving `$refs` in all kind of yaml documents, not only OpenAPI. This implicitly adds `$ref` resolving support for AsyncAPI specs. Thus, the `openApiPlaceholderResolver` has been renamed to `jsonSchemaRefPlaceholderResolver`.
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-catalog-backend@1.6.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-node@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-model@1.1.4-next.1

## @backstage/plugin-catalog-common@1.0.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-catalog-graph@0.2.24-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog-graphql@0.3.16-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-model@1.1.4-next.1

## @backstage/plugin-catalog-import@0.9.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/integration-react@1.1.7-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.1

## @backstage/plugin-catalog-node@1.2.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.1

## @backstage/plugin-catalog-react@1.2.2-next.1

### Patch Changes

- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/version-bridge@1.0.3-next.0
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/plugin-permission-react@0.4.8-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-cicd-statistics@0.1.14-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1

## @backstage/plugin-cicd-statistics-module-gitlab@0.1.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/plugin-cicd-statistics@0.1.14-next.1

## @backstage/plugin-circleci@0.3.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-cloudbuild@0.3.12-next.1

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-code-climate@0.1.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage@0.2.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage-backend@0.2.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-codescene@0.1.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-config-schema@0.1.35-next.1

### Patch Changes

- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-cost-insights@0.12.1-next.1

### Patch Changes

- d379b6f070: Internal refactor to avoid usage of deprecated symbols
- f97c7fd1f7: added an optional config entry `costInsights.engineerThreshold` to allow users to control the threshold value for the 'negligible' change in costs.
- a5ecdb99f0: Making a possibility to hide a trending line in a cost insights plugin
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/test-utils@1.2.3-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-cost-insights-common@0.1.1

## @backstage/plugin-dynatrace@1.0.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-events-backend@0.2.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-events-node@0.2.0-next.1

## @backstage/plugin-events-backend-module-aws-sqs@0.1.1-next.1

### Patch Changes

- dd008a10c1: Upgrade to AWS SDK for Javascript v3
- Updated dependencies
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-events-node@0.2.0-next.1

## @backstage/plugin-events-backend-module-azure@0.1.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/plugin-events-node@0.2.0-next.1

## @backstage/plugin-events-backend-module-bitbucket-cloud@0.1.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/plugin-events-node@0.2.0-next.1

## @backstage/plugin-events-backend-module-gerrit@0.1.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/plugin-events-node@0.2.0-next.1

## @backstage/plugin-events-backend-module-github@0.1.1-next.1

### Patch Changes

- 0f46ec304c: Add `createGithubSignatureValidator(config)` which can be used
  to create a validator used at an ingress for topic `github`.

  On top, there is a new `githubWebhookEventsModule` for the new backend plugin API
  which auto-registers the `HttpPostIngress` for topic `github` incl. the validator.

  Please find more information at
  <https://github.com/backstage/backstage/tree/master/plugins/events-backend-module-github/README.md>.

- Updated dependencies
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-events-node@0.2.0-next.1

## @backstage/plugin-events-backend-module-gitlab@0.1.1-next.1

### Patch Changes

- 31fe8f256a: Add `createGitlabTokenValidator(config)` which can be used
  to create a validator used at an ingress for topic `gitlab`.

  On top, there is a new `gitlabWebhookEventsModule` for the new backend plugin API
  which auto-registers the `HttpPostIngress` for topic `gitlab` incl. the validator.

  Please find more information at
  <https://github.com/backstage/backstage/tree/master/plugins/events-backend-module-gitlab/README.md>.

- Updated dependencies
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-events-node@0.2.0-next.1

## @backstage/plugin-events-backend-test-utils@0.1.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.2.0-next.1

## @backstage/plugin-events-node@0.2.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.1.5-next.1

## @backstage/plugin-explore@0.3.43-next.1

### Patch Changes

- 4dec6f16be: Added new `@backstage/plugin-explore-backend` & `@backstage/plugin-explore-common` packages.

  This deprecates the `ExploreToolsConfig` API (in `@backstage/plugin-explore-react`) which is replaced by the `ExploreApi` & `ExploreClient`. The list of `ExploreTool` data can now be provided on the backend by either using the supplied `StaticExploreToolProvider` or by implementing a custom `ExploreToolProvider`. See the [explore-backend README](https://github.com/backstage/backstage/blob/v1.9.0-next.1/plugins/explore-backend/README.md) for full details.

  NOTE: Existing installations that have customized the `ExploreToolConfig` will continue to work through the new `ExploreClient`. However, existing data should be migrated over to new `explore-backend` plugin as `ExploreToolConfig` will be removed in the future.

  BREAKING CHANGE: If you have previously installed the `explore` plugin, but not yet customized the `ExploreToolConfig` API in your `packages/app`, this will result in an empty list of tools.

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/plugin-search-react@1.2.2-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/plugin-explore-common@0.0.1-next.0
  - @backstage/plugin-explore-react@0.0.24-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-explore-backend@0.0.1-next.0

### Patch Changes

- 4dec6f16be: Added new `@backstage/plugin-explore-backend` & `@backstage/plugin-explore-common` packages.

  This deprecates the `ExploreToolsConfig` API (in `@backstage/plugin-explore-react`) which is replaced by the `ExploreApi` & `ExploreClient`. The list of `ExploreTool` data can now be provided on the backend by either using the supplied `StaticExploreToolProvider` or by implementing a custom `ExploreToolProvider`. See the [explore-backend README](https://github.com/backstage/backstage/blob/v1.9.0-next.1/plugins/explore-backend/README.md) for full details.

  NOTE: Existing installations that have customized the `ExploreToolConfig` will continue to work through the new `ExploreClient`. However, existing data should be migrated over to new `explore-backend` plugin as `ExploreToolConfig` will be removed in the future.

  BREAKING CHANGE: If you have previously installed the `explore` plugin, but not yet customized the `ExploreToolConfig` API in your `packages/app`, this will result in an empty list of tools.

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-explore-common@0.0.1-next.0
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-explore-common@0.0.1-next.0

### Patch Changes

- 4dec6f16be: Added new `@backstage/plugin-explore-backend` & `@backstage/plugin-explore-common` packages.

  This deprecates the `ExploreToolsConfig` API (in `@backstage/plugin-explore-react`) which is replaced by the `ExploreApi` & `ExploreClient`. The list of `ExploreTool` data can now be provided on the backend by either using the supplied `StaticExploreToolProvider` or by implementing a custom `ExploreToolProvider`. See the [explore-backend README](https://github.com/backstage/backstage/blob/v1.9.0-next.1/plugins/explore-backend/README.md) for full details.

  NOTE: Existing installations that have customized the `ExploreToolConfig` will continue to work through the new `ExploreClient`. However, existing data should be migrated over to new `explore-backend` plugin as `ExploreToolConfig` will be removed in the future.

  BREAKING CHANGE: If you have previously installed the `explore` plugin, but not yet customized the `ExploreToolConfig` API in your `packages/app`, this will result in an empty list of tools.

## @backstage/plugin-explore-react@0.0.24-next.1

### Patch Changes

- 4dec6f16be: Added new `@backstage/plugin-explore-backend` & `@backstage/plugin-explore-common` packages.

  This deprecates the `ExploreToolsConfig` API (in `@backstage/plugin-explore-react`) which is replaced by the `ExploreApi` & `ExploreClient`. The list of `ExploreTool` data can now be provided on the backend by either using the supplied `StaticExploreToolProvider` or by implementing a custom `ExploreToolProvider`. See the [explore-backend README](https://github.com/backstage/backstage/blob/v1.9.0-next.1/plugins/explore-backend/README.md) for full details.

  NOTE: Existing installations that have customized the `ExploreToolConfig` will continue to work through the new `ExploreClient`. However, existing data should be migrated over to new `explore-backend` plugin as `ExploreToolConfig` will be removed in the future.

  BREAKING CHANGE: If you have previously installed the `explore` plugin, but not yet customized the `ExploreToolConfig` API in your `packages/app`, this will result in an empty list of tools.

- Updated dependencies
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-explore-common@0.0.1-next.0

## @backstage/plugin-firehydrant@0.1.29-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-fossa@0.2.44-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-gcalendar@0.3.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-gcp-projects@0.3.31-next.1

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-git-release-manager@0.3.25-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-actions@0.5.12-next.1

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-deployments@0.1.43-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/integration-react@1.1.7-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-issues@0.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-pull-requests-board@0.1.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-gitops-profiles@0.3.30-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-gocd@0.1.18-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-graphiql@0.2.44-next.1

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-graphql-backend@0.1.29-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-catalog-graphql@0.3.16-next.1

## @backstage/plugin-home@0.4.28-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/plugin-stack-overflow@0.1.8-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-ilert@0.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-jenkins@0.7.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-jenkins-common@0.1.11-next.1

## @backstage/plugin-jenkins-backend@0.1.29-next.1

### Patch Changes

- cfe2b98fbe: Remove unnecessary dependency promise-any-polyfill
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-auth-node@0.2.8-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-jenkins-common@0.1.11-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-jenkins-common@0.1.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-common@1.0.9-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-kafka@0.3.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-kafka-backend@0.2.32-next.1

### Patch Changes

- 0b54dbaf7d: Internal tweak to adapt to `kafkajs` 2.2.3
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-kubernetes@0.7.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-kubernetes-common@0.4.5-next.1

## @backstage/plugin-kubernetes-backend@0.8.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/backend-test-utils@0.1.31-next.1
  - @backstage/plugin-auth-node@0.2.8-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-kubernetes-common@0.4.5-next.1

## @backstage/plugin-kubernetes-common@0.4.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.4-next.1

## @backstage/plugin-lighthouse@0.3.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic@0.3.30-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic-dashboard@0.2.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-org@0.6.2-next.1

### Patch Changes

- 67cedfe42e: Update `UserProfileCard` and `GroupProfileCard` to not render links unless the `showLinks` prop is set. The primary component for rendering links are the `EntityLinksCard` from plugin-catalog.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-org-react@0.1.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-pagerduty@0.5.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop@0.1.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop-backend@0.1.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-permission-backend@0.5.14-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-auth-node@0.2.8-next.1
  - @backstage/plugin-permission-node@0.7.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-permission-common@0.7.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-permission-node@0.7.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-auth-node@0.2.8-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-permission-react@0.4.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-playlist@0.1.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/plugin-search-react@1.2.2-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/plugin-permission-react@0.4.8-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1
  - @backstage/plugin-playlist-common@0.1.3-next.1

## @backstage/plugin-playlist-backend@0.2.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/backend-test-utils@0.1.31-next.1
  - @backstage/plugin-auth-node@0.2.8-next.1
  - @backstage/plugin-permission-node@0.7.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1
  - @backstage/plugin-playlist-common@0.1.3-next.1

## @backstage/plugin-playlist-common@0.1.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-proxy-backend@0.2.33-next.1

### Patch Changes

- 03843259b4: Documented the `createRouter` method.
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-rollbar@0.4.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-rollbar-backend@0.1.36-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-scaffolder@1.9.0-next.1

### Patch Changes

- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/integration-react@1.1.7-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/plugin-permission-react@0.4.8-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9-next.1
  - @backstage/plugin-scaffolder-common@1.2.3-next.1

## @backstage/plugin-scaffolder-backend@1.8.1-next.1

### Patch Changes

- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-catalog-backend@1.6.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/backend-plugin-api@0.1.5-next.1
  - @backstage/plugin-auth-node@0.2.8-next.1
  - @backstage/plugin-catalog-node@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-scaffolder-common@1.2.3-next.1

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.2.14-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-scaffolder-backend@1.8.1-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-scaffolder-backend-module-rails@0.4.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-scaffolder-backend@1.8.1-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-scaffolder-backend-module-yeoman@0.2.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-scaffolder-backend@1.8.1-next.1
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-scaffolder-common@1.2.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2-next.1
  - @backstage/catalog-model@1.1.4-next.1

## @backstage/plugin-search@1.0.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/version-bridge@1.0.3-next.0
  - @backstage/plugin-search-react@1.2.2-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-search-backend@1.1.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-search-backend-node@1.0.5-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-auth-node@0.2.8-next.1
  - @backstage/plugin-permission-node@0.7.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-search-backend-module-elasticsearch@1.0.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-backend-node@1.0.5-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-search-backend-module-pg@0.4.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-search-backend-node@1.0.5-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-search-backend-node@1.0.5-next.1

### Patch Changes

- de8a975911: Changed to use native `AbortController` and `AbortSignal` from Node.js, instead
  of the one from `node-abort-controller`. This is possible now that the minimum
  supported Node.js version of the project is 16.

  Note that their interfaces are very slightly different, but typically not in a
  way that matters to consumers. If you see any typescript errors as a direct
  result from this, they are compatible with each other in the ways that we
  interact with them, and should be possible to type-cast across without ill
  effects.

- 54c5836f7a: Use of `TestPipeline.withSubject()` is now deprecated. Instead, use the `fromCollator`, `fromDecorator`, or `fromIndexer` static methods to instantiate a test pipeline. You may also use the class' `withCollator`, `withDecorator`, and `withIndexer` instance methods to build test pipelines that consist of multiple test subjects.

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-search-common@1.1.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-search-react@1.2.2-next.1

### Patch Changes

- a19cffbeed: Update search links to only have header as linkable text
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/version-bridge@1.0.3-next.0
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-sentry@0.4.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-shortcuts@0.3.4-next.1

### Patch Changes

- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-sonarqube@0.5.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-sonarqube-backend@0.1.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-splunk-on-call@0.4.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-stack-overflow@0.1.8-next.1

### Patch Changes

- a19cffbeed: Update search links to only have header as linkable text
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-home@0.4.28-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-stack-overflow-backend@0.1.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.21.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-tech-insights@0.3.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-tech-insights-common@0.2.9-next.1

## @backstage/plugin-tech-insights-backend@0.5.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-tech-insights-node@0.3.7-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-tech-insights-common@0.2.9-next.1

## @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.23-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-tech-insights-node@0.3.7-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-tech-insights-common@0.2.9-next.1

## @backstage/plugin-tech-insights-common@0.2.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-tech-insights-node@0.3.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-tech-insights-common@0.2.9-next.1

## @backstage/plugin-tech-radar@0.5.19-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs@1.4.1-next.1

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- a19cffbeed: Update search links to only have header as linkable text
- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/plugin-search-react@1.2.2-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/integration-react@1.1.7-next.1
  - @backstage/plugin-techdocs-react@1.0.7-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-techdocs-addons-test-utils@1.0.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.2.1-next.1
  - @backstage/core-components@0.12.1-next.1
  - @backstage/plugin-catalog@1.6.2-next.1
  - @backstage/plugin-techdocs@1.4.1-next.1
  - @backstage/test-utils@1.2.3-next.1
  - @backstage/plugin-search-react@1.2.2-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/integration-react@1.1.7-next.1
  - @backstage/plugin-techdocs-react@1.0.7-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-backend@1.4.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-techdocs-node@1.4.3-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-techdocs-module-addons-contrib@1.0.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/integration-react@1.1.7-next.1
  - @backstage/plugin-techdocs-react@1.0.7-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-node@1.4.3-next.1

### Patch Changes

- 0a61aab172: Bump dependency @azure/identity to next minor

- 37931c33ce: Upgrade to AWS SDK for Javascript v3

  Techdocs support for AWS S3 now requires defining the AWS region to connect to.
  If `techdocs.publisher.awsS3.region` is missing from the config, the AWS environment variable `AWS_REGION` will be used.

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-search-common@1.1.2-next.1

## @backstage/plugin-techdocs-react@1.0.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/version-bridge@1.0.3-next.0
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-model@1.1.4-next.1

## @backstage/plugin-todo@0.2.14-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-todo-backend@0.1.36-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-user-settings@0.6.0-next.1

### Patch Changes

- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/core-app-api@1.2.1-next.1
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-user-settings-backend@0.1.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-auth-node@0.2.8-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-vault@0.1.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-vault-backend@0.2.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/backend-test-utils@0.1.31-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-xcmetrics@0.2.32-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## example-app@0.2.78-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.2.1-next.1
  - @backstage/core-components@0.12.1-next.1
  - @backstage/plugin-cloudbuild@0.3.12-next.1
  - @backstage/plugin-gcp-projects@0.3.31-next.1
  - @backstage/plugin-github-actions@0.5.12-next.1
  - @backstage/plugin-graphiql@0.2.44-next.1
  - @backstage/plugin-techdocs@1.4.1-next.1
  - @backstage/plugin-cost-insights@0.12.1-next.1
  - @backstage/plugin-search-react@1.2.2-next.1
  - @backstage/plugin-stack-overflow@0.1.8-next.1
  - @backstage/cli@0.21.2-next.1
  - @backstage/plugin-org@0.6.2-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1
  - @backstage/plugin-scaffolder@1.9.0-next.1
  - @backstage/plugin-shortcuts@0.3.4-next.1
  - @backstage/plugin-user-settings@0.6.0-next.1
  - @backstage/plugin-explore@0.3.43-next.1
  - @backstage/app-defaults@1.0.9-next.1
  - @backstage/plugin-apache-airflow@0.2.5-next.1
  - @backstage/plugin-api-docs@0.8.12-next.1
  - @backstage/plugin-azure-devops@0.2.3-next.1
  - @backstage/plugin-azure-sites@0.1.1-next.1
  - @backstage/plugin-badges@0.2.36-next.1
  - @backstage/plugin-catalog-graph@0.2.24-next.1
  - @backstage/plugin-catalog-import@0.9.2-next.1
  - @backstage/plugin-circleci@0.3.12-next.1
  - @backstage/plugin-code-coverage@0.2.5-next.1
  - @backstage/plugin-dynatrace@1.0.2-next.1
  - @backstage/plugin-gcalendar@0.3.8-next.1
  - @backstage/plugin-gocd@0.1.18-next.1
  - @backstage/plugin-home@0.4.28-next.1
  - @backstage/plugin-jenkins@0.7.11-next.1
  - @backstage/plugin-kafka@0.3.12-next.1
  - @backstage/plugin-kubernetes@0.7.5-next.1
  - @backstage/plugin-lighthouse@0.3.12-next.1
  - @backstage/plugin-newrelic@0.3.30-next.1
  - @backstage/plugin-pagerduty@0.5.5-next.1
  - @backstage/plugin-playlist@0.1.3-next.1
  - @backstage/plugin-rollbar@0.4.12-next.1
  - @backstage/plugin-search@1.0.5-next.1
  - @backstage/plugin-sentry@0.4.5-next.1
  - @backstage/plugin-tech-insights@0.3.4-next.1
  - @backstage/plugin-tech-radar@0.5.19-next.1
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.7-next.1
  - @backstage/plugin-todo@0.2.14-next.1
  - @backstage/integration-react@1.1.7-next.1
  - @backstage/plugin-airbrake@0.3.12-next.1
  - @backstage/plugin-newrelic-dashboard@0.2.5-next.1
  - @backstage/plugin-techdocs-react@1.0.7-next.1
  - @internal/plugin-catalog-customized@0.0.5-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-permission-react@0.4.8-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9-next.1
  - @backstage/plugin-search-common@1.1.2-next.1

## example-backend@0.2.78-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-catalog-backend@1.6.0-next.1
  - @backstage/plugin-kafka-backend@0.2.32-next.1
  - @backstage/backend-tasks@0.4.0-next.1
  - @backstage/plugin-search-backend-node@1.0.5-next.1
  - @backstage/plugin-jenkins-backend@0.1.29-next.1
  - @backstage/plugin-scaffolder-backend@1.8.1-next.1
  - @backstage/plugin-explore-backend@0.0.1-next.0
  - @backstage/plugin-proxy-backend@0.2.33-next.1
  - @backstage/plugin-app-backend@0.3.39-next.1
  - @backstage/plugin-auth-backend@0.17.2-next.1
  - @backstage/plugin-auth-node@0.2.8-next.1
  - @backstage/plugin-azure-devops-backend@0.3.18-next.1
  - @backstage/plugin-azure-sites-backend@0.1.1-next.1
  - @backstage/plugin-badges-backend@0.1.33-next.1
  - @backstage/plugin-catalog-node@1.2.2-next.1
  - @backstage/plugin-code-coverage-backend@0.2.5-next.1
  - @backstage/plugin-events-backend@0.2.0-next.1
  - @backstage/plugin-graphql-backend@0.1.29-next.1
  - @backstage/plugin-kubernetes-backend@0.8.1-next.1
  - @backstage/plugin-permission-backend@0.5.14-next.1
  - @backstage/plugin-permission-node@0.7.2-next.1
  - @backstage/plugin-playlist-backend@0.2.2-next.1
  - @backstage/plugin-rollbar-backend@0.1.36-next.1
  - @backstage/plugin-scaffolder-backend-module-rails@0.4.7-next.1
  - @backstage/plugin-search-backend@1.1.2-next.1
  - @backstage/plugin-search-backend-module-elasticsearch@1.0.5-next.1
  - @backstage/plugin-search-backend-module-pg@0.4.3-next.1
  - @backstage/plugin-tech-insights-backend@0.5.5-next.1
  - @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.23-next.1
  - @backstage/plugin-tech-insights-node@0.3.7-next.1
  - @backstage/plugin-techdocs-backend@1.4.2-next.1
  - @backstage/plugin-todo-backend@0.1.36-next.1
  - example-app@0.2.78-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/plugin-events-node@0.2.0-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1
  - @backstage/plugin-search-common@1.1.2-next.1

## example-backend-next@0.0.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.1
  - @backstage/plugin-scaffolder-backend@1.8.1-next.1
  - @backstage/plugin-app-backend@0.3.39-next.1
  - @backstage/backend-defaults@0.1.4-next.1

## techdocs-cli-embedded-app@0.2.77-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.2.1-next.1
  - @backstage/core-components@0.12.1-next.1
  - @backstage/plugin-catalog@1.6.2-next.1
  - @backstage/plugin-techdocs@1.4.1-next.1
  - @backstage/test-utils@1.2.3-next.1
  - @backstage/cli@0.21.2-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/app-defaults@1.0.9-next.1
  - @backstage/integration-react@1.1.7-next.1
  - @backstage/plugin-techdocs-react@1.0.7-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/theme@0.2.16

## @internal/plugin-catalog-customized@0.0.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.6.2-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.1

## @internal/plugin-todo-list@1.0.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.1
  - @backstage/core-plugin-api@1.1.1-next.1
  - @backstage/theme@0.2.16

## @internal/plugin-todo-list-backend@1.0.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.1
  - @backstage/plugin-auth-node@0.2.8-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @internal/plugin-todo-list-common@1.0.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.1
