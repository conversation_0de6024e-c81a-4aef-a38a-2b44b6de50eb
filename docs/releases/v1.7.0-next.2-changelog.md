# Release v1.7.0-next.2

## @backstage/plugin-bazaar-backend@0.2.0-next.2

### Minor Changes

- 8554533546: **BREAKING** The bazaar-backend `createRouter` now requires that the `identityApi` is passed to the router.

  These changes are **required** to `packages/backend/src/plugins/bazaar.ts`

  The user entity ref is now added to the members table and is taken from the requesting user using the `identityApi`.

  ```diff
  import { PluginEnvironment } from '../types';
  import { createRouter } from '@backstage/plugin-bazaar-backend';
  import { Router } from 'express';

  export default async function createPlugin(
    env: PluginEnvironment,
  ): Promise<Router> {
    return await createRouter({
      logger: env.logger,
      config: env.config,
      database: env.database,
  +   identity: env.identity,
    });
  }
  ```

### Patch Changes

- f7c2855d76: Router now also has endpoint `getLatestProjects` that takes a limit of projects as prop.
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/backend-test-utils@0.1.29-next.2
  - @backstage/plugin-auth-node@0.2.6-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2

## @backstage/plugin-catalog-backend@1.5.0-next.2

### Minor Changes

- b2e6cb6acf: Added a new method `addLocationAnalyzers` to the `CatalogBuilder`. With this you can add location analyzers to your catalog. These analyzers will be used by the /analyze-location endpoint to decide if the provided URL contains any catalog-info.yaml files already or not.

  Moved the following types from this package to `@backstage/plugin-catalog-backend`.

  - AnalyzeLocationResponse
  - AnalyzeLocationRequest
  - AnalyzeLocationExistingEntity
  - AnalyzeLocationGenerateEntity
  - AnalyzeLocationEntityField

- eb25f7e12d: The exported permission rules and the API of `createCatalogConditionalDecision` have changed to reflect the breaking changes made to the `PermissionRule` type. Note that all involved types are exported from `@backstage/plugin-catalog-backend/alpha`

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/plugin-catalog-node@1.2.0-next.2
  - @backstage/plugin-catalog-common@1.0.7-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-permission-common@0.7.0-next.2
  - @backstage/plugin-permission-node@0.7.0-next.2
  - @backstage/backend-plugin-api@0.1.3-next.2
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/types@1.0.0
  - @backstage/plugin-scaffolder-common@1.2.1-next.2

## @backstage/plugin-catalog-import@0.9.0-next.2

### Minor Changes

- b2e6cb6acf: **Breaking**
  Moved the code search for the existing catalog-info.yaml files to the backend from the frontend. It means it will use the configured GitHub integration's credentials.

  Add the following to your `CatalogBuilder` to have the repo URL ingestion working again.

  ```ts
  // catalog.ts
  import { GitHubLocationAnalyzer } from '@backstage/plugin-catalog-backend-module-github';
  ...
    builder.addLocationAnalyzers(
      new GitHubLocationAnalyzer({
        discovery: env.discovery,
        config: env.config,
      }),
    );
  ...
  ```

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-common@1.0.7-next.2
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/integration-react@1.1.5-next.2

## @backstage/plugin-catalog-node@1.2.0-next.2

### Minor Changes

- 404366c853: Deprecated the `LocationSpec` type. It got moved from this package to the `@backstage/plugin-catalog-common` so make sure imports are updated.

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-common@1.0.7-next.2
  - @backstage/backend-plugin-api@0.1.3-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-permission-common@0.7.0-next.2

### Minor Changes

- 46b4a72cee: **BREAKING**: When defining permission rules, it's now necessary to provide a [ZodSchema](https://github.com/colinhacks/zod) that specifies the parameters the rule expects. This has been added to help better describe the parameters in the response of the metadata endpoint and to validate the parameters before a rule is executed.

  To help with this, we have also made a change to the API of permission rules. Before, the permission rules `toQuery` and `apply` signature expected parameters to be separate arguments, like so...

  ```ts
  createPermissionRule({
    apply: (resource, foo, bar) => true,
    toQuery: (foo, bar) => {},
  });
  ```

  The API has now changed to expect the parameters as a single object

  ```ts
  createPermissionRule({
    paramSchema: z.object({
      foo: z.string().describe('Foo value to match'),
      bar: z.string().describe('Bar value to match'),
    }),
    apply: (resource, { foo, bar }) => true,
    toQuery: ({ foo, bar }) => {},
  });
  ```

  One final change made is to limit the possible values for a parameter to primitives and arrays of primitives.

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-permission-node@0.7.0-next.2

### Minor Changes

- 46b4a72cee: **BREAKING**: When defining permission rules, it's now necessary to provide a [ZodSchema](https://github.com/colinhacks/zod) that specifies the parameters the rule expects. This has been added to help better describe the parameters in the response of the metadata endpoint and to validate the parameters before a rule is executed.

  To help with this, we have also made a change to the API of permission rules. Before, the permission rules `toQuery` and `apply` signature expected parameters to be separate arguments, like so...

  ```ts
  createPermissionRule({
    apply: (resource, foo, bar) => true,
    toQuery: (foo, bar) => {},
  });
  ```

  The API has now changed to expect the parameters as a single object

  ```ts
  createPermissionRule({
    paramSchema: z.object({
      foo: z.string().describe('Foo value to match'),
      bar: z.string().describe('Bar value to match'),
    }),
    apply: (resource, { foo, bar }) => true,
    toQuery: ({ foo, bar }) => {},
  });
  ```

  One final change made is to limit the possible values for a parameter to primitives and arrays of primitives.

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-permission-common@0.7.0-next.2
  - @backstage/plugin-auth-node@0.2.6-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2

## @backstage/plugin-playlist-backend@0.2.0-next.2

### Minor Changes

- eb25f7e12d: **BREAKING** The exported permission rules have changed to reflect the breaking changes made to the PermissionRule type.

  For example, the `playlistConditions.isOwner` API has changed from:

  ```ts
  playlistConditions.isOwner(['user:default/me', 'group:default/owner']);
  ```

  to:

  ```ts
  playlistConditions.isOwner({
    owners: ['user:default/me', 'group:default/owner'],
  });
  ```

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-permission-common@0.7.0-next.2
  - @backstage/plugin-permission-node@0.7.0-next.2
  - @backstage/backend-test-utils@0.1.29-next.2
  - @backstage/plugin-auth-node@0.2.6-next.2
  - @backstage/plugin-playlist-common@0.1.1-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2

## @backstage/plugin-scaffolder-backend@1.7.0-next.2

### Minor Changes

- 17ff77154c: Update the `github:publish` action to allow passing whether pull
  requests must be up to date with the default branch before merging.
- a8e9848479: Added optional `sourcePath` parameter to `publish:gitlab:merge-request` action, `targetPath` is now optional and falls back to current workspace path.

### Patch Changes

- 4880d43e25: Fixed setting default branch for Bitbucket Server
- Updated dependencies
  - @backstage/plugin-catalog-node@1.2.0-next.2
  - @backstage/plugin-catalog-backend@1.5.0-next.2
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/backend-plugin-api@0.1.3-next.2
  - @backstage/plugin-auth-node@0.2.6-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/types@1.0.0
  - @backstage/plugin-scaffolder-common@1.2.1-next.2

## @backstage/plugin-techdocs-backend@1.4.0-next.2

### Minor Changes

- 7ced1b4076: Add optional `catalogClient` argument to `createRoute` parameters

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-common@1.0.7-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-permission-common@0.7.0-next.2
  - @backstage/plugin-techdocs-node@1.4.1-next.2
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2

## @backstage/app-defaults@1.0.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-react@0.4.6-next.2
  - @backstage/core-app-api@1.1.1-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/backend-app-api@0.2.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-permission-node@0.7.0-next.2
  - @backstage/backend-plugin-api@0.1.3-next.2
  - @backstage/errors@1.1.2-next.2

## @backstage/backend-common@0.15.2-next.2

### Patch Changes

- c44cf412de: Fix BitBucket server integration
- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.3-next.2
  - @backstage/config-loader@1.1.5-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/types@1.0.0

## @backstage/backend-defaults@0.1.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-app-api@0.2.2-next.2
  - @backstage/backend-plugin-api@0.1.3-next.2

## @backstage/backend-plugin-api@0.1.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-permission-common@0.7.0-next.2
  - @backstage/config@1.0.3-next.2

## @backstage/backend-tasks@0.3.6-next.2

### Patch Changes

- d4fea86ea3: Added new function `readTaskScheduleDefinitionFromConfig` to read `TaskScheduleDefinition` (aka. schedule) from the `Config`.
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/types@1.0.0

## @backstage/backend-test-utils@0.1.29-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/cli@0.20.0-next.2
  - @backstage/backend-app-api@0.2.2-next.2
  - @backstage/backend-plugin-api@0.1.3-next.2
  - @backstage/config@1.0.3-next.2

## @backstage/catalog-client@1.1.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/errors@1.1.2-next.2

## @backstage/catalog-model@1.1.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/types@1.0.0

## @backstage/cli@0.20.0-next.2

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.3-next.2
  - @backstage/config-loader@1.1.5-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/release-manifests@0.0.6
  - @backstage/types@1.0.0

## @backstage/codemods@0.1.40-next.2

### Patch Changes

- Updated dependencies
  - @backstage/cli-common@0.1.10

## @backstage/config@1.0.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.0

## @backstage/config-loader@1.1.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/types@1.0.0

## @backstage/core-app-api@1.1.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1

## @backstage/core-components@0.11.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16
  - @backstage/version-bridge@1.0.1

## @backstage/core-plugin-api@1.0.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.2
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1

## @backstage/create-app@0.4.32-next.2

### Patch Changes

- 01dff06be4: Leverage cache mounts in Dockerfile during `yarn install ...` and `apt-get ...` commands to speed up repeated builds.
- Updated dependencies
  - @backstage/cli-common@0.1.10

## @backstage/dev-utils@1.0.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/test-utils@1.2.1-next.2
  - @backstage/app-defaults@1.0.7-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-app-api@1.1.1-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/integration-react@1.1.5-next.2
  - @backstage/theme@0.2.16

## @backstage/errors@1.1.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.0

## @backstage/integration@1.3.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2

## @backstage/integration-react@1.1.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/theme@0.2.16

## @techdocs/cli@1.2.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-techdocs-node@1.4.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.3-next.2

## @backstage/test-utils@1.2.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.0-next.2
  - @backstage/plugin-permission-react@0.4.6-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/core-app-api@1.1.1-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0

## @backstage/plugin-adr@0.2.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/integration-react@1.1.5-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-adr-common@0.2.2-next.2
  - @backstage/plugin-search-react@1.2.0-next.2

## @backstage/plugin-adr-backend@0.2.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/plugin-adr-common@0.2.2-next.2

## @backstage/plugin-adr-common@0.2.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2

## @backstage/plugin-airbrake@0.3.10-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/test-utils@1.2.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/dev-utils@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-airbrake-backend@0.2.10-next.2

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/config@1.0.3-next.2

## @backstage/plugin-allure@0.1.26-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-analytics-module-ga@0.1.21-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-apache-airflow@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2

## @backstage/plugin-api-docs@0.8.10-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.6.0-next.2
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-apollo-explorer@0.1.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-app-backend@0.3.37-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/backend-plugin-api@0.1.3-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/config-loader@1.1.5-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-auth-backend@0.17.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-auth-node@0.2.6-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-auth-node@0.2.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2

## @backstage/plugin-azure-devops@0.2.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-azure-devops-backend@0.3.16-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-badges@0.2.34-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-badges-backend@0.1.31-next.2

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2

## @backstage/plugin-bazaar@0.1.25-next.2

### Patch Changes

- f7c2855d76: Added a `Overview Card` for either latest or random projects. Changed `ProjectPreview.tsx` so it take `gridSize` and `useTablePagination` as props.
- c0352bbc69: Link to the user catalog entity of a member
- Updated dependencies
  - @backstage/cli@0.20.0-next.2
  - @backstage/plugin-catalog@1.6.0-next.2
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2

## @backstage/plugin-bitbucket-cloud-common@0.2.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.3.2-next.2

## @backstage/plugin-bitrise@0.1.37-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog@1.6.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-common@1.0.7-next.2
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration-react@1.1.5-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/plugin-search-react@1.2.0-next.2

## @backstage/plugin-catalog-backend-module-aws@0.1.10-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.5.0-next.2
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-azure@0.1.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.5.0-next.2
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-bitbucket@0.2.4-next.2

### Patch Changes

- 23f9199a0f: Deprecate `@backstage/plugin-catalog-backend-module-bitbucket`.

  Please migrate to `@backstage/plugin-catalog-backend-module-bitbucket-cloud`
  or `@backstage/plugin-catalog-backend-module-bitbucket-server` instead.

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.5.0-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/types@1.0.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.0-next.2

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.1.4-next.2

### Patch Changes

- f66e696e7b: Bitbucket Cloud provider: Add option to configure schedule via `app-config.yaml` instead of in code.

  Please find how to configure the schedule at the config at
  <https://backstage.io/docs/integrations/bitbucketCloud/discovery>

- a9b91d39bb: Add `bitbucketCloudCatalogModule` (new backend-plugin-api, alpha).

- Updated dependencies
  - @backstage/plugin-catalog-node@1.2.0-next.2
  - @backstage/plugin-catalog-backend@1.5.0-next.2
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/backend-plugin-api@0.1.3-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/plugin-bitbucket-cloud-common@0.2.0-next.2

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.1.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.5.0-next.2
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2

## @backstage/plugin-catalog-backend-module-gerrit@0.1.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.5.0-next.2
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2

## @backstage/plugin-catalog-backend-module-github@0.1.8-next.2

### Patch Changes

- 7022aebf35: Added `GitHubLocationAnalyzer`. This can be used to add to the `CatalogBuilder`. When added this will be used by `RepoLocationAnalyzer` to figure out if the given URL that you are trying to import from the /catalog-import page already contains catalog-info.yaml files.
- 7edb5909e8: Add missing config schema for the `GitHubEntityProvider`.
- Updated dependencies
  - @backstage/plugin-catalog-node@1.2.0-next.2
  - @backstage/plugin-catalog-backend@1.5.0-next.2
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/backend-plugin-api@0.1.3-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-gitlab@0.1.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.5.0-next.2
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-ldap@0.5.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.5.0-next.2
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-msgraph@0.4.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.5.0-next.2
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2

## @backstage/plugin-catalog-backend-module-openapi@0.1.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.2.0-next.2
  - @backstage/plugin-catalog-backend@1.5.0-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-common@1.0.7-next.2

### Patch Changes

- 823acaa88b: Moved the following types from `@backstage/plugin-catalog-backend` to this package.

  - AnalyzeLocationResponse
  - AnalyzeLocationRequest
  - AnalyzeLocationExistingEntity
  - AnalyzeLocationGenerateEntity
  - AnalyzeLocationEntityField

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.0-next.2
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/catalog-model@1.1.2-next.2

## @backstage/plugin-catalog-graph@0.2.22-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog-graphql@0.3.14-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-react@1.2.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-common@1.0.7-next.2
  - @backstage/plugin-permission-common@0.7.0-next.2
  - @backstage/plugin-permission-react@0.4.6-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1

## @backstage/plugin-cicd-statistics@0.1.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2

## @backstage/plugin-cicd-statistics-module-gitlab@0.1.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/plugin-cicd-statistics@0.1.12-next.2

## @backstage/plugin-circleci@0.3.10-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-cloudbuild@0.3.10-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-code-climate@0.1.10-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage-backend@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2

## @backstage/plugin-codescene@0.1.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-config-schema@0.1.33-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0

## @backstage/plugin-cost-insights@0.11.32-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-cost-insights-common@0.1.1

## @backstage/plugin-dynatrace@1.0.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-explore@0.3.41-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-explore-react@0.0.22-next.2

## @backstage/plugin-explore-react@0.0.22-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.0.7-next.2

## @backstage/plugin-firehydrant@0.1.27-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-fossa@0.2.42-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-gcalendar@0.3.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-gcp-projects@0.3.29-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-git-release-manager@0.3.23-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-github-actions@0.5.10-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-github-deployments@0.1.41-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/integration-react@1.1.5-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-github-issues@0.1.2-next.2

### Patch Changes

- df226e124c: Add filtering and ordering to the graphql query
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-github-pull-requests-board@0.1.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-gitops-profiles@0.3.28-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-gocd@0.1.16-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-graphiql@0.2.42-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-graphql-backend@0.1.27-next.2

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/plugin-catalog-graphql@0.3.14-next.2

## @backstage/plugin-home@0.4.26-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-stack-overflow@0.1.6-next.2

## @backstage/plugin-ilert@0.1.36-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-jenkins@0.7.9-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/plugin-jenkins-common@0.1.9-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-jenkins-backend@0.1.27-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-permission-common@0.7.0-next.2
  - @backstage/plugin-jenkins-common@0.1.9-next.2
  - @backstage/plugin-auth-node@0.2.6-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2

## @backstage/plugin-jenkins-common@0.1.9-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-common@1.0.7-next.2
  - @backstage/plugin-permission-common@0.7.0-next.2

## @backstage/plugin-kafka@0.3.10-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-kafka-backend@0.2.30-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2

## @backstage/plugin-kubernetes@0.7.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-kubernetes-common@0.4.3-next.2

## @backstage/plugin-kubernetes-backend@0.7.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-auth-node@0.2.6-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/plugin-kubernetes-common@0.4.3-next.2

## @backstage/plugin-kubernetes-common@0.4.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.2

## @backstage/plugin-lighthouse@0.3.10-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic@0.3.28-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic-dashboard@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2

## @backstage/plugin-org@0.5.10-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-pagerduty@0.5.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop@0.1.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop-backend@0.1.8-next.2

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/config@1.0.3-next.2

## @backstage/plugin-permission-backend@0.5.12-next.2

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-permission-common@0.7.0-next.2
  - @backstage/plugin-permission-node@0.7.0-next.2
  - @backstage/plugin-auth-node@0.2.6-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2

## @backstage/plugin-permission-react@0.4.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.0-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/core-plugin-api@1.0.7-next.2

## @backstage/plugin-playlist@0.1.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-common@1.0.7-next.2
  - @backstage/plugin-permission-common@0.7.0-next.2
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/plugin-permission-react@0.4.6-next.2
  - @backstage/plugin-playlist-common@0.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-react@1.2.0-next.2

## @backstage/plugin-playlist-common@0.1.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.0-next.2

## @backstage/plugin-proxy-backend@0.2.31-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/config@1.0.3-next.2

## @backstage/plugin-rollbar@0.4.10-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-rollbar-backend@0.1.34-next.2

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/config@1.0.3-next.2

## @backstage/plugin-scaffolder@1.7.0-next.2

### Patch Changes

- 92e490d6b4: Make the `/next` scaffolder work end to end with the old `TaskPage` view
- 1047baa926: Bump to `react-jsonschema-form@v5-beta` for the `NextRouter` under `@alpha` exports
- 98ae18b68f: Fixed a bug where the `allowed*` values for the `RepoUrlPicker` would be reset on render.
- Updated dependencies
  - @backstage/plugin-catalog-common@1.0.7-next.2
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/plugin-permission-react@0.4.6-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/integration-react@1.1.5-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/plugin-scaffolder-common@1.2.1-next.2

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.2.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-scaffolder-backend@1.7.0-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-scaffolder-backend-module-rails@0.4.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-scaffolder-backend@1.7.0-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-scaffolder-backend-module-yeoman@0.2.10-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.7.0-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-scaffolder-common@1.2.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-search@1.0.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1
  - @backstage/plugin-search-react@1.2.0-next.2

## @backstage/plugin-search-backend@1.1.0-next.2

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-permission-common@0.7.0-next.2
  - @backstage/plugin-permission-node@0.7.0-next.2
  - @backstage/plugin-search-backend-node@1.0.3-next.2
  - @backstage/plugin-auth-node@0.2.6-next.2
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-search-backend-module-elasticsearch@1.0.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-backend-node@1.0.3-next.2
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/config@1.0.3-next.2

## @backstage/plugin-search-backend-module-pg@0.4.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-search-backend-node@1.0.3-next.2
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/config@1.0.3-next.2

## @backstage/plugin-search-backend-node@1.0.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-permission-common@0.7.0-next.2
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2

## @backstage/plugin-search-common@1.1.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.0-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-search-react@1.2.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1

## @backstage/plugin-sentry@0.4.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-shortcuts@0.3.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0

## @backstage/plugin-sonarqube@0.4.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-sonarqube-backend@0.1.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2

## @backstage/plugin-splunk-on-call@0.3.34-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-stack-overflow@0.1.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-home@0.4.26-next.2

## @backstage/plugin-stack-overflow-backend@0.1.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.20.0-next.2
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/config@1.0.3-next.2

## @backstage/plugin-tech-insights@0.3.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/plugin-tech-insights-common@0.2.7-next.2

## @backstage/plugin-tech-insights-backend@0.5.3-next.2

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-tech-insights-node@0.3.5-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/plugin-tech-insights-common@0.2.7-next.2

## @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.21-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-tech-insights-node@0.3.5-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/plugin-tech-insights-common@0.2.7-next.2

## @backstage/plugin-tech-insights-common@0.2.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.0

## @backstage/plugin-tech-insights-node@0.3.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/types@1.0.0
  - @backstage/plugin-tech-insights-common@0.2.7-next.2

## @backstage/plugin-tech-radar@0.5.17-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs@1.3.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/integration-react@1.1.5-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-react@1.2.0-next.2
  - @backstage/plugin-techdocs-react@1.0.5-next.2

## @backstage/plugin-techdocs-addons-test-utils@1.0.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.6.0-next.2
  - @backstage/test-utils@1.2.1-next.2
  - @backstage/core-app-api@1.1.1-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/integration-react@1.1.5-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-react@1.2.0-next.2
  - @backstage/plugin-techdocs@1.3.3-next.2
  - @backstage/plugin-techdocs-react@1.0.5-next.2

## @backstage/plugin-techdocs-module-addons-contrib@1.0.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/integration@1.3.2-next.2
  - @backstage/integration-react@1.1.5-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-techdocs-react@1.0.5-next.2

## @backstage/plugin-techdocs-node@1.4.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2

## @backstage/plugin-techdocs-react@1.0.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/version-bridge@1.0.1

## @backstage/plugin-todo@0.2.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-todo-backend@0.1.34-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/integration@1.3.2-next.2

## @backstage/plugin-user-settings@0.5.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.1.1-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0

## @backstage/plugin-user-settings-backend@0.1.1-next.2

### Patch Changes

- f3463b176b: Use `Response.status` instead of `.send(number)`
- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-auth-node@0.2.6-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/types@1.0.0

## @backstage/plugin-vault@0.1.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-vault-backend@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/backend-test-utils@0.1.29-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2

## @backstage/plugin-xcmetrics@0.2.30-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/errors@1.1.2-next.2
  - @backstage/theme@0.2.16

## example-app@0.2.76-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder@1.7.0-next.2
  - @backstage/plugin-catalog-common@1.0.7-next.2
  - @backstage/plugin-catalog-import@0.9.0-next.2
  - @backstage/cli@0.20.0-next.2
  - @backstage/plugin-catalog-react@1.2.0-next.2
  - @backstage/plugin-playlist@0.1.1-next.2
  - @backstage/plugin-permission-react@0.4.6-next.2
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/app-defaults@1.0.7-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/core-app-api@1.1.1-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/integration-react@1.1.5-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-airbrake@0.3.10-next.2
  - @backstage/plugin-apache-airflow@0.2.3-next.2
  - @backstage/plugin-api-docs@0.8.10-next.2
  - @backstage/plugin-azure-devops@0.2.1-next.2
  - @backstage/plugin-badges@0.2.34-next.2
  - @backstage/plugin-catalog-graph@0.2.22-next.2
  - @backstage/plugin-circleci@0.3.10-next.2
  - @backstage/plugin-cloudbuild@0.3.10-next.2
  - @backstage/plugin-code-coverage@0.2.3-next.2
  - @backstage/plugin-cost-insights@0.11.32-next.2
  - @backstage/plugin-dynatrace@1.0.0-next.2
  - @backstage/plugin-explore@0.3.41-next.2
  - @backstage/plugin-gcalendar@0.3.6-next.2
  - @backstage/plugin-gcp-projects@0.3.29-next.2
  - @backstage/plugin-github-actions@0.5.10-next.2
  - @backstage/plugin-gocd@0.1.16-next.2
  - @backstage/plugin-graphiql@0.2.42-next.2
  - @backstage/plugin-home@0.4.26-next.2
  - @backstage/plugin-jenkins@0.7.9-next.2
  - @backstage/plugin-kafka@0.3.10-next.2
  - @backstage/plugin-kubernetes@0.7.3-next.2
  - @backstage/plugin-lighthouse@0.3.10-next.2
  - @backstage/plugin-newrelic@0.3.28-next.2
  - @backstage/plugin-newrelic-dashboard@0.2.3-next.2
  - @backstage/plugin-org@0.5.10-next.2
  - @backstage/plugin-pagerduty@0.5.3-next.2
  - @backstage/plugin-rollbar@0.4.10-next.2
  - @backstage/plugin-search@1.0.3-next.2
  - @backstage/plugin-search-react@1.2.0-next.2
  - @backstage/plugin-sentry@0.4.3-next.2
  - @backstage/plugin-shortcuts@0.3.2-next.2
  - @backstage/plugin-stack-overflow@0.1.6-next.2
  - @backstage/plugin-tech-insights@0.3.1-next.2
  - @backstage/plugin-tech-radar@0.5.17-next.2
  - @backstage/plugin-techdocs@1.3.3-next.2
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.5-next.2
  - @backstage/plugin-techdocs-react@1.0.5-next.2
  - @backstage/plugin-todo@0.2.12-next.2
  - @backstage/plugin-user-settings@0.5.0-next.2
  - @internal/plugin-catalog-customized@0.0.3-next.2

## example-backend@0.2.76-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.5.0-next.2
  - @backstage/backend-tasks@0.3.6-next.2
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-permission-common@0.7.0-next.2
  - @backstage/plugin-permission-node@0.7.0-next.2
  - @backstage/plugin-scaffolder-backend@1.7.0-next.2
  - @backstage/plugin-playlist-backend@0.2.0-next.2
  - @backstage/plugin-badges-backend@0.1.31-next.2
  - @backstage/plugin-graphql-backend@0.1.27-next.2
  - @backstage/plugin-permission-backend@0.5.12-next.2
  - @backstage/plugin-rollbar-backend@0.1.34-next.2
  - @backstage/plugin-search-backend@1.1.0-next.2
  - @backstage/plugin-tech-insights-backend@0.5.3-next.2
  - @backstage/plugin-techdocs-backend@1.4.0-next.2
  - example-app@0.2.76-next.2
  - @backstage/plugin-search-backend-node@1.0.3-next.2
  - @backstage/plugin-tech-insights-node@0.3.5-next.2
  - @backstage/plugin-app-backend@0.3.37-next.2
  - @backstage/plugin-auth-backend@0.17.0-next.2
  - @backstage/plugin-auth-node@0.2.6-next.2
  - @backstage/plugin-azure-devops-backend@0.3.16-next.2
  - @backstage/plugin-code-coverage-backend@0.2.3-next.2
  - @backstage/plugin-jenkins-backend@0.1.27-next.2
  - @backstage/plugin-kafka-backend@0.2.30-next.2
  - @backstage/plugin-kubernetes-backend@0.7.3-next.2
  - @backstage/plugin-proxy-backend@0.2.31-next.2
  - @backstage/plugin-scaffolder-backend-module-rails@0.4.5-next.2
  - @backstage/plugin-search-backend-module-elasticsearch@1.0.3-next.2
  - @backstage/plugin-search-backend-module-pg@0.4.1-next.2
  - @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.21-next.2
  - @backstage/plugin-todo-backend@0.1.34-next.2
  - @backstage/plugin-search-common@1.1.0-next.2
  - @backstage/catalog-client@1.1.1-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/integration@1.3.2-next.2

## example-backend-next@0.0.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.5.0-next.2
  - @backstage/plugin-scaffolder-backend@1.7.0-next.2
  - @backstage/plugin-app-backend@0.3.37-next.2
  - @backstage/backend-defaults@0.1.2-next.2

## techdocs-cli-embedded-app@0.2.75-next.2

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.20.0-next.2
  - @backstage/plugin-catalog@1.6.0-next.2
  - @backstage/test-utils@1.2.1-next.2
  - @backstage/app-defaults@1.0.7-next.2
  - @backstage/catalog-model@1.1.2-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/core-app-api@1.1.1-next.2
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/integration-react@1.1.5-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-techdocs@1.3.3-next.2
  - @backstage/plugin-techdocs-react@1.0.5-next.2

## @internal/plugin-catalog-customized@0.0.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.6.0-next.2
  - @backstage/plugin-catalog-react@1.2.0-next.2

## @internal/plugin-todo-list@1.0.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.2
  - @backstage/core-plugin-api@1.0.7-next.2
  - @backstage/theme@0.2.16

## @internal/plugin-todo-list-backend@1.0.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.2
  - @backstage/plugin-auth-node@0.2.6-next.2
  - @backstage/config@1.0.3-next.2
  - @backstage/errors@1.1.2-next.2

## @internal/plugin-todo-list-common@1.0.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.0-next.2
