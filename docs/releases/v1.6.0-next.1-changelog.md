# Release v1.6.0-next.1

## @backstage/cli@0.19.0-next.1

### Minor Changes

- 1fe6823bb5: Updated dependency `eslint-plugin-jest` to `^27.0.0`.

  Note that this major update to the Jest plugin contains some breaking changes.
  This means that some of your tests may start seeing some new lint errors. [Read about them here](https://github.com/jest-community/eslint-plugin-jest/blob/main/CHANGELOG.md#2700-2022-08-28).

  These are mostly possible to fix automatically. You can try to run `yarn backstage-cli repo lint --fix` in your repo root to have most or all of them
  corrected.

### Patch Changes

- 817f3196f6: Added a new `migrate react-router-deps` command to aid in the migration to React Router v6 stable.
- 33fbd9f9a4: Updated dependency `@types/minimatch` to `^5.0.0`.

## @backstage/core-app-api@1.1.0-next.1

### Minor Changes

- a448fea691: Updated the routing system to be compatible with React Router v6 stable.

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 70299c99d5: Updated `FlatRoutes` to be compatible with React Router v6 stable.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/test-utils@1.2.0-next.1

### Minor Changes

- 817f3196f6: Elements rendered in a test app are no longer wrapped in a `Routes` and `Route` element, as this is not compatible with React Router v6 stable.

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-app-api@1.1.0-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-permission-react@0.4.5-next.1

## @backstage/plugin-auth-backend@0.16.0-next.1

### Minor Changes

- 2fc41ebf07: Removed the previously deprecated class `AtlassianAuthProvider`. Please use `providers.atlassian.create(...)` instead.
- a291688bc5: Renamed the `RedirectInfo` type to `OAuthStartResponse`

### Patch Changes

- d669d89206: Minor API signatures cleanup
- e1ebaeb332: Cloudflare Access Provider: Add JWT to CloudflareAccessResult
- Updated dependencies
  - @backstage/plugin-auth-node@0.2.5-next.1
  - @backstage/backend-common@0.15.1-next.1

## @backstage/plugin-catalog-backend@1.4.0-next.1

### Minor Changes

- dd395335bc: Allow unknown typed location from being registered via the location service by configuration settings
- 651c9d6800: The search index now does retain fields that have a very long value, but in the form of just a null. This makes it possible to at least filter for their existence.

### Patch Changes

- ce77e78c93: Fixes a bug to be able to utilize refresh keys after the entity is loaded from cache
- 679f7c5e95: Include entity ref into error message when catalog policies fail
- Updated dependencies
  - @backstage/plugin-permission-node@0.6.5-next.1
  - @backstage/backend-common@0.15.1-next.1

## @backstage/plugin-scaffolder-backend@1.6.0-next.1

### Minor Changes

- 7db9613671: Added `projectId` for gitlab projects to be displayed in the `gitlab:publish` output

### Patch Changes

- 0d8d650e32: Applied the fix from version 1.5.1 of this package, which is part of the v1.5.1 release of Backstage.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.1

## @backstage/plugin-tech-insights@0.3.0-next.1

### Minor Changes

- f7e99ac1d8: Added the possibility to display check results of different types on a single scorecard.

  - **BREAKING** Removed the `getScorecardsDefinition` method from the `TechInsightsApi` interface. Added the `getCheckResultRenderers` method that returns rendering components for given types.
  - **BREAKING** The `CheckResultRenderer` type now exposes the `component` factory method that creates a React component used to display a result of a provided check result.
  - The `TechInsightsClient` constructor accepts now the optional `renderers` parameter that can be used to inject a custom renderer.
  - **BREAKING** The `title` parameter in the `EntityTechInsightsScorecardContent` and `EntityTechInsightsScorecardCard` components is now mandatory.
  - The `jsonRulesEngineCheckResultRenderer` used to render `json-rules-engine` check results is exported.
  - The `BooleanCheck` component that can be used to render other check results types is also exported.

  If you were overriding the `getScorecardsDefinition` method to adjust the rendering of check results, you should now provide a custom renderer using `renderers` parameter in the `TechInsightsClient` class.

  See the [README](https://github.com/backstage/backstage/tree/v1.6.0-next.1/plugins/tech-insights/README.md) for more details.

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 763fb81e82: Internal refactor to use more type safe code when dealing with route parameters.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/app-defaults@1.0.6-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-app-api@1.1.0-next.1
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-permission-react@0.4.5-next.1

## @backstage/backend-common@0.15.1-next.1

### Patch Changes

- c3c90280be: Added a keep-alive refresh loop to the `DatabaseManager`, keeping the connection
  pool up.
- c3c90280be: The options part of `DatabaseManager.fromConfig` now accepts an optional logger
  field.
- 60b85d8ade: Updated dependency `helmet` to `^6.0.0`.

  Please note that these policies are no longer applied by default:

  helmet.contentSecurityPolicy no longer sets block-all-mixed-content directive by default
  helmet.expectCt is no longer set by default. It can, however, be explicitly enabled. It will be removed in Helmet 7.

## @backstage/backend-test-utils@0.1.28-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1
  - @backstage/cli@0.19.0-next.1

## @backstage/core-components@0.11.1-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 37e8c5e128: The `RoutedTabs` component has been updated to be compatible with React Router v6 stable.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/core-plugin-api@1.0.6-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.

## @backstage/create-app@0.4.31-next.1

### Patch Changes

- c1f1a4c760: The Backstage packages and plugins have all been updated to support React Router v6 stable. The `create-app` template has not been migrated yet, but if you want to migrate your own app or plugins, check out the [migration guide](https://backstage.io/docs/tutorials/react-router-stable-migration).
- c3c90280be: The options part of `DatabaseManager.fromConfig` now accepts an optional logger
  field. You may want to supply that logger in your backend initialization code to
  ensure that you can get relevant logging data when things happen related to the
  connection pool.

  In `packages/backend/src/index.ts`:

  ```diff
   function makeCreateEnv(config: Config) {
     const root = getRootLogger();
     ...
  -  const databaseManager = DatabaseManager.fromConfig(config);
  +  const databaseManager = DatabaseManager.fromConfig(config, { logger: root });
  ```

## @backstage/dev-utils@1.0.6-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/app-defaults@1.0.6-next.1
  - @backstage/core-app-api@1.1.0-next.1
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/test-utils@1.2.0-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-adr@0.2.1-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- b489de83b1: Fix parsing of ADR location which includes a trailing slash
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1
  - @backstage/plugin-search-react@1.0.2-next.1

## @backstage/plugin-adr-backend@0.2.1-next.1

### Patch Changes

- b489de83b1: Continue processing subsequent entities when an error occurs in collator
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1

## @backstage/plugin-airbrake@0.3.9-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/dev-utils@1.0.6-next.1
  - @backstage/test-utils@1.2.0-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-airbrake-backend@0.2.9-next.1

### Patch Changes

- 148568b5c2: Switched to using node-fetch instead of cross-fetch as is standard for our backend packages
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1

## @backstage/plugin-allure@0.1.25-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-apache-airflow@0.2.2-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-api-docs@0.8.9-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1
  - @backstage/plugin-catalog@1.5.1-next.1

## @backstage/plugin-app-backend@0.3.36-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup

- 60b85d8ade: Updated dependency `helmet` to `^6.0.0`.

  Please note that these policies are no longer applied by default:

  helmet.contentSecurityPolicy no longer sets block-all-mixed-content directive by default
  helmet.expectCt is no longer set by default. It can, however, be explicitly enabled. It will be removed in Helmet 7.

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1

## @backstage/plugin-auth-node@0.2.5-next.1

### Patch Changes

- 9212439d15: Minor update to tests
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1

## @backstage/plugin-azure-devops@0.2.0-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-badges@0.2.33-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-bazaar@0.1.24-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1
  - @backstage/plugin-catalog@1.5.1-next.1
  - @backstage/cli@0.19.0-next.1

## @backstage/plugin-bitrise@0.1.36-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-catalog@1.5.1-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1
  - @backstage/plugin-search-react@1.0.2-next.1

## @backstage/plugin-catalog-backend-module-aws@0.1.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.1

## @backstage/plugin-catalog-backend-module-azure@0.1.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.1

## @backstage/plugin-catalog-backend-module-bitbucket@0.2.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.1

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.1.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.4.0-next.1

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.1.1-next.1

### Patch Changes

- 148568b5c2: Switched to using node-fetch instead of cross-fetch as is standard for our backend packages
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.1

## @backstage/plugin-catalog-backend-module-gerrit@0.1.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.1

## @backstage/plugin-catalog-backend-module-github@0.1.7-next.1

### Patch Changes

- 287a64bf97: Added the ability to configure the host for the `GitHubEntityProvider` to use against GitHub Enterprise
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.1

## @backstage/plugin-catalog-backend-module-gitlab@0.1.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.1

## @backstage/plugin-catalog-backend-module-ldap@0.5.3-next.1

### Patch Changes

- 7fa54c3c77: Added ae-dir LDAP vendor to correctly extract DN.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.4.0-next.1

## @backstage/plugin-catalog-backend-module-msgraph@0.4.2-next.1

### Patch Changes

- c1d32d2b76: Fixed a bug in the `MicrosoftGraphEntityProvider` that ignored the `userExpand` and `groupExpand` configuration parameters
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.4.0-next.1

## @backstage/plugin-catalog-backend-module-openapi@0.1.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.1

## @backstage/plugin-catalog-graph@0.2.21-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-catalog-graphql@0.3.13-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup

## @backstage/plugin-catalog-import@0.8.12-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-catalog-react@1.1.4-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- a6d551fad9: Properly handle free-text entity filtering in the case of empty tag arrays
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-permission-react@0.4.5-next.1

## @backstage/plugin-cicd-statistics@0.1.11-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-circleci@0.3.9-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-cloudbuild@0.3.9-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 763fb81e82: Internal refactor to use more type safe code when dealing with route parameters.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-code-climate@0.1.9-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-code-coverage@0.2.2-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-codescene@0.1.4-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-config-schema@0.1.32-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-cost-insights@0.11.31-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-dynatrace@0.2.0-next.1

### Patch Changes

- 148568b5c2: Switched to using node-fetch instead of cross-fetch as is standard for our backend packages
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-explore@0.3.40-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1
  - @backstage/plugin-explore-react@0.0.21-next.1

## @backstage/plugin-explore-react@0.0.21-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-firehydrant@0.1.26-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-gcalendar@0.3.5-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- a69ac5b63a: Upgrade `react-query:3` to `@tanstack/react-query:4`
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-gcp-projects@0.3.28-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-git-release-manager@0.3.22-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-github-actions@0.5.9-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-github-deployments@0.1.40-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-github-pull-requests-board@0.1.3-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- 723113296b: Add optional `pullRequestLimit` prop to `EntityTeamPullRequestsCard` and `EntityTeamPullRequestsContent` to limit the number of PRs shown per repository. Excluding this prop will default the number of pull requests shown to 10 per repository (the existing functionality).
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-gitops-profiles@0.3.27-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-graphiql@0.2.41-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-graphql-backend@0.1.26-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup

- 60b85d8ade: Updated dependency `helmet` to `^6.0.0`.

  Please note that these policies are no longer applied by default:

  helmet.contentSecurityPolicy no longer sets block-all-mixed-content directive by default
  helmet.expectCt is no longer set by default. It can, however, be explicitly enabled. It will be removed in Helmet 7.

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1
  - @backstage/plugin-catalog-graphql@0.3.13-next.1

## @backstage/plugin-home@0.4.25-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1
  - @backstage/plugin-stack-overflow@0.1.5-next.1

## @backstage/plugin-ilert@0.1.35-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-jenkins@0.7.8-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-jenkins-backend@0.1.26-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- 69ec1cf39f: Updated dependency `jenkins` to `^1.0.0`.
- Updated dependencies
  - @backstage/plugin-auth-node@0.2.5-next.1
  - @backstage/backend-common@0.15.1-next.1

## @backstage/plugin-kafka@0.3.9-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-kubernetes@0.7.2-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1
  - @backstage/plugin-kubernetes-common@0.4.2-next.0

## @backstage/plugin-kubernetes-backend@0.7.2-next.1

### Patch Changes

- 60b85d8ade: Updated dependency `helmet` to `^6.0.0`.

  Please note that these policies are no longer applied by default:

  helmet.contentSecurityPolicy no longer sets block-all-mixed-content directive by default
  helmet.expectCt is no longer set by default. It can, however, be explicitly enabled. It will be removed in Helmet 7.

- Updated dependencies
  - @backstage/plugin-auth-node@0.2.5-next.1
  - @backstage/backend-common@0.15.1-next.1
  - @backstage/plugin-kubernetes-common@0.4.2-next.0

## @backstage/plugin-kubernetes-common@0.4.2-next.0

### Patch Changes

- d669d89206: Minor API signatures cleanup

## @backstage/plugin-lighthouse@0.3.9-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-newrelic@0.3.27-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-newrelic-dashboard@0.2.2-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-org@0.5.9-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-pagerduty@0.5.2-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-periskop-backend@0.1.7-next.1

### Patch Changes

- 148568b5c2: Switched to using node-fetch instead of cross-fetch as is standard for our backend packages
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1

## @backstage/plugin-permission-node@0.6.5-next.1

### Patch Changes

- 9212439d15: Minor update to tests
- Updated dependencies
  - @backstage/plugin-auth-node@0.2.5-next.1
  - @backstage/backend-common@0.15.1-next.1

## @backstage/plugin-permission-react@0.4.5-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.

- 817f3196f6: **DEPRECATION**: The `PermissionedRoute` component has been deprecated in favor of the new `RequirePermission` component. This is because the usage pattern of `PermissionedRoute` is not compatible with React Router v6 stable.

  Embed the type from `react-router` instead of exporting it directly.

- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-rollbar@0.4.9-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-rollbar-backend@0.1.33-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1

## @backstage/plugin-scaffolder@1.6.0-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 763fb81e82: Internal refactor to use more type safe code when dealing with route parameters.
- a66d44b72b: Fixing bug when the workspace would not be automatically saved when using `allowedOwners`
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1
  - @backstage/plugin-permission-react@0.4.5-next.1

## @backstage/plugin-search@1.0.2-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1
  - @backstage/plugin-search-react@1.0.2-next.1

## @backstage/plugin-search-backend-module-pg@0.4.0-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1
  - @backstage/plugin-search-backend-node@1.0.2-next.1

## @backstage/plugin-search-backend-node@1.0.2-next.1

### Patch Changes

- a1124adf5e: Fixed bug in LunrSearchEngine where stopwords in title is causing in-doc search not working
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1

## @backstage/plugin-search-react@1.0.2-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-sentry@0.4.2-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-shortcuts@0.3.1-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-splunk-on-call@0.3.33-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-stack-overflow@0.1.5-next.1

### Patch Changes

- a720c44070: Support showing HTML entity references from the API response before rendering the question title to the list component.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-home@0.4.25-next.1

## @backstage/plugin-stack-overflow-backend@0.1.5-next.1

### Patch Changes

- 79040f73f7: Now requests all questions available using pagination. Default max page is set to 100, with a configurable `maxPage` option on the collator.
- 148568b5c2: Switched to using node-fetch instead of cross-fetch as is standard for our backend packages

## @backstage/plugin-tech-insights-backend@0.5.2-next.1

### Patch Changes

- 12c6a07c2a: Changed the description of the `techdocsAnnotationFactName` fact.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1

## @backstage/plugin-tech-radar@0.5.16-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-techdocs@1.3.2-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 763fb81e82: Internal refactor to use more type safe code when dealing with route parameters.
- 817f3196f6: Updated the `TechDocsReaderPage` to be compatible with React Router v6 stable.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1
  - @backstage/plugin-search-react@1.0.2-next.1
  - @backstage/plugin-techdocs-react@1.0.4-next.1

## @backstage/plugin-techdocs-addons-test-utils@1.0.4-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-app-api@1.1.0-next.1
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/test-utils@1.2.0-next.1
  - @backstage/plugin-catalog@1.5.1-next.1
  - @backstage/plugin-search-react@1.0.2-next.1
  - @backstage/plugin-techdocs-react@1.0.4-next.1
  - @backstage/plugin-techdocs@1.3.2-next.1

## @backstage/plugin-techdocs-react@1.0.4-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-todo@0.2.11-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1

## @backstage/plugin-user-settings@0.4.8-next.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## @backstage/plugin-vault-backend@0.2.2-next.1

### Patch Changes

- 148568b5c2: Switched to using node-fetch instead of cross-fetch as is standard for our backend packages

- 60b85d8ade: Updated dependency `helmet` to `^6.0.0`.

  Please note that these policies are no longer applied by default:

  helmet.contentSecurityPolicy no longer sets block-all-mixed-content directive by default
  helmet.expectCt is no longer set by default. It can, however, be explicitly enabled. It will be removed in Helmet 7.

- Updated dependencies
  - @backstage/backend-common@0.15.1-next.1
  - @backstage/backend-test-utils@0.1.28-next.1

## @backstage/plugin-xcmetrics@0.2.29-next.1

### Patch Changes

- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1

## example-app@0.2.75-next.1

### Patch Changes

- Updated dependencies
  - @backstage/app-defaults@1.0.6-next.1
  - @backstage/core-app-api@1.1.0-next.1
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/plugin-airbrake@0.3.9-next.1
  - @backstage/plugin-api-docs@0.8.9-next.1
  - @backstage/plugin-azure-devops@0.2.0-next.1
  - @backstage/plugin-badges@0.2.33-next.1
  - @backstage/plugin-catalog-graph@0.2.21-next.1
  - @backstage/plugin-catalog-import@0.8.12-next.1
  - @backstage/plugin-catalog-react@1.1.4-next.1
  - @backstage/plugin-circleci@0.3.9-next.1
  - @backstage/plugin-cloudbuild@0.3.9-next.1
  - @backstage/plugin-code-coverage@0.2.2-next.1
  - @backstage/plugin-cost-insights@0.11.31-next.1
  - @backstage/plugin-explore@0.3.40-next.1
  - @backstage/plugin-gcp-projects@0.3.28-next.1
  - @backstage/plugin-github-actions@0.5.9-next.1
  - @backstage/plugin-graphiql@0.2.41-next.1
  - @backstage/plugin-home@0.4.25-next.1
  - @backstage/plugin-jenkins@0.7.8-next.1
  - @backstage/plugin-kafka@0.3.9-next.1
  - @backstage/plugin-kubernetes@0.7.2-next.1
  - @backstage/plugin-lighthouse@0.3.9-next.1
  - @backstage/plugin-org@0.5.9-next.1
  - @backstage/plugin-pagerduty@0.5.2-next.1
  - @backstage/plugin-permission-react@0.4.5-next.1
  - @backstage/plugin-rollbar@0.4.9-next.1
  - @backstage/plugin-scaffolder@1.6.0-next.1
  - @backstage/plugin-search-react@1.0.2-next.1
  - @backstage/plugin-search@1.0.2-next.1
  - @backstage/plugin-sentry@0.4.2-next.1
  - @backstage/plugin-shortcuts@0.3.1-next.1
  - @backstage/plugin-tech-insights@0.3.0-next.1
  - @backstage/plugin-techdocs-react@1.0.4-next.1
  - @backstage/plugin-techdocs@1.3.2-next.1
  - @backstage/plugin-todo@0.2.11-next.1
  - @backstage/plugin-user-settings@0.4.8-next.1
  - @backstage/cli@0.19.0-next.1
  - @backstage/plugin-dynatrace@0.2.0-next.1
  - @backstage/plugin-apache-airflow@0.2.2-next.1
  - @backstage/plugin-gcalendar@0.3.5-next.1
  - @backstage/plugin-newrelic@0.3.27-next.1
  - @backstage/plugin-newrelic-dashboard@0.2.2-next.1
  - @backstage/plugin-tech-radar@0.5.16-next.1
  - @backstage/plugin-stack-overflow@0.1.5-next.1

## example-backend@0.2.75-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.2.5-next.1
  - @backstage/plugin-permission-node@0.6.5-next.1
  - @backstage/backend-common@0.15.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.1
  - @backstage/plugin-auth-backend@0.16.0-next.1
  - @backstage/plugin-scaffolder-backend@1.6.0-next.1
  - @backstage/plugin-search-backend-node@1.0.2-next.1
  - @backstage/plugin-app-backend@0.3.36-next.1
  - @backstage/plugin-graphql-backend@0.1.26-next.1
  - @backstage/plugin-jenkins-backend@0.1.26-next.1
  - @backstage/plugin-rollbar-backend@0.1.33-next.1
  - @backstage/plugin-search-backend-module-pg@0.4.0-next.1
  - @backstage/plugin-kubernetes-backend@0.7.2-next.1
  - @backstage/plugin-tech-insights-backend@0.5.2-next.1
  - example-app@0.2.75-next.1

## example-backend-next@0.0.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.4.0-next.1
  - @backstage/plugin-scaffolder-backend@1.6.0-next.1

## techdocs-cli-embedded-app@0.2.74-next.1

### Patch Changes

- Updated dependencies
  - @backstage/app-defaults@1.0.6-next.1
  - @backstage/core-app-api@1.1.0-next.1
  - @backstage/core-components@0.11.1-next.1
  - @backstage/core-plugin-api@1.0.6-next.1
  - @backstage/test-utils@1.2.0-next.1
  - @backstage/plugin-catalog@1.5.1-next.1
  - @backstage/plugin-techdocs-react@1.0.4-next.1
  - @backstage/plugin-techdocs@1.3.2-next.1
  - @backstage/cli@0.19.0-next.1
