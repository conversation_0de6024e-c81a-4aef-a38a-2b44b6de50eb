# Release v1.9.0-next.0

## @backstage/catalog-client@1.2.0-next.0

### Minor Changes

- 00d90b520a: **BREAKING PRODUCERS**: Added a new `getEntitiesByRefs` endpoint to `CatalogApi`, for efficient batch fetching of entities by ref.

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/repo-tools@0.1.0-next.0

### Minor Changes

- 99713fd671: Introducing repo-tools package

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-catalog-backend@1.6.0-next.0

### Minor Changes

- 16891a212c: Added new `POST /entities/by-refs` endpoint, which allows you to efficiently
  batch-fetch entities by their entity ref. This can be useful e.g. in graphql
  resolvers or similar contexts where you need to fetch many entities at the same
  time.

### Patch Changes

- d8593ce0e6: Do not use deprecated `LocationSpec` from the `@backstage/plugin-catalog-node` package
- **********: Updated dependency `msw` to `^0.49.0`.
- e982f77fe3: Registered shutdown hook in experimental catalog plugin.
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/plugin-permission-node@0.7.2-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0
  - @backstage/plugin-catalog-node@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/plugin-catalog-common@1.0.9-next.0
  - @backstage/plugin-scaffolder-common@1.2.3-next.0
  - @backstage/plugin-search-common@1.1.2-next.0

## @backstage/plugin-events-backend@0.2.0-next.0

### Minor Changes

- cf41eedf43: **BREAKING:** Remove required field `router` at `HttpPostIngressEventPublisher.fromConfig`
  and replace it with `bind(router: Router)`.
  Additionally, the path prefix `/http` will be added inside `HttpPostIngressEventPublisher`.

  ```diff
  // at packages/backend/src/plugins/events.ts
     const eventsRouter = Router();
  -  const httpRouter = Router();
  -  eventsRouter.use('/http', httpRouter);

     const http = HttpPostIngressEventPublisher.fromConfig({
       config: env.config,
       logger: env.logger,
  -    router: httpRouter,
     });
  +  http.bind(eventsRouter);
  ```

### Patch Changes

- cf41eedf43: Introduce a new interface `RequestDetails` to abstract `Request`
  providing access to request body and headers.

  **BREAKING:** Replace `request: Request` with `request: RequestDetails` at `RequestValidator`.

- Updated dependencies
  - @backstage/plugin-events-node@0.2.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0
  - @backstage/config@1.0.5-next.0

## @backstage/plugin-events-node@0.2.0-next.0

### Minor Changes

- cf41eedf43: Introduce a new interface `RequestDetails` to abstract `Request`
  providing access to request body and headers.

  **BREAKING:** Replace `request: Request` with `request: RequestDetails` at `RequestValidator`.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.1.5-next.0

## @backstage/plugin-scaffolder@1.9.0-next.0

### Minor Changes

- ddd1c3308d: Implement Custom Field Explorer to view and play around with available installed custom field extensions
- adb1b01e32: Adds the ability to supply a `transformErrors` function to the `Stepper` for `/next`

### Patch Changes

- d4d07cf55e: Enabling the customization of the last step in the scaffolder template.

  To override the content you have to do the next:

  ```typescript jsx
  <TemplatePage ReviewStepComponent={YourCustomComponent} />
  ```

- ef803022f1: Initialize all `formData` in the `Stepper` in `/next`

- **********: Updated dependency `msw` to `^0.49.0`.

- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.

- a63e2df559: fixed `headerOptions` not passed to `TemplatePage` component

- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration-react@1.1.7-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9-next.0
  - @backstage/plugin-permission-react@0.4.8-next.0
  - @backstage/plugin-scaffolder-common@1.2.3-next.0

## @backstage/plugin-user-settings@0.6.0-next.0

### Minor Changes

- 29bdda5442: Added the ability to fully customize settings page. Deprecated UserSettingsTab in favour of SettingsLayout.Route

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-app-api@1.2.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/app-defaults@1.0.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-app-api@1.2.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-permission-react@0.4.8-next.0

## @backstage/backend-app-api@0.2.4-next.0

### Patch Changes

- d6dbf1792b: Added `lifecycleFactory` implementation.
- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/plugin-permission-node@0.7.2-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/backend-common@0.16.1-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- dfc8edf9c5: Internal refactor to avoid usage of deprecated symbols.
- Updated dependencies
  - @backstage/config-loader@1.1.7-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/backend-defaults@0.1.4-next.0

### Patch Changes

- d6dbf1792b: Added `lifecycleFactory` to default service factories.
- Updated dependencies
  - @backstage/backend-app-api@0.2.4-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0

## @backstage/backend-plugin-api@0.1.5-next.0

### Patch Changes

- d6dbf1792b: Added initial support for registering shutdown hooks via `lifecycleServiceRef`.
- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/config@1.0.5-next.0

## @backstage/backend-tasks@0.3.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/backend-test-utils@0.1.31-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/cli@0.21.2-next.0
  - @backstage/backend-app-api@0.2.4-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0
  - @backstage/config@1.0.5-next.0

## @backstage/catalog-model@1.1.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/cli@0.21.2-next.0

### Patch Changes

- 91d050c140: changed tests created by create-plugin to follow eslint-rules best practices particularly testing-library/prefer-screen-queries and testing-library/render-result-naming-convention
- 459a3457e1: Bump `msw` version in default plugin/app templates
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/config-loader@1.1.7-next.0
  - @backstage/release-manifests@0.0.8-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/config@1.0.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2-next.0

## @backstage/config-loader@1.1.7-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/types@1.0.2-next.0
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/core-app-api@1.2.1-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/version-bridge@1.0.2

## @backstage/core-components@0.12.1-next.0

### Patch Changes

- ea4a5be8f3: Create a variable for minimum height and add a prop named 'fit' for determining if the graph height should grow or be contained.
- 64a579a998: Add items prop to SupportButton. This prop can be used to override the items that would otherwise be grabbed from the config.
- **********: Updated dependency `msw` to `^0.49.0`.
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- 17a8e32f39: Updated dependency `rc-progress` to `3.4.1`.
- dfc8edf9c5: Internal refactor to avoid usage of deprecated symbols.
- Updated dependencies
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16
  - @backstage/version-bridge@1.0.2

## @backstage/core-plugin-api@1.1.1-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- Updated dependencies
  - @backstage/types@1.0.2-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/version-bridge@1.0.2

## @backstage/create-app@0.4.35-next.0

### Patch Changes

- Bumped create-app version.
- Updated dependencies
  - @backstage/cli-common@0.1.10

## @backstage/dev-utils@1.0.9-next.0

### Patch Changes

- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-app-api@1.2.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration-react@1.1.7-next.0
  - @backstage/test-utils@1.2.3-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/app-defaults@1.0.9-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/errors@1.1.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2-next.0

## @backstage/integration@1.4.1-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 34b039ca9f: Added `integrations.github.apps.allowedInstallationOwners` to the configuration schema.
- Updated dependencies
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/integration-react@1.1.7-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/theme@0.2.16

## @backstage/release-manifests@0.0.8-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.

## @techdocs/cli@1.2.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.5-next.0
  - @backstage/plugin-techdocs-node@1.4.3-next.0

## @backstage/test-utils@1.2.3-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- Updated dependencies
  - @backstage/core-app-api@1.2.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-permission-react@0.4.8-next.0

## @backstage/types@1.0.2-next.0

### Patch Changes

- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.

## @backstage/plugin-adr@0.2.4-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration-react@1.1.7-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-adr-common@0.2.4-next.0
  - @backstage/plugin-search-common@1.1.2-next.0
  - @backstage/plugin-search-react@1.2.2-next.0

## @backstage/plugin-adr-backend@0.2.4-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/plugin-adr-common@0.2.4-next.0
  - @backstage/plugin-search-common@1.1.2-next.0

## @backstage/plugin-adr-common@0.2.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.4.1-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/plugin-search-common@1.1.2-next.0

## @backstage/plugin-airbrake@0.3.12-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/test-utils@1.2.3-next.0
  - @backstage/dev-utils@1.0.9-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-airbrake-backend@0.2.12-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/config@1.0.5-next.0

## @backstage/plugin-allure@0.1.28-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-analytics-module-ga@0.1.23-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-apache-airflow@0.2.5-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0

## @backstage/plugin-api-docs@0.8.12-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/plugin-catalog@1.6.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-apollo-explorer@0.1.5-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-app-backend@0.3.39-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/config-loader@1.1.7-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0
  - @backstage/config@1.0.5-next.0

## @backstage/plugin-auth-backend@0.17.2-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/plugin-auth-node@0.2.8-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-auth-node@0.2.8-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-azure-devops@0.2.3-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-azure-devops-backend@0.3.18-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-azure-sites@0.1.1-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-sites-common@0.1.0

## @backstage/plugin-azure-sites-backend@0.1.1-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/plugin-azure-sites-common@0.1.0

## @backstage/plugin-badges@0.2.36-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-badges-backend@0.1.33-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-bazaar@0.2.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/cli@0.21.2-next.0
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/plugin-catalog@1.6.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-bazaar-backend@0.2.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/backend-test-utils@0.1.31-next.0
  - @backstage/plugin-auth-node@0.2.8-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-bitbucket-cloud-common@0.2.2-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/integration@1.4.1-next.0

## @backstage/plugin-bitrise@0.1.39-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog@1.6.2-next.0

### Patch Changes

- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- 387d1d5218: Fixed Entity kind pluralisation in the `CatalogKindHeader` component.
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration-react@1.1.7-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9-next.0
  - @backstage/plugin-search-common@1.1.2-next.0
  - @backstage/plugin-search-react@1.2.2-next.0

## @backstage/plugin-catalog-backend-module-aws@0.1.12-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0
  - @backstage/plugin-catalog-node@1.2.2-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-catalog-backend-module-azure@0.1.10-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0
  - @backstage/plugin-catalog-node@1.2.2-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-catalog-backend-module-bitbucket@0.2.6-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.2-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.1.6-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/plugin-catalog-backend@1.6.0-next.0
  - @backstage/plugin-events-node@0.2.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.2-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0
  - @backstage/plugin-catalog-node@1.2.2-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/plugin-catalog-common@1.0.9-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.1.4-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0
  - @backstage/plugin-catalog-node@1.2.2-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-catalog-backend-module-gerrit@0.1.7-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0
  - @backstage/plugin-catalog-node@1.2.2-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-catalog-backend-module-github@0.2.2-next.0

### Patch Changes

- 70fa5ec3ec: Fixes the assignment of group member references in `GithubMultiOrgProcessor` so membership relations are resolved correctly.
- **********: Updated dependency `msw` to `^0.49.0`.
- 754b5854df: Fix incorrectly exported GithubOrgEntityProvider as a type
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/plugin-catalog-backend@1.6.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0
  - @backstage/plugin-catalog-node@1.2.2-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-catalog-backend-module-gitlab@0.1.10-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0
  - @backstage/plugin-catalog-node@1.2.2-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-catalog-backend-module-ldap@0.5.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-catalog-backend-module-msgraph@0.4.5-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0
  - @backstage/plugin-catalog-node@1.2.2-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0

## @backstage/plugin-catalog-backend-module-openapi@0.1.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/plugin-catalog-node@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0

## @backstage/plugin-catalog-common@1.0.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/plugin-search-common@1.1.2-next.0

## @backstage/plugin-catalog-graph@0.2.24-next.0

### Patch Changes

- cb716004ef: Internal refactor to improve tests
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog-graphql@0.3.16-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/types@1.0.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0

## @backstage/plugin-catalog-import@0.9.2-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration-react@1.1.7-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/plugin-catalog-common@1.0.9-next.0

## @backstage/plugin-catalog-node@1.2.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/plugin-catalog-common@1.0.9-next.0

## @backstage/plugin-catalog-react@1.2.2-next.0

### Patch Changes

- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16
  - @backstage/version-bridge@1.0.2
  - @backstage/plugin-catalog-common@1.0.9-next.0
  - @backstage/plugin-permission-react@0.4.8-next.0

## @backstage/plugin-cicd-statistics@0.1.14-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0

## @backstage/plugin-cicd-statistics-module-gitlab@0.1.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/plugin-cicd-statistics@0.1.14-next.0

## @backstage/plugin-circleci@0.3.12-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-cloudbuild@0.3.12-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-code-climate@0.1.12-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage@0.2.5-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage-backend@0.2.5-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-codescene@0.1.7-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 17a8e32f39: Updated dependency `rc-progress` to `3.4.1`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-config-schema@0.1.35-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-cost-insights@0.12.1-next.0

### Patch Changes

- f9bbb3be37: Provide the ability to change the base currency from USD to any other currency in cost insights plugin
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-cost-insights-common@0.1.1

## @backstage/plugin-dynatrace@1.0.2-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-events-backend-module-aws-sqs@0.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.2.0-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/config@1.0.5-next.0

## @backstage/plugin-events-backend-module-azure@0.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.2.0-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0

## @backstage/plugin-events-backend-module-bitbucket-cloud@0.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.2.0-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0

## @backstage/plugin-events-backend-module-gerrit@0.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.2.0-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0

## @backstage/plugin-events-backend-module-github@0.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.2.0-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0

## @backstage/plugin-events-backend-module-gitlab@0.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.2.0-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0

## @backstage/plugin-events-backend-test-utils@0.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.2.0-next.0

## @backstage/plugin-explore@0.3.43-next.0

### Patch Changes

- ea4a5be8f3: Adds styling to graph forcing it to always fill out the available space.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-explore-react@0.0.24-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-explore-react@0.0.24-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.1.1-next.0

## @backstage/plugin-firehydrant@0.1.29-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-fossa@0.2.44-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-gcalendar@0.3.8-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-gcp-projects@0.3.31-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-git-release-manager@0.3.25-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-github-actions@0.5.12-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-github-deployments@0.1.43-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration-react@1.1.7-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-github-issues@0.2.1-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-github-pull-requests-board@0.1.6-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-gitops-profiles@0.3.30-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-gocd@0.1.18-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-graphiql@0.2.44-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-graphql-backend@0.1.29-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/plugin-catalog-graphql@0.3.16-next.0
  - @backstage/config@1.0.5-next.0

## @backstage/plugin-home@0.4.28-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-stack-overflow@0.1.8-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-ilert@0.2.1-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-jenkins@0.7.11-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-jenkins-common@0.1.11-next.0

## @backstage/plugin-jenkins-backend@0.1.29-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/plugin-auth-node@0.2.8-next.0
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/plugin-jenkins-common@0.1.11-next.0

## @backstage/plugin-jenkins-common@0.1.11-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/plugin-catalog-common@1.0.9-next.0

## @backstage/plugin-kafka@0.3.12-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-kafka-backend@0.2.32-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-kubernetes@0.7.5-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/plugin-kubernetes-common@0.4.5-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-kubernetes-backend@0.8.1-next.0

### Patch Changes

- b585179770: Added Kubernetes proxy API route to backend Kubernetes plugin, allowing Backstage plugin developers to read/write new information from Kubernetes (if proper credentials are provided).
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/backend-test-utils@0.1.31-next.0
  - @backstage/plugin-auth-node@0.2.8-next.0
  - @backstage/plugin-kubernetes-common@0.4.5-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-kubernetes-common@0.4.5-next.0

### Patch Changes

- b585179770: Added Kubernetes proxy API route to backend Kubernetes plugin, allowing Backstage plugin developers to read/write new information from Kubernetes (if proper credentials are provided).
- Updated dependencies
  - @backstage/catalog-model@1.1.4-next.0

## @backstage/plugin-lighthouse@0.3.12-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic@0.3.30-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic-dashboard@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-org@0.6.1-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-org-react@0.1.1-next.0

### Patch Changes

- 4cb5066828: Bug fixes and adding the possibility to add a default value for the `GroupListPicker`. Fixes: Vertical size jump on text entry, left align for text, selecting a value closes the popup, auto focus on the popup when opening
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-pagerduty@0.5.5-next.0

### Patch Changes

- cb716004ef: Internal refactor to improve tests
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop@0.1.10-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop-backend@0.1.10-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/config@1.0.5-next.0

## @backstage/plugin-permission-backend@0.5.14-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/plugin-auth-node@0.2.8-next.0
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/plugin-permission-node@0.7.2-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-permission-common@0.7.2-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/types@1.0.2-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-permission-node@0.7.2-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/plugin-auth-node@0.2.8-next.0
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-permission-react@0.4.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/config@1.0.5-next.0

## @backstage/plugin-playlist@0.1.3-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9-next.0
  - @backstage/plugin-permission-react@0.4.8-next.0
  - @backstage/plugin-playlist-common@0.1.3-next.0
  - @backstage/plugin-search-react@1.2.2-next.0

## @backstage/plugin-playlist-backend@0.2.2-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/backend-test-utils@0.1.31-next.0
  - @backstage/plugin-auth-node@0.2.8-next.0
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/plugin-permission-node@0.7.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/plugin-playlist-common@0.1.3-next.0

## @backstage/plugin-playlist-common@0.1.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.0

## @backstage/plugin-proxy-backend@0.2.33-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/config@1.0.5-next.0

## @backstage/plugin-rollbar@0.4.12-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-rollbar-backend@0.1.36-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/config@1.0.5-next.0

## @backstage/plugin-scaffolder-backend@1.8.1-next.0

### Patch Changes

- cb716004ef: Internal refactor to improve tests
- 26404430bc: Use Json types from @backstage/types
- **********: Updated dependency `msw` to `^0.49.0`.
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/plugin-catalog-backend@1.6.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/plugin-auth-node@0.2.8-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/backend-plugin-api@0.1.5-next.0
  - @backstage/plugin-catalog-node@1.2.2-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/plugin-scaffolder-common@1.2.3-next.0

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.2.14-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.8.1-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-scaffolder-backend-module-rails@0.4.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.8.1-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-scaffolder-backend-module-yeoman@0.2.12-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.8.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/config@1.0.5-next.0

## @backstage/plugin-scaffolder-common@1.2.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2-next.0
  - @backstage/catalog-model@1.1.4-next.0

## @backstage/plugin-search@1.0.5-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16
  - @backstage/version-bridge@1.0.2
  - @backstage/plugin-search-common@1.1.2-next.0
  - @backstage/plugin-search-react@1.2.2-next.0

## @backstage/plugin-search-backend@1.1.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-backend-node@1.0.5-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/plugin-auth-node@0.2.8-next.0
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/plugin-permission-node@0.7.2-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/plugin-search-common@1.1.2-next.0

## @backstage/plugin-search-backend-module-elasticsearch@1.0.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-backend-node@1.0.5-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/plugin-search-common@1.1.2-next.0

## @backstage/plugin-search-backend-module-pg@0.4.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-backend-node@1.0.5-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/plugin-search-common@1.1.2-next.0

## @backstage/plugin-search-backend-node@1.0.5-next.0

### Patch Changes

- a962ce0551: Wait for indexer initialization before finalizing indexing.
- 683ced83f6: Fixed a bug that could cause a `max listeners exceeded warning` to be logged when more than 10 collators were running simultaneously.
- 81b1e7b0fe: Updated indexer and decorator base classes to take advantage of features introduced in Node.js v16; be sure you are running a [supported version of Node.js](https://backstage.io/docs/releases/v1.8.0#node-16-and-18).
- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/plugin-search-common@1.1.2-next.0

## @backstage/plugin-search-common@1.1.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/types@1.0.2-next.0

## @backstage/plugin-search-react@1.2.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/version-bridge@1.0.2
  - @backstage/plugin-search-common@1.1.2-next.0

## @backstage/plugin-sentry@0.4.5-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-shortcuts@0.3.4-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-sonarqube@0.5.1-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 17a8e32f39: Updated dependency `rc-progress` to `3.4.1`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-sonarqube-backend@0.1.4-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-splunk-on-call@0.4.1-next.0

### Patch Changes

- cb716004ef: Internal refactor to improve tests
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-stack-overflow@0.1.8-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-home@0.4.28-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-common@1.1.2-next.0

## @backstage/plugin-stack-overflow-backend@0.1.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.21.2-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/plugin-search-common@1.1.2-next.0

## @backstage/plugin-tech-insights@0.3.4-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-tech-insights-common@0.2.9-next.0

## @backstage/plugin-tech-insights-backend@0.5.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/plugin-tech-insights-common@0.2.9-next.0
  - @backstage/plugin-tech-insights-node@0.3.7-next.0

## @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.23-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/plugin-tech-insights-common@0.2.9-next.0
  - @backstage/plugin-tech-insights-node@0.3.7-next.0

## @backstage/plugin-tech-insights-common@0.2.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2-next.0

## @backstage/plugin-tech-insights-node@0.3.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/plugin-tech-insights-common@0.2.9-next.0

## @backstage/plugin-tech-radar@0.5.19-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs@1.4.1-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-techdocs-react@1.0.7-next.0
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration-react@1.1.7-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-common@1.1.2-next.0
  - @backstage/plugin-search-react@1.2.2-next.0

## @backstage/plugin-techdocs-addons-test-utils@1.0.7-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-techdocs-react@1.0.7-next.0
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-app-api@1.2.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration-react@1.1.7-next.0
  - @backstage/test-utils@1.2.3-next.0
  - @backstage/plugin-techdocs@1.4.1-next.0
  - @backstage/plugin-catalog@1.6.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-react@1.2.2-next.0

## @backstage/plugin-techdocs-backend@1.4.2-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/plugin-catalog-common@1.0.9-next.0
  - @backstage/plugin-search-common@1.1.2-next.0
  - @backstage/plugin-techdocs-node@1.4.3-next.0

## @backstage/plugin-techdocs-module-addons-contrib@1.0.7-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 8536e7c281: Use `app.title` from `app-config.yaml` when creating new Documentation Feedback issue. `Backstage` is the default value.
- Updated dependencies
  - @backstage/plugin-techdocs-react@1.0.7-next.0
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration-react@1.1.7-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-node@1.4.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/plugin-search-common@1.1.2-next.0

## @backstage/plugin-techdocs-react@1.0.7-next.0

### Patch Changes

- cb716004ef: Internal refactor to improve tests
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/version-bridge@1.0.2

## @backstage/plugin-todo@0.2.14-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-todo-backend@0.1.36-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-user-settings-backend@0.1.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/plugin-auth-node@0.2.8-next.0
  - @backstage/types@1.0.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-vault@0.1.6-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-vault-backend@0.2.5-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 7a3d2688ed: Use `express-promise-router` to catch errors properly.
  Add `403` error as a known one. It will now return a `NotAllowed` error.
- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/backend-test-utils@0.1.31-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @backstage/plugin-xcmetrics@0.2.32-next.0

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/errors@1.1.4-next.0
  - @backstage/theme@0.2.16

## example-app@0.2.78-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-graph@0.2.24-next.0
  - @backstage/plugin-pagerduty@0.5.5-next.0
  - @backstage/plugin-techdocs-react@1.0.7-next.0
  - @backstage/plugin-scaffolder@1.9.0-next.0
  - @backstage/cli@0.21.2-next.0
  - @backstage/plugin-cost-insights@0.12.1-next.0
  - @backstage/core-components@0.12.1-next.0
  - @backstage/plugin-user-settings@0.6.0-next.0
  - @backstage/plugin-explore@0.3.43-next.0
  - @backstage/core-app-api@1.2.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration-react@1.1.7-next.0
  - @backstage/plugin-airbrake@0.3.12-next.0
  - @backstage/plugin-apache-airflow@0.2.5-next.0
  - @backstage/plugin-api-docs@0.8.12-next.0
  - @backstage/plugin-azure-devops@0.2.3-next.0
  - @backstage/plugin-azure-sites@0.1.1-next.0
  - @backstage/plugin-badges@0.2.36-next.0
  - @backstage/plugin-catalog-import@0.9.2-next.0
  - @backstage/plugin-circleci@0.3.12-next.0
  - @backstage/plugin-cloudbuild@0.3.12-next.0
  - @backstage/plugin-code-coverage@0.2.5-next.0
  - @backstage/plugin-dynatrace@1.0.2-next.0
  - @backstage/plugin-gcalendar@0.3.8-next.0
  - @backstage/plugin-gcp-projects@0.3.31-next.0
  - @backstage/plugin-github-actions@0.5.12-next.0
  - @backstage/plugin-gocd@0.1.18-next.0
  - @backstage/plugin-graphiql@0.2.44-next.0
  - @backstage/plugin-home@0.4.28-next.0
  - @backstage/plugin-jenkins@0.7.11-next.0
  - @backstage/plugin-kafka@0.3.12-next.0
  - @backstage/plugin-kubernetes@0.7.5-next.0
  - @backstage/plugin-lighthouse@0.3.12-next.0
  - @backstage/plugin-newrelic@0.3.30-next.0
  - @backstage/plugin-org@0.6.1-next.0
  - @backstage/plugin-playlist@0.1.3-next.0
  - @backstage/plugin-rollbar@0.4.12-next.0
  - @backstage/plugin-search@1.0.5-next.0
  - @backstage/plugin-sentry@0.4.5-next.0
  - @backstage/plugin-shortcuts@0.3.4-next.0
  - @backstage/plugin-stack-overflow@0.1.8-next.0
  - @backstage/plugin-tech-insights@0.3.4-next.0
  - @backstage/plugin-tech-radar@0.5.19-next.0
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.7-next.0
  - @backstage/plugin-techdocs@1.4.1-next.0
  - @backstage/plugin-todo@0.2.14-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/app-defaults@1.0.9-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9-next.0
  - @backstage/plugin-newrelic-dashboard@0.2.5-next.0
  - @backstage/plugin-permission-react@0.4.8-next.0
  - @backstage/plugin-search-common@1.1.2-next.0
  - @backstage/plugin-search-react@1.2.2-next.0
  - @internal/plugin-catalog-customized@0.0.5-next.0

## example-backend@0.2.78-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.8.1-next.0
  - @backstage/catalog-client@1.2.0-next.0
  - @backstage/plugin-catalog-backend@1.6.0-next.0
  - @backstage/plugin-events-backend@0.2.0-next.0
  - @backstage/plugin-search-backend-node@1.0.5-next.0
  - @backstage/plugin-events-node@0.2.0-next.0
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/integration@1.4.1-next.0
  - @backstage/plugin-app-backend@0.3.39-next.0
  - @backstage/plugin-auth-backend@0.17.2-next.0
  - @backstage/plugin-auth-node@0.2.8-next.0
  - @backstage/plugin-azure-devops-backend@0.3.18-next.0
  - @backstage/plugin-azure-sites-backend@0.1.1-next.0
  - @backstage/plugin-code-coverage-backend@0.2.5-next.0
  - @backstage/plugin-graphql-backend@0.1.29-next.0
  - @backstage/plugin-jenkins-backend@0.1.29-next.0
  - @backstage/plugin-permission-backend@0.5.14-next.0
  - @backstage/plugin-permission-common@0.7.2-next.0
  - @backstage/plugin-permission-node@0.7.2-next.0
  - @backstage/plugin-playlist-backend@0.2.2-next.0
  - @backstage/plugin-proxy-backend@0.2.33-next.0
  - @backstage/plugin-rollbar-backend@0.1.36-next.0
  - @backstage/plugin-techdocs-backend@1.4.2-next.0
  - @backstage/plugin-todo-backend@0.1.36-next.0
  - @backstage/plugin-kubernetes-backend@0.8.1-next.0
  - example-app@0.2.78-next.0
  - @backstage/plugin-scaffolder-backend-module-rails@0.4.7-next.0
  - @backstage/plugin-badges-backend@0.1.33-next.0
  - @backstage/plugin-catalog-node@1.2.2-next.0
  - @backstage/plugin-tech-insights-backend@0.5.5-next.0
  - @backstage/backend-tasks@0.3.8-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/plugin-kafka-backend@0.2.32-next.0
  - @backstage/plugin-search-backend@1.1.2-next.0
  - @backstage/plugin-search-backend-module-elasticsearch@1.0.5-next.0
  - @backstage/plugin-search-backend-module-pg@0.4.3-next.0
  - @backstage/plugin-search-common@1.1.2-next.0
  - @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.23-next.0
  - @backstage/plugin-tech-insights-node@0.3.7-next.0

## example-backend-next@0.0.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.8.1-next.0
  - @backstage/plugin-catalog-backend@1.6.0-next.0
  - @backstage/plugin-app-backend@0.3.39-next.0
  - @backstage/backend-defaults@0.1.4-next.0

## techdocs-cli-embedded-app@0.2.77-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs-react@1.0.7-next.0
  - @backstage/cli@0.21.2-next.0
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-app-api@1.2.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/integration-react@1.1.7-next.0
  - @backstage/test-utils@1.2.3-next.0
  - @backstage/plugin-techdocs@1.4.1-next.0
  - @backstage/plugin-catalog@1.6.2-next.0
  - @backstage/app-defaults@1.0.9-next.0
  - @backstage/catalog-model@1.1.4-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/theme@0.2.16

## @internal/plugin-catalog-customized@0.0.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.2-next.0
  - @backstage/plugin-catalog@1.6.2-next.0

## @internal/plugin-todo-list@1.0.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.0
  - @backstage/core-plugin-api@1.1.1-next.0
  - @backstage/theme@0.2.16

## @internal/plugin-todo-list-backend@1.0.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.1-next.0
  - @backstage/plugin-auth-node@0.2.8-next.0
  - @backstage/config@1.0.5-next.0
  - @backstage/errors@1.1.4-next.0

## @internal/plugin-todo-list-common@1.0.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.0
