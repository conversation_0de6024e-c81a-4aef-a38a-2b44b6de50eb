# Release v1.8.0-next.2

## @backstage/cli@0.21.0-next.1

### Minor Changes

- 384eaa2307: Switched `tsconfig.json` to target and support `ES2021`, in line with the bump to Node.js 16 & 18.

### Patch Changes

- 88f99b8b13: Bumped `tar` dependency to `^6.1.12` in order to ensure Node.js v18 compatibility.
- 969a8444ea: Updated dependency `esbuild` to `^0.15.0`.
- Updated dependencies
  - @backstage/release-manifests@0.0.7-next.0
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.4-next.0
  - @backstage/config-loader@1.1.6-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-org@0.6.0-next.1

### Minor Changes

- 0b11500151: Updates the User and Group Profile cards to add the links from the UserEntity or the GroupEntity

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-org-react@0.1.0-next.0

### Minor Changes

- e96274f1fe: Implemented the org-react plugin, with it's first component being: a `GroupListPicker` component that will give the user the ability to choose a group

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-scaffolder-backend@1.8.0-next.2

### Minor Changes

- 5025d2e8b6: Adds the ability to pass (an optional) array of strings that will be applied to the newly scaffolded repository as topic labels.

### Patch Changes

- 969a8444ea: Updated dependency `esbuild` to `^0.15.0`.
- 9ff4ff3745: Implement "Branch protection rules" support for "publish:github" action
- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-plugin-api@0.1.4-next.1
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/plugin-auth-node@0.2.7-next.1
  - @backstage/plugin-catalog-backend@1.5.1-next.1
  - @backstage/plugin-catalog-node@1.2.1-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-scaffolder-common@1.2.2-next.0

## @backstage/plugin-splunk-on-call@0.4.0-next.1

### Minor Changes

- 34b772ef31: Use the routing key if it's available instead of team name when triggering incidents.

  BREAKING CHANGE:
  Before, the team name was used even if the routing key (with or without team) was used.
  Now, the routing key defined for the component will be used instead of the team name.

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/app-defaults@1.0.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-app-api@1.2.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-permission-react@0.4.7-next.0

## @backstage/backend-app-api@0.2.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-plugin-api@0.1.4-next.1
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/plugin-permission-node@0.7.1-next.1
  - @backstage/errors@1.1.3-next.0

## @backstage/backend-common@0.16.0-next.1

### Patch Changes

- 88f99b8b13: Bumped `tar` dependency to `^6.1.12` in order to ensure Node.js v18 compatibility.
- cfb30b700c: Pin `@kubernetes/client-node` version to `0.17.0`.
- Updated dependencies
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.4-next.0
  - @backstage/config-loader@1.1.6-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/backend-defaults@0.1.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-app-api@0.2.3-next.1
  - @backstage/backend-plugin-api@0.1.4-next.1

## @backstage/backend-plugin-api@0.1.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0

## @backstage/backend-tasks@0.3.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/backend-test-utils@0.1.30-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/cli@0.21.0-next.1
  - @backstage/backend-app-api@0.2.3-next.1
  - @backstage/backend-plugin-api@0.1.4-next.1
  - @backstage/config@1.0.4-next.0

## @backstage/codemods@0.1.41-next.0

### Patch Changes

- 58502ec285: Updated dependency `jscodeshift` to `^0.14.0`.
- Updated dependencies
  - @backstage/cli-common@0.1.10

## @backstage/core-components@0.12.0-next.1

### Patch Changes

- b4fb5c8ecc: MissingAnnotationEmptyState now accepts either a string or an array of strings to support multiple missing annotations.
- Updated dependencies
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/version-bridge@1.0.1

## @backstage/create-app@0.4.34-next.2

### Patch Changes

- 384eaa2307: Switched Node.js version to support version 16 & 18, rather than 14 & 16. To switch the Node.js version in your own project, apply the following change to the root `package.json`:

  ```diff
     "engines": {
  -    "node": "14 || 16"
  +    "node": "16 || 18"
     },
  ```

  As well as the following change to `packages/app/package.json`:

  ```diff
  -    "@types/node": "^14.14.32",
  +    "@types/node": "^16.11.26",
  ```

- 864c876e57: Fixed incorrect comments in the templated `app-config.yaml` and `app-config.production.yaml`. The `backend.listen` directive is not in fact needed to override the `backend.baseUrl`, the backend listens to all interfaces by default. The configuration has also been updated to listen to all interfaces, rather than just IPv4 ones, as this is required for Node.js v18. The production configuration now also shows the option to specify `backend.listen` as a single string.

  To apply this changes to an existing app, make the following change to `app-config.yaml`:

  ```diff
  -    # Uncomment the following host directive to bind to all IPv4 interfaces and
  -    # not just the baseUrl hostname.
  -    # host: 0.0.0.0
  +    # Uncomment the following host directive to bind to specific interfaces
  +    # host: 127.0.0.1
  ```

  And the following change to `app-config.production.yaml`:

  ```diff
  -  listen:
  -    port: 7007
  -    # The following host directive binds to all IPv4 interfaces when its value
  -    # is "0.0.0.0". This is the most permissive setting. The right value depends
  -    # on your specific deployment. If you remove the host line entirely, the
  -    # backend will bind on the interface that corresponds to the backend.baseUrl
  -    # hostname.
  -    host: 0.0.0.0
  +  # The listener can also be expressed as a single <host>:<port> string. In this case we bind to
  +  # all interfaces, the most permissive setting. The right value depends on your specific deployment.
  +  listen: ':7007'
  ```

- Updated dependencies
  - @backstage/cli-common@0.1.10

## @backstage/dev-utils@1.0.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/app-defaults@1.0.8-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-app-api@1.2.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration-react@1.1.6-next.1
  - @backstage/test-utils@1.2.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/integration-react@1.1.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/theme@0.2.16

## @backstage/release-manifests@0.0.7-next.0

### Patch Changes

- a4496131fa: Added a fallback that fetches manifests from `https://raw.githubusercontent.com` if `https://versions.backstage.io` is unavailable.

## @techdocs/cli@1.2.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-techdocs-node@1.4.2-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-adr@0.2.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration-react@1.1.6-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-adr-common@0.2.3-next.0
  - @backstage/plugin-catalog-react@1.2.1-next.1
  - @backstage/plugin-search-common@1.1.1-next.0
  - @backstage/plugin-search-react@1.2.1-next.1

## @backstage/plugin-adr-backend@0.2.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/plugin-adr-common@0.2.3-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-airbrake@0.3.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/dev-utils@1.0.8-next.1
  - @backstage/test-utils@1.2.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-airbrake-backend@0.2.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-allure@0.1.27-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-analytics-module-ga@0.1.22-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-apache-airflow@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-plugin-api@1.1.0-next.0

## @backstage/plugin-api-docs@0.8.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog@1.6.1-next.1
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-apollo-explorer@0.1.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-app-backend@0.3.38-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-plugin-api@0.1.4-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/config-loader@1.1.6-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-auth-backend@0.17.1-next.1

### Patch Changes

- 0d6837ca4e: Fix wrong GitHub callback URL documentation
- abaed9770e: Improve logging
- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-auth-node@0.2.7-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-auth-node@0.2.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-azure-devops@0.2.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-devops-common@0.3.0
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-azure-devops-backend@0.3.17-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-azure-sites@0.1.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-sites-common@0.1.0-next.0
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-azure-sites-backend@0.1.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/plugin-azure-sites-common@0.1.0-next.0

## @backstage/plugin-badges@0.2.35-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-badges-backend@0.1.32-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-bazaar@0.2.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.21.0-next.1
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-catalog@1.6.1-next.1
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-bazaar-backend@0.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-test-utils@0.1.30-next.1
  - @backstage/plugin-auth-node@0.2.7-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-bitrise@0.1.38-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-catalog@1.6.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration-react@1.1.6-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-catalog-common@1.0.8-next.0
  - @backstage/plugin-catalog-react@1.2.1-next.1
  - @backstage/plugin-search-common@1.1.1-next.0
  - @backstage/plugin-search-react@1.2.1-next.1

## @backstage/plugin-catalog-backend@1.5.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-plugin-api@0.1.4-next.1
  - @backstage/plugin-catalog-node@1.2.1-next.1
  - @backstage/plugin-permission-node@0.7.1-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-catalog-common@1.0.8-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/plugin-scaffolder-common@1.2.2-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-catalog-backend-module-aws@0.1.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-plugin-api@0.1.4-next.1
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/plugin-catalog-backend@1.5.1-next.1
  - @backstage/plugin-catalog-node@1.2.1-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-catalog-backend-module-azure@0.1.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-plugin-api@0.1.4-next.1
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/plugin-catalog-backend@1.5.1-next.1
  - @backstage/plugin-catalog-node@1.2.1-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-catalog-backend-module-bitbucket@0.2.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-catalog-backend@1.5.1-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.1-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.1.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.1.4-next.1
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/plugin-catalog-backend@1.5.1-next.1
  - @backstage/plugin-catalog-node@1.2.1-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.1-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.1.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-plugin-api@0.1.4-next.1
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/plugin-catalog-backend@1.5.1-next.1
  - @backstage/plugin-catalog-node@1.2.1-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0

## @backstage/plugin-catalog-backend-module-gerrit@0.1.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-plugin-api@0.1.4-next.1
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/plugin-catalog-backend@1.5.1-next.1
  - @backstage/plugin-catalog-node@1.2.1-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0

## @backstage/plugin-catalog-backend-module-github@0.2.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-plugin-api@0.1.4-next.1
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/plugin-catalog-backend@1.5.1-next.1
  - @backstage/plugin-catalog-node@1.2.1-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-catalog-backend-module-gitlab@0.1.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-plugin-api@0.1.4-next.1
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/plugin-catalog-backend@1.5.1-next.1
  - @backstage/plugin-catalog-node@1.2.1-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-catalog-backend-module-ldap@0.5.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/plugin-catalog-backend@1.5.1-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-catalog-backend-module-msgraph@0.4.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.1.4-next.1
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/plugin-catalog-backend@1.5.1-next.1
  - @backstage/plugin-catalog-node@1.2.1-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-catalog-backend-module-openapi@0.1.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-catalog-backend@1.5.1-next.1
  - @backstage/plugin-catalog-node@1.2.1-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-catalog-graph@0.2.23-next.1

### Patch Changes

- da0bf25d1a: Preserve graph options and increment `maxDepth` by 1.

  The change will preserve options used at the `CatalogGraphCard`
  (displayed at the entity page) and additionally, increments the
  `maxDepth` option by 1 to increase the scope slightly compared to
  the graph already seen by the users.

  The default for `maxDepth` at `CatalogGraphCard` is 1.

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-catalog-import@0.9.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/integration-react@1.1.6-next.1
  - @backstage/plugin-catalog-common@1.0.8-next.0
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-catalog-node@1.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.1.4-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-catalog-common@1.0.8-next.0

## @backstage/plugin-catalog-react@1.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.1-next.0
  - @backstage/version-bridge@1.0.1
  - @backstage/plugin-catalog-common@1.0.8-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/plugin-permission-react@0.4.7-next.0

## @backstage/plugin-cicd-statistics@0.1.13-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-cicd-statistics-module-gitlab@0.1.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/plugin-cicd-statistics@0.1.13-next.1

## @backstage/plugin-circleci@0.3.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-cloudbuild@0.3.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-code-climate@0.1.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-code-coverage@0.2.4-next.1

### Patch Changes

- fcab2579a0: Adds installation instructions
- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-code-coverage-backend@0.2.4-next.1

### Patch Changes

- fcab2579a0: Adds installation instructions
- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0

## @backstage/plugin-codescene@0.1.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-config-schema@0.1.34-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-cost-insights@0.12.0-next.1

### Patch Changes

- e92aa15f01: Bumped `canvas` dependency to the latest version, which has better Node.js v18 support.
- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-cost-insights-common@0.1.1

## @backstage/plugin-dynatrace@1.0.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-explore@0.3.42-next.1

### Patch Changes

- 5c25ce6d9e: Added a section to explore plugin README that describes the customization of explore tools content.
- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1
  - @backstage/plugin-explore-react@0.0.23-next.0

## @backstage/plugin-firehydrant@0.1.28-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-fossa@0.2.43-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-gcalendar@0.3.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-gcp-projects@0.3.30-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-git-release-manager@0.3.24-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-github-actions@0.5.11-next.1

### Patch Changes

- ed438a3ba5: Add error panel when the plugin fails.
- 0d6837ca4e: Fix wrong GitHub callback URL documentation
- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-github-deployments@0.1.42-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/integration-react@1.1.6-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-github-issues@0.2.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-github-pull-requests-board@0.1.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-gitops-profiles@0.3.29-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-gocd@0.1.17-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-graphiql@0.2.43-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-graphql-backend@0.1.28-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/plugin-catalog-graphql@0.3.15-next.0

## @backstage/plugin-home@0.4.27-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1
  - @backstage/plugin-stack-overflow@0.1.7-next.1

## @backstage/plugin-ilert@0.2.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-jenkins@0.7.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1
  - @backstage/plugin-jenkins-common@0.1.10-next.0

## @backstage/plugin-jenkins-backend@0.1.28-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-auth-node@0.2.7-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-jenkins-common@0.1.10-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0

## @backstage/plugin-kafka@0.3.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-kafka-backend@0.2.31-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-kubernetes@0.7.4-next.1

### Patch Changes

- cfb30b700c: Pin `@kubernetes/client-node` version to `0.17.0`.
- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/plugin-kubernetes-common@0.4.4-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-kubernetes-backend@0.8.0-next.1

### Patch Changes

- cfb30b700c: Pin `@kubernetes/client-node` version to `0.17.0`.
- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-kubernetes-common@0.4.4-next.1
  - @backstage/plugin-auth-node@0.2.7-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-kubernetes-common@0.4.4-next.1

### Patch Changes

- cfb30b700c: Pin `@kubernetes/client-node` version to `0.17.0`.
- Updated dependencies
  - @backstage/catalog-model@1.1.3-next.0

## @backstage/plugin-lighthouse@0.3.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-newrelic@0.3.29-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic-dashboard@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-pagerduty@0.5.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-periskop@0.1.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-periskop-backend@0.1.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-permission-backend@0.5.13-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-auth-node@0.2.7-next.1
  - @backstage/plugin-permission-node@0.7.1-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0

## @backstage/plugin-permission-node@0.7.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-auth-node@0.2.7-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0

## @backstage/plugin-playlist@0.1.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.8-next.0
  - @backstage/plugin-catalog-react@1.2.1-next.1
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/plugin-permission-react@0.4.7-next.0
  - @backstage/plugin-playlist-common@0.1.2-next.0
  - @backstage/plugin-search-react@1.2.1-next.1

## @backstage/plugin-playlist-backend@0.2.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-test-utils@0.1.30-next.1
  - @backstage/plugin-auth-node@0.2.7-next.1
  - @backstage/plugin-permission-node@0.7.1-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/plugin-playlist-common@0.1.2-next.0

## @backstage/plugin-proxy-backend@0.2.32-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-rollbar@0.4.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-rollbar-backend@0.1.35-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/config@1.0.4-next.0

## @backstage/plugin-scaffolder@1.8.0-next.1

### Patch Changes

- 580285787d: The `create` and `click` analytics events are now also captured on the "next" version of the component creation page.
- 3b3fc3cc3c: Fix `formData` not being present in the `next` version
- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/integration-react@1.1.6-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-catalog-common@1.0.8-next.0
  - @backstage/plugin-catalog-react@1.2.1-next.1
  - @backstage/plugin-permission-react@0.4.7-next.0
  - @backstage/plugin-scaffolder-common@1.2.2-next.0

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.2.13-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-scaffolder-backend@1.8.0-next.2
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-scaffolder-backend-module-rails@0.4.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-scaffolder-backend@1.8.0-next.2
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-scaffolder-backend-module-yeoman@0.2.11-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.8.0-next.2
  - @backstage/config@1.0.4-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-search@1.0.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.1-next.0
  - @backstage/version-bridge@1.0.1
  - @backstage/plugin-catalog-react@1.2.1-next.1
  - @backstage/plugin-search-common@1.1.1-next.0
  - @backstage/plugin-search-react@1.2.1-next.1

## @backstage/plugin-search-backend@1.1.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-auth-node@0.2.7-next.1
  - @backstage/plugin-permission-node@0.7.1-next.1
  - @backstage/plugin-search-backend-node@1.0.4-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-search-backend-module-elasticsearch@1.0.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-backend-node@1.0.4-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-search-backend-module-pg@0.4.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-search-backend-node@1.0.4-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-search-backend-node@1.0.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-search-react@1.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.1-next.0
  - @backstage/version-bridge@1.0.1
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-sentry@0.4.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-shortcuts@0.3.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-sonarqube@0.4.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-sonarqube-backend@0.1.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-stack-overflow@0.1.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-home@0.4.27-next.1
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-stack-overflow-backend@0.1.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.21.0-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-tech-insights@0.3.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-catalog-react@1.2.1-next.1
  - @backstage/plugin-tech-insights-common@0.2.8-next.0

## @backstage/plugin-tech-insights-backend@0.5.4-next.1

### Patch Changes

- f12e9e5b8c: Add Documentation on 404 Errors
- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/plugin-tech-insights-node@0.3.6-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-tech-insights-common@0.2.8-next.0

## @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.22-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-tech-insights-node@0.3.6-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/plugin-tech-insights-common@0.2.8-next.0

## @backstage/plugin-tech-insights-node@0.3.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/types@1.0.1-next.0
  - @backstage/plugin-tech-insights-common@0.2.8-next.0

## @backstage/plugin-tech-radar@0.5.18-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs@1.4.0-next.2

### Patch Changes

- e92aa15f01: Bumped `canvas` dependency to the latest version, which has better Node.js v18 support.
- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/integration-react@1.1.6-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1
  - @backstage/plugin-search-common@1.1.1-next.0
  - @backstage/plugin-search-react@1.2.1-next.1
  - @backstage/plugin-techdocs-react@1.0.6-next.1

## @backstage/plugin-techdocs-addons-test-utils@1.0.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs@1.4.0-next.2
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-app-api@1.2.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration-react@1.1.6-next.1
  - @backstage/test-utils@1.2.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog@1.6.1-next.1
  - @backstage/plugin-search-react@1.2.1-next.1
  - @backstage/plugin-techdocs-react@1.0.6-next.1

## @backstage/plugin-techdocs-backend@1.4.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-techdocs-node@1.4.2-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/plugin-catalog-common@1.0.8-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-techdocs-module-addons-contrib@1.0.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/integration-react@1.1.6-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-techdocs-react@1.0.6-next.1

## @backstage/plugin-techdocs-node@1.4.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## @backstage/plugin-techdocs-react@1.0.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/version-bridge@1.0.1

## @backstage/plugin-todo@0.2.13-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-todo-backend@0.1.35-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/integration@1.4.0-next.0

## @backstage/plugin-user-settings@0.5.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-app-api@1.2.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-user-settings-backend@0.1.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-auth-node@0.2.7-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/types@1.0.1-next.0

## @backstage/plugin-vault@0.1.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @backstage/plugin-vault-backend@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/backend-test-utils@0.1.30-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0

## @backstage/plugin-xcmetrics@0.2.31-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/errors@1.1.3-next.0
  - @backstage/theme@0.2.16

## example-app@0.2.77-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder@1.8.0-next.1
  - @backstage/plugin-cost-insights@0.12.0-next.1
  - @backstage/plugin-techdocs@1.4.0-next.2
  - @backstage/plugin-explore@0.3.42-next.1
  - @backstage/plugin-github-actions@0.5.11-next.1
  - @backstage/cli@0.21.0-next.1
  - @backstage/plugin-catalog-graph@0.2.23-next.1
  - @backstage/core-components@0.12.0-next.1
  - @backstage/plugin-code-coverage@0.2.4-next.1
  - @backstage/plugin-org@0.6.0-next.1
  - @backstage/plugin-kubernetes@0.7.4-next.1
  - @backstage/app-defaults@1.0.8-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/core-app-api@1.2.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration-react@1.1.6-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-airbrake@0.3.11-next.1
  - @backstage/plugin-apache-airflow@0.2.4-next.1
  - @backstage/plugin-api-docs@0.8.11-next.1
  - @backstage/plugin-azure-devops@0.2.2-next.1
  - @backstage/plugin-azure-sites@0.1.0-next.1
  - @backstage/plugin-badges@0.2.35-next.1
  - @backstage/plugin-catalog-common@1.0.8-next.0
  - @backstage/plugin-catalog-import@0.9.1-next.1
  - @backstage/plugin-catalog-react@1.2.1-next.1
  - @backstage/plugin-circleci@0.3.11-next.1
  - @backstage/plugin-cloudbuild@0.3.11-next.1
  - @backstage/plugin-dynatrace@1.0.1-next.1
  - @backstage/plugin-gcalendar@0.3.7-next.1
  - @backstage/plugin-gcp-projects@0.3.30-next.1
  - @backstage/plugin-gocd@0.1.17-next.1
  - @backstage/plugin-graphiql@0.2.43-next.1
  - @backstage/plugin-home@0.4.27-next.1
  - @backstage/plugin-jenkins@0.7.10-next.1
  - @backstage/plugin-kafka@0.3.11-next.1
  - @backstage/plugin-lighthouse@0.3.11-next.1
  - @backstage/plugin-newrelic@0.3.29-next.1
  - @backstage/plugin-newrelic-dashboard@0.2.4-next.1
  - @backstage/plugin-pagerduty@0.5.4-next.1
  - @backstage/plugin-permission-react@0.4.7-next.0
  - @backstage/plugin-playlist@0.1.2-next.2
  - @backstage/plugin-rollbar@0.4.11-next.1
  - @backstage/plugin-search@1.0.4-next.1
  - @backstage/plugin-search-common@1.1.1-next.0
  - @backstage/plugin-search-react@1.2.1-next.1
  - @backstage/plugin-sentry@0.4.4-next.1
  - @backstage/plugin-shortcuts@0.3.3-next.1
  - @backstage/plugin-stack-overflow@0.1.7-next.1
  - @backstage/plugin-tech-insights@0.3.3-next.1
  - @backstage/plugin-tech-radar@0.5.18-next.1
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.6-next.1
  - @backstage/plugin-techdocs-react@1.0.6-next.1
  - @backstage/plugin-todo@0.2.13-next.1
  - @backstage/plugin-user-settings@0.5.1-next.1
  - @internal/plugin-catalog-customized@0.0.4-next.1

## example-backend@0.2.77-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-backend@0.17.1-next.1
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-scaffolder-backend@1.8.0-next.2
  - @backstage/plugin-code-coverage-backend@0.2.4-next.1
  - @backstage/plugin-kubernetes-backend@0.8.0-next.1
  - @backstage/plugin-tech-insights-backend@0.5.4-next.1
  - example-app@0.2.77-next.2
  - @backstage/backend-tasks@0.3.7-next.1
  - @backstage/plugin-app-backend@0.3.38-next.1
  - @backstage/plugin-auth-node@0.2.7-next.1
  - @backstage/plugin-azure-devops-backend@0.3.17-next.2
  - @backstage/plugin-azure-sites-backend@0.1.0-next.1
  - @backstage/plugin-badges-backend@0.1.32-next.1
  - @backstage/plugin-catalog-backend@1.5.1-next.1
  - @backstage/plugin-graphql-backend@0.1.28-next.1
  - @backstage/plugin-jenkins-backend@0.1.28-next.1
  - @backstage/plugin-kafka-backend@0.2.31-next.1
  - @backstage/plugin-permission-backend@0.5.13-next.1
  - @backstage/plugin-permission-node@0.7.1-next.1
  - @backstage/plugin-playlist-backend@0.2.1-next.2
  - @backstage/plugin-proxy-backend@0.2.32-next.1
  - @backstage/plugin-rollbar-backend@0.1.35-next.1
  - @backstage/plugin-scaffolder-backend-module-rails@0.4.6-next.2
  - @backstage/plugin-search-backend@1.1.1-next.1
  - @backstage/plugin-search-backend-module-elasticsearch@1.0.4-next.1
  - @backstage/plugin-search-backend-module-pg@0.4.2-next.1
  - @backstage/plugin-search-backend-node@1.0.4-next.1
  - @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.22-next.1
  - @backstage/plugin-tech-insights-node@0.3.6-next.1
  - @backstage/plugin-techdocs-backend@1.4.1-next.1
  - @backstage/plugin-todo-backend@0.1.35-next.1
  - @backstage/catalog-client@1.1.2-next.0
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/integration@1.4.0-next.0
  - @backstage/plugin-permission-common@0.7.1-next.0
  - @backstage/plugin-search-common@1.1.1-next.0

## example-backend-next@0.0.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.8.0-next.2
  - @backstage/plugin-app-backend@0.3.38-next.1
  - @backstage/plugin-catalog-backend@1.5.1-next.1
  - @backstage/backend-defaults@0.1.3-next.1

## techdocs-cli-embedded-app@0.2.76-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs@1.4.0-next.2
  - @backstage/cli@0.21.0-next.1
  - @backstage/core-components@0.12.0-next.1
  - @backstage/app-defaults@1.0.8-next.1
  - @backstage/catalog-model@1.1.3-next.0
  - @backstage/config@1.0.4-next.0
  - @backstage/core-app-api@1.2.0-next.0
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/integration-react@1.1.6-next.1
  - @backstage/test-utils@1.2.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog@1.6.1-next.1
  - @backstage/plugin-techdocs-react@1.0.6-next.1

## @internal/plugin-catalog-customized@0.0.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.6.1-next.1
  - @backstage/plugin-catalog-react@1.2.1-next.1

## @internal/plugin-todo-list@1.0.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.0-next.1
  - @backstage/core-plugin-api@1.1.0-next.0
  - @backstage/theme@0.2.16

## @internal/plugin-todo-list-backend@1.0.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.16.0-next.1
  - @backstage/plugin-auth-node@0.2.7-next.1
  - @backstage/config@1.0.4-next.0
  - @backstage/errors@1.1.3-next.0
