# Release v1.34.0-next.2

Upgrade Helper: [https://backstage.github.io/upgrade-helper/?to=1.34.0-next.2](https://backstage.github.io/upgrade-helper/?to=1.34.0-next.2)

## @backstage/backend-app-api@1.1.0-next.2

### Minor Changes

- ebf083d: Service factories added by feature loaders now have lower priority and will be ignored if a factory for the same service is added directly by `backend.add(serviceFactory)`.

### Patch Changes

- 0e9c9fa: As soon as a backend termination signal is received, call before shutting down root lifecycle hooks.
- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-permission-node@0.8.6-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/config-loader@1.9.3-next.1
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0

## @backstage/backend-defaults@0.6.0-next.2

### Minor Changes

- fd5d337: Added a new `backend.health.headers` configuration that can be used to set additional headers to include in health check responses.

  **BREAKING CONSUMERS**: As part of this change the `createHealthRouter` function exported from `@backstage/backend-defaults/rootHttpRouter` now requires the root config service to be passed through the `config` option.

- 3f34ea9: Throttles Bitbucket Server API calls

### Patch Changes

- dfc8b41: Updated dependency `@opentelemetry/api` to `^1.9.0`.
- 57e0b11: The user and plugin token verification in the default `AuthService` implementation will no longer forward verification errors to the caller, and instead log them as warnings.
- 57e0b11: The default `authServiceFactory` now correctly depends on the plugin scoped `Logger` services rather than the root scoped one.
- 0e9c9fa: Implements the `DefaultRootLifecycleService.addBeforeShutdownHook` method, and updates `DefaultRootHttpRouterService` and `DefaultRootHealthService` to listen to that event to stop accepting traffic and close service connections.
- d0cbd82: Remove use of the `stoppable` library on the `DefaultRootHttpRouterService` as Node's native http server [close](https://nodejs.org/api/http.html#serverclosecallback) method already drains requests.
  Also, we pass a new `lifecycleMiddleware` to the `rootHttpRouterServiceFactory` configure function that must be called manually if you don't call `applyDefaults`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/backend-app-api@1.1.0-next.2
  - @backstage/plugin-permission-node@0.8.6-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/cli-node@0.2.11-next.1
  - @backstage/config-loader@1.9.3-next.1
  - @backstage/backend-dev-utils@0.1.5
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/integration-aws-node@0.1.14-next.0
  - @backstage/types@1.2.0

## @backstage/backend-openapi-utils@0.4.0-next.2

### Minor Changes

- afcebea: Fixed a Typescript error when trying to use the new OpenAPI server-side generated code.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/types@1.2.0

## @backstage/plugin-catalog@1.26.0-next.2

### Minor Changes

- 25beb82: Adds an optional columns attribute to HasSubdomainsCardProps and changes its default columns
- 39f1abc: Consistent title behaviour across CatalogTable, CursorPaginatedCatalogTable, and OffsetPaginatedCatalogTable.

### Patch Changes

- Updated dependencies
  - @backstage/core-compat-api@0.3.4-next.2
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/integration-react@1.2.2-next.1
  - @backstage/types@1.2.0
  - @backstage/plugin-catalog-common@1.1.2-next.0
  - @backstage/plugin-permission-react@0.4.29-next.0
  - @backstage/plugin-scaffolder-common@1.5.8-next.1
  - @backstage/plugin-search-common@1.2.16-next.0
  - @backstage/plugin-search-react@1.8.4-next.2

## @backstage/plugin-catalog-backend@1.29.0-next.2

### Minor Changes

- c1307b4: Implement `/entities` in terms of `queryEntities` to not run into memory and performance problems on large catalogs

### Patch Changes

- dfc8b41: Updated dependency `@opentelemetry/api` to `^1.9.0`.
- 8013c9c: Perform the by-query count inlined with the main query
- feba9ee: Use a join based strategy for filtering, when having small page sizes
- 1fdb48e: Use a faster count method on pg when computing some metrics
- 0c33465: Implement `/entities/by-name/:kind/:namespace/:name` using `getEntitiesByRefs`
- d93390d: When parsing filters, do not make redundant `anyOf` and `allOf` nodes when there's only a single entry within them
- 24ecea8: Avoid extra ordering in by-query when the user doesn't ask for it
- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-permission-node@0.8.6-next.2
  - @backstage/backend-openapi-utils@0.4.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/plugin-search-backend-module-catalog@0.2.6-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/types@1.2.0
  - @backstage/plugin-catalog-common@1.1.2-next.0
  - @backstage/plugin-permission-common@0.8.3-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.3.0-next.2

### Minor Changes

- 3f34ea9: Throttles Bitbucket Server API calls

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1

## @backstage/plugin-catalog-backend-module-gitlab@0.6.0-next.2

### Minor Changes

- 99dce5c: Implemented discovery for top-level groups defined in config.group or if undefined global top-level group in Gitlab

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.6.0-next.2
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/plugin-catalog-common@1.1.2-next.0

## @backstage/plugin-notifications@0.5.0-next.2

### Minor Changes

- fc15b77: Switched to using the new `/notifications` endpoints. Be sure to update the `notifications` plugin backend before deploying this frontend plugin change.

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/theme@0.6.3-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-notifications-common@0.0.7-next.0
  - @backstage/plugin-signals-react@0.0.8-next.0

## @backstage/plugin-notifications-backend@0.5.0-next.2

### Minor Changes

- fc15b77: **BREAKING**: Removed redundant `/health` endpoint, switch to using [the built-in endpoint instead](https://backstage.io/docs/backend-system/core-services/root-health).

### Patch Changes

- fc15b77: Deprecated root '/' endpoints, moving them under `/notifications` instead.
- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/plugin-notifications-node@0.2.10-next.2
  - @backstage/plugin-signals-node@0.1.15-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/plugin-notifications-common@0.0.7-next.0

## @backstage/app-defaults@1.5.15-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.15.3-next.1
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/theme@0.6.3-next.0
  - @backstage/plugin-permission-react@0.4.29-next.0

## @backstage/backend-dynamic-feature-service@0.5.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.6.0-next.2
  - @backstage/plugin-catalog-backend@1.29.0-next.2
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/backend-app-api@1.1.0-next.2
  - @backstage/plugin-permission-node@0.8.6-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-events-backend@0.4.0-next.2
  - @backstage/plugin-search-backend-node@1.3.6-next.2
  - @backstage/plugin-app-node@0.1.28-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/cli-node@0.2.11-next.1
  - @backstage/config-loader@1.9.3-next.1
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-permission-common@0.8.3-next.0
  - @backstage/plugin-search-common@1.2.16-next.0

## @backstage/backend-plugin-api@1.1.0-next.2

### Patch Changes

- 0e9c9fa: The `RootLifecycleService` now has a new `addBeforeShutdownHook` method, and hooks added through this method will run immediately when a termination event is received.

  The backend will not proceed with the shutdown and run the `Shutdown` hooks until all `BeforeShutdown` hooks have completed.

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-permission-common@0.8.3-next.0

## @backstage/backend-test-utils@1.2.0-next.2

### Patch Changes

- 0e9c9fa: Mock the new `RootLifecycleService.addBeforeShutdownHook` method.
- Updated dependencies
  - @backstage/backend-defaults@0.6.0-next.2
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/backend-app-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0

## @backstage/catalog-client@1.9.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/catalog-model@1.7.2-next.0

## @backstage/catalog-model@1.7.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/types@1.2.0

## @backstage/cli@0.29.3-next.2

### Patch Changes

- 62a9062: Updated dependency `@module-federation/enhanced` to `^0.8.0`.
- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/cli-node@0.2.11-next.1
  - @backstage/config-loader@1.9.3-next.1
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.1-next.0
  - @backstage/eslint-plugin@0.1.10
  - @backstage/integration@1.16.0-next.1
  - @backstage/release-manifests@0.0.12-next.1
  - @backstage/types@1.2.0

## @backstage/cli-node@0.2.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/cli-common@0.1.15
  - @backstage/types@1.2.0

## @backstage/config@1.3.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/types@1.2.0

## @backstage/config-loader@1.9.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0

## @backstage/core-app-api@1.15.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.1-next.0
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/types@1.2.0
  - @backstage/version-bridge@1.0.10

## @backstage/core-compat-api@0.3.4-next.2

### Patch Changes

- 1f30730: Updated dependency `@oriflame/backstage-plugin-score-card` to `^0.9.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/version-bridge@1.0.10

## @backstage/core-components@0.16.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/theme@0.6.3-next.0
  - @backstage/version-bridge@1.0.10

## @backstage/core-plugin-api@1.10.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0
  - @backstage/version-bridge@1.0.10

## @backstage/create-app@0.5.23-next.2

### Patch Changes

- e913fdf: Add github backend module to create-app and improve error messages
- Updated dependencies
  - @backstage/cli-common@0.1.15

## @backstage/dev-utils@1.1.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/app-defaults@1.5.15-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/core-app-api@1.15.3-next.1
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/integration-react@1.2.2-next.1
  - @backstage/theme@0.6.3-next.0

## @backstage/errors@1.2.6-next.0

### Patch Changes

- 1d4b5b9: Trim `error.cause.stack` in addition to `error.stack` when trimming stack traces from serialized errors.
- Updated dependencies
  - @backstage/types@1.2.0

## @backstage/frontend-app-api@0.10.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/core-app-api@1.15.3-next.1
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-defaults@0.1.4-next.2
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/types@1.2.0
  - @backstage/version-bridge@1.0.10

## @backstage/frontend-defaults@0.1.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/frontend-app-api@0.10.3-next.2
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/plugin-app@0.1.4-next.2

## @backstage/frontend-plugin-api@0.9.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/types@1.2.0
  - @backstage/version-bridge@1.0.10

## @backstage/frontend-test-utils@0.2.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.1-next.0
  - @backstage/frontend-app-api@0.10.3-next.2
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/test-utils@1.7.3-next.1
  - @backstage/types@1.2.0
  - @backstage/version-bridge@1.0.10
  - @backstage/plugin-app@0.1.4-next.2

## @backstage/integration@1.16.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/config@1.3.1-next.0

## @backstage/integration-aws-node@0.1.14-next.0

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/config@1.3.1-next.0

## @backstage/integration-react@1.2.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.1-next.0
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/integration@1.16.0-next.1

## @backstage/repo-tools@0.12.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/cli-node@0.2.11-next.1
  - @backstage/config-loader@1.9.3-next.1
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/cli-common@0.1.15

## @techdocs/cli@1.8.24-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.6.0-next.2
  - @backstage/plugin-techdocs-node@1.12.15-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.1-next.0

## @backstage/test-utils@1.7.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.1-next.0
  - @backstage/core-app-api@1.15.3-next.1
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/theme@0.6.3-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-permission-common@0.8.3-next.0
  - @backstage/plugin-permission-react@0.4.29-next.0

## @backstage/plugin-api-docs@0.12.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.26.0-next.2
  - @backstage/core-compat-api@0.3.4-next.2
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/plugin-catalog-common@1.1.2-next.0
  - @backstage/plugin-permission-react@0.4.29-next.0

## @backstage/plugin-app@0.1.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/integration-react@1.2.2-next.1
  - @backstage/theme@0.6.3-next.0
  - @backstage/plugin-permission-react@0.4.29-next.0

## @backstage/plugin-app-backend@0.4.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-app-node@0.1.28-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/config-loader@1.9.3-next.1
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0

## @backstage/plugin-app-node@0.1.28-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/config-loader@1.9.3-next.1

## @backstage/plugin-app-visualizer@0.1.14-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2

## @backstage/plugin-auth-backend@0.24.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-backend-module-atlassian-provider@0.3.3-next.2
  - @backstage/plugin-auth-backend-module-auth0-provider@0.1.3-next.2
  - @backstage/plugin-auth-backend-module-bitbucket-provider@0.2.3-next.2
  - @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.1.3-next.2
  - @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.3.3-next.2
  - @backstage/plugin-auth-backend-module-github-provider@0.2.3-next.2
  - @backstage/plugin-auth-backend-module-gitlab-provider@0.2.3-next.2
  - @backstage/plugin-auth-backend-module-microsoft-provider@0.2.3-next.2
  - @backstage/plugin-auth-backend-module-oauth2-provider@0.3.3-next.2
  - @backstage/plugin-auth-backend-module-oidc-provider@0.3.3-next.2
  - @backstage/plugin-auth-backend-module-okta-provider@0.1.3-next.2
  - @backstage/plugin-auth-backend-module-onelogin-provider@0.2.3-next.2
  - @backstage/plugin-auth-backend-module-aws-alb-provider@0.3.1-next.2
  - @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.3-next.2
  - @backstage/plugin-auth-backend-module-gcp-iap-provider@0.3.3-next.2
  - @backstage/plugin-auth-backend-module-google-provider@0.2.3-next.2
  - @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.3-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0

## @backstage/plugin-auth-backend-module-atlassian-provider@0.3.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2

## @backstage/plugin-auth-backend-module-auth0-provider@0.1.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2

## @backstage/plugin-auth-backend-module-aws-alb-provider@0.3.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-backend@0.24.1-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2

## @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/catalog-model@1.7.2-next.0

## @backstage/plugin-auth-backend-module-bitbucket-provider@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2

## @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.1.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2

## @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.3.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/config@1.3.1-next.0

## @backstage/plugin-auth-backend-module-gcp-iap-provider@0.3.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/types@1.2.0

## @backstage/plugin-auth-backend-module-github-provider@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2

## @backstage/plugin-auth-backend-module-gitlab-provider@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2

## @backstage/plugin-auth-backend-module-google-provider@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2

## @backstage/plugin-auth-backend-module-guest-provider@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/catalog-model@1.7.2-next.0

## @backstage/plugin-auth-backend-module-microsoft-provider@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2

## @backstage/plugin-auth-backend-module-oauth2-provider@0.3.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2

## @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-node@0.5.5-next.2

## @backstage/plugin-auth-backend-module-oidc-provider@0.3.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-auth-backend@0.24.1-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2

## @backstage/plugin-auth-backend-module-okta-provider@0.1.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2

## @backstage/plugin-auth-backend-module-onelogin-provider@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2

## @backstage/plugin-auth-backend-module-pinniped-provider@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/config@1.3.1-next.0

## @backstage/plugin-auth-backend-module-vmware-cloud-provider@0.4.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/catalog-model@1.7.2-next.0

## @backstage/plugin-auth-node@0.5.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0

## @backstage/plugin-auth-react@0.1.10-next.2

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0

## @backstage/plugin-bitbucket-cloud-common@0.2.26-next.1

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.16.0-next.1

## @backstage/plugin-catalog-backend-module-aws@0.4.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.6.0-next.2
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/integration-aws-node@0.1.14-next.0
  - @backstage/plugin-catalog-common@1.1.2-next.0
  - @backstage/plugin-kubernetes-common@0.9.1-next.0

## @backstage/plugin-catalog-backend-module-azure@0.3.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/plugin-catalog-common@1.1.2-next.0

## @backstage/plugin-catalog-backend-module-backstage-openapi@0.4.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/backend-openapi-utils@0.4.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.4.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.26-next.1
  - @backstage/plugin-catalog-common@1.1.2-next.0

## @backstage/plugin-catalog-backend-module-gcp@0.3.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/plugin-kubernetes-common@0.9.1-next.0

## @backstage/plugin-catalog-backend-module-gerrit@0.2.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1

## @backstage/plugin-catalog-backend-module-github@0.7.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.29.0-next.2
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/plugin-catalog-common@1.1.2-next.0

## @backstage/plugin-catalog-backend-module-github-org@0.3.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-catalog-backend-module-github@0.7.8-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/config@1.3.1-next.0

## @backstage/plugin-catalog-backend-module-gitlab-org@0.2.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-catalog-backend-module-gitlab@0.6.0-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2

## @backstage/plugin-catalog-backend-module-incremental-ingestion@0.6.1-next.2

### Patch Changes

- dfc8b41: Updated dependency `@opentelemetry/api` to `^1.9.0`.
- d42ecb0: Remove backend-common package from incremental-ingestion plugin and update related code
- Updated dependencies
  - @backstage/backend-defaults@0.6.0-next.2
  - @backstage/plugin-catalog-backend@1.29.0-next.2
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-permission-common@0.8.3-next.0

## @backstage/plugin-catalog-backend-module-ldap@0.11.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-catalog-common@1.1.2-next.0

## @backstage/plugin-catalog-backend-module-logs@0.1.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.29.0-next.2
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2

## @backstage/plugin-catalog-backend-module-msgraph@0.6.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/plugin-catalog-common@1.1.2-next.0

## @backstage/plugin-catalog-backend-module-openapi@0.2.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.29.0-next.2
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/types@1.2.0
  - @backstage/plugin-catalog-common@1.1.2-next.0

## @backstage/plugin-catalog-backend-module-puppetdb@0.2.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0

## @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/plugin-catalog-common@1.1.2-next.0
  - @backstage/plugin-scaffolder-common@1.5.8-next.1

## @backstage/plugin-catalog-backend-module-unprocessed@0.5.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/plugin-catalog-unprocessed-entities-common@0.0.6-next.0
  - @backstage/plugin-permission-common@0.8.3-next.0

## @backstage/plugin-catalog-common@1.1.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/plugin-permission-common@0.8.3-next.0
  - @backstage/plugin-search-common@1.2.16-next.0

## @backstage/plugin-catalog-graph@0.4.14-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-compat-api@0.3.4-next.2
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/types@1.2.0

## @backstage/plugin-catalog-import@0.12.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-compat-api@0.3.4-next.2
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/integration@1.16.0-next.1
  - @backstage/integration-react@1.2.2-next.1
  - @backstage/plugin-catalog-common@1.1.2-next.0

## @backstage/plugin-catalog-node@1.15.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-permission-node@0.8.6-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-catalog-common@1.1.2-next.0
  - @backstage/plugin-permission-common@0.8.3-next.0

## @backstage/plugin-catalog-react@1.14.3-next.2

### Patch Changes

- 95092a6: Fixed an issue where `<CatalogFilterLayout.Filters />` would re-render its children on page load for smaller screens, potentially leading to unnecessary additional backend requests.
- 4a43398: Fixed an issue where the `EntityOwnerPicker` component failed to load when the `mode` prop was set to `owners-only`. In this mode, the `EntityOwnerPicker` does not load details about the owners, such as `displayName` or `title`. To display these details, use `mode=all` instead.
- Updated dependencies
  - @backstage/core-compat-api@0.3.4-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/frontend-test-utils@0.2.4-next.2
  - @backstage/integration-react@1.2.2-next.1
  - @backstage/types@1.2.0
  - @backstage/version-bridge@1.0.10
  - @backstage/plugin-catalog-common@1.1.2-next.0
  - @backstage/plugin-permission-common@0.8.3-next.0
  - @backstage/plugin-permission-react@0.4.29-next.0

## @backstage/plugin-catalog-unprocessed-entities@0.2.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0

## @backstage/plugin-catalog-unprocessed-entities-common@0.0.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.8.3-next.0

## @backstage/plugin-config-schema@0.1.63-next.2

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/types@1.2.0

## @backstage/plugin-devtools@0.1.22-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-compat-api@0.3.4-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/plugin-devtools-common@0.1.14-next.0
  - @backstage/plugin-permission-react@0.4.29-next.0

## @backstage/plugin-devtools-backend@0.5.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.6.0-next.2
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-permission-node@0.8.6-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/config-loader@1.9.3-next.1
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-devtools-common@0.1.14-next.0
  - @backstage/plugin-permission-common@0.8.3-next.0

## @backstage/plugin-devtools-common@0.1.14-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.0
  - @backstage/plugin-permission-common@0.8.3-next.0

## @backstage/plugin-events-backend@0.4.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/backend-openapi-utils@0.4.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0

## @backstage/plugin-events-backend-module-aws-sqs@0.4.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0

## @backstage/plugin-events-backend-module-azure@0.2.15-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2

## @backstage/plugin-events-backend-module-bitbucket-cloud@0.2.15-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2

## @backstage/plugin-events-backend-module-gerrit@0.2.15-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2

## @backstage/plugin-events-backend-module-github@0.2.15-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/config@1.3.1-next.0

## @backstage/plugin-events-backend-module-gitlab@0.2.15-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/config@1.3.1-next.0

## @backstage/plugin-events-backend-test-utils@0.1.39-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.6-next.2

## @backstage/plugin-events-node@0.4.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/types@1.2.0

## @backstage/plugin-home@0.8.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-compat-api@0.3.4-next.2
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/core-app-api@1.15.3-next.1
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/theme@0.6.3-next.0
  - @backstage/plugin-home-react@0.1.21-next.2

## @backstage/plugin-home-react@0.1.21-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0

## @backstage/plugin-kubernetes@0.12.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-compat-api@0.3.4-next.2
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/plugin-kubernetes-common@0.9.1-next.0
  - @backstage/plugin-kubernetes-react@0.5.2-next.2

## @backstage/plugin-kubernetes-backend@0.19.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-permission-node@0.8.6-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-kubernetes-node@0.2.1-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/integration-aws-node@0.1.14-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-kubernetes-common@0.9.1-next.0
  - @backstage/plugin-permission-common@0.8.3-next.0

## @backstage/plugin-kubernetes-cluster@0.0.20-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/plugin-kubernetes-common@0.9.1-next.0
  - @backstage/plugin-kubernetes-react@0.5.2-next.2

## @backstage/plugin-kubernetes-common@0.9.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-permission-common@0.8.3-next.0

## @backstage/plugin-kubernetes-node@0.2.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-kubernetes-common@0.9.1-next.0

## @backstage/plugin-kubernetes-react@0.5.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-kubernetes-common@0.9.1-next.0

## @backstage/plugin-notifications-backend-module-email@0.3.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/plugin-notifications-node@0.2.10-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/integration-aws-node@0.1.14-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-notifications-common@0.0.7-next.0

## @backstage/plugin-notifications-common@0.0.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.1-next.0

## @backstage/plugin-notifications-node@0.2.10-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-signals-node@0.1.15-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/plugin-notifications-common@0.0.7-next.0

## @backstage/plugin-org@0.6.34-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-compat-api@0.3.4-next.2
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/plugin-catalog-common@1.1.2-next.0

## @backstage/plugin-org-react@0.1.33-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0

## @backstage/plugin-permission-backend@0.5.52-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-permission-node@0.8.6-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/plugin-permission-common@0.8.3-next.0

## @backstage/plugin-permission-backend-module-allow-all-policy@0.2.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-permission-node@0.8.6-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/plugin-permission-common@0.8.3-next.0

## @backstage/plugin-permission-common@0.8.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.6-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0

## @backstage/plugin-permission-node@0.8.6-next.2

### Patch Changes

- b149e2a: The `createPermissionIntegrationRouter` function now detects and prevents the exposure of duplicate permissions.
- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/plugin-permission-common@0.8.3-next.0

## @backstage/plugin-permission-react@0.4.29-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.1-next.0
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/plugin-permission-common@0.8.3-next.0

## @backstage/plugin-proxy-backend@0.5.9-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0

## @backstage/plugin-scaffolder@1.27.2-next.2

### Patch Changes

- 184161f: Scaffolder field extensions registered with `FormFieldBlueprint` are now collected in the `useCustomFieldExtensions` hook, enabling them for use in the scaffolder.
- Updated dependencies
  - @backstage/core-compat-api@0.3.4-next.2
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/plugin-scaffolder-react@1.14.2-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/integration@1.16.0-next.1
  - @backstage/integration-react@1.2.2-next.1
  - @backstage/types@1.2.0
  - @backstage/plugin-catalog-common@1.1.2-next.0
  - @backstage/plugin-permission-react@0.4.29-next.0
  - @backstage/plugin-scaffolder-common@1.5.8-next.1

## @backstage/plugin-scaffolder-backend@1.28.0-next.2

### Patch Changes

- dfc8b41: Updated dependency `@opentelemetry/api` to `^1.9.0`.
- 6c326cf: The --no-node-snapshot check needs to be done against process.execArgv instead of process.argv
- e913fdf: Add github backend module to create-app and improve error messages
- 0851834: Resolved an issue where the `templateManagementPermission` was not being exposed through the `/permissions/metadata` endpoint.
- Updated dependencies
  - @backstage/backend-defaults@0.6.0-next.2
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-scaffolder-backend-module-github@0.5.4-next.2
  - @backstage/plugin-permission-node@0.8.6-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.3-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/plugin-scaffolder-backend-module-azure@0.2.4-next.2
  - @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.5-next.2
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.4-next.2
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.4-next.2
  - @backstage/plugin-scaffolder-backend-module-gerrit@0.2.4-next.2
  - @backstage/plugin-scaffolder-backend-module-gitea@0.2.4-next.2
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.7.0-next.2
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/types@1.2.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.26-next.1
  - @backstage/plugin-permission-common@0.8.3-next.0
  - @backstage/plugin-scaffolder-common@1.5.8-next.1

## @backstage/plugin-scaffolder-backend-module-azure@0.2.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1

## @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.4-next.2
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.4-next.2
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1

## @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.26-next.1

## @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1

## @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.3.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.6.0-next.2
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/types@1.2.0

## @backstage/plugin-scaffolder-backend-module-gcp@0.2.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1

## @backstage/plugin-scaffolder-backend-module-gerrit@0.2.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1

## @backstage/plugin-scaffolder-backend-module-gitea@0.2.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1

## @backstage/plugin-scaffolder-backend-module-github@0.5.4-next.2

### Patch Changes

- e913fdf: Add github backend module to create-app and improve error messages
- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1

## @backstage/plugin-scaffolder-backend-module-gitlab@0.7.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1

## @backstage/plugin-scaffolder-backend-module-notifications@0.1.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-notifications-node@0.2.10-next.2
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/plugin-notifications-common@0.0.7-next.0

## @backstage/plugin-scaffolder-backend-module-rails@0.5.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/types@1.2.0

## @backstage/plugin-scaffolder-backend-module-sentry@0.2.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/config@1.3.1-next.0

## @backstage/plugin-scaffolder-backend-module-yeoman@0.4.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/plugin-scaffolder-node-test-utils@0.1.17-next.2
  - @backstage/types@1.2.0

## @backstage/plugin-scaffolder-common@1.5.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-permission-common@0.8.3-next.0

## @backstage/plugin-scaffolder-node@0.6.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/types@1.2.0
  - @backstage/plugin-scaffolder-common@1.5.8-next.1

## @backstage/plugin-scaffolder-node-test-utils@0.1.17-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-test-utils@1.2.0-next.2
  - @backstage/plugin-scaffolder-node@0.6.2-next.2
  - @backstage/types@1.2.0

## @backstage/plugin-scaffolder-react@1.14.2-next.2

### Patch Changes

- 184161f: Scaffolder field extensions registered with `FormFieldBlueprint` are now collected in the `useCustomFieldExtensions` hook, enabling them for use in the scaffolder.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/theme@0.6.3-next.0
  - @backstage/types@1.2.0
  - @backstage/version-bridge@1.0.10
  - @backstage/plugin-permission-react@0.4.29-next.0
  - @backstage/plugin-scaffolder-common@1.5.8-next.1

## @backstage/plugin-search@1.4.21-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-compat-api@0.3.4-next.2
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/types@1.2.0
  - @backstage/version-bridge@1.0.10
  - @backstage/plugin-search-common@1.2.16-next.0
  - @backstage/plugin-search-react@1.8.4-next.2

## @backstage/plugin-search-backend@1.8.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.6.0-next.2
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-permission-node@0.8.6-next.2
  - @backstage/backend-openapi-utils@0.4.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-search-backend-node@1.3.6-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-permission-common@0.8.3-next.0
  - @backstage/plugin-search-common@1.2.16-next.0

## @backstage/plugin-search-backend-module-catalog@0.2.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-search-backend-node@1.3.6-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/plugin-catalog-common@1.1.2-next.0
  - @backstage/plugin-permission-common@0.8.3-next.0
  - @backstage/plugin-search-common@1.2.16-next.0

## @backstage/plugin-search-backend-module-elasticsearch@1.6.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-search-backend-node@1.3.6-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/integration-aws-node@0.1.14-next.0
  - @backstage/plugin-search-common@1.2.16-next.0

## @backstage/plugin-search-backend-module-explore@0.2.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-search-backend-node@1.3.6-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/plugin-search-common@1.2.16-next.0

## @backstage/plugin-search-backend-module-pg@0.5.39-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-search-backend-node@1.3.6-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/plugin-search-common@1.2.16-next.0

## @backstage/plugin-search-backend-module-stack-overflow-collator@0.3.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-search-backend-node@1.3.6-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/plugin-search-common@1.2.16-next.0

## @backstage/plugin-search-backend-module-techdocs@0.3.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-search-backend-node@1.3.6-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/plugin-techdocs-node@1.12.15-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/plugin-catalog-common@1.1.2-next.0
  - @backstage/plugin-permission-common@0.8.3-next.0
  - @backstage/plugin-search-common@1.2.16-next.0

## @backstage/plugin-search-backend-node@1.3.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/plugin-permission-common@0.8.3-next.0
  - @backstage/plugin-search-common@1.2.16-next.0

## @backstage/plugin-search-common@1.2.16-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.2.0
  - @backstage/plugin-permission-common@0.8.3-next.0

## @backstage/plugin-search-react@1.8.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/theme@0.6.3-next.0
  - @backstage/types@1.2.0
  - @backstage/version-bridge@1.0.10
  - @backstage/plugin-search-common@1.2.16-next.0

## @backstage/plugin-signals@0.0.14-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/theme@0.6.3-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-signals-react@0.0.8-next.0

## @backstage/plugin-signals-backend@0.2.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/plugin-signals-node@0.1.15-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0

## @backstage/plugin-signals-node@0.1.15-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0

## @backstage/plugin-signals-react@0.0.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/types@1.2.0

## @backstage/plugin-techdocs@1.11.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-compat-api@0.3.4-next.2
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/integration@1.16.0-next.1
  - @backstage/integration-react@1.2.2-next.1
  - @backstage/theme@0.6.3-next.0
  - @backstage/plugin-auth-react@0.1.10-next.2
  - @backstage/plugin-search-common@1.2.16-next.0
  - @backstage/plugin-search-react@1.8.4-next.2
  - @backstage/plugin-techdocs-common@0.1.0
  - @backstage/plugin-techdocs-react@1.2.12-next.2

## @backstage/plugin-techdocs-addons-test-utils@1.0.43-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.26.0-next.2
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/core-app-api@1.15.3-next.1
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/integration-react@1.2.2-next.1
  - @backstage/test-utils@1.7.3-next.1
  - @backstage/plugin-search-react@1.8.4-next.2
  - @backstage/plugin-techdocs@1.11.3-next.2
  - @backstage/plugin-techdocs-react@1.2.12-next.2

## @backstage/plugin-techdocs-backend@1.11.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/plugin-search-backend-module-techdocs@0.3.4-next.2
  - @backstage/plugin-techdocs-node@1.12.15-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/plugin-catalog-common@1.1.2-next.0
  - @backstage/plugin-permission-common@0.8.3-next.0
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-techdocs-module-addons-contrib@1.1.19-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/integration-react@1.2.2-next.1
  - @backstage/plugin-techdocs-react@1.2.12-next.2

## @backstage/plugin-techdocs-node@1.12.15-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/integration-aws-node@0.1.14-next.0
  - @backstage/plugin-search-common@1.2.16-next.0
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-techdocs-react@1.2.12-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/version-bridge@1.0.10

## @backstage/plugin-user-settings@0.8.17-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-compat-api@0.3.4-next.2
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/core-app-api@1.15.3-next.1
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/theme@0.6.3-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-signals-react@0.0.8-next.0
  - @backstage/plugin-user-settings-common@0.0.1

## @backstage/plugin-user-settings-backend@0.2.28-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.6.0-next.2
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/plugin-signals-node@0.1.15-next.2
  - @backstage/config@1.3.1-next.0
  - @backstage/types@1.2.0
  - @backstage/plugin-user-settings-common@0.0.1

## example-app@0.2.104-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.26.0-next.2
  - @backstage/plugin-notifications@0.5.0-next.2
  - @backstage/cli@0.29.3-next.2
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/plugin-scaffolder-react@1.14.2-next.2
  - @backstage/plugin-scaffolder@1.27.2-next.2
  - @backstage/plugin-api-docs@0.12.2-next.2
  - @backstage/plugin-catalog-graph@0.4.14-next.2
  - @backstage/plugin-catalog-import@0.12.8-next.2
  - @backstage/plugin-org@0.6.34-next.2
  - @backstage/plugin-user-settings@0.8.17-next.2
  - @backstage/app-defaults@1.5.15-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/core-app-api@1.15.3-next.1
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-app-api@0.10.3-next.2
  - @backstage/integration-react@1.2.2-next.1
  - @backstage/theme@0.6.3-next.0
  - @backstage/plugin-auth-react@0.1.10-next.2
  - @backstage/plugin-catalog-common@1.1.2-next.0
  - @backstage/plugin-catalog-unprocessed-entities@0.2.12-next.2
  - @backstage/plugin-devtools@0.1.22-next.2
  - @backstage/plugin-home@0.8.3-next.2
  - @backstage/plugin-kubernetes@0.12.2-next.2
  - @backstage/plugin-kubernetes-cluster@0.0.20-next.2
  - @backstage/plugin-permission-react@0.4.29-next.0
  - @backstage/plugin-search@1.4.21-next.2
  - @backstage/plugin-search-common@1.2.16-next.0
  - @backstage/plugin-search-react@1.8.4-next.2
  - @backstage/plugin-signals@0.0.14-next.2
  - @backstage/plugin-techdocs@1.11.3-next.2
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.19-next.2
  - @backstage/plugin-techdocs-react@1.2.12-next.2

## example-app-next@0.0.18-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.26.0-next.2
  - @backstage/plugin-notifications@0.5.0-next.2
  - @backstage/cli@0.29.3-next.2
  - @backstage/core-compat-api@0.3.4-next.2
  - @backstage/plugin-catalog-react@1.14.3-next.2
  - @backstage/plugin-scaffolder-react@1.14.2-next.2
  - @backstage/plugin-scaffolder@1.27.2-next.2
  - @backstage/plugin-api-docs@0.12.2-next.2
  - @backstage/plugin-catalog-graph@0.4.14-next.2
  - @backstage/plugin-catalog-import@0.12.8-next.2
  - @backstage/plugin-org@0.6.34-next.2
  - @backstage/plugin-user-settings@0.8.17-next.2
  - @backstage/app-defaults@1.5.15-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/core-app-api@1.15.3-next.1
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/frontend-app-api@0.10.3-next.2
  - @backstage/frontend-defaults@0.1.4-next.2
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/integration-react@1.2.2-next.1
  - @backstage/theme@0.6.3-next.0
  - @backstage/plugin-app@0.1.4-next.2
  - @backstage/plugin-app-visualizer@0.1.14-next.2
  - @backstage/plugin-auth-react@0.1.10-next.2
  - @backstage/plugin-catalog-common@1.1.2-next.0
  - @backstage/plugin-catalog-unprocessed-entities@0.2.12-next.2
  - @backstage/plugin-home@0.8.3-next.2
  - @backstage/plugin-kubernetes@0.12.2-next.2
  - @backstage/plugin-kubernetes-cluster@0.0.20-next.2
  - @backstage/plugin-permission-react@0.4.29-next.0
  - @backstage/plugin-search@1.4.21-next.2
  - @backstage/plugin-search-common@1.2.16-next.0
  - @backstage/plugin-search-react@1.8.4-next.2
  - @backstage/plugin-signals@0.0.14-next.2
  - @backstage/plugin-techdocs@1.11.3-next.2
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.19-next.2
  - @backstage/plugin-techdocs-react@1.2.12-next.2

## app-next-example-plugin@0.0.18-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.2-next.2
  - @backstage/frontend-plugin-api@0.9.3-next.2

## example-backend@0.0.33-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.28.0-next.2
  - @backstage/backend-defaults@0.6.0-next.2
  - @backstage/plugin-catalog-backend@1.29.0-next.2
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-scaffolder-backend-module-github@0.5.4-next.2
  - @backstage/plugin-notifications-backend@0.5.0-next.2
  - @backstage/plugin-permission-node@0.8.6-next.2
  - @backstage/plugin-app-backend@0.4.3-next.2
  - @backstage/plugin-auth-backend@0.24.1-next.2
  - @backstage/plugin-auth-backend-module-github-provider@0.2.3-next.2
  - @backstage/plugin-devtools-backend@0.5.0-next.2
  - @backstage/plugin-events-backend@0.4.0-next.2
  - @backstage/plugin-kubernetes-backend@0.19.1-next.2
  - @backstage/plugin-proxy-backend@0.5.9-next.2
  - @backstage/plugin-search-backend@1.8.0-next.2
  - @backstage/plugin-search-backend-node@1.3.6-next.2
  - @backstage/plugin-signals-backend@0.2.4-next.2
  - @backstage/plugin-techdocs-backend@1.11.4-next.2
  - @backstage/plugin-catalog-backend-module-openapi@0.2.5-next.2
  - @backstage/plugin-auth-backend-module-guest-provider@0.2.3-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/plugin-catalog-backend-module-backstage-openapi@0.4.3-next.2
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.3-next.2
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.3-next.2
  - @backstage/plugin-permission-backend@0.5.52-next.2
  - @backstage/plugin-permission-backend-module-allow-all-policy@0.2.3-next.2
  - @backstage/plugin-scaffolder-backend-module-notifications@0.1.5-next.2
  - @backstage/plugin-search-backend-module-catalog@0.2.6-next.2
  - @backstage/plugin-search-backend-module-explore@0.2.6-next.2
  - @backstage/plugin-search-backend-module-techdocs@0.3.4-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/plugin-permission-common@0.8.3-next.0

## example-backend-legacy@0.2.105-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.28.0-next.2
  - @backstage/backend-defaults@0.6.0-next.2
  - @backstage/plugin-catalog-backend@1.29.0-next.2
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/plugin-permission-node@0.8.6-next.2
  - @backstage/plugin-app-backend@0.4.3-next.2
  - @backstage/plugin-auth-backend@0.24.1-next.2
  - @backstage/plugin-events-backend@0.4.0-next.2
  - @backstage/plugin-kubernetes-backend@0.19.1-next.2
  - @backstage/plugin-proxy-backend@0.5.9-next.2
  - @backstage/plugin-search-backend@1.8.0-next.2
  - @backstage/plugin-search-backend-node@1.3.6-next.2
  - @backstage/plugin-signals-backend@0.2.4-next.2
  - @backstage/plugin-techdocs-backend@1.11.4-next.2
  - @backstage/plugin-auth-node@0.5.5-next.2
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.3-next.2
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.3-next.2
  - @backstage/plugin-catalog-node@1.15.0-next.2
  - @backstage/plugin-events-node@0.4.6-next.2
  - @backstage/plugin-permission-backend@0.5.52-next.2
  - @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.4-next.2
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.7.0-next.2
  - @backstage/plugin-scaffolder-backend-module-rails@0.5.4-next.2
  - @backstage/plugin-search-backend-module-catalog@0.2.6-next.2
  - @backstage/plugin-search-backend-module-elasticsearch@1.6.3-next.2
  - @backstage/plugin-search-backend-module-explore@0.2.6-next.2
  - @backstage/plugin-search-backend-module-pg@0.5.39-next.2
  - @backstage/plugin-search-backend-module-techdocs@0.3.4-next.2
  - @backstage/plugin-signals-node@0.1.15-next.2
  - @backstage/catalog-client@1.9.0-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/integration@1.16.0-next.1
  - @backstage/plugin-permission-common@0.8.3-next.0

## e2e-test@0.2.23-next.2

### Patch Changes

- Updated dependencies
  - @backstage/create-app@0.5.23-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/cli-common@0.1.15

## @internal/frontend@0.0.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.3-next.2
  - @backstage/types@1.2.0
  - @backstage/version-bridge@1.0.10

## @internal/scaffolder@0.0.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-react@1.14.2-next.2
  - @backstage/frontend-plugin-api@0.9.3-next.2

## techdocs-cli-embedded-app@0.2.103-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.26.0-next.2
  - @backstage/cli@0.29.3-next.2
  - @backstage/app-defaults@1.5.15-next.2
  - @backstage/catalog-model@1.7.2-next.0
  - @backstage/config@1.3.1-next.0
  - @backstage/core-app-api@1.15.3-next.1
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0
  - @backstage/integration-react@1.2.2-next.1
  - @backstage/test-utils@1.7.3-next.1
  - @backstage/theme@0.6.3-next.0
  - @backstage/plugin-techdocs@1.11.3-next.2
  - @backstage/plugin-techdocs-react@1.2.12-next.2

## @internal/plugin-todo-list@1.0.34-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.2-next.2
  - @backstage/core-plugin-api@1.10.2-next.0

## @internal/plugin-todo-list-backend@1.0.34-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.1.0-next.2
  - @backstage/errors@1.2.6-next.0
  - @backstage/plugin-auth-node@0.5.5-next.2

## @internal/plugin-todo-list-common@1.0.23-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.8.3-next.0
