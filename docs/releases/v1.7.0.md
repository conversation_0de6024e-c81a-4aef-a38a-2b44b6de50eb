---
id: v1.7.0
title: v1.7.0
description: Backstage Release v1.7.0
---

These are the release notes for the v1.7.0 release of
[Backstage](https://backstage.io/).

A huge thanks to the whole team of maintainers and contributors as well as the
amazing Backstage Community for the hard work in getting this release developed
and done.

## Highlights

### GitHub Catalog Import now Powered by the Backend

The analysis performed during catalog imports (i.e. when supplying the URL of a
repository rather than an individual YAML file in the Create flow) is now
powered by the backend rather than frontend code. This means that the catalog
backend needs to be supplied with a location analyzer for this use case to
continue to function.

If you want to make use of this feature, check out the installation instructions
in [the changelog](https://github.com/backstage/backstage/blob/master/plugins/catalog-import/CHANGELOG.md#090).

Contributed by [@kissmikijr](https://github.com/kissmikijr) in
[#13800](https://github.com/backstage/backstage/pull/13800)

### Permission Rule Changes

When defining permission rules, it's now necessary to provide a [Zod Schema](https://github.com/colinhacks/zod) that specifies the parameters the
rule expects. This has been added to help better describe the parameters in the
response of the metadata endpoint and to validate the parameters before a rule
is executed. The signatures of the rule methods (`apply` and `toQuery`) have
changed slightly as well.

You can read more about this in [the permissions documentation](https://backstage.io/docs/permissions/overview) and [the changelog](https://github.com/backstage/backstage/blob/master/plugins/permission-node/CHANGELOG.md#070).

### Migration: `jest` v29

Both `jest`, `jest-runtime`, and `jest-environment-jsdom` as used by the
Backstage CLI were bumped to version 29. This is up from version 27, so check
out both the [v28](https://jestjs.io/docs/28.x/upgrading-to-jest28) and
[v29](https://jestjs.io/docs/upgrading-to-jest29) (later
[here](https://jestjs.io/docs/29.x/upgrading-to-jest29)) migration guides, since
your existing tests may be affected.

Particular changes that were encountered in the main Backstage repository are:

- The updated snapshot format.
- `jest.useFakeTimers('legacy')` is now `jest.useFakeTimers({ legacyFakeTimers: true })`.
- Error objects collected by `withLogCollector` from `@backstage/test-utils` are
  now objects with a `detail` property rather than a string.

### Migration: `react-router` v6

Newly created Backstage repositories now use the stable version 6 of
`react-router`, just like the main repository does.

Migrating to the stable version of `react-router` is optional for the time
being; Backstage has support for both versions. But if you want to do the same
for your existing repository, please follow [this guide](https://backstage.io/docs/tutorials/react-router-stable-migration).
Support for the beta version will be removed in a later release.

### Support for `__mocks__` and `__testUtils__` directories

The Backstage CLI now has built-in support for `__mocks__` and `__testUtils__`
directories in your code. These can be used for mocks and shared utilities in
tests.

### New Arguments for the Router of `@backstage/plugin-bazaar-backend`

The bazaar-backend `createRouter` function now requires that the `identityApi`
is passed to the router.

### Deprecated plugin: `@backstage/plugin-catalog-backend-module-bitbucket`

This has been deprecated and split into
`@backstage/plugin-catalog-backend-module-bitbucket-cloud` and
`@backstage/plugin-catalog-backend-module-bitbucket-server`, for BitBucket Cloud
and BitBucket Server respectively. Please update your dependencies accordingly,
depending on which product you use.

The original package will be removed in a future release.

Contributed by [@pjungermann](https://github.com/pjungermann) in
[#14070](https://github.com/backstage/backstage/pull/14070)

## Security Fixes

This release does not contain any security fixes.

## Upgrade path

We recommend that you keep your Backstage project up to date with this latest
release. For more guidance on how to upgrade, check out the documentation for
[keeping Backstage updated](https://backstage.io/docs/getting-started/keeping-backstage-updated).

## Links and References

Below you can find a list of links and references to help you learn about and
start using this new release.

- [Backstage official website](https://backstage.io/),
  [documentation](https://backstage.io/docs/), and [getting started guide](https://backstage.io/docs/getting-started/)
- [GitHub repository](https://github.com/backstage/backstage)
- Backstage's [versioning and support policy](https://backstage.io/docs/overview/versioning-policy)
- [Community Discord](https://discord.gg/backstage-687207715902193673) for discussions and support
- [Changelog](https://github.com/backstage/backstage/tree/master/docs/releases/v1.7.0-changelog.md)
- Backstage [Demos](https://backstage.io/demos),
  [Blog](https://backstage.io/blog),
  [Roadmap](https://backstage.io/docs/overview/roadmap) and
  [Plugins](https://backstage.io/plugins)

Sign up for our [newsletter](https://mailchi.mp/spotify/backstage-community) if
you want to be informed about what is happening in the world of Backstage.
