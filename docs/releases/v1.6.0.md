---
id: v1.6.0
title: v1.6.0
description: Backstage Release v1.6.0
---

These are the release notes for the v1.6.0 release of [Backstage](https://backstage.io/).

A huge thanks to the whole team of maintainers and contributors as well as the amazing Backstage Community for the hard work in getting this release developed and done.

## Highlights

### Moved to `swc` for transpilation

We’ve replaced `sucrase` with [`swc`](https://swc.rs/) for transpilation in the Backstage CLI. While `swc` has slightly slower transpilation times, it’s a library backed by a larger community, and allows us to take advantage of [React Refresh](https://www.npmjs.com/package/react-refresh) out of the box. There’s a few things that could possibly break installations of Backstage and compilation, you can read more about it in the [changelog](https://github.com/backstage/backstage/blob/515aadf8840591860e4bbdcc7d99cef8f9d7ac3c/docs/releases/v1.6.0-changelog.md#patch-changes-1)

### React Router Stable Compatibility

Backstage has for a long time been using React Router version `6.0.0-beta.0`. We adopted this unstable version because v6 had some new features that fit really well with Backstage, particularly relative routing. Because we jumped on this early and unstable version, we knew that we would at some point need a breaking migration to the stable version of React Router v6, which is the point we're at now!

The migration is controlled by each app, meaning this release will not force you to migrate straight away, you can do so at your own pace. Check out the [migration guide](https://backstage.io/docs/tutorials/react-router-stable-migration) for all you need to know!

### Yarn 3 Support

It is now possible to migrate Backstage projects to use Yarn 3. See the [migration guide](https://backstage.io/docs/tutorials/yarn-migration) for more information. Migrating to Yarn 3 is optional, and Backstage projects created with `@backstage/create-app` will still use classic Yarn by default.

### New plugin: `@backstage/plugin-user-settings-backend`

The `user-settings` plugin now has an associated backend. This allows for the persistence of settings in your database, essentially in the form of a basic per-user key-value JSON store.

As this backend was added, `user-settings` also gained a `UserSettingsStore` class that implements the `storageApiRef` Utility API. If you install the backend as well as this frontend API, your starred entities and other storage-API-based features will no longer just be persisted in your browser’s local storage, but centrally so that all your devices can leverage them.

Contributed by [@dschwank](https://github.com/dschwank) in [#13570](https://github.com/backstage/backstage/pull/13570)

### New plugin: `@backstage/plugin-playlist`

This plugin can be used to create custom collections of Entities that can be shared throughout Backstage or for private usage.

Contributed by [@kuangp](https://github.com/kuangp) in [#12870](https://github.com/backstage/backstage/pull/12870)

## Security Fixes

Be sure to upgrade to the latest version of `@backstage/plugin-scaffolder-backend`, as it contains an explicit bump of a transitive dependency where a vulnerability was discovered. If you subscribe to CVE notifications you will already have received this update.

## Upgrade path

We recommend that you keep your Backstage project up to date with this latest release. For more guidance on how to upgrade, check out the documentation for [keeping Backstage updated](https://backstage.io/docs/getting-started/keeping-backstage-updated).

## Links and References

Below you can find a list of links and references to help you learn about and start using this new release.

- [Backstage official website](https://backstage.io/), [documentation](https://backstage.io/docs/), and [getting started guide](https://backstage.io/docs/getting-started/)
- [GitHub repository](https://github.com/backstage/backstage)
- Backstage's [versioning and support policy](https://backstage.io/docs/overview/versioning-policy)
- [Community Discord](https://discord.gg/backstage-687207715902193673) for discussions and support
- [Changelog](https://github.com/backstage/backstage/tree/master/docs/releases/v1.6.0-changelog.md)
- Backstage [Demos](https://backstage.io/demos), [Blog](https://backstage.io/blog), [Roadmap](https://backstage.io/docs/overview/roadmap) and [Plugins](https://backstage.io/plugins)

Sign up for our [newsletter](https://mailchi.mp/spotify/backstage-community) if you want to be informed about what is happening in the world of Backstage.
