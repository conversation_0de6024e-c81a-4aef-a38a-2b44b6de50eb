# Release v1.37.0-next.1

Upgrade Helper: [https://backstage.github.io/upgrade-helper/?to=1.37.0-next.1](https://backstage.github.io/upgrade-helper/?to=1.37.0-next.1)

## @backstage/canon@0.2.0-next.1

### Minor Changes

- 8689010: We are renaming CanonProvider to IconProvider to improve clarity on how to override icons.

### Patch Changes

- 89e8686: To avoid conflicts with Backstage, we removed global styles and set font-family and font-weight for each components.

## @backstage/plugin-app-backend@0.5.0-next.1

### Minor Changes

- 32be48c: **BREAKING**: Removed support for the old backend system.

  As part of this change the plugin export from `/alpha` as been removed. If you are currently importing `@backstage/plugin-app-backend/alpha`, please update your import to `@backstage/plugin-app-backend`.

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/config-loader@1.9.6
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-app-node@0.1.31-next.1

## @backstage/plugin-catalog@1.28.0-next.1

### Minor Changes

- 06d1226: Allow providing `kind` parameters to replace the default `Component` kind for `SubComponents` card

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-scaffolder-common@1.5.10-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/core-compat-api@0.3.7-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/integration-react@1.2.4
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-search-react@1.8.7-next.1

## @backstage/plugin-catalog-backend@1.32.0-next.1

### Minor Changes

- ca9c51b: Added opt-in ability to evict entities from the catalog whose provider is no longer configured. See [Catalog configuration documentation](https://backstage.io/docs/features/software-catalog/configuration#clean-up-entities-from-orphaned-entity-providers)

### Patch Changes

- fbc1666: Correctly use the `catalog.useUrlReadersSearch` config.
- 75cadc1: Minor internal tweak to `refreshByRefreshKeys`
- Updated dependencies
  - @backstage/backend-openapi-utils@0.5.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-events-node@0.4.9-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1
  - @backstage/plugin-search-backend-module-catalog@0.3.2-next.1
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-catalog-backend-module-backstage-openapi@0.5.0-next.1

### Minor Changes

- 62842ee: feat: Improve JSON format of OpenAPI definition, allow YAML format

### Patch Changes

- Updated dependencies
  - @backstage/backend-openapi-utils@0.5.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-proxy-backend@0.6.0-next.1

### Minor Changes

- 2d8b0e4: **BREAKING**: Removed support for the old backend system.

  As part of this change the plugin export from `/alpha` as been removed. If you are currently importing `@backstage/plugin-proxy-backend/alpha`, please update your import to `@backstage/plugin-proxy-backend`.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/types@1.2.1
  - @backstage/plugin-proxy-node@0.1.2-next.1

## @backstage/plugin-user-settings-backend@0.3.0-next.1

### Minor Changes

- e202017: **BREAKING**: Removed support for the old backend system.

  As part of this change the plugin export from `/alpha` as been removed. If you are currently importing `@backstage/plugin-user-settings-backend/alpha`, please update your import to `@backstage/plugin-user-settings-backend`.

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-defaults@0.8.2-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-signals-node@0.1.18-next.1
  - @backstage/plugin-user-settings-common@0.0.1

## @backstage/app-defaults@1.5.18-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/core-app-api@1.15.5
  - @backstage/core-plugin-api@1.10.4
  - @backstage/theme@0.6.4
  - @backstage/plugin-permission-react@0.4.31

## @backstage/backend-app-api@1.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/config-loader@1.9.6
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-node@0.8.9-next.1

## @backstage/backend-defaults@0.8.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-app-api@1.2.1-next.1
  - @backstage/backend-dev-utils@0.1.5
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/cli-common@0.1.15
  - @backstage/cli-node@0.2.13
  - @backstage/config@1.3.2
  - @backstage/config-loader@1.9.6
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.9-next.1
  - @backstage/plugin-permission-node@0.8.9-next.1

## @backstage/backend-dynamic-feature-service@0.6.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/plugin-catalog-backend@1.32.0-next.1
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-defaults@0.8.2-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/cli-common@0.1.15
  - @backstage/cli-node@0.2.13
  - @backstage/config@1.3.2
  - @backstage/config-loader@1.9.6
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-app-node@0.1.31-next.1
  - @backstage/plugin-events-backend@0.4.4-next.1
  - @backstage/plugin-events-node@0.4.9-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1
  - @backstage/plugin-search-backend-node@1.3.9-next.1
  - @backstage/plugin-search-common@1.2.17

## @backstage/backend-openapi-utils@0.5.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/backend-plugin-api@1.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1

## @backstage/backend-test-utils@1.3.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-app-api@1.2.1-next.1
  - @backstage/backend-defaults@0.8.2-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.9-next.1

## @backstage/cli@0.30.1-next.0

### Patch Changes

- 0586d4c: Internal change to move the `migrate` and `version:*` commands into a new migrate module.
- 485b3ba: Internal update to move `test` commands to a separate module.
- 8df78bf: Internal update to move build commands to a CLI module.
- d0fc357: Internal update to move `info` commands to a separate module.
- Updated dependencies
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/cli-node@0.2.13
  - @backstage/config@1.3.2
  - @backstage/config-loader@1.9.6
  - @backstage/errors@1.2.7
  - @backstage/eslint-plugin@0.1.10
  - @backstage/integration@1.16.1
  - @backstage/release-manifests@0.0.12
  - @backstage/types@1.2.1

## @backstage/core-compat-api@0.3.7-next.1

### Patch Changes

- d34e0e5: Added a new `convertLegacyAppOptions` helper that converts many of the options passed to `createApp` in the old frontend system to a module with app overrides for the new system. The supported options are `apis`, `icons`, `plugins`, `components`, and `themes`.

  For example, given the following options for the old `createApp`:

  ```ts
  import { createApp } from '@backstage/app-deafults';

  const app = createApp({
    apis,
    plugins,
    icons: {
      custom: MyIcon,
    },
    components: {
      SignInPage: MySignInPage,
    },
    themes: [myTheme],
  });
  ```

  They can be converted to the new system like this:

  ```ts
  import { createApp } from '@backstage/frontend-deafults';
  import { convertLegacyAppOptions } from '@backstage/core-compat-api';

  const app = createApp({
    features: [
      convertLegacyAppOptions({
        apis,
        plugins,
        icons: {
          custom: MyIcon,
        },
        components: {
          SignInPage: MySignInPage,
        },
        themes: [myTheme],
      }),
    ],
  });
  ```

- 18faf65: The `convertLegacyApp` has received the following changes:

  - `null` routes will now be ignored.
  - Converted routes no longer need to belong to a plugin, falling back to a `converted-orphan-routes` plugin instead.
  - The generate layout override extension is now properly attached to the `app/root` extension.
  - Converted root elements are now automatically wrapped with `compatWrapper`.

- Updated dependencies
  - @backstage/core-plugin-api@1.10.4
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/version-bridge@1.0.11

## @backstage/core-components@0.16.5-next.0

### Patch Changes

- fffe3c0: Added `classNames` prop to the `Page` component
- df3b9f0: Fixed a bug in the SidebarSubmenuItem within the core-components package that caused the dropdown button to be misaligned in the sidebar and the button text to appear in uppercase due to the default <Button> behavior. Also added an example dropdown menu to the app for reference.
- Updated dependencies
  - @backstage/config@1.3.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/theme@0.6.4
  - @backstage/version-bridge@1.0.11

## @backstage/create-app@0.5.26-next.1

### Patch Changes

- Bumped create-app version.
- Updated dependencies
  - @backstage/cli-common@0.1.15

## @backstage/dev-utils@1.1.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/app-defaults@1.5.18-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/core-app-api@1.15.5
  - @backstage/core-plugin-api@1.10.4
  - @backstage/integration-react@1.2.4
  - @backstage/theme@0.6.4

## @backstage/frontend-app-api@0.10.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.2
  - @backstage/core-app-api@1.15.5
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/frontend-defaults@0.1.7-next.1
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11

## @backstage/frontend-defaults@0.1.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-app@0.1.7-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/frontend-app-api@0.10.6-next.1
  - @backstage/frontend-plugin-api@0.9.6-next.1

## @backstage/frontend-plugin-api@0.9.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/core-plugin-api@1.10.4
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11

## @backstage/frontend-test-utils@0.2.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-app@0.1.7-next.1
  - @backstage/config@1.3.2
  - @backstage/frontend-app-api@0.10.6-next.1
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/test-utils@1.7.5
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11

## @backstage/repo-tools@0.13.1-next.1

### Patch Changes

- c7a58b2: Fix issue where generate-patch incorrectly encodes the locator not aligning with result of yarn patch
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/cli-node@0.2.13
  - @backstage/config-loader@1.9.6
  - @backstage/errors@1.2.7

## @techdocs/cli@1.9.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/plugin-techdocs-node@1.13.1-next.1

## @backstage/plugin-api-docs@0.12.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/core-compat-api@0.3.7-next.1
  - @backstage/plugin-catalog@1.28.0-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-react@0.4.31

## @backstage/plugin-app@0.1.7-next.1

### Patch Changes

- 583fc54: Fixed extra app elements not being rendered as part of apps without a sign-in page.
- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/core-plugin-api@1.10.4
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/integration-react@1.2.4
  - @backstage/theme@0.6.4
  - @backstage/plugin-permission-react@0.4.31

## @backstage/plugin-app-node@0.1.31-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config-loader@1.9.6

## @backstage/plugin-app-visualizer@0.1.17-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/core-plugin-api@1.10.4
  - @backstage/frontend-plugin-api@0.9.6-next.1

## @backstage/plugin-auth-backend@0.24.4-next.1

### Patch Changes

- 7956beb: Marked the remaining exports related to `createRouter` and the old backend system as deprecated.

  For more information about migrating to the new backend system, see the [migration guide](https://backstage.io/docs/backend-system/building-backends/migrating#the-auth-plugin).

  Support for the old backend system will be removed in the next release of this plugin.

- b6702ea: Deprecated `getDefaultOwnershipEntityRefs` in favor of the new `.resolveOwnershipEntityRefs(...)` method in the `AuthResolverContext`.

  The following code in a custom sign-in resolver:

  ```ts
  import { getDefaultOwnershipEntityRefs } from '@backstage/plugin-auth-backend';

  // ...

  const ent = getDefaultOwnershipEntityRefs(entity);
  ```

  Can be replaced with the following:

  ```ts
  const { ownershipEntityRefs: ent } = await ctx.resolveOwnershipEntityRefs(
    entity,
  );
  ```

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-backend-module-atlassian-provider@0.4.1-next.1
  - @backstage/plugin-auth-backend-module-auth0-provider@0.2.1-next.1
  - @backstage/plugin-auth-backend-module-aws-alb-provider@0.4.1-next.1
  - @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.6-next.1
  - @backstage/plugin-auth-backend-module-bitbucket-provider@0.3.1-next.1
  - @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.2.1-next.1
  - @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.4.1-next.1
  - @backstage/plugin-auth-backend-module-gcp-iap-provider@0.4.1-next.1
  - @backstage/plugin-auth-backend-module-github-provider@0.3.1-next.1
  - @backstage/plugin-auth-backend-module-gitlab-provider@0.3.1-next.1
  - @backstage/plugin-auth-backend-module-google-provider@0.3.1-next.1
  - @backstage/plugin-auth-backend-module-microsoft-provider@0.3.1-next.1
  - @backstage/plugin-auth-backend-module-oauth2-provider@0.4.1-next.1
  - @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.6-next.1
  - @backstage/plugin-auth-backend-module-oidc-provider@0.4.1-next.1
  - @backstage/plugin-auth-backend-module-okta-provider@0.2.1-next.1
  - @backstage/plugin-auth-backend-module-onelogin-provider@0.3.1-next.1
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-auth-backend-module-atlassian-provider@0.4.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-auth-backend-module-auth0-provider@0.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-auth-backend-module-aws-alb-provider@0.4.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/plugin-auth-backend@0.24.4-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/errors@1.2.7

## @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7

## @backstage/plugin-auth-backend-module-bitbucket-provider@0.3.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.4.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-auth-backend-module-gcp-iap-provider@0.4.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-auth-backend-module-github-provider@0.3.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-auth-backend-module-gitlab-provider@0.3.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-auth-backend-module-google-provider@0.3.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-auth-backend-module-guest-provider@0.2.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7

## @backstage/plugin-auth-backend-module-microsoft-provider@0.3.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-auth-backend-module-oauth2-provider@0.4.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/errors@1.2.7

## @backstage/plugin-auth-backend-module-oidc-provider@0.4.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/plugin-auth-backend@0.24.4-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-auth-backend-module-okta-provider@0.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-auth-backend-module-onelogin-provider@0.3.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-auth-backend-module-pinniped-provider@0.3.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1

## @backstage/plugin-auth-backend-module-vmware-cloud-provider@0.5.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3

## @backstage/plugin-auth-node@0.6.1-next.1

### Patch Changes

- b6702ea: Added `AuthResolverContext.resolveOwnershipEntityRefs` as a way of accessing the default ownership resolution logic in sign-in resolvers, replacing `getDefaultOwnershipEntityRefs` from `@backstage/plugin-auth-backend`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-auth-react@0.1.13-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7

## @backstage/plugin-catalog-backend-module-aws@0.4.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-aws-node@0.1.15
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-kubernetes-common@0.9.4-next.0

## @backstage/plugin-catalog-backend-module-azure@0.3.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.4.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.27
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-events-node@0.4.9-next.1

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.3.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-gcp@0.3.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-kubernetes-common@0.9.4-next.0

## @backstage/plugin-catalog-backend-module-gerrit@0.2.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-github@0.7.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.32.0-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-events-node@0.4.9-next.1

## @backstage/plugin-catalog-backend-module-github-org@0.3.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-backend-module-github@0.7.11-next.1
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-events-node@0.4.9-next.1

## @backstage/plugin-catalog-backend-module-gitlab@0.6.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-events-node@0.4.9-next.1

## @backstage/plugin-catalog-backend-module-gitlab-org@0.2.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/plugin-catalog-backend-module-gitlab@0.6.4-next.1
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-events-node@0.4.9-next.1

## @backstage/plugin-catalog-backend-module-incremental-ingestion@0.6.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.32.0-next.1
  - @backstage/backend-defaults@0.8.2-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-events-node@0.4.9-next.1
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-catalog-backend-module-ldap@0.11.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-logs@0.1.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.32.0-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/plugin-events-node@0.4.9-next.1

## @backstage/plugin-catalog-backend-module-msgraph@0.6.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-openapi@0.2.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-puppetdb@0.2.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-common@1.5.10-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-unprocessed@0.5.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-catalog-unprocessed-entities-common@0.0.7
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-catalog-graph@0.4.17-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/core-compat-api@0.3.7-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/types@1.2.1

## @backstage/plugin-catalog-import@0.12.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/core-compat-api@0.3.7-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/integration@1.16.1
  - @backstage/integration-react@1.2.4
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-node@1.16.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1

## @backstage/plugin-catalog-react@1.16.0-next.1

### Patch Changes

- bec1e15: update EntityAutocompletePicker selected options when filter value is changed externally
- 75a3551: Export CatalogAutocomplete so it can be used externally
- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/core-compat-api@0.3.7-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/frontend-test-utils@0.2.7-next.1
  - @backstage/integration-react@1.2.4
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-react@0.4.31

## @backstage/plugin-catalog-unprocessed-entities@0.2.15-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/core-compat-api@0.3.7-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/frontend-plugin-api@0.9.6-next.1

## @backstage/plugin-config-schema@0.1.66-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-devtools@0.1.25-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/core-compat-api@0.3.7-next.1
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/plugin-devtools-common@0.1.15
  - @backstage/plugin-permission-react@0.4.31

## @backstage/plugin-devtools-backend@0.5.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/config-loader@1.9.6
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-devtools-common@0.1.15
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1

## @backstage/plugin-events-backend@0.4.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-openapi-utils@0.5.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.9-next.1

## @backstage/plugin-events-backend-module-aws-sqs@0.4.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.9-next.1

## @backstage/plugin-events-backend-module-azure@0.2.18-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/plugin-events-node@0.4.9-next.1

## @backstage/plugin-events-backend-module-bitbucket-cloud@0.2.18-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/plugin-events-node@0.4.9-next.1

## @backstage/plugin-events-backend-module-gerrit@0.2.18-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/plugin-events-node@0.4.9-next.1

## @backstage/plugin-events-backend-module-github@0.2.18-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/plugin-events-node@0.4.9-next.1

## @backstage/plugin-events-backend-module-gitlab@0.2.18-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/plugin-events-node@0.4.9-next.1

## @backstage/plugin-events-backend-test-utils@0.1.42-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.9-next.1

## @backstage/plugin-events-node@0.4.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-home@0.8.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-home-react@0.1.24-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/core-compat-api@0.3.7-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-app-api@1.15.5
  - @backstage/core-plugin-api@1.10.4
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/theme@0.6.4

## @backstage/plugin-home-react@0.1.24-next.0

### Patch Changes

- c5a82fc: Don't render header divider on homepage cards if no title was specified.
- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/core-plugin-api@1.10.4

## @backstage/plugin-kubernetes@0.12.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/core-compat-api@0.3.7-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/plugin-kubernetes-common@0.9.4-next.0
  - @backstage/plugin-kubernetes-react@0.5.5-next.1
  - @backstage/plugin-permission-react@0.4.31

## @backstage/plugin-kubernetes-backend@0.19.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-kubernetes-common@0.9.4-next.0
  - @backstage/plugin-kubernetes-node@0.2.4-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1

## @backstage/plugin-kubernetes-cluster@0.0.23-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-kubernetes-common@0.9.4-next.0
  - @backstage/plugin-kubernetes-react@0.5.5-next.1
  - @backstage/plugin-permission-react@0.4.31

## @backstage/plugin-kubernetes-node@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/types@1.2.1
  - @backstage/plugin-kubernetes-common@0.9.4-next.0

## @backstage/plugin-kubernetes-react@0.5.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-kubernetes-common@0.9.4-next.0

## @backstage/plugin-notifications@0.5.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/core-compat-api@0.3.7-next.1
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/theme@0.6.4
  - @backstage/types@1.2.1
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-signals-react@0.0.10

## @backstage/plugin-notifications-backend@0.5.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-events-node@0.4.9-next.1
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-notifications-node@0.2.13-next.1
  - @backstage/plugin-signals-node@0.1.18-next.1

## @backstage/plugin-notifications-backend-module-email@0.3.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-notifications-node@0.2.13-next.1

## @backstage/plugin-notifications-node@0.2.13-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-signals-node@0.1.18-next.1

## @backstage/plugin-org@0.6.37-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/core-compat-api@0.3.7-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-org-react@0.1.36-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4

## @backstage/plugin-permission-backend@0.5.55-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1

## @backstage/plugin-permission-backend-module-allow-all-policy@0.2.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1

## @backstage/plugin-permission-node@0.8.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-proxy-node@0.1.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-scaffolder@1.29.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-scaffolder-react@1.14.6-next.1
  - @backstage/plugin-scaffolder-common@1.5.10-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/core-compat-api@0.3.7-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/integration@1.16.1
  - @backstage/integration-react@1.2.4
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-react@0.4.31

## @backstage/plugin-scaffolder-backend@1.30.1-next.1

### Patch Changes

- 09cf038: Got rid of most `@backstage/backend-common` usages
- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.8.1-next.1
  - @backstage/plugin-scaffolder-backend-module-github@0.6.1-next.1
  - @backstage/plugin-scaffolder-common@1.5.10-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-defaults@0.8.2-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.27
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.6-next.1
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-events-node@0.4.9-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1
  - @backstage/plugin-scaffolder-backend-module-azure@0.2.7-next.1
  - @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.8-next.1
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.7-next.1
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.7-next.1
  - @backstage/plugin-scaffolder-backend-module-gerrit@0.2.7-next.1
  - @backstage/plugin-scaffolder-backend-module-gitea@0.2.7-next.1

## @backstage/plugin-scaffolder-backend-module-azure@0.2.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.7-next.1
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.7-next.1

## @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.27

## @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.7-next.1

### Patch Changes

- 09cf038: Got rid of most `@backstage/backend-common` usages
- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.3.8-next.1

### Patch Changes

- 09cf038: Got rid of most `@backstage/backend-common` usages
- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-defaults@0.8.2-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-backend-module-gcp@0.2.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-gerrit@0.2.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-gitea@0.2.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-github@0.6.1-next.1

### Patch Changes

- 09cf038: Got rid of most `@backstage/backend-common` usages
- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-gitlab@0.8.1-next.1

### Patch Changes

- a08cb20: Remove usages of `@backstage/backend-common`
- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-notifications@0.1.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-notifications-node@0.2.13-next.1

## @backstage/plugin-scaffolder-backend-module-rails@0.5.7-next.1

### Patch Changes

- 09cf038: Got rid of most `@backstage/backend-common` usages
- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-backend-module-sentry@0.2.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-scaffolder-backend-module-yeoman@0.4.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node-test-utils@0.1.20-next.1
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-common@1.5.10-next.0

### Patch Changes

- 09cf038: Got rid of most `@backstage/backend-common` usages
- Updated dependencies
  - @backstage/catalog-model@1.7.3
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-scaffolder-node@0.7.1-next.1

### Patch Changes

- 09cf038: Got rid of most `@backstage/backend-common` usages
- Updated dependencies
  - @backstage/plugin-scaffolder-common@1.5.10-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-node-test-utils@0.1.20-next.1

### Patch Changes

- 09cf038: Got rid of most `@backstage/backend-common` usages
- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.7.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/backend-test-utils@1.3.1-next.1
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-react@1.14.6-next.1

### Patch Changes

- 4d26652: Fix field extension validation not working when field is in dependencies in an array field
- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-scaffolder-common@1.5.10-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/theme@0.6.4
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11
  - @backstage/plugin-permission-react@0.4.31

## @backstage/plugin-search@1.4.24-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/core-compat-api@0.3.7-next.1
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-search-react@1.8.7-next.1

## @backstage/plugin-search-backend@1.8.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.1
  - @backstage/backend-openapi-utils@0.5.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1
  - @backstage/plugin-search-backend-node@1.3.9-next.1
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-catalog@0.3.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-backend-node@1.3.9-next.1
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-elasticsearch@1.6.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/integration-aws-node@0.1.15
  - @backstage/plugin-search-backend-node@1.3.9-next.1
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-explore@0.2.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/plugin-search-backend-node@1.3.9-next.1
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-pg@0.5.42-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/plugin-search-backend-node@1.3.9-next.1
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-stack-overflow-collator@0.3.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/plugin-search-backend-node@1.3.9-next.1
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-techdocs@0.3.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-backend-node@1.3.9-next.1
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-techdocs-node@1.13.1-next.1

## @backstage/plugin-search-backend-node@1.3.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-react@1.8.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/core-plugin-api@1.10.4
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/theme@0.6.4
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-signals@0.0.17-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/core-plugin-api@1.10.4
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/theme@0.6.4
  - @backstage/types@1.2.1
  - @backstage/plugin-signals-react@0.0.10

## @backstage/plugin-signals-backend@0.3.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.9-next.1
  - @backstage/plugin-signals-node@0.1.18-next.1

## @backstage/plugin-signals-node@0.1.18-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.9-next.1

## @backstage/plugin-techdocs@1.12.4-next.1

### Patch Changes

- fffe3c0: Fixed double scrollbar issue that would appear on the Entity TechDocs view page that would stop the page from full scrolling to the top when navigating to a new page
- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/core-compat-api@0.3.7-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/integration@1.16.1
  - @backstage/integration-react@1.2.4
  - @backstage/theme@0.6.4
  - @backstage/plugin-auth-react@0.1.13-next.0
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-search-react@1.8.7-next.1
  - @backstage/plugin-techdocs-common@0.1.0
  - @backstage/plugin-techdocs-react@1.2.15-next.1

## @backstage/plugin-techdocs-addons-test-utils@1.0.46-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs@1.12.4-next.1
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/plugin-catalog@1.28.0-next.1
  - @backstage/core-app-api@1.15.5
  - @backstage/core-plugin-api@1.10.4
  - @backstage/integration-react@1.2.4
  - @backstage/test-utils@1.7.5
  - @backstage/plugin-search-react@1.8.7-next.1
  - @backstage/plugin-techdocs-react@1.2.15-next.1

## @backstage/plugin-techdocs-backend@1.11.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-backend-module-techdocs@0.3.7-next.1
  - @backstage/plugin-techdocs-common@0.1.0
  - @backstage/plugin-techdocs-node@1.13.1-next.1

## @backstage/plugin-techdocs-module-addons-contrib@1.1.22-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/core-plugin-api@1.10.4
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/integration@1.16.1
  - @backstage/integration-react@1.2.4
  - @backstage/plugin-techdocs-react@1.2.15-next.1

## @backstage/plugin-techdocs-node@1.13.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-aws-node@0.1.15
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-techdocs-react@1.2.15-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/version-bridge@1.0.11

## @backstage/plugin-user-settings@0.8.20-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/core-compat-api@0.3.7-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-app-api@1.15.5
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/theme@0.6.4
  - @backstage/types@1.2.1
  - @backstage/plugin-signals-react@0.0.10
  - @backstage/plugin-user-settings-common@0.0.1

## example-app@0.2.107-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs@1.12.4-next.1
  - @backstage/cli@0.30.1-next.0
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-scaffolder-react@1.14.6-next.1
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/plugin-catalog@1.28.0-next.1
  - @backstage/app-defaults@1.5.18-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-app-api@1.15.5
  - @backstage/core-plugin-api@1.10.4
  - @backstage/frontend-app-api@0.10.6-next.1
  - @backstage/integration-react@1.2.4
  - @backstage/theme@0.6.4
  - @backstage/plugin-api-docs@0.12.5-next.1
  - @backstage/plugin-auth-react@0.1.13-next.0
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-graph@0.4.17-next.1
  - @backstage/plugin-catalog-import@0.12.11-next.1
  - @backstage/plugin-catalog-unprocessed-entities@0.2.15-next.1
  - @backstage/plugin-devtools@0.1.25-next.1
  - @backstage/plugin-home@0.8.6-next.1
  - @backstage/plugin-kubernetes@0.12.5-next.1
  - @backstage/plugin-kubernetes-cluster@0.0.23-next.1
  - @backstage/plugin-notifications@0.5.3-next.1
  - @backstage/plugin-org@0.6.37-next.1
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/plugin-scaffolder@1.29.0-next.1
  - @backstage/plugin-search@1.4.24-next.1
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-search-react@1.8.7-next.1
  - @backstage/plugin-signals@0.0.17-next.1
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.22-next.1
  - @backstage/plugin-techdocs-react@1.2.15-next.1
  - @backstage/plugin-user-settings@0.8.20-next.1

## example-app-next@0.0.21-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs@1.12.4-next.1
  - @backstage/cli@0.30.1-next.0
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-app@0.1.7-next.1
  - @backstage/plugin-scaffolder-react@1.14.6-next.1
  - @backstage/plugin-catalog-react@1.16.0-next.1
  - @backstage/core-compat-api@0.3.7-next.1
  - @backstage/plugin-catalog@1.28.0-next.1
  - @backstage/app-defaults@1.5.18-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-app-api@1.15.5
  - @backstage/core-plugin-api@1.10.4
  - @backstage/frontend-app-api@0.10.6-next.1
  - @backstage/frontend-defaults@0.1.7-next.1
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/integration-react@1.2.4
  - @backstage/theme@0.6.4
  - @backstage/plugin-api-docs@0.12.5-next.1
  - @backstage/plugin-app-visualizer@0.1.17-next.1
  - @backstage/plugin-auth-react@0.1.13-next.0
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-graph@0.4.17-next.1
  - @backstage/plugin-catalog-import@0.12.11-next.1
  - @backstage/plugin-catalog-unprocessed-entities@0.2.15-next.1
  - @backstage/plugin-home@0.8.6-next.1
  - @backstage/plugin-kubernetes@0.12.5-next.1
  - @backstage/plugin-kubernetes-cluster@0.0.23-next.1
  - @backstage/plugin-notifications@0.5.3-next.1
  - @backstage/plugin-org@0.6.37-next.1
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/plugin-scaffolder@1.29.0-next.1
  - @backstage/plugin-search@1.4.24-next.1
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-search-react@1.8.7-next.1
  - @backstage/plugin-signals@0.0.17-next.1
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.22-next.1
  - @backstage/plugin-techdocs-react@1.2.15-next.1
  - @backstage/plugin-user-settings@0.8.20-next.1

## app-next-example-plugin@0.0.21-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/frontend-plugin-api@0.9.6-next.1

## example-backend@0.0.36-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/plugin-catalog-backend@1.32.0-next.1
  - @backstage/plugin-scaffolder-backend-module-github@0.6.1-next.1
  - @backstage/plugin-scaffolder-backend@1.30.1-next.1
  - @backstage/plugin-catalog-backend-module-backstage-openapi@0.5.0-next.1
  - @backstage/plugin-proxy-backend@0.6.0-next.1
  - @backstage/plugin-app-backend@0.5.0-next.1
  - @backstage/plugin-auth-backend@0.24.4-next.1
  - @backstage/backend-defaults@0.8.2-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-auth-backend-module-github-provider@0.3.1-next.1
  - @backstage/plugin-auth-backend-module-guest-provider@0.2.6-next.1
  - @backstage/plugin-catalog-backend-module-openapi@0.2.8-next.1
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.6-next.1
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.6-next.1
  - @backstage/plugin-devtools-backend@0.5.3-next.1
  - @backstage/plugin-events-backend@0.4.4-next.1
  - @backstage/plugin-kubernetes-backend@0.19.4-next.1
  - @backstage/plugin-notifications-backend@0.5.4-next.1
  - @backstage/plugin-permission-backend@0.5.55-next.1
  - @backstage/plugin-permission-backend-module-allow-all-policy@0.2.6-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1
  - @backstage/plugin-scaffolder-backend-module-notifications@0.1.8-next.1
  - @backstage/plugin-search-backend@1.8.3-next.1
  - @backstage/plugin-search-backend-module-catalog@0.3.2-next.1
  - @backstage/plugin-search-backend-module-explore@0.2.9-next.1
  - @backstage/plugin-search-backend-module-techdocs@0.3.7-next.1
  - @backstage/plugin-search-backend-node@1.3.9-next.1
  - @backstage/plugin-signals-backend@0.3.2-next.1
  - @backstage/plugin-techdocs-backend@1.11.7-next.1

## example-backend-legacy@0.2.108-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.8.1-next.1
  - @backstage/plugin-catalog-backend@1.32.0-next.1
  - @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.7-next.1
  - @backstage/plugin-scaffolder-backend-module-rails@0.5.7-next.1
  - @backstage/plugin-scaffolder-backend@1.30.1-next.1
  - @backstage/plugin-auth-backend@0.24.4-next.1
  - @backstage/backend-defaults@0.8.2-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.6-next.1
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.6-next.1
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-events-backend@0.4.4-next.1
  - @backstage/plugin-events-node@0.4.9-next.1
  - @backstage/plugin-kubernetes-backend@0.19.4-next.1
  - @backstage/plugin-permission-backend@0.5.55-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1
  - @backstage/plugin-search-backend@1.8.3-next.1
  - @backstage/plugin-search-backend-module-catalog@0.3.2-next.1
  - @backstage/plugin-search-backend-module-elasticsearch@1.6.6-next.1
  - @backstage/plugin-search-backend-module-explore@0.2.9-next.1
  - @backstage/plugin-search-backend-module-pg@0.5.42-next.1
  - @backstage/plugin-search-backend-module-techdocs@0.3.7-next.1
  - @backstage/plugin-search-backend-node@1.3.9-next.1
  - @backstage/plugin-signals-backend@0.3.2-next.1
  - @backstage/plugin-signals-node@0.1.18-next.1
  - @backstage/plugin-techdocs-backend@1.11.7-next.1

## e2e-test@0.2.26-next.1

### Patch Changes

- Updated dependencies
  - @backstage/create-app@0.5.26-next.1
  - @backstage/cli-common@0.1.15
  - @backstage/errors@1.2.7

## @internal/frontend@0.0.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.6-next.1
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11

## @internal/scaffolder@0.0.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-react@1.14.6-next.1
  - @backstage/frontend-plugin-api@0.9.6-next.1

## techdocs-cli-embedded-app@0.2.106-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs@1.12.4-next.1
  - @backstage/cli@0.30.1-next.0
  - @backstage/core-components@0.16.5-next.0
  - @backstage/plugin-catalog@1.28.0-next.1
  - @backstage/app-defaults@1.5.18-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-app-api@1.15.5
  - @backstage/core-plugin-api@1.10.4
  - @backstage/integration-react@1.2.4
  - @backstage/test-utils@1.7.5
  - @backstage/theme@0.6.4
  - @backstage/plugin-techdocs-react@1.2.15-next.1

## @internal/plugin-todo-list@1.0.37-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.0
  - @backstage/core-plugin-api@1.10.4

## @internal/plugin-todo-list-backend@1.0.37-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/errors@1.2.7
