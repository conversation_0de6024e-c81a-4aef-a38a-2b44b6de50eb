---
id: v1.9.0
title: v1.9.0
description: Backstage Release v1.9.0
---

These are the release notes for the v1.9.0 release of [Backstage](https://backstage.io/).

A huge thanks to the whole team of maintainers and contributors as well as the amazing Backstage Community for the hard work in getting this release developed and done.

## Highlights

### Catalog Batch Fetching

The catalog client has a new `getEntitiesByRefs` method, and the catalog backend has a corresponding new [`/entities/by-refs` endpoint](https://backstage.io/docs/features/software-catalog/software-catalog-api). These allow for the efficient batch fetching of entities by their refs, which may be useful e.g. in GraphQL resolvers or similar contexts where you need to fetch large numbers of entities at the same time.

### New plugin: AWS Credentials Provider

The management of AWS credentials in the backend saw some significant improvement, through the introduction of the `@backstage/integration-aws-node` package and its corresponding `AwsCredentialsManager` interface. This is leveraged e.g. in the [TechDocs S3 publisher](https://backstage.io/docs/features/techdocs/using-cloud-storage).

Contributed by [@clareliguori](https://github.com/clareliguori) in [#14125](https://github.com/backstage/backstage/pull/14125)

### Events

There was further work to advance the [`events` backend](https://github.com/backstage/backstage/tree/master/plugins/events-backend) functionality, which propagates and processes events such as webhooks. It has modules for [AWS SQS](https://github.com/backstage/backstage/tree/master/plugins/events-backend-module-aws-sqs), [Azure](https://github.com/backstage/backstage/tree/master/plugins/events-backend-module-azure), [Bitbucket Cloud](https://github.com/backstage/backstage/tree/master/plugins/events-backend-module-bitbucket-cloud), [Gerrit](https://github.com/backstage/backstage/tree/master/plugins/events-backend-module-gerrit), [GitHub](https://github.com/backstage/backstage/tree/master/plugins/events-backend-module-github), and [Gitlab](https://github.com/backstage/backstage/tree/master/plugins/events-backend-module-gitlab), along with other improvements.

Contributed by [@pjungermann](https://github.com/pjungermann) and others in the community.

### New Plugin: Incremental Ingestion

Introduces incremental entity providers, which are used for streaming very large data sources into the catalog.

Contributed by [@dekoding](https://github.com/dekoding) [#14356](https://github.com/backstage/backstage/pull/14356)

### Scaffolder Custom Field Explorer

The new custom field explorer lets you view and play around with available installed custom field extensions.

Contributed by [@kuangp](https://github.com/kuangp) in [#14008](https://github.com/backstage/backstage/pull/14008)

### Scaffolder concurrent task execution

Individual Scaffolder task workers can now run multiple (defaults to 10) tasks concurrently using the `concurrentTasksLimit` option, leading to more efficient resource usage.

Contributed by [@howlowck](https://github.com/howlowck) in [#13817](https://github.com/backstage/backstage/pull/13817)

### Deprecating Catalog Prometheus metrics for OpenTelemetry

We have started working on phasing out the Prometheus Metrics exposed using the `prom-client` in favor of replacement metrics using [OpenTelemetery](https://opentelemetry.io/). The `prom-client` metrics have always been a band-aid and never fully established but we want to notify existing users of their future removal. Backstage does not provide any default exporter or instructions on how to export the new metrics yet but you should expect documentation shortly. We hope to fully incorporate OpenTelemetry into other plugins once we gain confidence and establish patterns. Added in [#15145](https://github.com/backstage/backstage/pull/15145).

### Updated API for `createApp`

The `getProvider` and `getRouter` methods of the main app instance from `@backstage/core-app-api` have been deprecated. Instead, there is now an `AppRouter` component that is a drop-in replacement for the router, and a new `createRoot` method for creating the root app component. This change will simplify the internal logic of the app, and also makes it more clear that the app is created from a React element tree.

### Advancements on the New Backend System

Work has progressed with the new system and we have been able to add a couple of new features, most notable are shutdown lifecycle events and stop functionality to the Backend.

All core service references are now exported via a single `coreServices` object. For example, the `loggerServiceRef` is now accessed via `coreServices.logger` instead, [#14986](https://github.com/backstage/backstage/pull/14986).
All service interfaces are now suffixed with `*Service`. Root scoped services are now always initialized, whether they are depended directly on by others or not; this allows features such as the metrics exporter to work correctly.

### New package: `@backstage/repo-tools`

This new package is part of an ongoing effort to make more of the repository tooling in the main Backstage repository available to the rest of the community. The main piece that has been added so far is the tooling needed to use API reports.

## Security Fixes

This release does not contain any security fixes.

## Upgrade path

We recommend that you keep your Backstage project up to date with this latest release. For more guidance on how to upgrade, check out the documentation for [keeping Backstage updated](https://backstage.io/docs/getting-started/keeping-backstage-updated).

## Links and References

Below you can find a list of links and references to help you learn about and start using this new release.

- [Backstage official website](https://backstage.io/), [documentation](https://backstage.io/docs/), and [getting started guide](https://backstage.io/docs/getting-started/)
- [GitHub repository](https://github.com/backstage/backstage)
- Backstage's [versioning and support policy](https://backstage.io/docs/overview/versioning-policy)
- [Community Discord](https://discord.gg/backstage-687207715902193673) for discussions and support
- [Changelog](https://github.com/backstage/backstage/tree/master/docs/releases/v1.9.0-changelog.md)
- Backstage [Demos](https://backstage.io/demos), [Blog](https://backstage.io/blog), [Roadmap](https://backstage.io/docs/overview/roadmap) and [Plugins](https://backstage.io/plugins)

Sign up for our [newsletter](https://mailchi.mp/spotify/backstage-community) if you want to be informed about what is happening in the world of Backstage.
