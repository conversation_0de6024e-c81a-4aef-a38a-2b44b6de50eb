# Release v1.41.0-next.1

Upgrade Helper: [https://backstage.github.io/upgrade-helper/?to=1.41.0-next.1](https://backstage.github.io/upgrade-helper/?to=1.41.0-next.1)

## @backstage/app-defaults@1.6.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.17.2-next.0
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/plugin-permission-react@0.4.36-next.0

## @backstage/backend-app-api@1.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0

## @backstage/backend-defaults@0.11.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config-loader@1.10.2-next.0
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-node@0.10.2-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/integration-aws-node@0.1.17-next.0
  - @backstage/backend-app-api@1.2.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/backend-dynamic-feature-service@0.7.2-next.1

### Patch Changes

- 3507fcd: Just some more circular dep cleanup
- 3d61c36: Fix wrong imports which lead to module initialization failures when enabling dynamic plugins.
- Updated dependencies
  - @backstage/config-loader@1.10.2-next.0
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/plugin-permission-node@0.10.2-next.0
  - @backstage/backend-defaults@0.11.1-next.1
  - @backstage/plugin-app-node@0.1.35-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0
  - @backstage/plugin-catalog-backend@3.0.0-next.1
  - @backstage/plugin-events-backend@0.5.4-next.0
  - @backstage/plugin-scaffolder-node@0.9.1-next.1
  - @backstage/plugin-search-backend-node@1.3.13-next.0
  - @backstage/plugin-search-common@1.2.19-next.0
  - @backstage/backend-openapi-utils@0.5.5-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/backend-openapi-utils@0.5.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0

## @backstage/backend-plugin-api@1.4.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/plugin-permission-node@0.10.2-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/backend-test-utils@1.7.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/backend-defaults@0.11.1-next.1
  - @backstage/backend-app-api@1.2.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/catalog-client@1.10.2-next.0

### Patch Changes

- 6fb4143: allow arrays in the InMemoryCatalogClient to filter entities
- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0

## @backstage/catalog-model@1.7.5-next.0

### Patch Changes

- 3507fcd: Just some more circular dep cleanup

## @backstage/cli@0.33.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config-loader@1.10.2-next.0
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/integration@1.17.1-next.1

## @backstage/config@1.3.3-next.0

### Patch Changes

- ff23618: Loosen the requirements for a key to be considered valid config.
- 3507fcd: Just some more circular dep cleanup

## @backstage/config-loader@1.10.2-next.0

### Patch Changes

- ff23618: Loosen the requirements for a key to be considered valid config.
- Updated dependencies
  - @backstage/config@1.3.3-next.0

## @backstage/core-app-api@1.17.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/core-plugin-api@1.10.9-next.0

## @backstage/core-compat-api@0.4.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/core-components@0.17.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/core-plugin-api@1.10.9-next.0

## @backstage/core-plugin-api@1.10.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0

## @backstage/create-app@0.7.1-next.1

### Patch Changes

- Bumped create-app version.

## @backstage/dev-utils@1.1.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/core-app-api@1.17.2-next.0
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/integration-react@1.2.9-next.1
  - @backstage/app-defaults@1.6.4-next.1

## @backstage/frontend-app-api@0.11.4-next.1

### Patch Changes

- 3507fcd: Just some more circular dep cleanup
- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/core-app-api@1.17.2-next.0
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/frontend-defaults@0.2.4-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/frontend-defaults@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/frontend-app-api@0.11.4-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1
  - @backstage/plugin-app@0.1.11-next.1

## @backstage/frontend-dynamic-feature-loader@0.1.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/frontend-plugin-api@0.10.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0

## @backstage/frontend-test-utils@0.3.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/frontend-app-api@0.11.4-next.1
  - @backstage/test-utils@1.7.10-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1
  - @backstage/plugin-app@0.1.11-next.1

## @backstage/integration@1.17.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0

## @backstage/integration-aws-node@0.1.17-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0

## @backstage/integration-react@1.2.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/core-plugin-api@1.10.9-next.0

## @backstage/repo-tools@0.15.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config-loader@1.10.2-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0

## @techdocs/cli@1.9.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-defaults@0.11.1-next.1
  - @backstage/plugin-techdocs-node@1.13.5-next.1

## @backstage/test-utils@1.7.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/core-app-api@1.17.2-next.0
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/plugin-permission-react@0.4.36-next.0

## @backstage/plugin-api-docs@0.12.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/plugin-permission-react@0.4.36-next.0
  - @backstage/plugin-catalog@1.31.1-next.1
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/core-compat-api@0.4.4-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-app@0.1.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/integration-react@1.2.9-next.1
  - @backstage/plugin-permission-react@0.4.36-next.0
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-app-backend@0.5.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config-loader@1.10.2-next.0
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-app-node@0.1.35-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-app-node@0.1.35-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config-loader@1.10.2-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0

## @backstage/plugin-app-visualizer@0.1.21-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-auth-backend@0.25.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0

## @backstage/plugin-auth-backend-module-atlassian-provider@0.4.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-auth0-provider@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-aws-alb-provider@0.4.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-backend@0.25.2-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-bitbucket-provider@0.3.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.4.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-gcp-iap-provider@0.4.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-github-provider@0.3.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-gitlab-provider@0.3.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-google-provider@0.3.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-guest-provider@0.2.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-microsoft-provider@0.3.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-oauth2-provider@0.4.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-oidc-provider@0.4.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-backend@0.25.2-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-okta-provider@0.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-onelogin-provider@0.3.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-pinniped-provider@0.3.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-backend-module-vmware-cloud-provider@0.5.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-auth-node@0.6.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0

## @backstage/plugin-auth-react@0.1.17-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0

## @backstage/plugin-catalog@1.31.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/integration-react@1.2.9-next.1
  - @backstage/plugin-permission-react@0.4.36-next.0
  - @backstage/plugin-techdocs-react@1.3.1-next.1
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-scaffolder-common@1.5.12-next.0
  - @backstage/plugin-search-common@1.2.19-next.0
  - @backstage/core-compat-api@0.4.4-next.1
  - @backstage/plugin-search-react@1.9.2-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-catalog-backend@3.0.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/plugin-permission-node@0.10.2-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/backend-openapi-utils@0.5.5-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-catalog-backend-module-aws@0.4.13-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-defaults@0.11.1-next.1
  - @backstage/integration@1.17.1-next.1
  - @backstage/integration-aws-node@0.1.17-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-kubernetes-common@0.9.6-next.0

## @backstage/plugin-catalog-backend-module-azure@0.3.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0

## @backstage/plugin-catalog-backend-module-backstage-openapi@0.5.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/backend-openapi-utils@0.5.5-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.5.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.5.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-catalog-backend-module-gcp@0.3.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-kubernetes-common@0.9.6-next.0

## @backstage/plugin-catalog-backend-module-gerrit@0.3.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0

## @backstage/plugin-catalog-backend-module-gitea@0.1.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0

## @backstage/plugin-catalog-backend-module-github@0.10.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-backend@3.0.0-next.1
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-catalog-backend-module-github-org@0.3.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-backend-module-github@0.10.1-next.1
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-catalog-backend-module-gitlab@0.7.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-defaults@0.11.1-next.1
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-catalog-backend-module-gitlab-org@0.2.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-backend-module-gitlab@0.7.1-next.1
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-catalog-backend-module-incremental-ingestion@0.7.2-next.1

### Patch Changes

- 3507fcd: Just some more circular dep cleanup
- e2dd095: Fixed bug in `IncrementalIngestionEngine` by adding `burstLength` check when a burst completes
- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-defaults@0.11.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-backend@3.0.0-next.1
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-catalog-backend-module-ldap@0.11.7-next.0

### Patch Changes

- 3507fcd: Just some more circular dep cleanup
- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0

## @backstage/plugin-catalog-backend-module-logs@0.1.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-backend@3.0.0-next.1
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-catalog-backend-module-msgraph@0.7.2-next.0

### Patch Changes

- 3507fcd: Just some more circular dep cleanup
- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0

## @backstage/plugin-catalog-backend-module-openapi@0.2.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0

## @backstage/plugin-catalog-backend-module-puppetdb@0.2.12-next.0

### Patch Changes

- 3507fcd: Just some more circular dep cleanup
- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0

## @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-scaffolder-common@1.5.12-next.0

## @backstage/plugin-catalog-backend-module-unprocessed@0.6.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-catalog-unprocessed-entities-common@0.0.9-next.0

## @backstage/plugin-catalog-common@1.1.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-search-common@1.2.19-next.0

## @backstage/plugin-catalog-graph@0.4.21-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/core-compat-api@0.4.4-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-catalog-import@0.13.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/integration-react@1.2.9-next.1
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/core-compat-api@0.4.4-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-catalog-node@1.17.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/plugin-permission-node@0.10.2-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0

## @backstage/plugin-catalog-react@1.19.1-next.1

### Patch Changes

- 3507fcd: Just some more circular dep cleanup
- Updated dependencies
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/frontend-test-utils@0.3.4-next.1
  - @backstage/integration-react@1.2.9-next.1
  - @backstage/plugin-permission-react@0.4.36-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/core-compat-api@0.4.4-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-catalog-unprocessed-entities@0.2.19-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/core-compat-api@0.4.4-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-catalog-unprocessed-entities-common@0.0.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.9.1-next.0

## @backstage/plugin-config-schema@0.1.70-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0

## @backstage/plugin-devtools@0.1.29-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/plugin-permission-react@0.4.36-next.0
  - @backstage/plugin-devtools-common@0.1.17-next.0
  - @backstage/core-compat-api@0.4.4-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-devtools-backend@0.5.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config-loader@1.10.2-next.0
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/plugin-permission-node@0.10.2-next.0
  - @backstage/backend-defaults@0.11.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-devtools-common@0.1.17-next.0

## @backstage/plugin-devtools-common@0.1.17-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.9.1-next.0

## @backstage/plugin-events-backend@0.5.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/backend-openapi-utils@0.5.5-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-events-backend-module-aws-sqs@0.4.13-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-events-backend-module-azure@0.2.22-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-events-backend-module-bitbucket-cloud@0.2.22-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-events-backend-module-bitbucket-server@0.1.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-events-backend-module-gerrit@0.2.22-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-events-backend-module-github@0.4.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-events-backend-module-gitlab@0.3.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-events-backend-module-google-pubsub@0.1.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-events-backend-module-kafka@0.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-events-backend-test-utils@0.1.46-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-events-node@0.4.13-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0

## @backstage/plugin-gateway-backend@1.0.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0

## @backstage/plugin-home@0.8.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/core-app-api@1.17.2-next.0
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/core-compat-api@0.4.4-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1
  - @backstage/plugin-home-react@0.1.28-next.1

## @backstage/plugin-home-react@0.1.28-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-kubernetes@0.12.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/plugin-permission-react@0.4.36-next.0
  - @backstage/plugin-kubernetes-common@0.9.6-next.0
  - @backstage/plugin-kubernetes-react@0.5.9-next.1
  - @backstage/core-compat-api@0.4.4-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-kubernetes-backend@0.19.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/plugin-kubernetes-node@0.3.2-next.0
  - @backstage/plugin-permission-node@0.10.2-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/integration-aws-node@0.1.17-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-kubernetes-common@0.9.6-next.0

## @backstage/plugin-kubernetes-cluster@0.0.27-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/plugin-permission-react@0.4.36-next.0
  - @backstage/plugin-kubernetes-common@0.9.6-next.0
  - @backstage/plugin-kubernetes-react@0.5.9-next.1

## @backstage/plugin-kubernetes-common@0.9.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/catalog-model@1.7.5-next.0

## @backstage/plugin-kubernetes-node@0.3.2-next.0

### Patch Changes

- 3507fcd: Just some more circular dep cleanup
- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-kubernetes-common@0.9.6-next.0

## @backstage/plugin-kubernetes-react@0.5.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/plugin-kubernetes-common@0.9.6-next.0

## @backstage/plugin-mcp-actions-backend@0.1.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/backend-defaults@0.11.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0

## @backstage/plugin-notifications@0.5.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/plugin-notifications-common@0.0.10-next.0
  - @backstage/core-compat-api@0.4.4-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1
  - @backstage/plugin-signals-react@0.0.15-next.0

## @backstage/plugin-notifications-backend@0.5.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0
  - @backstage/plugin-notifications-common@0.0.10-next.0
  - @backstage/plugin-signals-node@0.1.22-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-notifications-node@0.2.17-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-notifications-backend-module-email@0.3.11-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/integration-aws-node@0.1.17-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-notifications-common@0.0.10-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-notifications-node@0.2.17-next.0

## @backstage/plugin-notifications-backend-module-slack@0.1.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-notifications-common@0.0.10-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-notifications-node@0.2.17-next.0

## @backstage/plugin-notifications-common@0.0.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0

## @backstage/plugin-notifications-node@0.2.17-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-notifications-common@0.0.10-next.0
  - @backstage/plugin-signals-node@0.1.22-next.0

## @backstage/plugin-org@0.6.41-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/core-compat-api@0.4.4-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-org-react@0.1.40-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0

## @backstage/plugin-permission-backend@0.7.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/plugin-permission-node@0.10.2-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-permission-backend-module-allow-all-policy@0.2.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/plugin-permission-node@0.10.2-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-permission-common@0.9.1-next.0

### Patch Changes

- 3507fcd: Just some more circular dep cleanup
- Updated dependencies
  - @backstage/config@1.3.3-next.0

## @backstage/plugin-permission-node@0.10.2-next.0

### Patch Changes

- 3507fcd: Just some more circular dep cleanup
- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0

## @backstage/plugin-permission-react@0.4.36-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/core-plugin-api@1.10.9-next.0

## @backstage/plugin-proxy-backend@0.6.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-proxy-node@0.1.6-next.0

## @backstage/plugin-proxy-node@0.1.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0

## @backstage/plugin-scaffolder@1.32.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/integration-react@1.2.9-next.1
  - @backstage/plugin-permission-react@0.4.36-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-scaffolder-common@1.5.12-next.0
  - @backstage/plugin-scaffolder-react@1.17.1-next.1
  - @backstage/core-compat-api@0.4.4-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-scaffolder-backend@2.0.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/plugin-permission-node@0.10.2-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-defaults@0.11.1-next.1
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0
  - @backstage/plugin-scaffolder-backend-module-azure@0.2.11-next.1
  - @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.12-next.1
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.11-next.1
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.11-next.1
  - @backstage/plugin-scaffolder-backend-module-gerrit@0.2.11-next.1
  - @backstage/plugin-scaffolder-backend-module-gitea@0.2.11-next.1
  - @backstage/plugin-scaffolder-backend-module-github@0.8.1-next.1
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.9.3-next.1
  - @backstage/plugin-scaffolder-node@0.9.1-next.1
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-scaffolder-common@1.5.12-next.0
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.10-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-scaffolder-backend-module-azure@0.2.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-scaffolder-node@0.9.1-next.1

## @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.11-next.1
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.11-next.1
  - @backstage/plugin-scaffolder-node@0.9.1-next.1

## @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-scaffolder-node@0.9.1-next.1

## @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-scaffolder-node@0.9.1-next.1

## @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-scaffolder-node@0.9.1-next.1

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.3.13-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-defaults@0.11.1-next.1
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-scaffolder-node@0.9.1-next.1

## @backstage/plugin-scaffolder-backend-module-gcp@0.2.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-scaffolder-node@0.9.1-next.1

## @backstage/plugin-scaffolder-backend-module-gerrit@0.2.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-scaffolder-node@0.9.1-next.1

## @backstage/plugin-scaffolder-backend-module-gitea@0.2.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-scaffolder-node@0.9.1-next.1

## @backstage/plugin-scaffolder-backend-module-github@0.8.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-scaffolder-node@0.9.1-next.1
  - @backstage/plugin-catalog-node@1.17.2-next.0

## @backstage/plugin-scaffolder-backend-module-gitlab@0.9.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-scaffolder-node@0.9.1-next.1

## @backstage/plugin-scaffolder-backend-module-notifications@0.1.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-notifications-common@0.0.10-next.0
  - @backstage/plugin-scaffolder-node@0.9.1-next.1
  - @backstage/plugin-notifications-node@0.2.17-next.0

## @backstage/plugin-scaffolder-backend-module-rails@0.5.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-scaffolder-node@0.9.1-next.1

## @backstage/plugin-scaffolder-backend-module-sentry@0.2.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-scaffolder-node@0.9.1-next.1

## @backstage/plugin-scaffolder-backend-module-yeoman@0.4.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-scaffolder-node@0.9.1-next.1
  - @backstage/plugin-scaffolder-node-test-utils@0.3.1-next.1

## @backstage/plugin-scaffolder-common@1.5.12-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/catalog-model@1.7.5-next.0

## @backstage/plugin-scaffolder-node@0.9.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-scaffolder-common@1.5.12-next.0

## @backstage/plugin-scaffolder-node-test-utils@0.3.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/backend-test-utils@1.7.0-next.1
  - @backstage/plugin-scaffolder-node@0.9.1-next.1

## @backstage/plugin-scaffolder-react@1.17.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/plugin-permission-react@0.4.36-next.0
  - @backstage/plugin-scaffolder-common@1.5.12-next.0
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-search@1.4.28-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/plugin-search-common@1.2.19-next.0
  - @backstage/core-compat-api@0.4.4-next.1
  - @backstage/plugin-search-react@1.9.2-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-search-backend@2.0.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/plugin-permission-node@0.10.2-next.0
  - @backstage/backend-defaults@0.11.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-search-backend-node@1.3.13-next.0
  - @backstage/plugin-search-common@1.2.19-next.0
  - @backstage/backend-openapi-utils@0.5.5-next.0

## @backstage/plugin-search-backend-module-catalog@0.3.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-search-backend-node@1.3.13-next.0
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-search-common@1.2.19-next.0

## @backstage/plugin-search-backend-module-elasticsearch@1.7.4-next.0

### Patch Changes

- 3507fcd: Just some more circular dep cleanup
- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/integration-aws-node@0.1.17-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-search-backend-node@1.3.13-next.0
  - @backstage/plugin-search-common@1.2.19-next.0

## @backstage/plugin-search-backend-module-explore@0.3.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-search-backend-node@1.3.13-next.0
  - @backstage/plugin-search-common@1.2.19-next.0

## @backstage/plugin-search-backend-module-pg@0.5.46-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-search-backend-node@1.3.13-next.0
  - @backstage/plugin-search-common@1.2.19-next.0

## @backstage/plugin-search-backend-module-stack-overflow-collator@0.3.11-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-search-backend-node@1.3.13-next.0
  - @backstage/plugin-search-common@1.2.19-next.0

## @backstage/plugin-search-backend-module-techdocs@0.4.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-search-backend-node@1.3.13-next.0
  - @backstage/plugin-techdocs-node@1.13.5-next.1
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0
  - @backstage/plugin-search-common@1.2.19-next.0

## @backstage/plugin-search-backend-node@1.3.13-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-search-common@1.2.19-next.0

## @backstage/plugin-search-common@1.2.19-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.9.1-next.0

## @backstage/plugin-search-react@1.9.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/plugin-search-common@1.2.19-next.0
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-signals@0.0.21-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/frontend-plugin-api@0.10.4-next.1
  - @backstage/plugin-signals-react@0.0.15-next.0

## @backstage/plugin-signals-backend@0.3.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0
  - @backstage/plugin-signals-node@0.1.22-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-signals-node@0.1.22-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0
  - @backstage/plugin-events-node@0.4.13-next.0

## @backstage/plugin-signals-react@0.0.15-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.10.9-next.0

## @backstage/plugin-techdocs@1.13.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/integration-react@1.2.9-next.1
  - @backstage/plugin-techdocs-react@1.3.1-next.1
  - @backstage/plugin-search-common@1.2.19-next.0
  - @backstage/core-compat-api@0.4.4-next.1
  - @backstage/plugin-search-react@1.9.2-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1
  - @backstage/plugin-auth-react@0.1.17-next.1

## @backstage/plugin-techdocs-addons-test-utils@1.0.51-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/core-app-api@1.17.2-next.0
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/integration-react@1.2.9-next.1
  - @backstage/test-utils@1.7.10-next.1
  - @backstage/plugin-techdocs@1.13.2-next.1
  - @backstage/plugin-techdocs-react@1.3.1-next.1
  - @backstage/plugin-catalog@1.31.1-next.1
  - @backstage/plugin-search-react@1.9.2-next.1

## @backstage/plugin-techdocs-backend@2.0.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/catalog-client@1.10.2-next.0
  - @backstage/backend-defaults@0.11.1-next.1
  - @backstage/integration@1.17.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-search-backend-module-techdocs@0.4.4-next.1
  - @backstage/plugin-techdocs-node@1.13.5-next.1
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-catalog-node@1.17.2-next.0

## @backstage/plugin-techdocs-module-addons-contrib@1.1.26-next.1

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.17.1-next.1
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/integration-react@1.2.9-next.1
  - @backstage/plugin-techdocs-react@1.3.1-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-techdocs-node@1.13.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/integration@1.17.1-next.1
  - @backstage/integration-aws-node@0.1.17-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-search-common@1.2.19-next.0

## @backstage/plugin-techdocs-react@1.3.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/frontend-plugin-api@0.10.4-next.1

## @backstage/plugin-user-settings@0.8.24-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/core-app-api@1.17.2-next.0
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/core-compat-api@0.4.4-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1
  - @backstage/plugin-signals-react@0.0.15-next.0

## @backstage/plugin-user-settings-backend@0.3.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.11.1-next.1
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0
  - @backstage/plugin-signals-node@0.1.22-next.0

## example-app@0.2.111-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/frontend-app-api@0.11.4-next.1
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/cli@0.33.1-next.1
  - @backstage/core-app-api@1.17.2-next.0
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/integration-react@1.2.9-next.1
  - @backstage/plugin-catalog-import@0.13.3-next.1
  - @backstage/plugin-home@0.8.10-next.1
  - @backstage/plugin-permission-react@0.4.36-next.0
  - @backstage/plugin-techdocs@1.13.2-next.1
  - @backstage/plugin-techdocs-react@1.3.1-next.1
  - @backstage/plugin-catalog@1.31.1-next.1
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-org@0.6.41-next.1
  - @backstage/plugin-scaffolder@1.32.1-next.1
  - @backstage/plugin-scaffolder-react@1.17.1-next.1
  - @backstage/plugin-search-common@1.2.19-next.0
  - @backstage/plugin-api-docs@0.12.9-next.1
  - @backstage/plugin-catalog-graph@0.4.21-next.1
  - @backstage/plugin-catalog-unprocessed-entities@0.2.19-next.1
  - @backstage/plugin-kubernetes@0.12.9-next.1
  - @backstage/plugin-kubernetes-cluster@0.0.27-next.1
  - @backstage/plugin-user-settings@0.8.24-next.1
  - @backstage/app-defaults@1.6.4-next.1
  - @backstage/plugin-notifications@0.5.7-next.1
  - @backstage/plugin-search@1.4.28-next.1
  - @backstage/plugin-search-react@1.9.2-next.1
  - @backstage/plugin-signals@0.0.21-next.1
  - @backstage/plugin-auth-react@0.1.17-next.1
  - @backstage/plugin-devtools@0.1.29-next.1
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.26-next.1

## example-app-next@0.0.25-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/frontend-app-api@0.11.4-next.1
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/plugin-catalog-react@1.19.1-next.1
  - @backstage/cli@0.33.1-next.1
  - @backstage/core-app-api@1.17.2-next.0
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/frontend-defaults@0.2.4-next.1
  - @backstage/integration-react@1.2.9-next.1
  - @backstage/plugin-catalog-import@0.13.3-next.1
  - @backstage/plugin-home@0.8.10-next.1
  - @backstage/plugin-permission-react@0.4.36-next.0
  - @backstage/plugin-techdocs@1.13.2-next.1
  - @backstage/plugin-techdocs-react@1.3.1-next.1
  - @backstage/plugin-catalog@1.31.1-next.1
  - @backstage/plugin-catalog-common@1.1.5-next.0
  - @backstage/plugin-org@0.6.41-next.1
  - @backstage/plugin-scaffolder@1.32.1-next.1
  - @backstage/plugin-scaffolder-react@1.17.1-next.1
  - @backstage/plugin-search-common@1.2.19-next.0
  - @backstage/plugin-api-docs@0.12.9-next.1
  - @backstage/plugin-catalog-graph@0.4.21-next.1
  - @backstage/plugin-catalog-unprocessed-entities@0.2.19-next.1
  - @backstage/plugin-kubernetes@0.12.9-next.1
  - @backstage/plugin-kubernetes-cluster@0.0.27-next.1
  - @backstage/plugin-user-settings@0.8.24-next.1
  - @backstage/app-defaults@1.6.4-next.1
  - @backstage/core-compat-api@0.4.4-next.1
  - @backstage/plugin-notifications@0.5.7-next.1
  - @backstage/plugin-search@1.4.28-next.1
  - @backstage/plugin-search-react@1.9.2-next.1
  - @backstage/plugin-signals@0.0.21-next.1
  - @backstage/frontend-plugin-api@0.10.4-next.1
  - @backstage/plugin-app@0.1.11-next.1
  - @backstage/plugin-app-visualizer@0.1.21-next.1
  - @backstage/plugin-auth-react@0.1.17-next.1
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.26-next.1

## example-backend@0.0.40-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.9.1-next.0
  - @backstage/plugin-permission-node@0.10.2-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/backend-defaults@0.11.1-next.1
  - @backstage/plugin-app-backend@0.5.4-next.0
  - @backstage/plugin-devtools-backend@0.5.7-next.1
  - @backstage/plugin-proxy-backend@0.6.4-next.0
  - @backstage/backend-plugin-api@1.4.1-next.0
  - @backstage/plugin-auth-backend@0.25.2-next.0
  - @backstage/plugin-auth-backend-module-guest-provider@0.2.10-next.0
  - @backstage/plugin-auth-node@0.6.5-next.0
  - @backstage/plugin-catalog-backend@3.0.0-next.1
  - @backstage/plugin-catalog-backend-module-backstage-openapi@0.5.4-next.0
  - @backstage/plugin-events-backend@0.5.4-next.0
  - @backstage/plugin-events-backend-module-google-pubsub@0.1.2-next.0
  - @backstage/plugin-kubernetes-backend@0.19.8-next.0
  - @backstage/plugin-notifications-backend@0.5.8-next.1
  - @backstage/plugin-permission-backend@0.7.2-next.0
  - @backstage/plugin-scaffolder-backend@2.0.1-next.1
  - @backstage/plugin-scaffolder-backend-module-github@0.8.1-next.1
  - @backstage/plugin-search-backend@2.0.4-next.1
  - @backstage/plugin-search-backend-module-catalog@0.3.6-next.0
  - @backstage/plugin-search-backend-module-explore@0.3.4-next.0
  - @backstage/plugin-search-backend-module-techdocs@0.4.4-next.1
  - @backstage/plugin-search-backend-node@1.3.13-next.0
  - @backstage/plugin-signals-backend@0.3.6-next.0
  - @backstage/plugin-techdocs-backend@2.0.4-next.1
  - @backstage/plugin-catalog-backend-module-unprocessed@0.6.2-next.0
  - @backstage/plugin-permission-backend-module-allow-all-policy@0.2.10-next.0
  - @backstage/plugin-catalog-backend-module-openapi@0.2.12-next.1
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.10-next.0
  - @backstage/plugin-mcp-actions-backend@0.1.1-next.1
  - @backstage/plugin-auth-backend-module-github-provider@0.3.5-next.0
  - @backstage/plugin-scaffolder-backend-module-notifications@0.1.12-next.1

## techdocs-cli-embedded-app@0.2.110-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.3-next.0
  - @backstage/catalog-model@1.7.5-next.0
  - @backstage/cli@0.33.1-next.1
  - @backstage/core-app-api@1.17.2-next.0
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0
  - @backstage/integration-react@1.2.9-next.1
  - @backstage/test-utils@1.7.10-next.1
  - @backstage/plugin-techdocs@1.13.2-next.1
  - @backstage/plugin-techdocs-react@1.3.1-next.1
  - @backstage/plugin-catalog@1.31.1-next.1
  - @backstage/app-defaults@1.6.4-next.1

## yarn-plugin-backstage@0.0.7-next.0

### Patch Changes

- d6084b8: Fixed a bug that would prevent the yarn plugin from installing new dependencies with the `backstage:^` protocol.

## @internal/plugin-todo-list@1.0.41-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.17.4-next.1
  - @backstage/core-plugin-api@1.10.9-next.0

## @internal/plugin-todo-list-backend@1.0.41-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.4.1-next.0

## @internal/plugin-todo-list-common@1.0.26-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.9.1-next.0
