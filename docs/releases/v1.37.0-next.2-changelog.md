# Release v1.37.0-next.2

Upgrade Helper: [https://backstage.github.io/upgrade-helper/?to=1.37.0-next.2](https://backstage.github.io/upgrade-helper/?to=1.37.0-next.2)

## @backstage/app-defaults@1.6.0-next.1

### Minor Changes

- 12f8e01: **BREAKING**: The default `DiscoveryApi` implementation has been switched to use `FrontendHostDiscovery`, which adds support for the `discovery.endpoints` configuration.

  This is marked as a breaking change because it will cause any existing `discovery.endpoints` configuration to be picked up and used, which may break existing setups.

  For example, consider the following configuration:

  ```yaml
  app:
    baseUrl: https://backstage.acme.org

  backend:
    baseUrl: https://backstage.internal.acme.org

  discovery:
    endpoints:
      - target: https://catalog.internal.acme.org/api/{{pluginId}}
        plugins: [catalog]
  ```

  This will now cause requests from the frontend towards the `catalog` plugin to be routed to `https://catalog.internal.acme.org/api/catalog`, but this might not be reachable from the frontend. To fix this, you should update the `discovery.endpoints` configuration to only override the internal URL of the plugin:

  ```yaml
  discovery:
    endpoints:
      - target:
          internal: https://catalog.internal.acme.org/api/{{pluginId}}
        plugins: [catalog]
  ```

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.16.0-next.0
  - @backstage/core-components@0.16.5-next.1
  - @backstage/core-plugin-api@1.10.4
  - @backstage/theme@0.6.4
  - @backstage/plugin-permission-react@0.4.31

## @backstage/cli@0.31.0-next.1

### Minor Changes

- 5b70679: **BREAKING**: ESLint warnings no longer trigger system exit codes like errors do.

  Set the max number of warnings to `-1` during linting to enable the gradual adoption of new ESLint rules. To restore the previous behavior, include the `--max-warnings 0` flag in the `backstage-cli <repo|package> lint` command.

### Patch Changes

- e0b226b: build(deps): bump `esbuild` from 0.24.2 to 0.25.0
- 4d45498: Fixed the package prepack command so that it no longer produces unnecessary `index` entries in the `typesVersions` map, which could cause `/index` to be added when automatically adding imports.
- f8bd342: Fix a bug in the translation of the deprecated `--scope` option for the `new` command that could cause plugins to have `backstage-backstage-plugin` in their name.
- Updated dependencies
  - @backstage/config-loader@1.10.0-next.0
  - @backstage/integration@1.16.2-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/cli-node@0.2.13
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/eslint-plugin@0.1.10
  - @backstage/release-manifests@0.0.12
  - @backstage/types@1.2.1

## @backstage/config-loader@1.10.0-next.0

### Minor Changes

- 2fd73aa: The include transforms applied during config loading will now only apply to the known keys `$file`, `$env`, and `$include`. Any other key that begins with a \`# @backstage/config-loader will now be passed through as is.

### Patch Changes

- Updated dependencies
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/core-app-api@1.16.0-next.0

### Minor Changes

- 9262001: The default auth injection middleware for the `FetchApi` will now also take configuration under `discovery.endpoints` into consideration when deciding whether to include credentials or not.
- 12f8e01: The `discovery.endpoints` configuration no longer requires both `internal` and `external` target when using the object form, instead falling back to the default.

### Patch Changes

- Updated dependencies
  - @backstage/config@1.3.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11

## @backstage/core-compat-api@0.4.0-next.2

### Minor Changes

- 8250ffe: **BREAKING**: Dropped support for the removed opaque `@backstage/ExtensionOverrides` and `@backstage/BackstagePlugin` types.

### Patch Changes

- e7fab55: Added the `entityPage` option to `convertLegacyApp`, which you can read more about in the [app migration docs](https://backstage.io/docs/frontend-system/building-apps/migrating#entity-pages).
- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/version-bridge@1.0.11

## @backstage/create-app@0.6.0-next.2

### Minor Changes

- 31731b0: Upgraded the TypeScript version in the template to `5.8`.

### Patch Changes

- 19e5c3f: Added link to multi-stage Dockerfile documentation as alternative option
- Updated dependencies
  - @backstage/cli-common@0.1.15

## @backstage/frontend-app-api@0.11.0-next.2

### Minor Changes

- abcdf44: **BREAKING**: The returned object from `createSpecializedApp` no longer contains a `createRoot()` method, and it instead now contains `apis` and `tree`.

  You can replace existing usage of `app.createRoot()` with the following:

  ```ts
  const root = tree.root.instance?.getData(coreExtensionData.reactElement);
  ```

- 8250ffe: **BREAKING**: Dropped support for the removed opaque `@backstage/ExtensionOverrides` and `@backstage/BackstagePlugin` types.

### Patch Changes

- 4d18b55: It's now possible to provide a middleware that wraps all extension factories by passing an `extensionFactoryMiddleware` to either `createApp()` or `createSpecializedApp()`.
- Updated dependencies
  - @backstage/frontend-defaults@0.2.0-next.2
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/core-app-api@1.16.0-next.0
  - @backstage/config@1.3.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11

## @backstage/frontend-defaults@0.2.0-next.2

### Minor Changes

- 8250ffe: **BREAKING**: Dropped support for the removed opaque `@backstage/ExtensionOverrides` and `@backstage/BackstagePlugin` types.

### Patch Changes

- 4d18b55: It's now possible to provide a middleware that wraps all extension factories by passing an `extensionFactoryMiddleware` to either `createApp()` or `createSpecializedApp()`.
- abcdf44: Internal refactor to match updated `createSpecializedApp`.
- e3f19db: Feature discovery and resolution logic used in `createApp` is now exposed via the `discoverAvailableFeatures` and `resolveAsyncFeatures` functions respectively.
- Updated dependencies
  - @backstage/frontend-app-api@0.11.0-next.2
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/plugin-app@0.1.7-next.2
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/frontend-plugin-api@0.10.0-next.2

### Minor Changes

- 8250ffe: **BREAKING**: Removed the deprecated `ExtensionOverrides` and `FrontendFeature` types.
- 0d1a397: **BREAKING**: Removed deprecated variant of `createExtensionDataRef` where the ID is passed directly.

### Patch Changes

- 5aa7f2c: Added a new Utility API, `DialogApi`, which can be used to show dialogs in the React tree that can collect input from the user.
- e23f5e0: Added new `ExtensionMiddlewareFactory` type.
- a6cb67d: The extensions map for plugins created with `createFrontendPlugin` is now sorted alphabetically by ID in the TypeScript type.
- Updated dependencies
  - @backstage/core-components@0.16.5-next.1
  - @backstage/core-plugin-api@1.10.4
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11

## @backstage/frontend-test-utils@0.3.0-next.2

### Minor Changes

- bba525b: **BREAKING**: Removed deprecated `setupRequestMockHandlers` which was replaced by `registerMswTestHooks`.

### Patch Changes

- f861bfc: Added a `initialRouteEntries` option to `renderInTestApp`.
- f861bfc: The `renderInTestApp` helper now provides a default mock config with mock values for both `app.baseUrl` and `backend.baseUrl`.
- abcdf44: Internal refactor to match updated `createSpecializedApp`.
- Updated dependencies
  - @backstage/frontend-app-api@0.11.0-next.2
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/plugin-app@0.1.7-next.2
  - @backstage/test-utils@1.7.6-next.0
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11

## @backstage/plugin-catalog@1.28.0-next.2

### Minor Changes

- 247a40b: Now a custom entity page header can be passed as input to the default entity page.
- 93533bd: The order in which group tabs appear on the entity page has been changed.

  ### Before

  Previously, entity contents determined the order in which groups were rendered, so a group was rendered as soon as its first entity content was detected.

  ### After

  Groups are now rendered first by default based on their order in the `app-config.yaml` file:

  ```diff
  app:
    extensions:
      - page:catalog/entity:
  +       config:
  +         groups:
  +           # this will be the first tab of the default entity page
  +           - deployment:
  +               title: Deployment
  +           # this will be the second tab of the default entiy page
  +           - documentation:
  +               title: Documentation
  ```

  If you wish to place a normal tab before a group, you must add the tab to a group and place the group in the order you wish it to appear on the entity page (groups that contains only one tab are rendered as normal tabs).

  ```diff
  app:
    extensions:
      - page:catalog/entity:
          config:
            groups:
  +            # Example placing the overview tab first
  +           - overview:
  +               title: Overview
              - deployment:
                  title: Deployment
              # this will be the second tab of the default entiy page
              - documentation:
                  title: Documentation
      - entity-content:catalog/overview:
  +       config:
  +          group: 'overview'
  ```

### Patch Changes

- 31731b0: Internal refactor to avoid `expiry-map` dependency.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-compat-api@0.4.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/plugin-search-react@1.8.7-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/integration-react@1.2.5-next.0
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/plugin-scaffolder-common@1.5.10-next.0
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-catalog-react@1.16.0-next.2

### Minor Changes

- 7f57365: Add support for a new entity predicate syntax when defining `filter`s related to the blueprints exported via `/alpha` for the new frontend system. For more information, see the [entity filters documentation](https://backstage.io/docs/features/software-catalog/catalog-customization#advanced-customization#entity-filters).
- 247a40b: Introduces a new `EntityHeaderBlueprint` that allows you to override the default entity page header.

  ```jsx
  import { EntityHeaderBlueprint } from '@backstage/plugin-catalog-react/alpha';

  EntityHeaderBlueprint.make({
    name: 'my-default-header',
    params: {
      loader: () =>
        import('./MyDefaultHeader').then(m => <m.MyDefaultHeader />),
    },
  });
  ```

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/frontend-test-utils@0.3.0-next.2
  - @backstage/core-compat-api@0.4.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/integration-react@1.2.5-next.0
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-react@0.4.31

## @backstage/plugin-scaffolder-backend@1.31.0-next.2

### Minor Changes

- 36677bb: Support new `createTemplateAction` type, and convert `catalog:fetch` action to new way of defining actions.
- 2b1e50d: use CreatedTemplate[Filter|Global*] as canonical template extensions in scaffolder plugin

### Patch Changes

- e0b226b: build(deps): bump `esbuild` from 0.24.2 to 0.25.0
- 4f8b5b6: Allow signing git commits using configured private PGP key in scaffolder
- 59dcf37: Fixed bug in fs:delete causing no files to be deleted on windows machines
- Updated dependencies
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.8.1-next.2
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.7-next.2
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/plugin-scaffolder-backend-module-github@0.6.1-next.2
  - @backstage/backend-defaults@0.8.2-next.2
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.7-next.2
  - @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.8-next.2
  - @backstage/plugin-scaffolder-backend-module-gerrit@0.2.7-next.2
  - @backstage/plugin-scaffolder-backend-module-azure@0.2.7-next.2
  - @backstage/plugin-scaffolder-backend-module-gitea@0.2.7-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.28-next.0
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.6-next.1
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1
  - @backstage/plugin-scaffolder-common@1.5.10-next.0

## @backstage/plugin-scaffolder-node@0.8.0-next.2

### Minor Changes

- 1a58846: **DEPRECATION**: We've deprecated the old way of defining actions using `createTemplateAction` with raw `JSONSchema` and type parameters, as well as using `zod` through an import. You can now use the new format to define `createTemplateActions` with `zod` provided by the framework. This change also removes support for `logStream` in the `context` as well as moving the `logger` to an instance of `LoggerService`.

  Before:

  ```ts
  createTemplateAction<{ repoUrl: string }, { test: string }>({
    id: 'test',
    schema: {
      input: {
        type: 'object',
        required: ['repoUrl'],
        properties: {
          repoUrl: { type: 'string' },
        },
      },
      output: {
        type: 'object',
        required: ['test'],
        properties: {
          test: { type: 'string' },
        },
      },
    },
    handler: async ctx => {
      ctx.logStream.write('blob');
    },
  });

  // or

  createTemplateAction({
    id: 'test',
    schema: {
      input: z.object({
        repoUrl: z.string(),
      }),
      output: z.object({
        test: z.string(),
      }),
    },
    handler: async ctx => {
      ctx.logStream.write('something');
    },
  });
  ```

  After:

  ```ts
  createTemplateAction({
    id: 'test',
    schema: {
      input: {
        repoUrl: d => d.string(),
      },
      output: {
        test: d => d.string(),
      },
    },
    handler: async ctx => {
      // you can just use ctx.logger.log('...'), or if you really need a log stream you can do this:
      const logStream = new PassThrough();
      logStream.on('data', chunk => {
        ctx.logger.info(chunk.toString());
      });
    },
  });
  ```

### Patch Changes

- 4f8b5b6: Allow signing git commits using configured private PGP key in scaffolder
- Updated dependencies
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-scaffolder-common@1.5.10-next.0

## @backstage/plugin-scaffolder-node-test-utils@0.2.0-next.2

### Minor Changes

- 36677bb: Use update `createTemplateAction` kinds

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/backend-test-utils@1.3.1-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/types@1.2.1

## @backstage/backend-app-api@1.2.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config-loader@1.10.0-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/plugin-permission-node@0.8.9-next.1

## @backstage/backend-defaults@0.8.2-next.2

### Patch Changes

- 12f8e01: The `discovery.endpoints` configuration no longer requires both `internal` and `external` target when using the object form, instead falling back to the default.
- Updated dependencies
  - @backstage/config-loader@1.10.0-next.0
  - @backstage/integration@1.16.2-next.0
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-app-api@1.2.1-next.2
  - @backstage/backend-dev-utils@0.1.5
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/cli-common@0.1.15
  - @backstage/cli-node@0.2.13
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/plugin-permission-node@0.8.9-next.1

## @backstage/backend-dynamic-feature-service@0.6.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/plugin-catalog-backend@1.32.0-next.2
  - @backstage/config-loader@1.10.0-next.0
  - @backstage/backend-defaults@0.8.2-next.2
  - @backstage/plugin-events-backend@0.4.4-next.2
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/cli-common@0.1.15
  - @backstage/cli-node@0.2.13
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-app-node@0.1.31-next.2
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1
  - @backstage/plugin-search-backend-node@1.3.9-next.1
  - @backstage/plugin-search-common@1.2.17

## @backstage/backend-test-utils@1.3.1-next.2

### Patch Changes

- 37c6510: Moved `@types/jest` to `devDependencies`.
- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.2
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-app-api@1.2.1-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.6.1-next.1

## @backstage/core-components@0.16.5-next.1

### Patch Changes

- 48aab13: Add i18n support for scaffolder-react plugin
- Updated dependencies
  - @backstage/config@1.3.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/theme@0.6.4
  - @backstage/version-bridge@1.0.11

## @backstage/dev-utils@1.1.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.16.0-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/app-defaults@1.6.0-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/integration-react@1.2.5-next.0
  - @backstage/theme@0.6.4

## @backstage/integration@1.16.2-next.0

### Patch Changes

- 4f8b5b6: Allow signing git commits using configured private PGP key in scaffolder
- Updated dependencies
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/integration-react@1.2.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.16.2-next.0
  - @backstage/config@1.3.2
  - @backstage/core-plugin-api@1.10.4

## @backstage/repo-tools@0.13.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config-loader@1.10.0-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/cli-node@0.2.13
  - @backstage/errors@1.2.7

## @techdocs/cli@1.9.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/plugin-techdocs-node@1.13.1-next.2

## @backstage/test-utils@1.7.6-next.0

### Patch Changes

- 37c6510: Moved `@types/jest` to `devDependencies`.
- Updated dependencies
  - @backstage/core-app-api@1.16.0-next.0
  - @backstage/config@1.3.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/theme@0.6.4
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-react@0.4.31

## @backstage/plugin-api-docs@0.12.5-next.2

### Patch Changes

- 74871cc: Use consistent Typography in Entity HasApisCard
- Updated dependencies
  - @backstage/plugin-catalog@1.28.0-next.2
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-compat-api@0.4.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-react@0.4.31

## @backstage/plugin-app@0.1.7-next.2

### Patch Changes

- 0aa9d82: Added implementation of the new `DialogApi`.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/core-plugin-api@1.10.4
  - @backstage/integration-react@1.2.5-next.0
  - @backstage/theme@0.6.4
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-react@0.4.31

## @backstage/plugin-app-backend@0.5.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config-loader@1.10.0-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-app-node@0.1.31-next.2
  - @backstage/plugin-auth-node@0.6.1-next.1

## @backstage/plugin-app-node@0.1.31-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config-loader@1.10.0-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-app-visualizer@0.1.17-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/core-plugin-api@1.10.4

## @backstage/plugin-auth-backend@0.24.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-backend-module-oauth2-provider@0.4.1-next.2
  - @backstage/plugin-auth-backend-module-oidc-provider@0.4.1-next.2
  - @backstage/plugin-auth-backend-module-okta-provider@0.2.1-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-backend-module-atlassian-provider@0.4.1-next.1
  - @backstage/plugin-auth-backend-module-auth0-provider@0.2.1-next.1
  - @backstage/plugin-auth-backend-module-aws-alb-provider@0.4.1-next.2
  - @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.6-next.1
  - @backstage/plugin-auth-backend-module-bitbucket-provider@0.3.1-next.1
  - @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.2.1-next.1
  - @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.4.1-next.1
  - @backstage/plugin-auth-backend-module-gcp-iap-provider@0.4.1-next.1
  - @backstage/plugin-auth-backend-module-github-provider@0.3.1-next.1
  - @backstage/plugin-auth-backend-module-gitlab-provider@0.3.1-next.1
  - @backstage/plugin-auth-backend-module-google-provider@0.3.1-next.1
  - @backstage/plugin-auth-backend-module-microsoft-provider@0.3.1-next.1
  - @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.6-next.1
  - @backstage/plugin-auth-backend-module-onelogin-provider@0.3.1-next.1
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-auth-backend-module-aws-alb-provider@0.4.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/errors@1.2.7
  - @backstage/plugin-auth-backend@0.24.4-next.2
  - @backstage/plugin-auth-node@0.6.1-next.1

## @backstage/plugin-auth-backend-module-oauth2-provider@0.4.1-next.2

### Patch Changes

- ce15e30: Fixed repository url in `README.md`
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/plugin-auth-node@0.6.1-next.1

## @backstage/plugin-auth-backend-module-oidc-provider@0.4.1-next.2

### Patch Changes

- ce15e30: Fixed repository url in `README.md`
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/plugin-auth-backend@0.24.4-next.2
  - @backstage/plugin-auth-node@0.6.1-next.1

## @backstage/plugin-auth-backend-module-okta-provider@0.2.1-next.2

### Patch Changes

- ce15e30: Fixed repository url in `README.md`
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/plugin-auth-node@0.6.1-next.1

## @backstage/plugin-auth-react@0.1.13-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.1
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7

## @backstage/plugin-bitbucket-cloud-common@0.2.28-next.0

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.16.2-next.0

## @backstage/plugin-catalog-backend@1.32.0-next.2

### Patch Changes

- 4306303: Added a fix in `@backstage/plugin-catalog-backend` to prevent duplicate path keys in entity search if only casing is different.
- 5243aa4: Fixed an issue occurred when authorizing permissions using custom rules passed via the `PermissionsRegistryService`.
- Updated dependencies
  - @backstage/integration@1.16.2-next.0
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-openapi-utils@0.5.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1
  - @backstage/plugin-search-backend-module-catalog@0.3.2-next.1
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-catalog-backend-module-aws@0.4.9-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration-aws-node@0.1.15
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-kubernetes-common@0.9.4-next.0

## @backstage/plugin-catalog-backend-module-azure@0.3.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.4.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.16.2-next.0
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-bitbucket-cloud-common@0.2.28-next.0
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.3.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-gerrit@0.2.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-github@0.7.11-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.32.0-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-github-org@0.3.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-backend-module-github@0.7.11-next.2
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-gitlab@0.6.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-gitlab-org@0.2.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/plugin-catalog-backend-module-gitlab@0.6.4-next.2
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-incremental-ingestion@0.6.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.32.0-next.2
  - @backstage/backend-defaults@0.8.2-next.2
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-catalog-backend-module-ldap@0.11.3-next.2

### Patch Changes

- e43f41b: Fix `config.d.ts` for `ldapOrg` being incorrect. The documentation says a single
  object or an array are accepted, but the definition only allows an object.
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-backend-module-logs@0.1.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.32.0-next.2
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-catalog-backend-module-openapi@0.2.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1

## @backstage/plugin-catalog-graph@0.4.17-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-compat-api@0.4.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/types@1.2.1

## @backstage/plugin-catalog-import@0.12.11-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-compat-api@0.4.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/integration@1.16.2-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/integration-react@1.2.5-next.0
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-unprocessed-entities@0.2.15-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/core-compat-api@0.4.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7

## @backstage/plugin-config-schema@0.1.66-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.1
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-devtools@0.1.25-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/core-compat-api@0.4.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/plugin-devtools-common@0.1.15
  - @backstage/plugin-permission-react@0.4.31

## @backstage/plugin-devtools-backend@0.5.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/config-loader@1.10.0-next.0
  - @backstage/backend-defaults@0.8.2-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-devtools-common@0.1.15
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1

## @backstage/plugin-events-backend@0.4.4-next.2

### Patch Changes

- b95aa77: add `addHttpPostBodyParser` to events extension to allow body parse customization. This feature will enhance flexibility in handling HTTP POST requests in event-related operations.
- Updated dependencies
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-openapi-utils@0.5.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-events-backend-module-aws-sqs@0.4.9-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1

## @backstage/plugin-events-backend-module-azure@0.2.18-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-events-backend-module-bitbucket-cloud@0.2.18-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-events-backend-module-gerrit@0.2.18-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1

## @backstage/plugin-events-backend-module-github@0.2.18-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2

## @backstage/plugin-events-backend-module-gitlab@0.2.18-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2

## @backstage/plugin-events-backend-test-utils@0.1.42-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.9-next.2

## @backstage/plugin-events-node@0.4.9-next.2

### Patch Changes

- b95aa77: add `addHttpPostBodyParser` to events extension to allow body parse customization. This feature will enhance flexibility in handling HTTP POST requests in event-related operations.
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-home@0.8.6-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/core-app-api@1.16.0-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-compat-api@0.4.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/theme@0.6.4
  - @backstage/plugin-home-react@0.1.24-next.1

## @backstage/plugin-home-react@0.1.24-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.1
  - @backstage/core-plugin-api@1.10.4

## @backstage/plugin-kubernetes@0.12.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-compat-api@0.4.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-kubernetes-common@0.9.4-next.0
  - @backstage/plugin-kubernetes-react@0.5.5-next.2
  - @backstage/plugin-permission-react@0.4.31

## @backstage/plugin-kubernetes-cluster@0.0.23-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-kubernetes-common@0.9.4-next.0
  - @backstage/plugin-kubernetes-react@0.5.5-next.2
  - @backstage/plugin-permission-react@0.4.31

## @backstage/plugin-kubernetes-react@0.5.5-next.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-kubernetes-common@0.9.4-next.0

## @backstage/plugin-notifications@0.5.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/core-compat-api@0.4.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/theme@0.6.4
  - @backstage/types@1.2.1
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-signals-react@0.0.10

## @backstage/plugin-notifications-backend@0.5.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-notifications-node@0.2.13-next.2
  - @backstage/plugin-signals-node@0.1.18-next.2

## @backstage/plugin-notifications-backend-module-email@0.3.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-notifications-node@0.2.13-next.2

## @backstage/plugin-notifications-node@0.2.13-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-signals-node@0.1.18-next.2

## @backstage/plugin-org@0.6.37-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-compat-api@0.4.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-org-react@0.1.36-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4

## @backstage/plugin-scaffolder@1.29.0-next.2

### Patch Changes

- 3db64ba: Disable the submit button on creating
- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-compat-api@0.4.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/plugin-scaffolder-react@1.14.6-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/integration-react@1.2.5-next.0
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/plugin-scaffolder-common@1.5.10-next.0

## @backstage/plugin-scaffolder-backend-module-azure@0.2.7-next.2

### Patch Changes

- 4f8b5b6: Allow signing git commits using configured private PGP key in scaffolder
- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.8-next.2

### Patch Changes

- 4f8b5b6: Allow signing git commits using configured private PGP key in scaffolder
- Updated dependencies
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.7-next.2
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.7-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.7-next.2

### Patch Changes

- c56a279: Added `bitbucketCloud:branchRestriction:create` to allow users to create bitbucket cloud branch restrictions in templates
- 4f8b5b6: Allow signing git commits using configured private PGP key in scaffolder
- e279c30: Fixing spelling mistake in `jsonschema`
- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-bitbucket-cloud-common@0.2.28-next.0

## @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.7-next.2

### Patch Changes

- 4f8b5b6: Allow signing git commits using configured private PGP key in scaffolder
- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.3.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/backend-defaults@0.8.2-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-backend-module-gcp@0.2.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-scaffolder-backend-module-gerrit@0.2.7-next.2

### Patch Changes

- 4f8b5b6: Allow signing git commits using configured private PGP key in scaffolder
- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-scaffolder-backend-module-gitea@0.2.7-next.2

### Patch Changes

- 4f8b5b6: Allow signing git commits using configured private PGP key in scaffolder
- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-scaffolder-backend-module-github@0.6.1-next.2

### Patch Changes

- 9391f58: Pass `undefined` to some parameters for `createOrUpdateEnvironment` as these values are not always supported in different plans of GitHub
- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-scaffolder-backend-module-gitlab@0.8.1-next.2

### Patch Changes

- 0df33ea: fix: Creating a repository in a user namespace would always lead to an error
- ac58f84: Made gitlab:issue:edit action idempotent.
- a75e18f: Change the if statement in the catch block to match what the new version of Gitbeaker will return
- 4f8b5b6: Allow signing git commits using configured private PGP key in scaffolder
- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-scaffolder-backend-module-notifications@0.1.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-notifications-node@0.2.13-next.2

## @backstage/plugin-scaffolder-backend-module-rails@0.5.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-backend-module-sentry@0.2.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-scaffolder-backend-module-yeoman@0.4.8-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node@0.8.0-next.2
  - @backstage/plugin-scaffolder-node-test-utils@0.2.0-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-react@1.14.6-next.2

### Patch Changes

- 48aab13: Add i18n support for scaffolder-react plugin
- 3db64ba: Disable the submit button on creating
- 34ea3f5: Updated dependency `flatted` to `3.3.3`.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/theme@0.6.4
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/plugin-scaffolder-common@1.5.10-next.0

## @backstage/plugin-search@1.4.24-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-compat-api@0.4.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/plugin-search-react@1.8.7-next.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend@1.8.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.2
  - @backstage/backend-openapi-utils@0.5.1-next.1
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1
  - @backstage/plugin-search-backend-node@1.3.9-next.1
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-techdocs@0.3.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-backend-node@1.3.9-next.1
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-techdocs-node@1.13.1-next.2

## @backstage/plugin-search-react@1.8.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/core-plugin-api@1.10.4
  - @backstage/theme@0.6.4
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-signals@0.0.17-next.2

### Patch Changes

- ac3e8c0: Fixed multiple signal connection attempts when there are multiple subscriptions at the same time
- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/core-plugin-api@1.10.4
  - @backstage/theme@0.6.4
  - @backstage/types@1.2.1
  - @backstage/plugin-signals-react@0.0.10

## @backstage/plugin-signals-backend@0.3.2-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/plugin-signals-node@0.1.18-next.2

## @backstage/plugin-signals-node@0.1.18-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.6.1-next.1

## @backstage/plugin-techdocs@1.12.4-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-compat-api@0.4.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/integration@1.16.2-next.0
  - @backstage/plugin-search-react@1.8.7-next.2
  - @backstage/plugin-techdocs-react@1.2.15-next.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/integration-react@1.2.5-next.0
  - @backstage/theme@0.6.4
  - @backstage/plugin-auth-react@0.1.13-next.1
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-techdocs-addons-test-utils@1.0.46-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.28.0-next.2
  - @backstage/core-app-api@1.16.0-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/test-utils@1.7.6-next.0
  - @backstage/plugin-techdocs@1.12.4-next.2
  - @backstage/plugin-search-react@1.8.7-next.2
  - @backstage/plugin-techdocs-react@1.2.15-next.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/integration-react@1.2.5-next.0

## @backstage/plugin-techdocs-backend@1.11.7-next.2

### Patch Changes

- 7828186: Minor type fix
- 8f03776: Properly clean up temporary files on build failures
- Updated dependencies
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-backend-module-techdocs@0.3.7-next.2
  - @backstage/plugin-techdocs-common@0.1.0
  - @backstage/plugin-techdocs-node@1.13.1-next.2

## @backstage/plugin-techdocs-module-addons-contrib@1.1.22-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/integration@1.16.2-next.0
  - @backstage/plugin-techdocs-react@1.2.15-next.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/integration-react@1.2.5-next.0

## @backstage/plugin-techdocs-node@1.13.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.16.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration-aws-node@0.1.15
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-techdocs-react@1.2.15-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/version-bridge@1.0.11

## @backstage/plugin-user-settings@0.8.20-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/core-app-api@1.16.0-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-compat-api@0.4.0-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/theme@0.6.4
  - @backstage/types@1.2.1
  - @backstage/plugin-signals-react@0.0.10
  - @backstage/plugin-user-settings-common@0.0.1

## @backstage/plugin-user-settings-backend@0.3.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/plugin-signals-node@0.1.18-next.2
  - @backstage/plugin-user-settings-common@0.0.1

## example-app@0.2.107-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.28.0-next.2
  - @backstage/frontend-app-api@0.11.0-next.2
  - @backstage/cli@0.31.0-next.1
  - @backstage/core-app-api@1.16.0-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/plugin-signals@0.0.17-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/plugin-scaffolder-react@1.14.6-next.2
  - @backstage/app-defaults@1.6.0-next.1
  - @backstage/plugin-scaffolder@1.29.0-next.2
  - @backstage/plugin-api-docs@0.12.5-next.2
  - @backstage/plugin-catalog-graph@0.4.17-next.2
  - @backstage/plugin-catalog-import@0.12.11-next.2
  - @backstage/plugin-org@0.6.37-next.2
  - @backstage/plugin-techdocs@1.12.4-next.2
  - @backstage/plugin-user-settings@0.8.20-next.2
  - @backstage/plugin-search-react@1.8.7-next.2
  - @backstage/plugin-catalog-unprocessed-entities@0.2.15-next.2
  - @backstage/plugin-devtools@0.1.25-next.2
  - @backstage/plugin-home@0.8.6-next.2
  - @backstage/plugin-kubernetes@0.12.5-next.2
  - @backstage/plugin-notifications@0.5.3-next.2
  - @backstage/plugin-search@1.4.24-next.2
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.22-next.2
  - @backstage/plugin-techdocs-react@1.2.15-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/integration-react@1.2.5-next.0
  - @backstage/theme@0.6.4
  - @backstage/plugin-auth-react@0.1.13-next.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-kubernetes-cluster@0.0.23-next.2
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/plugin-search-common@1.2.17

## example-app-next@0.0.21-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.28.0-next.2
  - @backstage/frontend-app-api@0.11.0-next.2
  - @backstage/frontend-defaults@0.2.0-next.2
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/cli@0.31.0-next.1
  - @backstage/core-app-api@1.16.0-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.2
  - @backstage/core-compat-api@0.4.0-next.2
  - @backstage/plugin-signals@0.0.17-next.2
  - @backstage/plugin-app@0.1.7-next.2
  - @backstage/core-components@0.16.5-next.1
  - @backstage/plugin-scaffolder-react@1.14.6-next.2
  - @backstage/app-defaults@1.6.0-next.1
  - @backstage/plugin-scaffolder@1.29.0-next.2
  - @backstage/plugin-api-docs@0.12.5-next.2
  - @backstage/plugin-catalog-graph@0.4.17-next.2
  - @backstage/plugin-catalog-import@0.12.11-next.2
  - @backstage/plugin-org@0.6.37-next.2
  - @backstage/plugin-techdocs@1.12.4-next.2
  - @backstage/plugin-user-settings@0.8.20-next.2
  - @backstage/plugin-search-react@1.8.7-next.2
  - @backstage/plugin-app-visualizer@0.1.17-next.2
  - @backstage/plugin-catalog-unprocessed-entities@0.2.15-next.2
  - @backstage/plugin-home@0.8.6-next.2
  - @backstage/plugin-kubernetes@0.12.5-next.2
  - @backstage/plugin-notifications@0.5.3-next.2
  - @backstage/plugin-search@1.4.24-next.2
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.22-next.2
  - @backstage/plugin-techdocs-react@1.2.15-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/integration-react@1.2.5-next.0
  - @backstage/theme@0.6.4
  - @backstage/plugin-auth-react@0.1.13-next.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-kubernetes-cluster@0.0.23-next.2
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/plugin-search-common@1.2.17

## app-next-example-plugin@0.0.21-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/core-components@0.16.5-next.1

## example-backend@0.0.36-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.31.0-next.2
  - @backstage/plugin-catalog-backend@1.32.0-next.2
  - @backstage/plugin-techdocs-backend@1.11.7-next.2
  - @backstage/plugin-scaffolder-backend-module-github@0.6.1-next.2
  - @backstage/backend-defaults@0.8.2-next.2
  - @backstage/plugin-events-backend@0.4.4-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-app-backend@0.5.0-next.2
  - @backstage/plugin-auth-backend@0.24.4-next.2
  - @backstage/plugin-auth-backend-module-github-provider@0.3.1-next.1
  - @backstage/plugin-auth-backend-module-guest-provider@0.2.6-next.1
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/plugin-catalog-backend-module-backstage-openapi@0.5.0-next.1
  - @backstage/plugin-catalog-backend-module-openapi@0.2.8-next.2
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.6-next.1
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.6-next.1
  - @backstage/plugin-devtools-backend@0.5.3-next.2
  - @backstage/plugin-kubernetes-backend@0.19.4-next.1
  - @backstage/plugin-notifications-backend@0.5.4-next.2
  - @backstage/plugin-permission-backend@0.5.55-next.1
  - @backstage/plugin-permission-backend-module-allow-all-policy@0.2.6-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1
  - @backstage/plugin-proxy-backend@0.6.0-next.1
  - @backstage/plugin-scaffolder-backend-module-notifications@0.1.8-next.2
  - @backstage/plugin-search-backend@1.8.3-next.2
  - @backstage/plugin-search-backend-module-catalog@0.3.2-next.1
  - @backstage/plugin-search-backend-module-explore@0.2.9-next.1
  - @backstage/plugin-search-backend-module-techdocs@0.3.7-next.2
  - @backstage/plugin-search-backend-node@1.3.9-next.1
  - @backstage/plugin-signals-backend@0.3.2-next.2

## example-backend-legacy@0.2.108-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.8.1-next.2
  - @backstage/plugin-scaffolder-backend@1.31.0-next.2
  - @backstage/plugin-catalog-backend@1.32.0-next.2
  - @backstage/plugin-techdocs-backend@1.11.7-next.2
  - @backstage/backend-defaults@0.8.2-next.2
  - @backstage/integration@1.16.2-next.0
  - @backstage/plugin-events-backend@0.4.4-next.2
  - @backstage/plugin-events-node@0.4.9-next.2
  - @backstage/backend-plugin-api@1.2.1-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-auth-backend@0.24.4-next.2
  - @backstage/plugin-auth-node@0.6.1-next.1
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.6-next.1
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.6-next.1
  - @backstage/plugin-catalog-node@1.16.1-next.1
  - @backstage/plugin-kubernetes-backend@0.19.4-next.1
  - @backstage/plugin-permission-backend@0.5.55-next.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.9-next.1
  - @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.7-next.2
  - @backstage/plugin-scaffolder-backend-module-rails@0.5.7-next.2
  - @backstage/plugin-search-backend@1.8.3-next.2
  - @backstage/plugin-search-backend-module-catalog@0.3.2-next.1
  - @backstage/plugin-search-backend-module-elasticsearch@1.6.6-next.1
  - @backstage/plugin-search-backend-module-explore@0.2.9-next.1
  - @backstage/plugin-search-backend-module-pg@0.5.42-next.1
  - @backstage/plugin-search-backend-module-techdocs@0.3.7-next.2
  - @backstage/plugin-search-backend-node@1.3.9-next.1
  - @backstage/plugin-signals-backend@0.3.2-next.2
  - @backstage/plugin-signals-node@0.1.18-next.2

## e2e-test@0.2.26-next.2

### Patch Changes

- Updated dependencies
  - @backstage/create-app@0.6.0-next.2
  - @backstage/cli-common@0.1.15
  - @backstage/errors@1.2.7

## @internal/frontend@0.0.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/types@1.2.1
  - @backstage/version-bridge@1.0.11

## @internal/scaffolder@0.0.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.10.0-next.2
  - @backstage/plugin-scaffolder-react@1.14.6-next.2

## techdocs-cli-embedded-app@0.2.106-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.28.0-next.2
  - @backstage/cli@0.31.0-next.1
  - @backstage/core-app-api@1.16.0-next.0
  - @backstage/core-components@0.16.5-next.1
  - @backstage/app-defaults@1.6.0-next.1
  - @backstage/test-utils@1.7.6-next.0
  - @backstage/plugin-techdocs@1.12.4-next.2
  - @backstage/plugin-techdocs-react@1.2.15-next.2
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/integration-react@1.2.5-next.0
  - @backstage/theme@0.6.4

## @internal/plugin-todo-list@1.0.37-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.5-next.1
  - @backstage/core-plugin-api@1.10.4
