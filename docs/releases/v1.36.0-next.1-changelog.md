# Release v1.36.0-next.1

Upgrade Helper: [https://backstage.github.io/upgrade-helper/?to=1.36.0-next.1](https://backstage.github.io/upgrade-helper/?to=1.36.0-next.1)

## @backstage/backend-openapi-utils@0.5.0-next.1

### Minor Changes

- 700d32b: **BREAKING**: The `wrapInOpenApiTestServer` and `wrapServer` functions are now exported via `/testUtils` subpath. If you were importing these functions directly from the root of the package, you will need to update your imports to use the `/testUtils` subpath:

  ```diff
  - import { wrapInOpenApiTestServer } from '@backstage/backend-openapi-utils';
  + import { wrapInOpenApiTestServer } from '@backstage/backend-openapi-utils/testUtils';
  ```

  or

  ```diff
  - import { wrapServer } from '@backstage/backend-openapi-utils';
  + import { wrapServer } from '@backstage/backend-openapi-utils/testUtils';
  ```

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/canon@0.1.0-next.1

### Minor Changes

- 72c9800: **BREAKING**: Merged the Stack and Inline component into a single component called Flex.
- 1e4ccce: **BREAKING**: Fixing css structure and making sure that props are applying the correct styles for all responsive values.

### Patch Changes

- 989af25: Removed client directive as they are not needed in React 18.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.

## @backstage/plugin-catalog-backend@1.31.0-next.1

### Minor Changes

- 9b9737c: Added support for emitting entities with an explicit location key during processing.

### Patch Changes

- 4ae6884: Fixed an bug in the entity processing caching that would prevent entities that were emitted during processing to be restored after being overridden.
- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-openapi-utils@0.5.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-events-node@0.4.8-next.0
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/plugin-search-backend-module-catalog@0.3.1-next.1
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-catalog-node@1.16.0-next.1

### Minor Changes

- 9b9737c: Added the `locationKey` option to `processingResult.entity(...)`.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.8-next.0

## @backstage/plugin-scaffolder@1.28.0-next.1

### Minor Changes

- 17088d2: Updating the `TaskLogStream` to take up all space in a running task, and also show the last line of the log by default

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/core-compat-api@0.3.6-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-permission-react@0.4.31-next.0
  - @backstage/plugin-scaffolder-react@1.14.4-next.1
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-scaffolder-backend-module-gitlab@0.8.0-next.1

### Minor Changes

- 06eee14: Support empty repository creation in gitlab without workspace pushing and conditionally skip if the repository already exists.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-scaffolder-node@0.7.0-next.0

## @backstage/app-defaults@1.5.17-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-permission-react@0.4.31-next.0
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/theme@0.6.4-next.0

## @backstage/backend-app-api@1.1.2-next.1

### Patch Changes

- ad9aba2: The log message written when plugins fail to initialize now includes the error that caused the plugin startup to fail.
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-permission-node@0.8.8-next.0

## @backstage/backend-defaults@0.8.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-app-api@1.1.2-next.1
  - @backstage/backend-dev-utils@0.1.5
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/cli-common@0.1.15
  - @backstage/cli-node@0.2.13-next.0
  - @backstage/config@1.3.2
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-events-node@0.4.8-next.0
  - @backstage/plugin-permission-node@0.8.8-next.0

## @backstage/backend-dynamic-feature-service@0.5.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.31.0-next.1
  - @backstage/backend-app-api@1.1.2-next.1
  - @backstage/backend-defaults@0.8.0-next.1
  - @backstage/plugin-events-backend@0.4.2-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/cli-common@0.1.15
  - @backstage/cli-node@0.2.13-next.0
  - @backstage/config@1.3.2
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-app-node@0.1.30-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-events-node@0.4.8-next.0
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/plugin-scaffolder-node@0.7.0-next.0
  - @backstage/plugin-search-backend-node@1.3.8-next.0
  - @backstage/plugin-search-common@1.2.17

## @backstage/backend-test-utils@1.3.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-app-api@1.1.2-next.1
  - @backstage/backend-defaults@0.8.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/cli@0.30.0-next.1

### Patch Changes

- 207f88f: Fixed the file path pattern of many static assets output as part of the frontend build process, where there was an extra `.` before the extension, leading to names like `image-af7946b..png`.
- Updated dependencies
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/cli-node@0.2.13-next.0
  - @backstage/config@1.3.2
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/errors@1.2.7
  - @backstage/eslint-plugin@0.1.10
  - @backstage/integration@1.16.1
  - @backstage/release-manifests@0.0.12
  - @backstage/types@1.2.1

## @backstage/core-app-api@1.15.5-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1

## @backstage/core-compat-api@0.3.6-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/version-bridge@1.0.11-next.0

## @backstage/core-components@0.16.4-next.0

### Patch Changes

- 9c9f4ff: Added `nestedValuesAsYaml` option to `StructuredMetadataTable` to render data as yaml.
- 17088d2: Updating the `TaskLogStream` to take up all space in a running task, and also show the last line of the log by default
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/core-plugin-api@1.10.4-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/create-app@0.5.25-next.1

### Patch Changes

- Bumped create-app version.
- Updated dependencies
  - @backstage/cli-common@0.1.15

## @backstage/dev-utils@1.1.7-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/app-defaults@1.5.17-next.0
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/theme@0.6.4-next.0
  - @backstage/catalog-model@1.7.3

## @backstage/frontend-app-api@0.10.5-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/frontend-defaults@0.1.6-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/frontend-defaults@0.1.6-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/frontend-app-api@0.10.5-next.1
  - @backstage/plugin-app@0.1.6-next.1
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/frontend-plugin-api@0.9.5-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- 9ff3322: Allow route references to be installed in multiple app instances as long as their name is the same.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/types@1.2.1

## @backstage/frontend-test-utils@0.2.6-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/frontend-app-api@0.10.5-next.1
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/test-utils@1.7.5-next.0
  - @backstage/plugin-app@0.1.6-next.1
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1

## @backstage/integration-react@1.2.4-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1

## @backstage/repo-tools@0.12.2-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/cli-node@0.2.13-next.0
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/errors@1.2.7

## @techdocs/cli@1.9.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.0-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/plugin-techdocs-node@1.13.0-next.0

## @backstage/test-utils@1.7.5-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-permission-react@0.4.31-next.0
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4

## @backstage/theme@0.6.4-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.

## @backstage/version-bridge@1.0.11-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.

## @backstage/plugin-api-docs@0.12.4-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/core-compat-api@0.3.6-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-permission-react@0.4.31-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/plugin-catalog@1.26.2-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-api-docs-module-protoc-gen-doc@0.1.9-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.

## @backstage/plugin-app@0.1.6-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-permission-react@0.4.31-next.0
  - @backstage/theme@0.6.4-next.0

## @backstage/plugin-app-visualizer@0.1.16-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/core-plugin-api@1.10.4-next.0

## @backstage/plugin-auth-backend@0.24.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-backend-module-atlassian-provider@0.3.5-next.0
  - @backstage/plugin-auth-backend-module-auth0-provider@0.1.5-next.0
  - @backstage/plugin-auth-backend-module-aws-alb-provider@0.3.3-next.1
  - @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.5-next.0
  - @backstage/plugin-auth-backend-module-bitbucket-provider@0.2.5-next.0
  - @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.1.5-next.0
  - @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.3.5-next.0
  - @backstage/plugin-auth-backend-module-gcp-iap-provider@0.3.5-next.0
  - @backstage/plugin-auth-backend-module-github-provider@0.2.5-next.0
  - @backstage/plugin-auth-backend-module-gitlab-provider@0.2.5-next.0
  - @backstage/plugin-auth-backend-module-google-provider@0.2.5-next.0
  - @backstage/plugin-auth-backend-module-microsoft-provider@0.2.5-next.0
  - @backstage/plugin-auth-backend-module-oauth2-provider@0.3.5-next.0
  - @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.5-next.0
  - @backstage/plugin-auth-backend-module-oidc-provider@0.3.5-next.1
  - @backstage/plugin-auth-backend-module-okta-provider@0.1.5-next.0
  - @backstage/plugin-auth-backend-module-onelogin-provider@0.2.5-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-aws-alb-provider@0.3.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/errors@1.2.7
  - @backstage/plugin-auth-backend@0.24.3-next.1
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-backend-module-oidc-provider@0.3.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-auth-backend@0.24.3-next.1
  - @backstage/plugin-auth-node@0.5.7-next.0

## @backstage/plugin-auth-react@0.1.12-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/errors@1.2.7

## @backstage/plugin-catalog@1.26.2-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/core-compat-api@0.3.6-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-permission-react@0.4.31-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/plugin-search-react@1.8.6-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-scaffolder-common@1.5.9
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-catalog-backend-module-aws@0.4.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-defaults@0.8.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-aws-node@0.1.15
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-kubernetes-common@0.9.3-next.0

## @backstage/plugin-catalog-backend-module-azure@0.3.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-backstage-openapi@0.4.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-openapi-utils@0.5.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.4.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.27
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.3.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-catalog-backend-module-gcp@0.3.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-kubernetes-common@0.9.3-next.0

## @backstage/plugin-catalog-backend-module-gerrit@0.2.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-catalog-backend-module-github@0.7.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.31.0-next.1
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-catalog-backend-module-github-org@0.3.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/plugin-catalog-backend-module-github@0.7.10-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-catalog-backend-module-gitlab@0.6.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-defaults@0.8.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-catalog-backend-module-gitlab-org@0.2.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-catalog-backend-module-gitlab@0.6.3-next.1
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-catalog-backend-module-incremental-ingestion@0.6.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.31.0-next.1
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-defaults@0.8.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.8-next.0
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-catalog-backend-module-ldap@0.11.2-next.1

### Patch Changes

- 636744a: Make `ldapOrg` config key optional
- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-logs@0.1.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.31.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-catalog-backend-module-msgraph@0.6.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-openapi@0.2.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-puppetdb@0.2.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-catalog-backend-module-unprocessed@0.5.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-catalog-unprocessed-entities-common@0.0.7
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-catalog-graph@0.4.16-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/core-compat-api@0.3.6-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/types@1.2.1

## @backstage/plugin-catalog-import@0.12.10-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/core-compat-api@0.3.6-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-react@1.15.2-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/frontend-test-utils@0.2.6-next.1
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/core-compat-api@0.3.6-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-permission-react@0.4.31-next.0
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-catalog-unprocessed-entities@0.2.14-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7

## @backstage/plugin-config-schema@0.1.65-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-devtools@0.1.24-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/core-compat-api@0.3.6-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-permission-react@0.4.31-next.0
  - @backstage/errors@1.2.7
  - @backstage/plugin-devtools-common@0.1.15

## @backstage/plugin-devtools-backend@0.5.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/config-loader@1.9.6-next.0
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-devtools-common@0.1.15
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.8-next.0

## @backstage/plugin-events-backend@0.4.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-openapi-utils@0.5.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.8-next.0

## @backstage/plugin-home@0.8.5-next.1

### Patch Changes

- 8e86f96: Enable collision prevention by default in custom home page.

  This change ensures that items in the home page will not collide with each other
  making the user experience better.

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.

- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/core-compat-api@0.3.6-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/plugin-home-react@0.1.23-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2

## @backstage/plugin-home-react@0.1.23-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0

## @backstage/plugin-kubernetes@0.12.4-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/plugin-kubernetes-react@0.5.4-next.1
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/core-compat-api@0.3.6-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-kubernetes-common@0.9.3-next.0

## @backstage/plugin-kubernetes-backend@0.19.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/plugin-kubernetes-node@0.2.3-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-kubernetes-common@0.9.3-next.0
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.8-next.0

## @backstage/plugin-kubernetes-cluster@0.0.22-next.1

### Patch Changes

- d80b08f: Improved rendering of Kubernetes resources' metadata.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/plugin-kubernetes-react@0.5.4-next.1
  - @backstage/core-components@0.16.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-kubernetes-common@0.9.3-next.0

## @backstage/plugin-kubernetes-react@0.5.4-next.1

### Patch Changes

- d80b08f: Improved rendering of Kubernetes resources' metadata.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-kubernetes-common@0.9.3-next.0

## @backstage/plugin-notifications@0.5.2-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-signals-react@0.0.10-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-notifications-common@0.0.8

## @backstage/plugin-notifications-backend@0.5.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-events-node@0.4.8-next.0
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-notifications-node@0.2.12-next.0
  - @backstage/plugin-signals-node@0.1.17-next.0

## @backstage/plugin-notifications-backend-module-email@0.3.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-notifications-node@0.2.12-next.0

## @backstage/plugin-org@0.6.36-next.1

### Patch Changes

- 9cf1260: Added support for `spec.profile.displayName` to be used in the `MyGroupsSidebarItem` component via the `EntityDisplayName` component when you are a member of multiple Groups.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/core-compat-api@0.3.6-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-org-react@0.1.35-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3

## @backstage/plugin-permission-react@0.4.31-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/config@1.3.2
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-scaffolder-backend@1.30.0-next.1

### Patch Changes

- 37ab712: Fixed an issue where invalid expressions or non-object values in `step.each` caused an error.
- Updated dependencies
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.8.0-next.1
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-defaults@0.8.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.27
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.5-next.1
  - @backstage/plugin-events-node@0.4.8-next.0
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/plugin-scaffolder-backend-module-azure@0.2.6-next.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.7-next.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.6-next.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.6-next.0
  - @backstage/plugin-scaffolder-backend-module-gerrit@0.2.6-next.0
  - @backstage/plugin-scaffolder-backend-module-gitea@0.2.6-next.0
  - @backstage/plugin-scaffolder-backend-module-github@0.5.6-next.0
  - @backstage/plugin-scaffolder-common@1.5.9
  - @backstage/plugin-scaffolder-node@0.7.0-next.0

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.3.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-scaffolder-node@0.7.0-next.0

## @backstage/plugin-scaffolder-backend-module-yeoman@0.4.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-node-test-utils@0.1.19-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/types@1.2.1
  - @backstage/plugin-scaffolder-node@0.7.0-next.0

## @backstage/plugin-scaffolder-node-test-utils@0.1.19-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/backend-test-utils@1.3.0-next.1
  - @backstage/types@1.2.1
  - @backstage/plugin-scaffolder-node@0.7.0-next.0

## @backstage/plugin-scaffolder-react@1.14.4-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-permission-react@0.4.31-next.0
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/theme@0.6.4-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/types@1.2.1
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-search@1.4.23-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/core-compat-api@0.3.6-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/plugin-search-react@1.8.6-next.1
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend@1.8.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-openapi-utils@0.5.0-next.1
  - @backstage/backend-defaults@0.8.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/plugin-search-backend-node@1.3.8-next.0
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-catalog@0.3.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-backend-node@1.3.8-next.0
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-techdocs@0.3.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-backend-node@1.3.8-next.0
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-techdocs-node@1.13.0-next.0

## @backstage/plugin-search-react@1.8.6-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/types@1.2.1
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-signals@0.0.16-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-signals-react@0.0.10-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/types@1.2.1

## @backstage/plugin-signals-react@0.0.10-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/types@1.2.1

## @backstage/plugin-techdocs@1.12.2-next.1

### Patch Changes

- 524f0af: Add missing route ref to the `/alpha` entity content extension.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/core-compat-api@0.3.6-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-techdocs-react@1.2.14-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/plugin-search-react@1.8.6-next.1
  - @backstage/plugin-auth-react@0.1.12-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-techdocs-addons-test-utils@1.0.45-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/plugin-techdocs@1.12.2-next.1
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-techdocs-react@1.2.14-next.0
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/plugin-search-react@1.8.6-next.1
  - @backstage/test-utils@1.7.5-next.0
  - @backstage/plugin-catalog@1.26.2-next.1

## @backstage/plugin-techdocs-backend@1.11.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-backend-module-techdocs@0.3.6-next.1
  - @backstage/plugin-techdocs-common@0.1.0
  - @backstage/plugin-techdocs-node@1.13.0-next.0

## @backstage/plugin-techdocs-module-addons-contrib@1.1.21-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-techdocs-react@1.2.14-next.0
  - @backstage/integration@1.16.1

## @backstage/plugin-techdocs-react@1.2.14-next.0

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2

## @backstage/plugin-user-settings@0.8.19-next.1

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/core-compat-api@0.3.6-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/plugin-signals-react@0.0.10-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-user-settings-common@0.0.1

## @backstage/plugin-user-settings-backend@0.2.30-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.0-next.1
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-signals-node@0.1.17-next.0
  - @backstage/plugin-user-settings-common@0.0.1

## example-app@0.2.106-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-kubernetes-cluster@0.0.22-next.1
  - @backstage/core-components@0.16.4-next.0
  - @backstage/plugin-techdocs@1.12.2-next.1
  - @backstage/plugin-scaffolder@1.28.0-next.1
  - @backstage/cli@0.30.0-next.1
  - @backstage/plugin-home@0.8.5-next.1
  - @backstage/plugin-org@0.6.36-next.1
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.21-next.0
  - @backstage/plugin-catalog-unprocessed-entities@0.2.14-next.0
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/frontend-app-api@0.10.5-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-permission-react@0.4.31-next.0
  - @backstage/plugin-scaffolder-react@1.14.4-next.1
  - @backstage/plugin-catalog-import@0.12.10-next.1
  - @backstage/plugin-techdocs-react@1.2.14-next.0
  - @backstage/app-defaults@1.5.17-next.0
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/plugin-catalog-graph@0.4.16-next.1
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/plugin-notifications@0.5.2-next.1
  - @backstage/plugin-user-settings@0.8.19-next.1
  - @backstage/plugin-search-react@1.8.6-next.1
  - @backstage/plugin-auth-react@0.1.12-next.0
  - @backstage/plugin-kubernetes@0.12.4-next.1
  - @backstage/plugin-api-docs@0.12.4-next.1
  - @backstage/plugin-devtools@0.1.24-next.1
  - @backstage/plugin-catalog@1.26.2-next.1
  - @backstage/plugin-signals@0.0.16-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/plugin-search@1.4.23-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-search-common@1.2.17

## example-app-next@0.0.20-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-kubernetes-cluster@0.0.22-next.1
  - @backstage/core-components@0.16.4-next.0
  - @backstage/plugin-techdocs@1.12.2-next.1
  - @backstage/plugin-scaffolder@1.28.0-next.1
  - @backstage/cli@0.30.0-next.1
  - @backstage/plugin-home@0.8.5-next.1
  - @backstage/plugin-org@0.6.36-next.1
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.21-next.0
  - @backstage/plugin-catalog-unprocessed-entities@0.2.14-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/frontend-defaults@0.1.6-next.1
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/frontend-app-api@0.10.5-next.1
  - @backstage/core-compat-api@0.3.6-next.1
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-permission-react@0.4.31-next.0
  - @backstage/plugin-scaffolder-react@1.14.4-next.1
  - @backstage/plugin-app-visualizer@0.1.16-next.1
  - @backstage/plugin-catalog-import@0.12.10-next.1
  - @backstage/plugin-techdocs-react@1.2.14-next.0
  - @backstage/app-defaults@1.5.17-next.0
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/plugin-catalog-graph@0.4.16-next.1
  - @backstage/plugin-catalog-react@1.15.2-next.1
  - @backstage/plugin-notifications@0.5.2-next.1
  - @backstage/plugin-user-settings@0.8.19-next.1
  - @backstage/plugin-search-react@1.8.6-next.1
  - @backstage/plugin-auth-react@0.1.12-next.0
  - @backstage/plugin-kubernetes@0.12.4-next.1
  - @backstage/plugin-api-docs@0.12.4-next.1
  - @backstage/plugin-catalog@1.26.2-next.1
  - @backstage/plugin-signals@0.0.16-next.0
  - @backstage/theme@0.6.4-next.0
  - @backstage/plugin-search@1.4.23-next.1
  - @backstage/plugin-app@0.1.6-next.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-search-common@1.2.17

## app-next-example-plugin@0.0.20-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/frontend-plugin-api@0.9.5-next.1

## example-backend@0.0.35-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.30.0-next.1
  - @backstage/plugin-catalog-backend@1.31.0-next.1
  - @backstage/backend-defaults@0.8.0-next.1
  - @backstage/plugin-app-backend@0.4.5-next.0
  - @backstage/plugin-events-backend@0.4.2-next.1
  - @backstage/plugin-kubernetes-backend@0.19.3-next.1
  - @backstage/plugin-proxy-backend@0.5.11-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-auth-backend@0.24.3-next.1
  - @backstage/plugin-auth-backend-module-github-provider@0.2.5-next.0
  - @backstage/plugin-auth-backend-module-guest-provider@0.2.5-next.0
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-catalog-backend-module-backstage-openapi@0.4.5-next.1
  - @backstage/plugin-catalog-backend-module-openapi@0.2.7-next.1
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.5-next.1
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.5-next.1
  - @backstage/plugin-devtools-backend@0.5.2-next.1
  - @backstage/plugin-notifications-backend@0.5.2-next.1
  - @backstage/plugin-permission-backend@0.5.54-next.0
  - @backstage/plugin-permission-backend-module-allow-all-policy@0.2.5-next.0
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/plugin-scaffolder-backend-module-github@0.5.6-next.0
  - @backstage/plugin-scaffolder-backend-module-notifications@0.1.7-next.0
  - @backstage/plugin-search-backend@1.8.2-next.1
  - @backstage/plugin-search-backend-module-catalog@0.3.1-next.1
  - @backstage/plugin-search-backend-module-explore@0.2.8-next.0
  - @backstage/plugin-search-backend-module-techdocs@0.3.6-next.1
  - @backstage/plugin-search-backend-node@1.3.8-next.0
  - @backstage/plugin-signals-backend@0.3.1-next.0
  - @backstage/plugin-techdocs-backend@1.11.6-next.1

## example-backend-legacy@0.2.107-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.8.0-next.1
  - @backstage/plugin-scaffolder-backend@1.30.0-next.1
  - @backstage/plugin-catalog-backend@1.31.0-next.1
  - @backstage/plugin-catalog-node@1.16.0-next.1
  - @backstage/backend-defaults@0.8.0-next.1
  - @backstage/plugin-app-backend@0.4.5-next.0
  - @backstage/plugin-events-backend@0.4.2-next.1
  - @backstage/plugin-kubernetes-backend@0.19.3-next.1
  - @backstage/plugin-proxy-backend@0.5.11-next.0
  - @backstage/backend-plugin-api@1.2.0-next.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-auth-backend@0.24.3-next.1
  - @backstage/plugin-auth-node@0.5.7-next.0
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.5-next.1
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.5-next.1
  - @backstage/plugin-events-node@0.4.8-next.0
  - @backstage/plugin-permission-backend@0.5.54-next.0
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-permission-node@0.8.8-next.0
  - @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.6-next.0
  - @backstage/plugin-scaffolder-backend-module-rails@0.5.6-next.0
  - @backstage/plugin-search-backend@1.8.2-next.1
  - @backstage/plugin-search-backend-module-catalog@0.3.1-next.1
  - @backstage/plugin-search-backend-module-elasticsearch@1.6.5-next.0
  - @backstage/plugin-search-backend-module-explore@0.2.8-next.0
  - @backstage/plugin-search-backend-module-pg@0.5.41-next.0
  - @backstage/plugin-search-backend-module-techdocs@0.3.6-next.1
  - @backstage/plugin-search-backend-node@1.3.8-next.0
  - @backstage/plugin-signals-backend@0.3.1-next.0
  - @backstage/plugin-signals-node@0.1.17-next.0
  - @backstage/plugin-techdocs-backend@1.11.6-next.1

## e2e-test@0.2.25-next.1

### Patch Changes

- Updated dependencies
  - @backstage/create-app@0.5.25-next.1
  - @backstage/cli-common@0.1.15
  - @backstage/errors@1.2.7

## @internal/frontend@0.0.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/version-bridge@1.0.11-next.0
  - @backstage/types@1.2.1

## @internal/scaffolder@0.0.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5-next.1
  - @backstage/plugin-scaffolder-react@1.14.4-next.1

## techdocs-cli-embedded-app@0.2.105-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/plugin-techdocs@1.12.2-next.1
  - @backstage/cli@0.30.0-next.1
  - @backstage/integration-react@1.2.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
  - @backstage/plugin-techdocs-react@1.2.14-next.0
  - @backstage/app-defaults@1.5.17-next.0
  - @backstage/core-app-api@1.15.5-next.0
  - @backstage/test-utils@1.7.5-next.0
  - @backstage/plugin-catalog@1.26.2-next.1
  - @backstage/theme@0.6.4-next.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2

## @internal/plugin-todo-list@1.0.36-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.4-next.0
  - @backstage/core-plugin-api@1.10.4-next.0
