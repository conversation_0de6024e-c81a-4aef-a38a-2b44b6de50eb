# Release v1.6.0

## @backstage/catalog-client@1.1.0

### Minor Changes

- 65d1d4343f: Adding `validateEntity` method that calls `/validate-entity` endpoint.

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1

## @backstage/cli@0.19.0

### Minor Changes

- 1fe6823bb5: Updated dependency `eslint-plugin-jest` to `^27.0.0`.

  Note that this major update to the Jest plugin contains some breaking changes.
  This means that some of your tests may start seeing some new lint errors. [Read about them here](https://github.com/jest-community/eslint-plugin-jest/blob/main/CHANGELOG.md#2700-2022-08-28).

  These are mostly possible to fix automatically. You can try to run `yarn backstage-cli repo lint --fix` in your repo root to have most or all of them
  corrected.

### Patch Changes

- 8d886dd33e: The `create-plugin` and `create` commands have both been deprecated in favor of a new `new` command. The `new` command is functionally identical to `create`, but the new naming makes it possible to use as yarn script, since `yarn create` is reserved.

- cc63eb8611: Sort entries in skeleton.tar.gz for better docker layer caching

- 548053614a: Deprecated the `plugin:diff` command. If you wish to keep running similar checks in your project we recommend using bespoke scripts. A useful utility for such scripts is `@manypkg/get-packages`, which helps you enumerate all packages in a monorepo.

- 513b4dd4ef: The `versions:bump` command will now update dependency ranges in `package.json`, even if the new version is within the current range.

- 221e951298: Added support for custom certificate for webpack dev server.

- 934cc34563: Avoid validating the backend configuration schema when loading static configuration for building the frontend.

- 3d4f5daadf: Remove use of deprecated trimLeft/trimRight

- 817f3196f6: Added a new `migrate react-router-deps` command to aid in the migration to React Router v6 stable.

- 742cb4f3d7: Fix issue when using `.jsx` files inside tests

- e7600bdb04: Tweaked workspace packaging to not rewrite existing `package.json` files.

- 6ae0f6a719: Switch out `sucrase` for `swc` for transpilation.

  `sucrase` is a little more relaxed when it comes to supporting the ways of mocking in `jest`. You might have to make some changes to your tests to meet the `jest` standard and spec if your tests seems to start failing.

  Mocks that look like this are invalid, and they will throw a reference error in line with the `jest` documentation [here on example 3](https://jestjs.io/docs/es6-class-mocks#calling-jestmock-with-the-module-factory-parameter)

  ```ts
  const mockCommandExists = jest.fn();
  jest.mock('command-exists', () => mockCommandExists);
  ```

  You might need to update these mocks to look a little like the following to defer the call to the `jest.fn()` spy until the mock is called.

  ```ts
  const mockCommandExists = jest.fn();
  jest.mock(
    'command-exists',
    () =>
      (...args: any[]) =>
        commandExists(...args),
  );
  ```

  Also, imports are immutable. So it means that you might get some errors when trying to use `jest.spyOn` with starred imports. You might see an error like this:

  ```log
  TypeError: Cannot redefine property: executeFrameHandlerStrategy
          at Function.defineProperty (<anonymous>)

        20 | import { AuthResolverContext } from '../types';
        21 |
      > 22 | const mockFrameHandler = jest.spyOn(
           |                               ^
        23 |   helpers,
        24 |   'executeFrameHandlerStrategy',
        25 | ) as unknown as jest.MockedFunction<
  ```

  This happens when you try to do `import * as something from './something'` and then `jest.spyOn(something, 'test)`. You will need to add a `jest.mock` call to mock out the required starred import to return `jest.fn()` functions from the start. Something like this fixes the above test:

  ```ts
  jest.mock('../../helpers', () => ({
    executeFrameHandlerStrategy: jest.fn(),
  }));
  ```

  You can also remove any occurrence of `hot(App)` and any import of `react-hot-loader` if you're using the that package locally, as all this has now been replaced with [React Refresh](https://www.npmjs.com/package/react-refresh) which you will get out of the box with the new CLI.

  **Note** If you're experiencing difficulties with running tests after the migration, please reach out to us on Discord to see if we can help, or raise an issue. But in the meantime you can switch back to the existing behaviour by using the following config in your root `package.json`.

  ```json
  "jest": {
    "transform": {
      "\\.(js|jsx|ts|tsx|mjs|cjs)$": "@backstage/cli/config/jestSucraseTransform.js",
      "\\.(bmp|gif|jpg|jpeg|png|frag|xml|svg|eot|woff|woff2|ttf)$": "@backstage/cli/config/jestFileTransform.js",
      "\\.(yaml)$": "jest-transform-yaml"
    }
  }
  ```

- 1cb078ad9f: Fixed a misconfiguration where all modules where treated as ESM by the React Refresh plugin for Webpack.

- 1fd4f2746f: Removed internal dependencies on Lerna. It is now no longer necessary to have Lerna installed in a project to use all features of the Backstage CLI.

- 667d917488: Updated dependency `msw` to `^0.47.0`.

- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.

- 33fbd9f9a4: Updated dependency `@types/minimatch` to `^5.0.0`.

- 68c2697077: Added a new `backstage-cli repo clean` command that cleans the repo root and runs the clean script in all packages.

- 7d47def9c4: Added dependency on `@types/jest` v27. The `@types/jest` dependency has also been removed from the plugin template and should be removed from any of your own internal packages. If you wish to override the version of `@types/jest` or `jest`, use Yarn resolutions.

- a7e82c9b01: Updated `versions:bump` command to be compatible with Yarn 3.

- Updated dependencies
  - @backstage/config-loader@1.1.4
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/release-manifests@0.0.6

## @backstage/core-app-api@1.1.0

### Minor Changes

- a448fea691: Updated the routing system to be compatible with React Router v6 stable.

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.

- f9ec4e46e3: When using React Router v6 stable, it is now possible for components within the `Route` element tree to have `path` props, although they will be ignored.

- 7d47def9c4: Removed dependency on `@types/jest`.

- 744fea158b: Added `getSystemIcons()` function to the `AppContext` available through `useApp` that will pull a list of all the icons that have been registered in the App.

- 667d917488: Updated dependency `msw` to `^0.47.0`.

- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.

- 8448b53dd6: Clarify that the `WebStorage` observable returns `JsonValue` items.

- 70299c99d5: Updated `FlatRoutes` to be compatible with React Router v6 stable.

- e9d40ebf54: If you'd like to send analytics events to multiple implementations, you may now
  do so using the `MultipleAnalyticsApi` implementation provided by this package.

  ```tsx
  import { MultipleAnalyticsApi } from '@backstage/core-app-api';
  import {
    analyticsApiRef,
    configApiRef,
    storageApiRef,
    identityApiRef,
  } from '@internal/backstage/core-plugin-api';
  import { CustomAnalyticsApi } from '@internal/analytics';
  import { VendorAnalyticsApi } from '@vendor/analytics';

  createApiFactory({
    api: analyticsApiRef,
    deps: { configApi: configApiRef, identityApi: identityApiRef, storageApi: storageApiRef },
    factory: ({ configApi, identityApi, storageApi }) =>
      MultipleAnalyticsApi.fromApis([
        VendorAnalyticsApi.fromConfig(configApi, { identityApi }),
        CustomAnalyticsApi.fromConfig(configApi, { identityApi, storageApi }),
      ]),
  }),
  ```

- Updated dependencies
  - @backstage/core-plugin-api@1.0.6
  - @backstage/config@1.0.2

## @backstage/test-utils@1.2.0

### Minor Changes

- 817f3196f6: Elements rendered in a test app are no longer wrapped in a `Routes` and `Route` element, as this is not compatible with React Router v6 stable.

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- d9e39544be: Add missing peer dependencies
- Updated dependencies
  - @backstage/core-app-api@1.1.0
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-permission-react@0.4.5
  - @backstage/config@1.0.2
  - @backstage/plugin-permission-common@0.6.4

## @backstage/plugin-auth-backend@0.16.0

### Minor Changes

- 2fc41ebf07: Removed the previously deprecated class `AtlassianAuthProvider`. Please use `providers.atlassian.create(...)` instead.
- a291688bc5: Renamed the `RedirectInfo` type to `OAuthStartResponse`
- 8600855fbf: The auth0 integration is updated to use the `passport-auth0` library. The configuration under `auth.providers.auth0.\*` now supports an optional `audience` parameter; providing that allows you to connect to the correct API to get permissions, access tokens, and full profile information.

  [What is an Audience](https://community.auth0.com/t/what-is-the-audience/71414)

### Patch Changes

- 5b011fb2e6: Allow adding misc claims to JWT
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- e1ebaeb332: Cloudflare Access Provider: Add JWT to CloudflareAccessResult
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-auth-node@0.2.5
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-azure-devops@0.2.0

### Minor Changes

- 6c1c59b96e: Added README card `EntityAzureReadmeCard` for Azure Devops.

  To get the README component working you'll need to do the following two steps:

  1.  First we need to add the @backstage/plugin-azure-devops package to your frontend app:

      ```bash title="From your Backstage root directory"
      yarn add --cwd packages/app @backstage/plugin-azure-devops
      ```

  2.  Second we need to add the `EntityAzureReadmeCard` extension to the entity page in your app:

      ```tsx
      // In packages/app/src/components/catalog/EntityPage.tsx
      import {
        EntityAzureReadmeCard,
        isAzureDevOpsAvailable,
      } from '@backstage/plugin-azure-devops';

      // As it is a card, you can customize it the way you prefer
      // For example in the Service section

      const overviewContent = (
        <Grid container spacing={3} alignItems="stretch">
          <EntitySwitch>
            <EntitySwitch.Case if={isAzureDevOpsAvailable}>
              <Grid item md={6}>
                ...
              </Grid>
              <Grid item md={6}>
                <EntityAzureReadmeCard maxHeight={350} />
              </Grid>
            </EntitySwitch.Case>
          </EntitySwitch>
        </Grid>
      );
      ```

  **Notes:**

  - You'll need to add the `EntitySwitch.Case` above from step 2 to all the entity sections you want to see Readme in. For example if you wanted to see Readme when looking at Website entities then you would need to add this to the `websiteEntityPage` section.
  - The `if` prop is optional on the `EntitySwitch.Case`, you can remove it if you always want to see the tab even if the entity being viewed does not have the needed annotation
  - The `maxHeight` property on the `EntityAzureReadmeCard` will set the maximum screen size you would like to see, if not set it will default to 100%

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 3f739be9d9: Minor API signatures cleanup
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/plugin-azure-devops-common@0.3.0
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1

## @backstage/plugin-azure-devops-common@0.3.0

### Minor Changes

- 6c1c59b96e: Added README card `EntityAzureReadmeCard` for Azure Devops.

  To get the README component working you'll need to do the following two steps:

  1.  First we need to add the @backstage/plugin-azure-devops package to your frontend app:

      ```bash title="From your Backstage root directory"
      yarn add --cwd packages/app @backstage/plugin-azure-devops
      ```

  2.  Second we need to add the `EntityAzureReadmeCard` extension to the entity page in your app:

      ```tsx
      // In packages/app/src/components/catalog/EntityPage.tsx
      import {
        EntityAzureReadmeCard,
        isAzureDevOpsAvailable,
      } from '@backstage/plugin-azure-devops';

      // As it is a card, you can customize it the way you prefer
      // For example in the Service section

      const overviewContent = (
        <Grid container spacing={3} alignItems="stretch">
          <EntitySwitch>
            <EntitySwitch.Case if={isAzureDevOpsAvailable}>
              <Grid item md={6}>
                ...
              </Grid>
              <Grid item md={6}>
                <EntityAzureReadmeCard maxHeight={350} />
              </Grid>
            </EntitySwitch.Case>
          </EntitySwitch>
        </Grid>
      );
      ```

  **Notes:**

  - You'll need to add the `EntitySwitch.Case` above from step 2 to all the entity sections you want to see Readme in. For example if you wanted to see Readme when looking at Website entities then you would need to add this to the `websiteEntityPage` section.
  - The `if` prop is optional on the `EntitySwitch.Case`, you can remove it if you always want to see the tab even if the entity being viewed does not have the needed annotation
  - The `maxHeight` property on the `EntityAzureReadmeCard` will set the maximum screen size you would like to see, if not set it will default to 100%

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup

## @backstage/plugin-catalog-backend@1.4.0

### Minor Changes

- dd395335bc: Allow unknown typed location from being registered via the location service by configuration settings
- 651c9d6800: The search index now does retain fields that have a very long value, but in the form of just a null. This makes it possible to at least filter for their existence.
- 6e63bc43f2: Added the `refresh` function to the Connection of the entity providers.

### Patch Changes

- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- 07dda0b746: Add optional value to `hasAnnotation` permission rule
- 243533ecdc: Added support to mysql on some raw queries
- ce77e78c93: Fixes a bug to be able to utilize refresh keys after the entity is loaded from cache
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 679f7c5e95: Include entity ref into error message when catalog policies fail
- 06e2b077a1: Limit the length of error messages that get written to the database and logs - to prevent performance issues
- 62788b2ee8: The experimental `CatalogProcessingExtensionPoint` now accepts multiple providers and processors at once.
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.2
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-permission-node@0.6.5
  - @backstage/plugin-catalog-node@1.1.0
  - @backstage/integration@1.3.1
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/plugin-permission-common@0.6.4
  - @backstage/plugin-scaffolder-common@1.2.0
  - @backstage/plugin-catalog-common@1.0.6
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-catalog-node@1.1.0

### Minor Changes

- 9743bc788c: Added refresh function to the `EntityProviderConnection` to be able to schedule refreshes from entity providers.

### Patch Changes

- 7d7d947352: Adds experimental `catalogServiceRef` for obtaining a `CatalogClient` in the new backend system.
- 409ed984e8: Updated usage of experimental backend service APIs.
- 62788b2ee8: The experimental `CatalogProcessingExtensionPoint` now accepts multiple providers and processors at once.
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.2
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1

## @backstage/plugin-dynatrace@0.2.0

### Minor Changes

- e44c0b3811: New features:

  - Some visual improvements to the table that displays Problems
  - Added support for viewing recent Synthetics results using
  - Added some additional linking to the configured Dynatrace instance

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 148568b5c2: Switched to using node-fetch instead of cross-fetch as is standard for our backend packages
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-playlist@0.1.0

### Minor Changes

- d3737da337: Implement playlist plugin, check out the `README.md` for more details!

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/plugin-permission-react@0.4.5
  - @backstage/plugin-search-react@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1
  - @backstage/plugin-permission-common@0.6.4
  - @backstage/plugin-playlist-common@0.1.0
  - @backstage/plugin-catalog-common@1.0.6

## @backstage/plugin-playlist-backend@0.1.0

### Minor Changes

- d3737da337: Implement playlist plugin, check out the `README.md` for more details!

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-auth-node@0.2.5
  - @backstage/plugin-permission-node@0.6.5
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/plugin-permission-common@0.6.4
  - @backstage/backend-test-utils@0.1.28
  - @backstage/plugin-playlist-common@0.1.0

## @backstage/plugin-playlist-common@0.1.0

### Minor Changes

- d3737da337: Implement playlist plugin, check out the `README.md` for more details!

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.6.4

## @backstage/plugin-scaffolder@1.6.0

### Minor Changes

- 3424a8075d: Added support for `async` validation for the `next` version of the plugin
- ad036784e9: Ability to modify the layout of the step form
- 192d856495: Implementing review step for the scaffolder under `create/next`

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- 9ffb75616d: Fix bug with empty strings in `EntityPicker`
- 3f739be9d9: Minor API signatures cleanup
- 763fb81e82: Internal refactor to use more type safe code when dealing with route parameters.
- a66d44b72b: Fixing bug when the workspace would not be automatically saved when using `allowedOwners`
- 7d47def9c4: Removed dependency on `@types/jest`.
- 6522e459aa: Support displaying and ordering by counts in `EntityTagPicker` field. Add the `showCounts` option to enable this. Also support configuring `helperText`.
- f0510a20b5: Addition of a dismissible Error Banner in Scaffolder page
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- 9097278df2: Updated dependency `json-schema-library` to `^7.0.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- de336de9cd: fix uiSchema generation when using complex dependencies
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/plugin-permission-react@0.4.5
  - @backstage/integration@1.3.1
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/integration-react@1.1.4
  - @backstage/plugin-scaffolder-common@1.2.0
  - @backstage/plugin-catalog-common@1.0.6

## @backstage/plugin-scaffolder-backend@1.6.0

### Minor Changes

- ea2eee9e6a: Add the option for a homepage when using the `github:publish` action

- 8872cc735d: Fixed a bug in plugin-scaffolder-backend where it ignores the skip migration database options.

  To use this new implementation you need to create the instance of `DatabaseTaskStore` using the `PluginDatabaseManager` instead of `Knex`;

      import { DatabaseManager, getRootLogger, loadBackendConfig } from '@backstage/backend-common';
      import { DatabaseTaskStore } from '@backstage/plugin-scaffolder-backend';

      const config = await loadBackendConfig({ argv: process.argv, logger: getRootLogger() });
      const databaseManager = DatabaseManager.fromConfig(config, { migrations: { skip: true } });
      const databaseTaskStore = await DatabaseTaskStore.create(databaseManager);

- 7db9613671: Added `projectId` for gitlab projects to be displayed in the `gitlab:publish` output

- d1f7ba58e3: Added `repositoryId` output when create a repository in Azure

- 1ff817b3f0: add entity metadata to the template info type

### Patch Changes

- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- de8ee4afe3: Provide information about the user into scaffolder template action's context
- 096631e571: Added support for handling broken symlinks within the scaffolder backend. This is intended for templates that may hold a symlink that is invalid at build time but valid within the destination repo.
- 0d8d650e32: Applied the fix from version 1.5.1 of this package, which is part of the v1.5.1 release of Backstage.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 2df9955f4a: Removed the depreacated `publish:file` action, use the template editor to test templates instead.
- 0ecc9a6784: Properly set `ctx.isDryRun` when running actions in dry run mode. Also always log action inputs for debugging purposes when running in dry run mode.
- 6b9f6c0a4d: Added alpha `scaffolderPlugin` to be used with experimental backend system.
- 83c037cd46: Disable octokit throttling in publish:github:pull-request
- 2cbd533426: Uptake the `IdentityApi` change to use `getIdentity` instead of `authenticate` for retrieving the logged in users identity.
- ef9ab322de: Minor API signatures cleanup
- 50467bc15b: The number of task workers used to execute templates now default to 3, rather than 1.
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.2
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-auth-node@0.2.5
  - @backstage/plugin-catalog-node@1.1.0
  - @backstage/integration@1.3.1
  - @backstage/plugin-catalog-backend@1.4.0
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/plugin-scaffolder-common@1.2.0

## @backstage/plugin-scaffolder-common@1.2.0

### Minor Changes

- 1ff817b3f0: add entity metadata to the template info type

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-search-backend-module-pg@0.4.0

### Minor Changes

- 8872cc735d: Fixed a bug in search-backend-module-pg where it ignores the skip migration database options when using the database.

  To use this new implementation you need to create the instance of `DatabaseDocumentStore` using the `PluginDatabaseManager` instead of `Knex`;

      import { DatabaseManager, getRootLogger, loadBackendConfig } from '@backstage/backend-common';
      import { DatabaseDocumentStore } from '@backstage/plugin-search-backend-module-pg';

      const config = await loadBackendConfig({ argv: process.argv, logger: getRootLogger() });
      const databaseManager = DatabaseManager.fromConfig(config, { migrations: { skip: true } });
      const databaseDocumentStore = await DatabaseDocumentStore.create(databaseManager);

### Patch Changes

- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-search-backend-node@1.0.2
  - @backstage/config@1.0.2
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-search-react@1.1.0

### Minor Changes

- 97f2b8f3fd: The `<SearchResult/>` component now accepts a optional `query` prop to request results from the search api:

  > Note: If a query prop is not defined, the results will by default be consumed from the context.

  Example:

  ```jsx
  import React, { useState, useCallback } from 'react';

  import { Grid, List, Paper } from '@material-ui/core';

  import { Page, Header, Content, Lifecycle } from '@backstage/core-components';
  import {
    DefaultResultListItem,
    SearchBarBase,
    SearchResult,
  } from '@backstage/plugin-search-react';

  const SearchPage = () => {
    const [query, setQuery] = useState({
      term: '',
      types: [],
      filters: {},
    });

    const handleChange = useCallback(
      (term: string) => {
        setQuery(prevQuery => ({ ...prevQuery, term }));
      },
      [setQuery],
    );

    return (
      <Page themeId="home">
        <Header title="Search" subtitle={<Lifecycle alpha />} />
        <Content>
          <Grid container direction="row">
            <Grid item xs={12}>
              <Paper>
                <SearchBarBase debounceTime={100} onChange={handleChange} />
              </Paper>
            </Grid>
            <Grid item xs>
              <SearchResult query={query}>
                {({ results }) => (
                  <List>
                    {results.map(({ document }) => (
                      <DefaultResultListItem
                        key={document.location}
                        result={document}
                      />
                    ))}
                  </List>
                )}
              </SearchResult>
            </Grid>
          </Grid>
        </Content>
      </Page>
    );
  };
  ```

  Additionally, a search page can also be composed using these two new results layout components:

  ```jsx
  // Example rendering results as list
  <SearchResult>
    {({ results }) => (
      <SearchResultListLayout
        resultItems={results}
        renderResultItem={({ type, document }) => {
          switch (type) {
            case 'custom-result-item':
              return (
                <CustomResultListItem
                  key={document.location}
                  result={document}
                />
              );
            default:
              return (
                <DefaultResultListItem
                  key={document.location}
                  result={document}
                />
              );
          }
        }}
      />
    )}
  </SearchResult>
  ```

  ```jsx
  // Example rendering results as groups
  <SearchResult>
    {({ results }) => (
      <>
        <SearchResultGroupLayout
          icon={<CustomIcon />}
          title="Custom"
          link="See all custom results"
          resultItems={results.filter(
            ({ type }) => type === 'custom-result-item',
          )}
          renderResultItem={({ document }) => (
            <CustomResultListItem key={document.location} result={document} />
          )}
        />
        <SearchResultGroupLayout
          icon={<DefaultIcon />}
          title="Default"
          resultItems={results.filter(
            ({ type }) => type !== 'custom-result-item',
          )}
          renderResultItem={({ document }) => (
            <DefaultResultListItem key={document.location} result={document} />
          )}
        />
      </>
    )}
  </SearchResult>
  ```

  A `SearchResultList` and `SearchResultGroup` components were also created for users who have search pages with multiple queries, both are specializations of `SearchResult` and also accept a `query` as a prop as well:

  ```jsx
  // Example using the <SearchResultList />
  const SearchPage = () => {
    const query = {
      term: 'example',
    };

    return (
      <SearchResultList
        query={query}
        renderResultItem={({ type, document, highlight, rank }) => {
          switch (type) {
            case 'custom':
              return (
                <CustomResultListItem
                  key={document.location}
                  icon={<CatalogIcon />}
                  result={document}
                  highlight={highlight}
                  rank={rank}
                />
              );
            default:
              return (
                <DefaultResultListItem
                  key={document.location}
                  result={document}
                />
              );
          }
        }}
      />
    );
  };
  ```

  ```jsx
  // Example using the <SearchResultGroup /> for creating a component that search and group software catalog results
  import React, { useState, useCallback } from 'react';

  import { MenuItem } from '@material-ui/core';

  import { JsonValue } from '@backstage/types';
  import { CatalogIcon } from '@backstage/core-components';
  import { CatalogSearchResultListItem } from '@backstage/plugin-catalog';
  import {
    SearchResultGroup,
    SearchResultGroupTextFilterField,
    SearchResultGroupSelectFilterField,
  } from @backstage/plugin-search-react;
  import { SearchQuery } from '@backstage/plugin-search-common';

  const CatalogResultsGroup = () => {
    const [query, setQuery] = useState<Partial<SearchQuery>>({
      types: ['software-catalog'],
    });

    const filterOptions = [
      {
        label: 'Lifecycle',
        value: 'lifecycle',
      },
      {
        label: 'Owner',
        value: 'owner',
      },
    ];

    const handleFilterAdd = useCallback(
      (key: string) => () => {
        setQuery(prevQuery => {
          const { filters: prevFilters, ...rest } = prevQuery;
          const newFilters = { ...prevFilters, [key]: undefined };
          return { ...rest, filters: newFilters };
        });
      },
      [],
    );

    const handleFilterChange = useCallback(
      (key: string) => (value: JsonValue) => {
        setQuery(prevQuery => {
          const { filters: prevFilters, ...rest } = prevQuery;
          const newFilters = { ...prevFilters, [key]: value };
          return { ...rest, filters: newFilters };
        });
      },
      [],
    );

    const handleFilterDelete = useCallback(
      (key: string) => () => {
        setQuery(prevQuery => {
          const { filters: prevFilters, ...rest } = prevQuery;
          const newFilters = { ...prevFilters };
          delete newFilters[key];
          return { ...rest, filters: newFilters };
        });
      },
      [],
    );

    return (
      <SearchResultGroup
        query={query}
        icon={<CatalogIcon />}
        title="Software Catalog"
        link="See all software catalog results"
        filterOptions={filterOptions}
        renderFilterOption={({ label, value }) => (
          <MenuItem key={value} onClick={handleFilterAdd(value)}>
            {label}
          </MenuItem>
        )}
        renderFilterField={(key: string) => {
          switch (key) {
            case 'lifecycle':
              return (
                <SearchResultGroupSelectFilterField
                  key={key}
                  label="Lifecycle"
                  value={query.filters?.lifecycle}
                  onChange={handleFilterChange('lifecycle')}
                  onDelete={handleFilterDelete('lifecycle')}
                >
                  <MenuItem value="production">Production</MenuItem>
                  <MenuItem value="experimental">Experimental</MenuItem>
                </SearchResultGroupSelectFilterField>
              );
            case 'owner':
              return (
                <SearchResultGroupTextFilterField
                  key={key}
                  label="Owner"
                  value={query.filters?.owner}
                  onChange={handleFilterChange('owner')}
                  onDelete={handleFilterDelete('owner')}
                />
              );
            default:
              return null;
          }
        }
        renderResultItem={({ document, highlight, rank }) => (
          <CatalogSearchResultListItem
            key={document.location}
            result={document}
            highlight={highlight}
            rank={rank}
          />
        )}
      />
    );
  };
  ```

- 18f60427f2: Provides search autocomplete functionality through a `SearchAutocomplete` component.
  A `SearchAutocompleteDefaultOption` can also be used to render options with icons, primary texts, and secondary texts.
  Example:

  ```jsx
  import React, { ChangeEvent, useState, useCallback } from 'react';
  import useAsync from 'react-use/lib/useAsync';

  import { Grid, Paper } from '@material-ui/core';

  import { Page, Content } from '@backstage/core-components';
  import { SearchAutocomplete, SearchAutocompleteDefaultOption} from '@backstage/plugin-search-react';

  const OptionsIcon = () => <svg />

  const SearchPage = () => {
    const [inputValue, setInputValue] = useState('');

    const options = useAsync(async () => {
      // Gets and returns autocomplete options
    }, [inputValue])

    const useCallback((_event: ChangeEvent<{}>, newInputValue: string) => {
      setInputValue(newInputValue);
    }, [setInputValue])

    return (
      <Page themeId="home">
        <Content>
          <Grid container direction="row">
            <Grid item xs={12}>
              <Paper>
                <SearchAutocomplete
                  options={options}
                  inputValue={inputValue}
                  inputDebounceTime={100}
                  onInputChange={handleInputChange}
                  getOptionLabel={option => option.title}
                  renderOption={option => (
                    <SearchAutocompleteDefaultOption
                      icon={<OptionIcon />}
                      primaryText={option.title}
                      secondaryText={option.text}
                    />
                  )}
                />
              </Paper>
            </Grid>
          </Grid>
          {'/* Filters and results are omitted */'}
        </Content>
      </Page>
    );
  };
  ```

- ca8d5a6eae: We noticed a repeated check for the existence of a parent context before creating a child search context in more the one component such as Search Modal and Search Bar and to remove code duplication we extract the conditional to the context provider, now you can use it passing an `inheritParentContextIfAvailable` prop to the `SearchContextProvider`.

  Note: This added property does not create a local context if there is a parent context and in this case, you cannot use it together with `initialState`, it will result in a type error because the parent context is already initialized.

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d3737da337: Reset page cursor on search filter change
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-tech-insights@0.3.0

### Minor Changes

- f7e99ac1d8: Added the possibility to display check results of different types on a single scorecard.

  - **BREAKING** Removed the `getScorecardsDefinition` method from the `TechInsightsApi` interface. Added the `getCheckResultRenderers` method that returns rendering components for given types.
  - **BREAKING** The `CheckResultRenderer` type now exposes the `component` factory method that creates a React component used to display a result of a provided check result.
  - The `TechInsightsClient` constructor accepts now the optional `renderers` parameter that can be used to inject a custom renderer.
  - **BREAKING** The `title` parameter in the `EntityTechInsightsScorecardContent` and `EntityTechInsightsScorecardCard` components is now mandatory.
  - The `jsonRulesEngineCheckResultRenderer` used to render `json-rules-engine` check results is exported.
  - The `BooleanCheck` component that can be used to render other check results types is also exported.

  If you were overriding the `getScorecardsDefinition` method to adjust the rendering of check results, you should now provide a custom renderer using `renderers` parameter in the `TechInsightsClient` class.

  See the [README](https://github.com/backstage/backstage/tree/v1.6.0/plugins/tech-insights/README.md) for more details.

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 3f739be9d9: Minor API signatures cleanup
- 763fb81e82: Internal refactor to use more type safe code when dealing with route parameters.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1

## @backstage/plugin-techdocs-backend@1.3.0

### Minor Changes

- aa524a5377: Add `projectId` config option to GCP Cloud Storage techdocs publisher. This will allow users to override the project ID, instead of implicitly using the same one as found in a credentials bundle.

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-techdocs-node@1.4.0
  - @backstage/integration@1.3.1
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/plugin-permission-common@0.6.4
  - @backstage/plugin-catalog-common@1.0.6
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-techdocs-node@1.4.0

### Minor Changes

- aa524a5377: Add `projectId` config option to GCP Cloud Storage techdocs publisher. This will allow users to override the project ID, instead of implicitly using the same one as found in a credentials bundle.

### Patch Changes

- 33ac0c0546: Fix Techdocs S3 and GCS publisher to include bucketRootPath in requests
- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/integration@1.3.1
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-user-settings-backend@0.1.0

### Minor Changes

- 108cdc3912: Added new plugin `@backstage/plugin-user-settings-backend` to store user related
  settings in the database.

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-auth-node@0.2.5
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1

## @backstage/app-defaults@1.0.6

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- d9e39544be: Add missing peer dependencies
- Updated dependencies
  - @backstage/core-app-api@1.1.0
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-permission-react@0.4.5

## @backstage/backend-app-api@0.2.1

### Patch Changes

- 2c57c0c499: Made `ApiRef.defaultFactory` internal.
- 854ba37357: Updated to support new `ServiceFactory` formats.
- af6bb42c68: Updated `ServiceRegistry` to not initialize factories more than once.
- 409ed984e8: Updated service implementations and backend wiring to support scoped service.
- de3347ca74: Updated usages of `ServiceFactory`.
- 1f384c5644: Improved error messaging when failing to instantiate services.
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.2
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-permission-node@0.6.5
  - @backstage/backend-tasks@0.3.5
  - @backstage/errors@1.1.1

## @backstage/backend-common@0.15.1

### Patch Changes

- 399286d7dd: Workaround support for `swc` instead of `sucrase`

- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`

- c3c90280be: Added a keep-alive refresh loop to the `DatabaseManager`, keeping the connection
  pool up.

- c3c90280be: The options part of `DatabaseManager.fromConfig` now accepts an optional logger
  field.

- 3d4f5daadf: Remove use of deprecated trimLeft/trimRight

- bf3cc134eb: Implemented KubernetesContainerRunner: a ContainerRunner implementation that leverages Jobs on a kubernetes cluster

  ```ts
  const kubeConfig = new KubeConfig();
  kubeConfig.loadFromDefault();

  const options: KubernetesContainerRunnerOptions = {
    kubeConfig,
    // namespace where Jobs will be created
    namespace: 'default',
    // Jobs name will be prefixed with this name
    name: 'my-runner',
    // An existing Kubernetes volume that will be used
    // as base for mounts
    mountBase: {
      volumeName: 'workdir',
      // Every mount must start with the base path
      // see example below
      basePath: '/workdir',
    },
    // Define a Pod template for the Jobs. It has to include
    // a volume definition named as the mountBase volumeName
    podTemplate: {
      spec: {
        containers: [],
        volumes: [
          {
            name: 'workdir',
            persistentVolumeClaim: {
              claimName: 'workdir-claim',
            },
          },
        ],
      },
    },
  };
  const containerRunner = new KubernetesContainerRunner(options);

  const runOptions: RunContainerOptions = {
    imageName: 'golang:1.17',
    args: ['echo', 'hello world'],
    mountDirs: {
      '/workdir/app': '/app',
    },
  };
  containerRunner.runContainer(runOptions);
  ```

- e3b1993788: Added port ranges in allowed hosts:

  ```yaml
  reading:
      allow:
        - host: *.examples.org:900-1000
  ```

- 2f52e74b49: Got rid of usages of the uppercase String type

- 60b85d8ade: Updated dependency `helmet` to `^6.0.0`.

  Please note that these policies are no longer applied by default:

  helmet.contentSecurityPolicy no longer sets block-all-mixed-content directive by default
  helmet.expectCt is no longer set by default. It can, however, be explicitly enabled. It will be removed in Helmet 7.

- 667d917488: Updated dependency `msw` to `^0.47.0`.

- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.

- 709f468330: The `branch` command has been added to the `isomorphic-git` wrapper.

- 0c780278e0: Fix for entries being skipped or incomplete when reading large zip archives.

- 96689fbdcb: Workaround for a rare race condition in tests.

- Updated dependencies
  - @backstage/integration@1.3.1
  - @backstage/config-loader@1.1.4
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/backend-defaults@0.1.1

### Patch Changes

- 854ba37357: Updated to support new `ServiceFactory` formats.
- de3347ca74: Updated usages of `ServiceFactory`.
- Updated dependencies
  - @backstage/backend-app-api@0.2.1
  - @backstage/backend-plugin-api@0.1.2

## @backstage/backend-plugin-api@0.1.2

### Patch Changes

- 2c57c0c499: Made `ApiRef.defaultFactory` internal.
- 91eed37a39: Updated `createBackendPlugin` and `createBackendModule` to properly forward lack of options.
- 409ed984e8: Service are now scoped to either `'plugin'` or `'root'` scope. Service factories have been updated to provide dependency instances directly rather than factory functions.
- eef91a2558: Simplified the `ServiceFactory` type and removed `AnyServiceFactory`.
- 854ba37357: The `createServiceFactory` method has been updated to return a higher-order factory that can accept options.
- 68513f169a: When defining a new `ServiceRef` you can now also include a `defaultFactory`, which will be used to construct instances of the service in case there is no explicit factory defined.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/backend-tasks@0.3.5
  - @backstage/config@1.0.2
  - @backstage/plugin-permission-common@0.6.4

## @backstage/backend-tasks@0.3.5

### Patch Changes

- 243533ecdc: Added support to mysql on some raw queries
- 8872cc735d: Fixed a bug where the database option to skip migrations was ignored.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/backend-test-utils@0.1.28

### Patch Changes

- 854ba37357: Updated to support new `ServiceFactory` formats.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- de3347ca74: Updated usages of `ServiceFactory`.
- Updated dependencies
  - @backstage/cli@0.19.0
  - @backstage/backend-app-api@0.2.1
  - @backstage/backend-plugin-api@0.1.2
  - @backstage/backend-common@0.15.1
  - @backstage/config@1.0.2

## @backstage/catalog-model@1.1.1

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/cli-common@0.1.10

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.

## @backstage/codemods@0.1.39

### Patch Changes

- Updated dependencies
  - @backstage/cli-common@0.1.10

## @backstage/config@1.0.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.

## @backstage/config-loader@1.1.4

### Patch Changes

- 5ecca7e44b: No longer log when reloading remote config.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/core-components@0.11.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- d3737da337: Export `HeaderActionMenu` and expose default `Table` icons via `Table.icons`
- 7d47def9c4: Removed dependency on `@types/jest`.
- b29c44d895: Adds code to generate ids for headers parsed through the MarkdownContent component.
- 37e8c5e128: The `RoutedTabs` component has been updated to be compatible with React Router v6 stable.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 13f23f6510: Allow custom images in the empty state component
- 023d14c52b: Fix overflow bug on MicDrop image for 404 page by moving the image and making it relative rather than absolute
- c8bb0ff8ce: Minor cleanup of the public API surface to reduce the number of warnings
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/core-plugin-api@1.0.6

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 744fea158b: Added `getSystemIcons()` function to the `AppContext` available through `useApp` that will pull a list of all the icons that have been registered in the App.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/config@1.0.2

## @backstage/create-app@0.4.31

### Patch Changes

- 6ff94d60d5: Removed usage of the deprecated `diff` command in the root `package.json`.

  To make this change in an existing app, make the following change in the root `package.json`:

  ```diff
  -    "diff": "lerna run diff --",
  ```

- c1f1a4c760: The Backstage packages and plugins have all been updated to support React Router v6 stable. The `create-app` template has not been migrated yet, but if you want to migrate your own app or plugins, check out the [migration guide](https://backstage.io/docs/tutorials/react-router-stable-migration).

- e83de28e36: Fix typo in the documentation

- 7d47def9c4: Removed dependency on `@types/jest`.

- 208d6780c9: The `packages/backend/Dockerfile` received a couple of updates, it now looks as follows:

  ```Dockerfile
  FROM node:16-bullseye-slim

  # Install sqlite3 dependencies. You can skip this if you don't use sqlite3 in the image,
  # in which case you should also move better-sqlite3 to "devDependencies" in package.json.
  RUN apt-get update && \
      apt-get install -y --no-install-recommends libsqlite3-dev python3 build-essential && \
      rm -rf /var/lib/apt/lists/* && \
      yarn config set python /usr/bin/python3

  # From here on we use the least-privileged `node` user to run the backend.
  USER node
  WORKDIR /app

  # This switches many Node.js dependencies to production mode.
  ENV NODE_ENV production

  # Copy repo skeleton first, to avoid unnecessary docker cache invalidation.
  # The skeleton contains the package.json of each package in the monorepo,
  # and along with yarn.lock and the root package.json, that's enough to run yarn install.
  COPY --chown=node:node yarn.lock package.json packages/backend/dist/skeleton.tar.gz ./
  RUN tar xzf skeleton.tar.gz && rm skeleton.tar.gz

  RUN yarn install --frozen-lockfile --production --network-timeout 300000 && rm -rf "$(yarn cache dir)"

  # Then copy the rest of the backend bundle, along with any other files we might want.
  COPY --chown=node:node packages/backend/dist/bundle.tar.gz app-config*.yaml ./
  RUN tar xzf bundle.tar.gz && rm bundle.tar.gz

  CMD ["node", "packages/backend", "--config", "app-config.yaml", "--config", "app-config.production.yaml"]
  ```

  The two notable changes are that a `USER node` instruction has been added and the ordering of instructions has been changed accordingly. This means that the app will now be running using the least-privileged `node` user. In order for this to work we now need to make sure that all app files are owned by the `node` user, which we do by adding the `--chown=node:node` option to the `COPY` instructions.

  The second change is the addition of `ENV NODE_ENV production`, which ensured that all Node.js modules run in production mode. If you apply this change to an existing app, note that one of the more significant changes is that this switches the log formatting to use the default production format, JSON. Rather than your log lines looking like this:

  ```log
  2022-08-10T11:36:05.478Z catalog info Performing database migration type=plugin
  ```

  They will now look like this:

  ```log
  {"level":"info","message":"Performing database migration","plugin":"catalog","service":"backstage","type":"plugin"}
  ```

  If you wish to keep the existing format, you can override this change by applying the following change to `packages/backend/src/index.ts`:

  ```diff
     getRootLogger,
  +  setRootLogger,
  +  createRootLogger,
  +  coloredFormat,
     useHotMemoize,
   ...
     ServerTokenManager,
   } from '@backstage/backend-common';

   ...

   async function main() {
  +  setRootLogger(createRootLogger({ format: coloredFormat }));
  +
     const config = await loadBackendConfig({
  ```

- 49416194e8: Adds `IdentityApi` configuration to `create-app` scaffolding templates.

  To migrate to the new `IdentityApi`, edit the `packages/backend/src/index.ts` adding the following import:

  ```typescript
  import { DefaultIdentityClient } from '@backstage/plugin-auth-node';
  ```

  Use the factory function to create an `IdentityApi` in the `makeCreateEnv` function and return it from the
  function as follows:

  ```typescript
  function makeCreateEnv(config: Config) {
  ...
    const identity = DefaultIdentityClient.create({
      discovery,
    });
  ...

    return {
      ...,
      identity
    }
  }
  ```

  Backend plugins can be upgraded to work with this new `IdentityApi`.

  Add `identity` to the `RouterOptions` type.

  ```typescript
  export interface RouterOptions {
    ...
    identity: IdentityApi;
  }
  ```

  Then you can use the `IdentityApi` from the plugin.

  ```typescript
  export async function createRouter(
    options: RouterOptions,
  ): Promise<express.Router> {
    const { identity } = options;

    router.get('/user', async (req, res) => {
      const user = await identity.getIdentity({ request: req });
      ...
  ```

- 8d886dd33e: Added `yarn new` as one of the scripts installed by default, which calls `backstage-cli new`. This script replaces `create-plugin`, which you can now remove if you want to. It is kept in the `create-app` template for backwards compatibility.

  The `remove-plugin` command has been removed, as it has been removed from the Backstage CLI.

  To apply these changes to an existing app, make the following change to the root `package.json`:

  ```diff
  -    "remove-plugin": "backstage-cli remove-plugin"
  +    "new": "backstage-cli new --scope internal"
  ```

- c3c90280be: The options part of `DatabaseManager.fromConfig` now accepts an optional logger
  field. You may want to supply that logger in your backend initialization code to
  ensure that you can get relevant logging data when things happen related to the
  connection pool.

  In `packages/backend/src/index.ts`:

  ```diff
   function makeCreateEnv(config: Config) {
     const root = getRootLogger();
     ...
  -  const databaseManager = DatabaseManager.fromConfig(config);
  +  const databaseManager = DatabaseManager.fromConfig(config, { logger: root });
  ```

- a578558180: Updated the root `package.json` to use the new `backstage-cli repo clean` command.

  To apply this change to an existing project, make the following change to the root `package.json`:

  ```diff
  -    "clean": "backstage-cli clean && lerna run clean",
  +    "clean": "backstage-cli repo clean",
  ```

- c0a08fd08c: Added `EntityLinksCard` to the system `EntityPage`.

  For an existing installation where you want to display the links card for entity pages of kind `system` you should make the following adjustment to `packages/app/src/components/catalog/EntityPage.tsx`

  ```diff
  const systemPage = (
    ...
          <Grid item md={6} xs={12}>
            <EntityCatalogGraphCard variant="gridItem" height={400} />
          </Grid>
  +       <Grid item md={4} xs={12}>
  +         <EntityLinksCard />
  +       </Grid>
  -      <Grid item md={6}>
  +      <Grid item md={8}>
            <EntityHasComponentsCard variant="gridItem" />
          </Grid>
    ...
  );
  ```

- Updated dependencies
  - @backstage/cli-common@0.1.10

## @backstage/dev-utils@1.0.6

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 329ed2b9c7: Fixed routing when using React Router v6 stable.
- 4c5e85ddf3: Removed the dependency and setup of `react-hot-loader`, since the `@backstage/cli` now uses `swc` with `React Refresh` instead.
- Updated dependencies
  - @backstage/app-defaults@1.0.6
  - @backstage/core-app-api@1.1.0
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/test-utils@1.2.0
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/integration-react@1.1.4

## @backstage/errors@1.1.1

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.

## @backstage/integration@1.3.1

### Patch Changes

- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 42918e085c: Fixed bug in the `bitbucketServer` integration where token did not take precedence over supplied username and password which is described in the documentation.
- f76f22c649: Improved caching around github app tokens.
  Tokens are now cached for 50 minutes, not 10.
  Calls to get app installations are also included in this cache.
  If you have more than one github app configured, consider adding `allowedInstallationOwners` to your apps configuration to gain the most benefit from these performance changes.
- Updated dependencies
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/integration-react@1.1.4

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/integration@1.3.1
  - @backstage/config@1.0.2

## @backstage/release-manifests@0.0.6

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.

## @techdocs/cli@1.2.1

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-techdocs-node@1.4.0
  - @backstage/catalog-model@1.1.1
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.2

## @backstage/plugin-adr@0.2.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- c8bb0ff8ce: Minor cleanup of the public API surface to reduce the number of warnings
- b489de83b1: Fix parsing of ADR location which includes a trailing slash
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/plugin-search-react@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/integration-react@1.1.4
  - @backstage/plugin-adr-common@0.2.1
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-adr-backend@0.2.1

### Patch Changes

- b489de83b1: Continue processing subsequent entities when an error occurs in collator
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/integration@1.3.1
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/plugin-adr-common@0.2.1
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-adr-common@0.2.1

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.3.1
  - @backstage/catalog-model@1.1.1
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-airbrake@0.3.9

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/dev-utils@1.0.6
  - @backstage/test-utils@1.2.0
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-airbrake-backend@0.2.9

### Patch Changes

- 148568b5c2: Switched to using node-fetch instead of cross-fetch as is standard for our backend packages
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/config@1.0.2

## @backstage/plugin-allure@0.1.25

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-analytics-module-ga@0.1.20

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/config@1.0.2

## @backstage/plugin-apache-airflow@0.2.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 2f52e74b49: Got rid of usages of the uppercase String type
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- c8bb0ff8ce: Minor cleanup of the public API surface to reduce the number of warnings
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6

## @backstage/plugin-api-docs@0.8.9

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup
- c8bb0ff8ce: Minor cleanup of the public API surface to reduce the number of warnings
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/plugin-catalog@1.5.1
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-apollo-explorer@0.1.2

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6

## @backstage/plugin-app-backend@0.3.36

### Patch Changes

- d669d89206: Minor API signatures cleanup

- 60b85d8ade: Updated dependency `helmet` to `^6.0.0`.

  Please note that these policies are no longer applied by default:

  helmet.contentSecurityPolicy no longer sets block-all-mixed-content directive by default
  helmet.expectCt is no longer set by default. It can, however, be explicitly enabled. It will be removed in Helmet 7.

- 667d917488: Updated dependency `msw` to `^0.47.0`.

- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.

- 8872cc735d: Fixed a bug where the database option to skip migrations was ignored.

- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/config-loader@1.1.4
  - @backstage/config@1.0.2

## @backstage/plugin-auth-node@0.2.5

### Patch Changes

- 9212439d15: Minor update to tests
- 2cbd533426: `IdentityClient` is now deprecated. Please migrate to `IdentityApi` and `DefaultIdentityClient` instead. The authenticate function on `DefaultIdentityClient` is also deprecated. Please use `getIdentity` instead.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-azure-devops-backend@0.3.15

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup

- 667d917488: Updated dependency `msw` to `^0.47.0`.

- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.

- cb1cfc018b: `createRouter` now requires an additional reader: `UrlReader` argument

  ```diff
  export default async function createPlugin(
    env: PluginEnvironment,
  ): Promise<Router> {
    return createRouter({
      logger: env.logger,
      config: env.config,
  +   reader: env.reader,
    });
  }
  ```

  Remember to check if you have already provided these settings previously.

  #### [Azure DevOps]

  ```yaml
  # app-config.yaml
  azureDevOps:
    host: dev.azure.com
    token: my-token
    organization: my-company
  ```

  #### [Azure Integrations]

  ```yaml
  # app-config.yaml
  integrations:
    azure:
      - host: dev.azure.com
        token: ${AZURE_TOKEN}
  ```

- ef9ab322de: Minor API signatures cleanup

- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-azure-devops-common@0.3.0
  - @backstage/config@1.0.2

## @backstage/plugin-badges@0.2.33

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1

## @backstage/plugin-badges-backend@0.1.30

### Patch Changes

- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-bazaar@0.1.24

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 1dd12349d1: Fixed broken routing by removing the wrapping `Router` from the `RoutedTabs` children.
- Updated dependencies
  - @backstage/cli@0.19.0
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/plugin-catalog@1.5.1
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-bazaar-backend@0.1.20

### Patch Changes

- 8872cc735d: Fixed a bug where the database option to skip migrations was ignored.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/config@1.0.2
  - @backstage/backend-test-utils@0.1.28

## @backstage/plugin-bitbucket-cloud-common@0.1.3

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/integration@1.3.1

## @backstage/plugin-bitrise@0.1.36

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-catalog@1.5.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/plugin-search-react@1.1.0
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1
  - @backstage/integration-react@1.1.4
  - @backstage/plugin-catalog-common@1.0.6
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-catalog-backend-module-aws@0.1.9

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/integration@1.3.1
  - @backstage/plugin-catalog-backend@1.4.0
  - @backstage/backend-tasks@0.3.5
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-catalog-backend-module-azure@0.1.7

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/integration@1.3.1
  - @backstage/plugin-catalog-backend@1.4.0
  - @backstage/backend-tasks@0.3.5
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-catalog-backend-module-bitbucket@0.2.3

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/integration@1.3.1
  - @backstage/plugin-catalog-backend@1.4.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/plugin-bitbucket-cloud-common@0.1.3

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.1.3

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/integration@1.3.1
  - @backstage/plugin-catalog-backend@1.4.0
  - @backstage/backend-tasks@0.3.5
  - @backstage/config@1.0.2
  - @backstage/plugin-bitbucket-cloud-common@0.1.3

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.1.1

### Patch Changes

- 148568b5c2: Switched to using node-fetch instead of cross-fetch as is standard for our backend packages
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/integration@1.3.1
  - @backstage/plugin-catalog-backend@1.4.0
  - @backstage/backend-tasks@0.3.5
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-catalog-backend-module-gerrit@0.1.4

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/integration@1.3.1
  - @backstage/plugin-catalog-backend@1.4.0
  - @backstage/backend-tasks@0.3.5
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-catalog-backend-module-github@0.1.7

### Patch Changes

- 3c4a388537: New experimental alpha exports for use with the upcoming backend system.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 3a62594a11: Add support for including (or excluding) Github repositories by topic
- 287a64bf97: Added the ability to configure the host for the `GitHubEntityProvider` to use against GitHub Enterprise
- 91e2abbd46: Remove the duplicated `scheduleFn` initialization in `GitHubEntityProvider`.
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.2
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-catalog-node@1.1.0
  - @backstage/integration@1.3.1
  - @backstage/plugin-catalog-backend@1.4.0
  - @backstage/backend-tasks@0.3.5
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-catalog-backend-module-gitlab@0.1.7

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/integration@1.3.1
  - @backstage/plugin-catalog-backend@1.4.0
  - @backstage/backend-tasks@0.3.5
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-catalog-backend-module-ldap@0.5.3

### Patch Changes

- 7fa54c3c77: Added ae-dir LDAP vendor to correctly extract DN.
- c54fcea9af: Now the `searchStream` method in LDAP client awaits the callbacks
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.4.0
  - @backstage/backend-tasks@0.3.5
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-catalog-backend-module-msgraph@0.4.2

### Patch Changes

- c1d32d2b76: Fixed a bug in the `MicrosoftGraphEntityProvider` that ignored the `userExpand` and `groupExpand` configuration parameters
- d80aab31ae: Added $select attribute to user query
- a246d5a9b8: Read `queryMode` from the `microsoftGraphOrg` config
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 4c82b955fc: Fix typo
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.4.0
  - @backstage/backend-tasks@0.3.5
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2

## @backstage/plugin-catalog-backend-module-openapi@0.1.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-catalog-node@1.1.0
  - @backstage/integration@1.3.1
  - @backstage/plugin-catalog-backend@1.4.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2

## @backstage/plugin-catalog-common@1.0.6

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.6.4
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-catalog-graph@0.2.21

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-catalog-graphql@0.3.13

### Patch Changes

- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2

## @backstage/plugin-catalog-import@0.8.12

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/integration@1.3.1
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/integration-react@1.1.4

## @backstage/plugin-catalog-react@1.1.4

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- f6033d1121: humanizeEntityRef function can now be forced to include default namespace
- c86741a052: Support showing counts in option labels of the `EntityTagPicker`. You can enable this by adding the `showCounts` property
- 7d47def9c4: Removed dependency on `@types/jest`.
- a6d551fad9: Properly handle free-text entity filtering in the case of empty tag arrays
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-permission-react@0.4.5
  - @backstage/integration@1.3.1
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1
  - @backstage/plugin-permission-common@0.6.4
  - @backstage/plugin-catalog-common@1.0.6

## @backstage/plugin-cicd-statistics@0.1.11

### Patch Changes

- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-cicd-statistics-module-gitlab@0.1.5

### Patch Changes

- 71b7464a7c: Fixing typo in the Readme file: `'@backstage plugin-cicd-statistics-module-gitlab';` -> `'@backstage/plugin-cicd-statistics-module-gitlab';`
- a98f5d4c21: add the ability to add CICD default options to createGitlabApi constructor
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6
  - @backstage/catalog-model@1.1.1
  - @backstage/plugin-cicd-statistics@0.1.11

## @backstage/plugin-circleci@0.3.9

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-cloudbuild@0.3.9

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 3f739be9d9: Minor API signatures cleanup
- 763fb81e82: Internal refactor to use more type safe code when dealing with route parameters.
- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-code-climate@0.1.9

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-code-coverage@0.2.2

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-code-coverage-backend@0.2.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 8872cc735d: Fixed a bug where the database option to skip migrations was ignored.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/integration@1.3.1
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-codescene@0.1.4

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-config-schema@0.1.32

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-cost-insights@0.11.31

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 9318bf15d4: The `CostInsightsHeader`component now uses group names if available
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2

## @backstage/plugin-explore@0.3.40

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 3f739be9d9: Minor API signatures cleanup
- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/plugin-explore-react@0.0.21

## @backstage/plugin-explore-react@0.0.21

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6

## @backstage/plugin-firehydrant@0.1.26

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4

## @backstage/plugin-fossa@0.2.41

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1

## @backstage/plugin-gcalendar@0.3.5

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- a69ac5b63a: Upgrade `react-query:3` to `@tanstack/react-query:4`
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/errors@1.1.1

## @backstage/plugin-gcp-projects@0.3.28

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6

## @backstage/plugin-git-release-manager@0.3.22

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 3f739be9d9: Minor API signatures cleanup
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/integration@1.3.1

## @backstage/plugin-github-actions@0.5.9

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 1bdb0bf24b: Modify RecentWorkflowRunsCard use constructed route instead of hardcoded route.
- 3f739be9d9: Minor API signatures cleanup
- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/integration@1.3.1
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-github-deployments@0.1.40

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/integration@1.3.1
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1
  - @backstage/integration-react@1.1.4

## @backstage/plugin-github-issues@0.1.1

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/integration@1.3.1
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1

## @backstage/plugin-github-pull-requests-board@0.1.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 2665ee4ed4: Clarified GitHub app permissions required by plugin
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 723113296b: Add optional `pullRequestLimit` prop to `EntityTeamPullRequestsCard` and `EntityTeamPullRequestsContent` to limit the number of PRs shown per repository. Excluding this prop will default the number of pull requests shown to 10 per repository (the existing functionality).
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/integration@1.3.1
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-gitops-profiles@0.3.27

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6

## @backstage/plugin-gocd@0.1.15

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1

## @backstage/plugin-graphiql@0.2.41

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6

## @backstage/plugin-graphql-backend@0.1.26

### Patch Changes

- d669d89206: Minor API signatures cleanup

- 60b85d8ade: Updated dependency `helmet` to `^6.0.0`.

  Please note that these policies are no longer applied by default:

  helmet.contentSecurityPolicy no longer sets block-all-mixed-content directive by default
  helmet.expectCt is no longer set by default. It can, however, be explicitly enabled. It will be removed in Helmet 7.

- 667d917488: Updated dependency `msw` to `^0.47.0`.

- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.

- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/config@1.0.2
  - @backstage/plugin-catalog-graphql@0.3.13

## @backstage/plugin-home@0.4.25

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/plugin-stack-overflow@0.1.5

## @backstage/plugin-ilert@0.1.35

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1

## @backstage/plugin-jenkins@0.7.8

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 3f739be9d9: Minor API signatures cleanup
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1
  - @backstage/plugin-jenkins-common@0.1.8

## @backstage/plugin-jenkins-backend@0.1.26

### Patch Changes

- 618c06f838: Add example on how to pass through permissions
- a966ed8385: Unwrap error message when getting projects
- 47952621dc: Extend configuration so that extra headers can be sent to jenkins instance
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- 69ec1cf39f: Updated dependency `jenkins` to `^1.0.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- e55d855a6b: Jenkins plugin works again
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-auth-node@0.2.5
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/plugin-permission-common@0.6.4
  - @backstage/plugin-jenkins-common@0.1.8

## @backstage/plugin-jenkins-common@0.1.8

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.6.4
  - @backstage/plugin-catalog-common@1.0.6

## @backstage/plugin-kafka@0.3.9

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- f6be17460d: Minor API signatures cleanup
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2

## @backstage/plugin-kafka-backend@0.2.29

### Patch Changes

- f6be17460d: Minor API signatures cleanup
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-kubernetes@0.7.2

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 0768d6dece: add new kubernetes backend endpoints to kubernetes backend client
- 19a27929fb: Reset error state on success
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/plugin-kubernetes-common@0.4.2

## @backstage/plugin-kubernetes-backend@0.7.2

### Patch Changes

- 8902c2e39d: chore: Exporting KubernetesClientProvider and everything in kubernetes-auth-translator as requested in issue #10457

- a57d29d572: Adds skipMetricsLookup to the kubernetes-backend schema

- 0768d6dece: add new kubernetes backend endpoints to kubernetes backend client

- 60b85d8ade: Updated dependency `helmet` to `^6.0.0`.

  Please note that these policies are no longer applied by default:

  helmet.contentSecurityPolicy no longer sets block-all-mixed-content directive by default
  helmet.expectCt is no longer set by default. It can, however, be explicitly enabled. It will be removed in Helmet 7.

- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-auth-node@0.2.5
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/plugin-kubernetes-common@0.4.2

## @backstage/plugin-kubernetes-common@0.4.2

### Patch Changes

- 0768d6dece: add new kubernetes backend endpoints to kubernetes backend client
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-lighthouse@0.3.9

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- f6be17460d: Minor API signatures cleanup
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2

## @backstage/plugin-newrelic@0.3.27

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6

## @backstage/plugin-newrelic-dashboard@0.2.2

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- d669d89206: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1

## @backstage/plugin-org@0.5.9

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- ab6650ede9: Added an edit button to the `UserProfileCard` that is enabled when the `backstage.io/edit-url` is present, this matches how the `GroupProfileCard` works
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-pagerduty@0.5.2

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 3f739be9d9: Minor API signatures cleanup
- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1

## @backstage/plugin-periskop@0.1.7

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1

## @backstage/plugin-periskop-backend@0.1.7

### Patch Changes

- 148568b5c2: Switched to using node-fetch instead of cross-fetch as is standard for our backend packages
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/config@1.0.2

## @backstage/plugin-permission-backend@0.5.11

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 2cbd533426: Uptake the `IdentityApi` change to use `getIdentity` instead of `authenticate` for retrieving the logged in users identity.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-auth-node@0.2.5
  - @backstage/plugin-permission-node@0.6.5
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/plugin-permission-common@0.6.4

## @backstage/plugin-permission-common@0.6.4

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-permission-node@0.6.5

### Patch Changes

- 9212439d15: Minor update to tests
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-auth-node@0.2.5
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/plugin-permission-common@0.6.4

## @backstage/plugin-permission-react@0.4.5

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.

- 7d47def9c4: Removed dependency on `@types/jest`.

- 817f3196f6: **DEPRECATION**: The `PermissionedRoute` component has been deprecated in favor of the new `RequirePermission` component. This is because the usage pattern of `PermissionedRoute` is not compatible with React Router v6 stable.

  Embed the type from `react-router` instead of exporting it directly.

- Updated dependencies
  - @backstage/core-plugin-api@1.0.6
  - @backstage/config@1.0.2
  - @backstage/plugin-permission-common@0.6.4

## @backstage/plugin-proxy-backend@0.2.30

### Patch Changes

- f6be17460d: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/config@1.0.2

## @backstage/plugin-rollbar@0.4.9

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-rollbar-backend@0.1.33

### Patch Changes

- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/config@1.0.2

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.2.11

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-scaffolder-backend@1.6.0
  - @backstage/integration@1.3.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-scaffolder-backend-module-rails@0.4.4

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-scaffolder-backend@1.6.0
  - @backstage/integration@1.3.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-scaffolder-backend-module-yeoman@0.2.9

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.6.0
  - @backstage/config@1.0.2

## @backstage/plugin-search@1.0.2

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 18f60427f2: Use the new `inheritParentContextIfAvailable` search context property in `SearchModal` instead of manually checking if a parent context exists, this conditional statement was previously duplicated in more than one component like in `SearchBar` as well and is now only done in ` SearchContextProvider`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/plugin-search-react@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-search-backend@1.0.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-auth-node@0.2.5
  - @backstage/plugin-permission-node@0.6.5
  - @backstage/plugin-search-backend-node@1.0.2
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/plugin-permission-common@0.6.4
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-search-backend-module-elasticsearch@1.0.2

### Patch Changes

- 1bea488bd4: Updated dependency `aws-os-connection` to `^0.2.0`.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/plugin-search-backend-node@1.0.2
  - @backstage/config@1.0.2
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-search-backend-node@1.0.2

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- a1124adf5e: Fixed bug in LunrSearchEngine where stopwords in title is causing in-doc search not working
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/backend-tasks@0.3.5
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/plugin-permission-common@0.6.4
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-search-common@1.0.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.6.4

## @backstage/plugin-sentry@0.4.2

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 3f739be9d9: Minor API signatures cleanup
- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-shortcuts@0.3.1

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 8a57ffc0fa: Ensure that a stable observable is used in the shortcuts API
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6

## @backstage/plugin-sonarqube@0.4.1

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-sonarqube-backend@0.1.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-splunk-on-call@0.3.33

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1

## @backstage/plugin-stack-overflow@0.1.5

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- b8190af939: Create a front end API.
- a720c44070: Support showing HTML entity references from the API response before rendering the question title to the list component.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-home@0.4.25
  - @backstage/config@1.0.2
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-stack-overflow-backend@0.1.5

### Patch Changes

- 79040f73f7: Now requests all questions available using pagination. Default max page is set to 100, with a configurable `maxPage` option on the collator.
- 148568b5c2: Switched to using node-fetch instead of cross-fetch as is standard for our backend packages
- Updated dependencies
  - @backstage/cli@0.19.0
  - @backstage/config@1.0.2
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-tech-insights-backend@0.5.2

### Patch Changes

- 8872cc735d: Fixed a bug where the database option to skip migrations was ignored.
- 12c6a07c2a: Changed the description of the `techdocsAnnotationFactName` fact.
- 2e0689e536: Support for timeout in FactRetrieverRegistrationOptions
- 9e8e9f5243: Modify Tech insight initialization to expose FactRetrieverEngine. Enables users to trigger fact retrieval manually or reschedule retrievers on runtime.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-tech-insights-node@0.3.4
  - @backstage/backend-tasks@0.3.5
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.20

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-tech-insights-node@0.3.4
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-tech-insights-node@0.3.4

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup
- 2e0689e536: Support for timeout in FactRetrieverRegistrationOptions
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/backend-tasks@0.3.5
  - @backstage/config@1.0.2

## @backstage/plugin-tech-radar@0.5.16

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6

## @backstage/plugin-techdocs@1.3.2

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- 3f739be9d9: Minor API signatures cleanup
- 763fb81e82: Internal refactor to use more type safe code when dealing with route parameters.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 817f3196f6: Updated the `TechDocsReaderPage` to be compatible with React Router v6 stable.
- 7a95c705fa: Fixed a bug where addons wouldn't render on sub pages when using React Route v6 stable.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- ca8d5a6eae: Use the new `SearchAutocomplete` component in the `TechDocsSearch` component to maintain consistency across search experiences and avoid code duplication.
- 829f14a9b0: Always update the title and sub-title when the location changes on a `TechDocs` reader page.
- e97d616f08: Fixed a bug where scrolling for anchors where the id starts with number didn't work for the current page.
- ef9ab322de: Minor API signatures cleanup
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/plugin-search-react@1.1.0
  - @backstage/plugin-techdocs-react@1.0.4
  - @backstage/integration@1.3.1
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/integration-react@1.1.4
  - @backstage/plugin-search-common@1.0.1

## @backstage/plugin-techdocs-addons-test-utils@1.0.4

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-app-api@1.1.0
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/test-utils@1.2.0
  - @backstage/plugin-catalog@1.5.1
  - @backstage/plugin-search-react@1.1.0
  - @backstage/plugin-techdocs-react@1.0.4
  - @backstage/plugin-techdocs@1.3.2
  - @backstage/integration-react@1.1.4

## @backstage/plugin-techdocs-module-addons-contrib@1.0.4

### Patch Changes

- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- 3f739be9d9: Minor API signatures cleanup
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-techdocs-react@1.0.4
  - @backstage/integration@1.3.1
  - @backstage/integration-react@1.1.4

## @backstage/plugin-techdocs-react@1.0.4

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2

## @backstage/plugin-todo@0.2.11

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1

## @backstage/plugin-todo-backend@0.1.33

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/integration@1.3.1
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @backstage/plugin-user-settings@0.4.8

### Patch Changes

- 817f3196f6: Updated React Router dependencies to be peer dependencies.
- 3f739be9d9: Minor API signatures cleanup
- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- 8448b53dd6: Added a `UserSettingsStorage` implementation of the `StorageApi` for use as
  drop-in replacement for the `WebStorage`, in conjunction with the newly created
  `@backstage/plugin-user-settings-backend`.
- Updated dependencies
  - @backstage/core-app-api@1.1.0
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/errors@1.1.1

## @backstage/plugin-vault@0.1.3

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/catalog-model@1.1.1
  - @backstage/errors@1.1.1

## @backstage/plugin-vault-backend@0.2.2

### Patch Changes

- 3f739be9d9: Minor API signatures cleanup

- 148568b5c2: Switched to using node-fetch instead of cross-fetch as is standard for our backend packages

- 60b85d8ade: Updated dependency `helmet` to `^6.0.0`.

  Please note that these policies are no longer applied by default:

  helmet.contentSecurityPolicy no longer sets block-all-mixed-content directive by default
  helmet.expectCt is no longer set by default. It can, however, be explicitly enabled. It will be removed in Helmet 7.

- 667d917488: Updated dependency `msw` to `^0.47.0`.

- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.

- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.

- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/backend-tasks@0.3.5
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1
  - @backstage/backend-test-utils@0.1.28

## @backstage/plugin-xcmetrics@0.2.29

### Patch Changes

- 7d47def9c4: Removed dependency on `@types/jest`.
- d669d89206: Minor API signatures cleanup
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- bf5e9030eb: Updated dependency `msw` to `^0.45.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/errors@1.1.1

## example-app@0.2.75

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.19.0
  - @backstage/app-defaults@1.0.6
  - @backstage/core-app-api@1.1.0
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/plugin-airbrake@0.3.9
  - @backstage/plugin-api-docs@0.8.9
  - @backstage/plugin-azure-devops@0.2.0
  - @backstage/plugin-badges@0.2.33
  - @backstage/plugin-catalog-graph@0.2.21
  - @backstage/plugin-catalog-import@0.8.12
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/plugin-circleci@0.3.9
  - @backstage/plugin-cloudbuild@0.3.9
  - @backstage/plugin-code-coverage@0.2.2
  - @backstage/plugin-cost-insights@0.11.31
  - @backstage/plugin-explore@0.3.40
  - @backstage/plugin-gcp-projects@0.3.28
  - @backstage/plugin-github-actions@0.5.9
  - @backstage/plugin-graphiql@0.2.41
  - @backstage/plugin-home@0.4.25
  - @backstage/plugin-jenkins@0.7.8
  - @backstage/plugin-kafka@0.3.9
  - @backstage/plugin-kubernetes@0.7.2
  - @backstage/plugin-lighthouse@0.3.9
  - @backstage/plugin-org@0.5.9
  - @backstage/plugin-pagerduty@0.5.2
  - @backstage/plugin-permission-react@0.4.5
  - @backstage/plugin-rollbar@0.4.9
  - @backstage/plugin-scaffolder@1.6.0
  - @backstage/plugin-search-react@1.1.0
  - @backstage/plugin-search@1.0.2
  - @backstage/plugin-sentry@0.4.2
  - @backstage/plugin-shortcuts@0.3.1
  - @backstage/plugin-tech-insights@0.3.0
  - @backstage/plugin-techdocs-react@1.0.4
  - @backstage/plugin-techdocs@1.3.2
  - @backstage/plugin-todo@0.2.11
  - @backstage/plugin-user-settings@0.4.8
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.4
  - @backstage/plugin-newrelic-dashboard@0.2.2
  - @backstage/plugin-dynatrace@0.2.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/integration-react@1.1.4
  - @backstage/plugin-apache-airflow@0.2.2
  - @backstage/plugin-gcalendar@0.3.5
  - @backstage/plugin-gocd@0.1.15
  - @backstage/plugin-newrelic@0.3.27
  - @backstage/plugin-stack-overflow@0.1.5
  - @backstage/plugin-tech-radar@0.5.16
  - @backstage/plugin-playlist@0.1.0
  - @backstage/plugin-catalog-common@1.0.6
  - @internal/plugin-catalog-customized@0.0.2
  - @backstage/plugin-search-common@1.0.1

## example-backend@0.2.75

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-scaffolder-backend@1.6.0
  - @backstage/plugin-auth-node@0.2.5
  - @backstage/plugin-permission-node@0.6.5
  - @backstage/plugin-kubernetes-backend@0.7.2
  - @backstage/plugin-kafka-backend@0.2.29
  - @backstage/plugin-proxy-backend@0.2.30
  - @backstage/plugin-auth-backend@0.16.0
  - @backstage/integration@1.3.1
  - @backstage/plugin-catalog-backend@1.4.0
  - @backstage/plugin-azure-devops-backend@0.3.15
  - @backstage/plugin-search-backend-node@1.0.2
  - @backstage/plugin-tech-insights-node@0.3.4
  - @backstage/backend-tasks@0.3.5
  - @backstage/plugin-techdocs-backend@1.3.0
  - @backstage/catalog-client@1.1.0
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/plugin-permission-common@0.6.4
  - @backstage/plugin-scaffolder-backend-module-rails@0.4.4
  - @backstage/plugin-search-backend-module-pg@0.4.0
  - @backstage/plugin-jenkins-backend@0.1.26
  - @backstage/plugin-playlist-backend@0.1.0
  - @backstage/plugin-app-backend@0.3.36
  - @backstage/plugin-graphql-backend@0.1.26
  - @backstage/plugin-rollbar-backend@0.1.33
  - @backstage/plugin-code-coverage-backend@0.2.2
  - @backstage/plugin-permission-backend@0.5.11
  - @backstage/plugin-todo-backend@0.1.33
  - @backstage/plugin-search-backend-module-elasticsearch@1.0.2
  - @backstage/plugin-tech-insights-backend@0.5.2
  - @backstage/plugin-badges-backend@0.1.30
  - example-app@0.2.75
  - @backstage/plugin-search-backend@1.0.2
  - @backstage/plugin-search-common@1.0.1
  - @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.20

## example-backend-next@0.0.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.6.0
  - @backstage/plugin-catalog-backend@1.4.0
  - @backstage/backend-defaults@0.1.1

## techdocs-cli-embedded-app@0.2.74

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.19.0
  - @backstage/app-defaults@1.0.6
  - @backstage/core-app-api@1.1.0
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6
  - @backstage/test-utils@1.2.0
  - @backstage/plugin-catalog@1.5.1
  - @backstage/plugin-techdocs-react@1.0.4
  - @backstage/plugin-techdocs@1.3.2
  - @backstage/catalog-model@1.1.1
  - @backstage/config@1.0.2
  - @backstage/integration-react@1.1.4

## @internal/plugin-catalog-customized@0.0.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.1.4
  - @backstage/plugin-catalog@1.5.1

## @internal/plugin-todo-list@1.0.5

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.1
  - @backstage/core-plugin-api@1.0.6

## @internal/plugin-todo-list-backend@1.0.5

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.1
  - @backstage/plugin-auth-node@0.2.5
  - @backstage/config@1.0.2
  - @backstage/errors@1.1.1

## @internal/plugin-todo-list-common@1.0.4

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.6.4
