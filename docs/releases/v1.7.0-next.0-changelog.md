# Release v1.7.0-next.0

## @backstage/cli@0.20.0-next.0

### Minor Changes

- f368ad7279: **BREAKING**: Bumped `jest`, `jest-runtime`, and `jest-environment-jsdom` to v29. This is up from v27, so check out both the [v28](https://jestjs.io/docs/28.x/upgrading-to-jest28) and [v29](https://jestjs.io/docs/upgrading-to-jest29) (later [here](https://jestjs.io/docs/29.x/upgrading-to-jest29)) migration guides.

  Particular changes that where encountered in the main Backstage repo are:

  - The updated snapshot format.
  - `jest.useFakeTimers('legacy')` is now `jest.useFakeTimers({ legacyFakeTimers: true })`.
  - Error objects collected by `withLogCollector` from `@backstage/test-utils` are now objects with a `detail` property rather than a string.

### Patch Changes

- 3e309107ca: Updated fallback versions of dependencies in all templates.

- 292a088807: Added a new `repo test` command.

- ba63cae41c: Updated lockfile parsing to have better support for Yarn 3.

- 2dddb32fea: Switched the Jest transform for YAML files to use a custom one available at `@backstage/cli/config/jestYamlTransform.js`.

- a541a3a78a: Switch to upfront resolution of `swc-loader` in Webpack config.

- cfb3598410: Removed `tsx` and `jsx` as supported extensions in backend packages. For most
  repos, this will not have any effect. But if you inadvertently had added some
  `tsx`/`jsx` files to your backend package, you may now start to see `code: 'MODULE_NOT_FOUND'` errors when launching the backend locally. The reason for
  this is that the offending files get ignored during transpilation. Hence, the
  importing file can no longer find anything to import.

  The fix is to rename any `.tsx` files in your backend packages to `.ts` instead,
  or `.jsx` to `.js`.

- Updated dependencies
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.3-next.0
  - @backstage/config-loader@1.1.5-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/release-manifests@0.0.6
  - @backstage/types@1.0.0

## @backstage/plugin-auth-backend@0.17.0-next.0

### Minor Changes

- 5fa831ce55: CookieConfigurer can optionally return the `SameSite` cookie attribute.
  CookieConfigurer now requires an additional argument `appOrigin` - the origin URL of the app - which is used to calculate the `SameSite` attribute.
  defaultCookieConfigurer returns the `SameSite` attribute which defaults to `Lax`. In cases where an auth-backend is running on a different domain than the App, `SameSite=None` is used - but only for secure contexts. This is so that cookies can be included in third-party requests.

  OAuthAdapterOptions has been modified to require additional arguments, `baseUrl`, and `cookieConfigurer`.
  OAuthAdapter now resolves cookie configuration using its supplied CookieConfigurer for each request to make sure that the proper attributes always are set.

### Patch Changes

- 8c6ec175bf: Fix GitLab provider setup so that it supports GitLab installations with a path in the URL.
- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/plugin-auth-node@0.2.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-bitbucket-cloud-common@0.2.0-next.0

### Minor Changes

- ad74723fbf: Update Bitbucket Cloud models to latest OAS version.

  The latest specification contained some BREAKING CHANGES
  due to removed fields.

  All of these fields are not used at other plugins, though.
  Therefore, this change has no impact on other modules here.

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.3.2-next.0

## @backstage/plugin-scaffolder@1.7.0-next.0

### Minor Changes

- f13d5f3f06: Add support for link to TechDocs and other links defined in template entity specification metadata on TemplateCard
- 05f22193c5: EntityPickers now support flags to control when to include default namespace
  in result

### Patch Changes

- 8960d83013: Add support for `allowedOrganizations` and `allowedOwners` to the `AzureRepoPicker`.
- b681275e69: Ignore .git directories in Template Editor, increase upload limit for dry-runs to 10MB.
- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/plugin-scaffolder-common@1.2.1-next.0
  - @backstage/integration-react@1.1.5-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/plugin-catalog-common@1.0.7-next.0
  - @backstage/plugin-permission-react@0.4.6-next.0

## @backstage/plugin-scaffolder-backend@1.7.0-next.0

### Minor Changes

- 253453fa14: Added a new property called `additionalTemplateGlobals` which allows you to add global functions to the scaffolder nunjucks templates.
- 304305dd20: Add `allowAutoMerge` option for `publish:github` action
- 694bfe2d61: Add functionality to shutdown scaffolder tasks if they are stale

### Patch Changes

- b681275e69: Ignore .git directories in Template Editor, increase upload limit for dry-runs to 10MB.
- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/backend-plugin-api@0.1.3-next.0
  - @backstage/plugin-catalog-backend@1.4.1-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/plugin-catalog-node@1.1.1-next.0
  - @backstage/plugin-scaffolder-common@1.2.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/plugin-auth-node@0.2.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-user-settings@0.5.0-next.0

### Minor Changes

- 5543e86660: **BREAKING**: The `apiRef` passed to `ProviderSettingsItem` now needs to
  implement `ProfileInfoApi & SessionApi`, rather than just the latter. This is
  unlikely to have an effect on most users though, since the builtin auth
  providers generally implement both.

  Fixed settings page showing providers as logged out when the user is using more
  than one provider, and displayed some additional login information.

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/core-app-api@1.1.1-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0

## @backstage/app-defaults@1.0.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/core-app-api@1.1.1-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-permission-react@0.4.6-next.0

## @backstage/backend-app-api@0.2.2-next.0

### Patch Changes

- 0027a749cd: Added possibility to configure index plugin of the HTTP router service.
- 45857bffae: Properly export `rootLoggerFactory`.
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.3-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/plugin-permission-node@0.6.6-next.0
  - @backstage/errors@1.1.2-next.0

## @backstage/backend-common@0.15.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.3-next.0
  - @backstage/config-loader@1.1.5-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/types@1.0.0

## @backstage/backend-defaults@0.1.2-next.0

### Patch Changes

- 96d288a02d: Added root logger service to the set of default services.
- Updated dependencies
  - @backstage/backend-app-api@0.2.2-next.0
  - @backstage/backend-plugin-api@0.1.3-next.0

## @backstage/backend-plugin-api@0.1.3-next.0

### Patch Changes

- 28377dc89f: Allow interfaces to be used for inferred option types.
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/plugin-permission-common@0.6.5-next.0

## @backstage/backend-tasks@0.3.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/types@1.0.0

## @backstage/backend-test-utils@0.1.29-next.0

### Patch Changes

- 72549952d1: Fixed handling of root scoped services in `startTestBackend`.
- e91e8e9c55: Increased test database max connection pool size to reduce the risk of resource exhaustion.
- Updated dependencies
  - @backstage/backend-app-api@0.2.2-next.0
  - @backstage/backend-plugin-api@0.1.3-next.0
  - @backstage/cli@0.20.0-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0

## @backstage/catalog-client@1.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/errors@1.1.2-next.0

## @backstage/catalog-model@1.1.2-next.0

### Patch Changes

- 6f3b8d0962: Defer `ajv` compilation of schema validators to improve module-import performance
- Updated dependencies
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/types@1.0.0

## @backstage/codemods@0.1.40-next.0

### Patch Changes

- Updated dependencies
  - @backstage/cli-common@0.1.10

## @backstage/config@1.0.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.0

## @backstage/config-loader@1.1.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/types@1.0.0

## @backstage/core-app-api@1.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1

## @backstage/core-components@0.11.2-next.0

### Patch Changes

- 882101cd9b: Deep-import LightAsync component to improve module-import speed
- Updated dependencies
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/version-bridge@1.0.1

## @backstage/core-plugin-api@1.0.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.0
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1

## @backstage/create-app@0.4.32-next.0

### Patch Changes

- 58c2264325: Newly created Backstage repositories now use the stable version 6 of
  `react-router`, just like the main repo does. Please let us know if you find any
  issues with this.

  Migrating to the stable version of `react-router` is optional for the time
  being. But if you want to do the same for your existing repository, please
  follow [this guide](https://backstage.io/docs/tutorials/react-router-stable-migration).

- e05e0f021b: Update versions of packages used in the create-app template, to match those in the main repo

- 52f25858a8: Added `*.session.sql` Visual Studio Code database functionality files to `.gitignore` in the default template. This is optional but potentially helpful if your developers use Visual Studio Code; you can add a line with that exact value to your own root `.gitignore` if you want the same.

- 6d00e80146: Updated the root `test` scripts to use `backstage-cli repo test`.

  To apply this change to an existing app, make the following change to the root `package.json`:

  ```diff
  -    "test": "backstage-cli test",
  -    "test:all": "lerna run test -- --coverage",
  +    "test": "backstage-cli repo test",
  +    "test:all": "backstage-cli repo test --coverage",
  ```

- Updated dependencies
  - @backstage/cli-common@0.1.10

## @backstage/dev-utils@1.0.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/test-utils@1.2.1-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/app-defaults@1.0.7-next.0
  - @backstage/integration-react@1.1.5-next.0
  - @backstage/core-app-api@1.1.1-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/errors@1.1.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.0

## @backstage/integration@1.3.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0

## @backstage/integration-react@1.1.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/theme@0.2.16

## @techdocs/cli@1.2.2-next.0

### Patch Changes

- 0b2a30dead: fixing techdocs-cli Docker client creation

  Docker client does not need to be created when --no-docker
  option is provided.

  If you had DOCKER_CERT_PATH environment variable defined
  the Docker client was looking for certificates
  and breaking techdocs-cli generate command even with --no-docker
  option.

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/plugin-techdocs-node@1.4.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.3-next.0

## @backstage/test-utils@1.2.1-next.0

### Patch Changes

- e05e0f021b: Align on the version of `@material-ui/icons` used, to `^4.9.1` like other packages in the main repo
- Updated dependencies
  - @backstage/config@1.0.3-next.0
  - @backstage/core-app-api@1.1.1-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/plugin-permission-common@0.6.5-next.0
  - @backstage/plugin-permission-react@0.4.6-next.0

## @backstage/plugin-adr@0.2.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-adr-common@0.2.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/integration-react@1.1.5-next.0
  - @backstage/plugin-search-react@1.1.1-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-adr-backend@0.2.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/plugin-adr-common@0.2.2-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-adr-common@0.2.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-airbrake@0.3.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/test-utils@1.2.1-next.0
  - @backstage/dev-utils@1.0.7-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-airbrake-backend@0.2.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0

## @backstage/plugin-allure@0.1.26-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-analytics-module-ga@0.1.21-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-apache-airflow@0.2.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/core-plugin-api@1.0.7-next.0

## @backstage/plugin-api-docs@0.8.10-next.0

### Patch Changes

- 3d5bb521ee: Updated dependency `@asyncapi/react-component` to `1.0.0-next.42`.
- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog@1.5.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-apollo-explorer@0.1.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-app-backend@0.3.37-next.0

### Patch Changes

- 11c9e0ad33: Added alpha plugin implementation for the new backend system. Available at `@backstage/plugin-app-backend/alpha`.
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.3-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/config-loader@1.1.5-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-auth-node@0.2.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0

## @backstage/plugin-azure-devops@0.2.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-azure-devops-backend@0.3.16-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-badges@0.2.34-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-badges-backend@0.1.31-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0

## @backstage/plugin-bazaar@0.1.25-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/cli@0.20.0-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/plugin-catalog@1.5.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0

## @backstage/plugin-bazaar-backend@0.1.21-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-test-utils@0.1.29-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0

## @backstage/plugin-bitrise@0.1.37-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog@1.5.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/integration-react@1.1.5-next.0
  - @backstage/plugin-search-react@1.1.1-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/plugin-catalog-common@1.0.7-next.0
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-catalog-backend@1.4.1-next.0

### Patch Changes

- 8cb6e10105: Fixed a bug where entities provided without a location key would always replace existing entities, rather than updating them.
- 63296ebcd4: Allow Placeholder value to be any value, not only string.
- 74022e0163: Make sure to stitch entities correctly after deletion, to ensure that their relations are updated.
- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/backend-plugin-api@0.1.3-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/plugin-catalog-node@1.1.1-next.0
  - @backstage/plugin-scaffolder-common@1.2.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/plugin-permission-node@0.6.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/types@1.0.0
  - @backstage/plugin-catalog-common@1.0.7-next.0
  - @backstage/plugin-permission-common@0.6.5-next.0
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-catalog-backend-module-aws@0.1.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/plugin-catalog-backend@1.4.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-azure@0.1.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/plugin-catalog-backend@1.4.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-bitbucket@0.2.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.0-next.0
  - @backstage/plugin-catalog-backend@1.4.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.1.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-bitbucket-cloud-common@0.2.0-next.0
  - @backstage/plugin-catalog-backend@1.4.1-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/integration@1.3.2-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.1.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/plugin-catalog-backend@1.4.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0

## @backstage/plugin-catalog-backend-module-gerrit@0.1.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/plugin-catalog-backend@1.4.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0

## @backstage/plugin-catalog-backend-module-github@0.1.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/backend-plugin-api@0.1.3-next.0
  - @backstage/plugin-catalog-backend@1.4.1-next.0
  - @backstage/plugin-catalog-node@1.1.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-gitlab@0.1.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/plugin-catalog-backend@1.4.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-ldap@0.5.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/plugin-catalog-backend@1.4.1-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-msgraph@0.4.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/plugin-catalog-backend@1.4.1-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/config@1.0.3-next.0

## @backstage/plugin-catalog-backend-module-openapi@0.1.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/plugin-catalog-backend@1.4.1-next.0
  - @backstage/plugin-catalog-node@1.1.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-common@1.0.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.6.5-next.0
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-catalog-graph@0.2.22-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog-graphql@0.3.14-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-import@0.8.13-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/integration-react@1.1.5-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0

## @backstage/plugin-catalog-node@1.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/backend-plugin-api@0.1.3-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-react@1.1.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1
  - @backstage/plugin-catalog-common@1.0.7-next.0
  - @backstage/plugin-permission-common@0.6.5-next.0
  - @backstage/plugin-permission-react@0.4.6-next.0

## @backstage/plugin-cicd-statistics@0.1.12-next.0

### Patch Changes

- e05e0f021b: Align on the version of `@material-ui/icons` used, to `^4.9.1` like other packages in the main repo
- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0

## @backstage/plugin-cicd-statistics-module-gitlab@0.1.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/plugin-cicd-statistics@0.1.12-next.0
  - @backstage/core-plugin-api@1.0.7-next.0

## @backstage/plugin-circleci@0.3.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-cloudbuild@0.3.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-code-climate@0.1.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage@0.2.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage-backend@0.2.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0

## @backstage/plugin-codescene@0.1.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-config-schema@0.1.33-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0

## @backstage/plugin-cost-insights@0.11.32-next.0

### Patch Changes

- a94c2ed1b7: Fixed bug in `CostOverviewBreakdownChart` component where some datasets caused the cost overview breakdown chart to tear.
- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-cost-insights-common@0.1.1

## @backstage/plugin-dynatrace@0.2.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-explore@0.3.41-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-explore-react@0.0.22-next.0

## @backstage/plugin-explore-react@0.0.22-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.0.7-next.0

## @backstage/plugin-firehydrant@0.1.27-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-fossa@0.2.42-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-gcalendar@0.3.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-gcp-projects@0.3.29-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-git-release-manager@0.3.23-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-github-actions@0.5.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-github-deployments@0.1.41-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/integration-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-github-issues@0.1.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-github-pull-requests-board@0.1.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-gitops-profiles@0.3.28-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-gocd@0.1.16-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-graphiql@0.2.42-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-graphql-backend@0.1.27-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-graphql@0.3.14-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0

## @backstage/plugin-home@0.4.26-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/plugin-stack-overflow@0.1.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-ilert@0.1.36-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-jenkins@0.7.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-jenkins-common@0.1.9-next.0

## @backstage/plugin-jenkins-backend@0.1.27-next.0

### Patch Changes

- b19ea927af: Fixed a bug where `extraRequestHeaders` configuration was ignored.
- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/plugin-auth-node@0.2.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/plugin-jenkins-common@0.1.9-next.0
  - @backstage/plugin-permission-common@0.6.5-next.0

## @backstage/plugin-jenkins-common@0.1.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-common@1.0.7-next.0
  - @backstage/plugin-permission-common@0.6.5-next.0

## @backstage/plugin-kafka@0.3.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-kafka-backend@0.2.30-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0

## @backstage/plugin-kubernetes@0.7.3-next.0

### Patch Changes

- 51af8361de: Add useCustomResources react hook for fetching Kubernetes Custom Resources
- 35a6cfe257: Fix infinite call bug in `useCustomResources` hook
- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/plugin-kubernetes-common@0.4.3-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-kubernetes-backend@0.7.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/plugin-kubernetes-common@0.4.3-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/plugin-auth-node@0.2.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0

## @backstage/plugin-kubernetes-common@0.4.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0

## @backstage/plugin-lighthouse@0.3.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic@0.3.28-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic-dashboard@0.2.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0

## @backstage/plugin-org@0.5.10-next.0

### Patch Changes

- f2b4b55636: consistently show parent and child relations in group profile card
- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-pagerduty@0.5.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop@0.1.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop-backend@0.1.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0

## @backstage/plugin-permission-backend@0.5.12-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/plugin-auth-node@0.2.6-next.0
  - @backstage/plugin-permission-node@0.6.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/plugin-permission-common@0.6.5-next.0

## @backstage/plugin-permission-common@0.6.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0

## @backstage/plugin-permission-node@0.6.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/plugin-auth-node@0.2.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/plugin-permission-common@0.6.5-next.0

## @backstage/plugin-permission-react@0.4.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/plugin-permission-common@0.6.5-next.0

## @backstage/plugin-playlist@0.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/plugin-search-react@1.1.1-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.7-next.0
  - @backstage/plugin-permission-common@0.6.5-next.0
  - @backstage/plugin-permission-react@0.4.6-next.0
  - @backstage/plugin-playlist-common@0.1.1-next.0

## @backstage/plugin-playlist-backend@0.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/backend-test-utils@0.1.29-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/plugin-auth-node@0.2.6-next.0
  - @backstage/plugin-permission-node@0.6.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/plugin-permission-common@0.6.5-next.0
  - @backstage/plugin-playlist-common@0.1.1-next.0

## @backstage/plugin-playlist-common@0.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.6.5-next.0

## @backstage/plugin-proxy-backend@0.2.31-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0

## @backstage/plugin-rollbar@0.4.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-rollbar-backend@0.1.34-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.2.12-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.7.0-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-scaffolder-backend-module-rails@0.4.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.7.0-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-scaffolder-backend-module-yeoman@0.2.10-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.7.0-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-scaffolder-common@1.2.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-search@1.0.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/plugin-search-react@1.1.1-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-search-backend@1.0.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/plugin-auth-node@0.2.6-next.0
  - @backstage/plugin-permission-node@0.6.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/types@1.0.0
  - @backstage/plugin-permission-common@0.6.5-next.0
  - @backstage/plugin-search-backend-node@1.0.3-next.0
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-search-backend-module-elasticsearch@1.0.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.0
  - @backstage/plugin-search-backend-node@1.0.3-next.0
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-search-backend-module-pg@0.4.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/plugin-search-backend-node@1.0.3-next.0
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-search-backend-node@1.0.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/plugin-permission-common@0.6.5-next.0
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-search-common@1.0.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.0
  - @backstage/plugin-permission-common@0.6.5-next.0

## @backstage/plugin-search-react@1.1.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-sentry@0.4.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-shortcuts@0.3.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0

## @backstage/plugin-sonarqube@0.4.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-sonarqube-backend@0.1.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0

## @backstage/plugin-splunk-on-call@0.3.34-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-stack-overflow@0.1.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-home@0.4.26-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-stack-overflow-backend@0.1.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.20.0-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-tech-insights@0.3.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/plugin-tech-insights-common@0.2.7-next.0

## @backstage/plugin-tech-insights-backend@0.5.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/plugin-tech-insights-node@0.3.5-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/plugin-tech-insights-common@0.2.7-next.0

## @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.21-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-tech-insights-node@0.3.5-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/plugin-tech-insights-common@0.2.7-next.0

## @backstage/plugin-tech-insights-common@0.2.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.0

## @backstage/plugin-tech-insights-node@0.3.5-next.0

### Patch Changes

- 0963b4d5fb: Updated package role to be `node-library`.
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/types@1.0.0
  - @backstage/plugin-tech-insights-common@0.2.7-next.0

## @backstage/plugin-tech-radar@0.5.17-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs@1.3.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/plugin-techdocs-react@1.0.5-next.0
  - @backstage/integration-react@1.1.5-next.0
  - @backstage/plugin-search-react@1.1.1-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-techdocs-addons-test-utils@1.0.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/test-utils@1.2.1-next.0
  - @backstage/plugin-catalog@1.5.2-next.0
  - @backstage/plugin-techdocs@1.3.3-next.0
  - @backstage/plugin-techdocs-react@1.0.5-next.0
  - @backstage/integration-react@1.1.5-next.0
  - @backstage/plugin-search-react@1.1.1-next.0
  - @backstage/core-app-api@1.1.1-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-backend@1.3.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/plugin-techdocs-node@1.4.1-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/plugin-catalog-common@1.0.7-next.0
  - @backstage/plugin-permission-common@0.6.5-next.0
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-techdocs-module-addons-contrib@1.0.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-techdocs-react@1.0.5-next.0
  - @backstage/integration-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-node@1.4.1-next.0

### Patch Changes

- 0b2a30dead: fixing techdocs-cli Docker client creation

  Docker client does not need to be created when --no-docker
  option is provided.

  If you had DOCKER_CERT_PATH environment variable defined
  the Docker client was looking for certificates
  and breaking techdocs-cli generate command even with --no-docker
  option.

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/plugin-search-common@1.0.2-next.0

## @backstage/plugin-techdocs-react@1.0.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/version-bridge@1.0.1

## @backstage/plugin-todo@0.2.12-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-todo-backend@0.1.34-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/integration@1.3.2-next.0

## @backstage/plugin-user-settings-backend@0.1.1-next.0

### Patch Changes

- 82ac9bcfe5: Fix wrong import statement in `README.md`.
- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/plugin-auth-node@0.2.6-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/types@1.0.0

## @backstage/plugin-vault@0.1.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16

## @backstage/plugin-vault-backend@0.2.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-test-utils@0.1.29-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0

## @backstage/plugin-xcmetrics@0.2.30-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/errors@1.1.2-next.0
  - @backstage/theme@0.2.16

## example-app@0.2.76-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-org@0.5.10-next.0
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/cli@0.20.0-next.0
  - @backstage/plugin-kubernetes@0.7.3-next.0
  - @backstage/plugin-scaffolder@1.7.0-next.0
  - @backstage/plugin-api-docs@0.8.10-next.0
  - @backstage/plugin-user-settings@0.5.0-next.0
  - @backstage/plugin-cost-insights@0.11.32-next.0
  - @backstage/plugin-airbrake@0.3.10-next.0
  - @backstage/plugin-azure-devops@0.2.1-next.0
  - @backstage/plugin-badges@0.2.34-next.0
  - @backstage/plugin-catalog-graph@0.2.22-next.0
  - @backstage/plugin-catalog-import@0.8.13-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0
  - @backstage/plugin-circleci@0.3.10-next.0
  - @backstage/plugin-cloudbuild@0.3.10-next.0
  - @backstage/plugin-code-coverage@0.2.3-next.0
  - @backstage/plugin-dynatrace@0.2.1-next.0
  - @backstage/plugin-explore@0.3.41-next.0
  - @backstage/plugin-github-actions@0.5.10-next.0
  - @backstage/plugin-gocd@0.1.16-next.0
  - @backstage/plugin-home@0.4.26-next.0
  - @backstage/plugin-jenkins@0.7.9-next.0
  - @backstage/plugin-kafka@0.3.10-next.0
  - @backstage/plugin-lighthouse@0.3.10-next.0
  - @backstage/plugin-newrelic-dashboard@0.2.3-next.0
  - @backstage/plugin-pagerduty@0.5.3-next.0
  - @backstage/plugin-playlist@0.1.1-next.0
  - @backstage/plugin-rollbar@0.4.10-next.0
  - @backstage/plugin-search@1.0.3-next.0
  - @backstage/plugin-sentry@0.4.3-next.0
  - @backstage/plugin-tech-insights@0.3.1-next.0
  - @backstage/plugin-techdocs@1.3.3-next.0
  - @backstage/plugin-techdocs-react@1.0.5-next.0
  - @backstage/plugin-todo@0.2.12-next.0
  - @backstage/app-defaults@1.0.7-next.0
  - @backstage/integration-react@1.1.5-next.0
  - @backstage/plugin-apache-airflow@0.2.3-next.0
  - @backstage/plugin-gcalendar@0.3.6-next.0
  - @backstage/plugin-gcp-projects@0.3.29-next.0
  - @backstage/plugin-graphiql@0.2.42-next.0
  - @backstage/plugin-newrelic@0.3.28-next.0
  - @backstage/plugin-search-react@1.1.1-next.0
  - @backstage/plugin-shortcuts@0.3.2-next.0
  - @backstage/plugin-stack-overflow@0.1.6-next.0
  - @backstage/plugin-tech-radar@0.5.17-next.0
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.5-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-app-api@1.1.1-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.7-next.0
  - @backstage/plugin-permission-react@0.4.6-next.0
  - @backstage/plugin-search-common@1.0.2-next.0
  - @internal/plugin-catalog-customized@0.0.3-next.0

## example-backend@0.2.76-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/plugin-scaffolder-backend@1.7.0-next.0
  - @backstage/plugin-auth-backend@0.17.0-next.0
  - @backstage/plugin-catalog-backend@1.4.1-next.0
  - @backstage/plugin-jenkins-backend@0.1.27-next.0
  - @backstage/plugin-app-backend@0.3.37-next.0
  - @backstage/plugin-tech-insights-node@0.3.5-next.0
  - example-app@0.2.76-next.0
  - @backstage/catalog-client@1.1.1-next.0
  - @backstage/plugin-badges-backend@0.1.31-next.0
  - @backstage/plugin-code-coverage-backend@0.2.3-next.0
  - @backstage/plugin-kafka-backend@0.2.30-next.0
  - @backstage/plugin-kubernetes-backend@0.7.3-next.0
  - @backstage/plugin-playlist-backend@0.1.1-next.0
  - @backstage/plugin-tech-insights-backend@0.5.3-next.0
  - @backstage/plugin-techdocs-backend@1.3.1-next.0
  - @backstage/plugin-todo-backend@0.1.34-next.0
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/backend-tasks@0.3.6-next.0
  - @backstage/plugin-auth-node@0.2.6-next.0
  - @backstage/plugin-permission-node@0.6.6-next.0
  - @backstage/plugin-rollbar-backend@0.1.34-next.0
  - @backstage/plugin-search-backend-module-pg@0.4.1-next.0
  - @backstage/plugin-scaffolder-backend-module-rails@0.4.5-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/integration@1.3.2-next.0
  - @backstage/plugin-azure-devops-backend@0.3.16-next.0
  - @backstage/plugin-graphql-backend@0.1.27-next.0
  - @backstage/plugin-permission-backend@0.5.12-next.0
  - @backstage/plugin-permission-common@0.6.5-next.0
  - @backstage/plugin-proxy-backend@0.2.31-next.0
  - @backstage/plugin-search-backend@1.0.3-next.0
  - @backstage/plugin-search-backend-module-elasticsearch@1.0.3-next.0
  - @backstage/plugin-search-backend-node@1.0.3-next.0
  - @backstage/plugin-search-common@1.0.2-next.0
  - @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.21-next.0

## example-backend-next@0.0.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.7.0-next.0
  - @backstage/backend-defaults@0.1.2-next.0
  - @backstage/plugin-catalog-backend@1.4.1-next.0
  - @backstage/plugin-app-backend@0.3.37-next.0

## techdocs-cli-embedded-app@0.2.75-next.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.0
  - @backstage/core-components@0.11.2-next.0
  - @backstage/cli@0.20.0-next.0
  - @backstage/test-utils@1.2.1-next.0
  - @backstage/plugin-catalog@1.5.2-next.0
  - @backstage/plugin-techdocs@1.3.3-next.0
  - @backstage/plugin-techdocs-react@1.0.5-next.0
  - @backstage/app-defaults@1.0.7-next.0
  - @backstage/integration-react@1.1.5-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/core-app-api@1.1.1-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @internal/plugin-catalog-customized@0.0.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.5.2-next.0
  - @backstage/plugin-catalog-react@1.1.5-next.0

## @internal/plugin-todo-list@1.0.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.0
  - @backstage/core-plugin-api@1.0.7-next.0
  - @backstage/theme@0.2.16

## @internal/plugin-todo-list-backend@1.0.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.0
  - @backstage/plugin-auth-node@0.2.6-next.0
  - @backstage/config@1.0.3-next.0
  - @backstage/errors@1.1.2-next.0

## @internal/plugin-todo-list-common@1.0.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.6.5-next.0
