# Release v1.37.0-next.0

Upgrade Helper: [https://backstage.github.io/upgrade-helper/?to=1.37.0-next.0](https://backstage.github.io/upgrade-helper/?to=1.37.0-next.0)

## @backstage/canon@0.2.0-next.0

### Minor Changes

- 5a5db29: Fix CSS imports and move CSS outputs out of the dist folder.

## @backstage/plugin-catalog@1.28.0-next.0

### Minor Changes

- a3d93ca: The `Overview` entity content now supports custom cards grid layouts.

### Patch Changes

- ba9649a: Update the default entity page extension component to support grouping multiple entity content items in the same tab.

  Disable all default groups:

  ```diff
  # app-config.yaml
  app:
    extensions:
      # Pages
  +   - page:catalog/entity:
  +       config:
  +         groups: []
  ```

  Create a custom list of :

  ```diff
  # app-config.yaml
  app:
    extensions:
      # Pages
  +   - page:catalog/entity:
  +       config:
  +         groups:
  +           # This array of groups completely replaces the default groups
  +           - custom:
  +               title: 'Custom'
  ```

- Updated dependencies
  - @backstage/plugin-search-react@1.8.7-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/core-compat-api@0.3.7-next.0
  - @backstage/integration-react@1.2.4

## @backstage/plugin-catalog-react@1.16.0-next.0

### Minor Changes

- ba9649a: Add a new `defaultGroup` parameter to the `EntityContentBlueprint`, here are usage examples:

  Set a default group while creating the extension:

  ```diff
  const entityKubernetesContent = EntityContentBlueprint.make({
    name: 'kubernetes',
    params: {
      defaultPath: '/kubernetes',
      defaultTitle: 'Kubernetes',
  +   defaultGroup: 'deployment',
      filter: 'kind:component,resource',
      loader: () =>
        import('./KubernetesContentPage').then(m =>
          compatWrapper(<m.KubernetesContentPage />),
        ),
    },
  });
  ```

  Disassociate an entity content from a default group:

  ```diff
  # app-config.yaml
  app:
    extensions:
      # Entity page content
  -   - entity-content:kubernetes/kubernetes
  +   - entity-content:kubernetes/kubernetes:
  +       config:
  +         group: false
  ```

  Associate an entity content with a different default or custom group than the one defined in code when the extension was created:

  ```diff
  # app-config.yaml
  app:
    extensions:
      # Entity page content
  -   - entity-content:kubernetes/kubernetes
  +   - entity-content:kubernetes/kubernetes:
  +       config:
  +         group: custom # associating this extension with a custom group id, the group should have previously been created via entity page configuration

  ```

- a3d93ca: Introduces a new `EntityContentLayoutBlueprint` that creates custom entity content layouts.

  The layout components receive card elements and can render them as they see fit. Cards is an array of objects with the following properties:

  - element: `JSx.Element`;
  - type: `"peek" | "info" | "full" | undefined`;

  ### Usage example

  Creating a custom overview tab layout:

  ```tsx
  import {
    EntityContentLayoutProps,
    EntityContentLayoutBlueprint,
  } from '@backstage/plugin-catalog-react/alpha';
  // ...

  function StickyEntityContentOverviewLayout(props: EntityContentLayoutProps) {
    const { cards } = props;
    const classes = useStyles();
    return (
      <Grid container spacing={3}>
        <Grid
          className={classes.infoArea}
          xs={12}
          md={4}
          item
        >
          <Grid container spacing={3}>
            {cards
              .filter(card => card.type === 'info')
              .map((card, index) => (
                <Grid key={index} xs={12} item>
                  {card.element}
                </Grid>
              ))}
          </Grid>
        </Grid>
        <Grid xs={12} md={8} item>
          <Grid container spacing={3}>
            {cards
              .filter(card => card.type === 'peek')
              .map((card, index) => (
                <Grid key={index} className={classes.card} xs={12} md={6} item>
                  {card.element}
                </Grid>
              ))}
            {cards
              .filter(card => !card.type || card.type === 'full')
              .map((card, index) => (
                <Grid key={index} className={classes.card} xs={12} md={6} item>
                  {card.element}
                </Grid>
              ))}
          </Grid>
        </Grid>
      </Grid>
    );
  }

  export const customEntityContentOverviewStickyLayoutModule = createFrontendModule({
    pluginId: 'app',
    extensions: [
      EntityContentLayoutBlueprint.make({
        name: 'sticky',
        params: {
          // (optional) defaults the `() => false` filter function
          defaultFilter: 'kind:template'
          loader: async () => StickyEntityContentOverviewLayout,
        },
      }),
    ],
  ```

  Disabling the custom layout:

  ```yaml
  # app-config.yaml
  app:
    extensions:
      - entity-content-layout:app/sticky: false
  ```

  Overriding the custom layout filter:

  ```yaml
  # app-config.yaml
  app:
    extensions:
      - entity-content-layout:app/sticky:
          config:
            # This layout will be used only with component entities
            filter: 'kind:component'
  ```

- d78bb71: Added `hidden` prop to `EntityTagPicker`, `EntityAutocompletePicker` and `UserListPicker`.
  Added `initialFilter` prop to `EntityTagPicker` to set an initial filter for the picker.
  Added `alwaysKeepFilters` prop to `UserListPicker` to prevent filters from resetting when no entities match the initial filters.

- a3d93ca: Add an optional `type` parameter to `EntityCard` extensions. A card's type determines characteristics such as its expected size and where it will be rendered by the entity content layout.

  Initially the following three types are supported:

  - `peek`: small vertical cards that provide information at a glance, for example recent builds, deployments, and service health.
  - `info`: medium size cards with high priority and frequently used information such as common actions, entity metadata, and links.
  - `full`: Large cards that are more feature rich with more information, typically used by plugins that don't quite need the full content view and want to show a card instead.

  ### Usage examples

  Defining a default type when creating a card:

  ```diff
  const myCard = EntityCardBlueprint.make({
    name: 'myCard',
    params: {
  +   type: 'info',
      loader: import('./MyCard).then(m => { default: m.MyCard }),
    },
  });
  ```

  Changing the card type via `app-config.yaml` file:

  ```diff
  app:
    extensions:
  +   - entity-card:myPlugin/myCard:
  +       config:
  +         type: info
  ```

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/core-compat-api@0.3.7-next.0
  - @backstage/frontend-test-utils@0.2.7-next.0
  - @backstage/integration-react@1.2.4

## @backstage/plugin-scaffolder@1.29.0-next.0

### Minor Changes

- 9d864ff: Allowed passing `ui:disabled` for disabling the input field of all the pickers.

### Patch Changes

- 6a3fa48: Fixes DryRunContext not forwarding the correct Scaffolder Secrets to the DryRun API
- b3b7c9c: Updated the alpha `page:scaffolder` extension to accept `formFields` input, matching the updated `FormFieldBlueprint`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/plugin-scaffolder-react@1.14.6-next.0
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/core-compat-api@0.3.7-next.0
  - @backstage/integration-react@1.2.4

## @backstage/backend-app-api@1.2.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.9-next.0
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/config-loader@1.9.6

## @backstage/backend-defaults@0.8.2-next.0

### Patch Changes

- e293b66: The default auditor service implementation will now log low severity events with `debug` level instead of `info`.
- ecb9bab: Explicitly stringify extra logger fields with `JSON.stringify` to prevent `[object Object]` errors.
- Updated dependencies
  - @backstage/plugin-permission-node@0.8.9-next.0
  - @backstage/backend-app-api@1.2.1-next.0
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/cli-node@0.2.13
  - @backstage/config-loader@1.9.6
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/backend-dynamic-feature-service@0.6.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.0
  - @backstage/plugin-events-backend@0.4.4-next.0
  - @backstage/plugin-permission-node@0.8.9-next.0
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/plugin-catalog-backend@1.31.1-next.0
  - @backstage/plugin-search-backend-node@1.3.9-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/cli-node@0.2.13
  - @backstage/config-loader@1.9.6
  - @backstage/plugin-events-node@0.4.9-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0
  - @backstage/plugin-app-node@0.1.31-next.0

## @backstage/backend-openapi-utils@0.5.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/backend-plugin-api@1.2.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.9-next.0
  - @backstage/plugin-auth-node@0.6.1-next.0

## @backstage/backend-test-utils@1.3.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.0
  - @backstage/backend-app-api@1.2.1-next.0
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/core-compat-api@0.3.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.6-next.0

## @backstage/create-app@0.5.26-next.0

### Patch Changes

- Bumped create-app version.

## @backstage/dev-utils@1.1.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/integration-react@1.2.4

## @backstage/frontend-app-api@0.10.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/frontend-defaults@0.1.7-next.0

## @backstage/frontend-defaults@0.1.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/frontend-app-api@0.10.6-next.0
  - @backstage/plugin-app@0.1.7-next.0

## @backstage/frontend-plugin-api@0.9.6-next.0

### Patch Changes

- de72253: Added a new `ExtensionBoundary.lazyComponent` helper in addition to the existing `ExtensionBoundary.lazy` helper.

## @backstage/frontend-test-utils@0.2.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/frontend-app-api@0.10.6-next.0
  - @backstage/plugin-app@0.1.7-next.0

## @backstage/repo-tools@0.13.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/cli-node@0.2.13
  - @backstage/config-loader@1.9.6

## @techdocs/cli@1.9.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.0
  - @backstage/plugin-techdocs-node@1.13.1-next.0

## @backstage/plugin-api-docs@0.12.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/plugin-catalog@1.28.0-next.0
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/core-compat-api@0.3.7-next.0

## @backstage/plugin-app@0.1.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/integration-react@1.2.4

## @backstage/plugin-app-backend@0.4.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/config-loader@1.9.6
  - @backstage/plugin-app-node@0.1.31-next.0

## @backstage/plugin-app-node@0.1.31-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/config-loader@1.9.6

## @backstage/plugin-app-visualizer@0.1.17-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.6-next.0

## @backstage/plugin-auth-backend@0.24.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-backend-module-microsoft-provider@0.3.1-next.0
  - @backstage/plugin-auth-backend-module-atlassian-provider@0.4.1-next.0
  - @backstage/plugin-auth-backend-module-auth0-provider@0.2.1-next.0
  - @backstage/plugin-auth-backend-module-bitbucket-provider@0.3.1-next.0
  - @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.2.1-next.0
  - @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.4.1-next.0
  - @backstage/plugin-auth-backend-module-github-provider@0.3.1-next.0
  - @backstage/plugin-auth-backend-module-gitlab-provider@0.3.1-next.0
  - @backstage/plugin-auth-backend-module-oauth2-provider@0.4.1-next.0
  - @backstage/plugin-auth-backend-module-oidc-provider@0.4.1-next.0
  - @backstage/plugin-auth-backend-module-okta-provider@0.2.1-next.0
  - @backstage/plugin-auth-backend-module-onelogin-provider@0.3.1-next.0
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0
  - @backstage/plugin-auth-backend-module-aws-alb-provider@0.4.1-next.0
  - @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.6-next.0
  - @backstage/plugin-auth-backend-module-gcp-iap-provider@0.4.1-next.0
  - @backstage/plugin-auth-backend-module-google-provider@0.3.1-next.0
  - @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.6-next.0

## @backstage/plugin-auth-backend-module-atlassian-provider@0.4.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-auth0-provider@0.2.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-aws-alb-provider@0.4.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-backend@0.24.4-next.0
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-bitbucket-provider@0.3.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.2.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.4.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-gcp-iap-provider@0.4.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-github-provider@0.3.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-gitlab-provider@0.3.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-google-provider@0.3.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-guest-provider@0.2.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-microsoft-provider@0.3.1-next.0

### Patch Changes

- fa15e80: Update `auth.microsoft.signIn.resolvers` config def to include the `userIdMatchingUserEntityAnnotation` resolver.
- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-oauth2-provider@0.4.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-oidc-provider@0.4.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-backend@0.24.4-next.0
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-okta-provider@0.2.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-onelogin-provider@0.3.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-pinniped-provider@0.3.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-backend-module-vmware-cloud-provider@0.5.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-auth-node@0.6.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-catalog-backend@1.31.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.9-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0
  - @backstage/plugin-search-backend-module-catalog@0.3.2-next.0
  - @backstage/backend-openapi-utils@0.5.1-next.0

## @backstage/plugin-catalog-backend-module-aws@0.4.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.0
  - @backstage/plugin-kubernetes-common@0.9.4-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-azure@0.3.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-backstage-openapi@0.4.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0
  - @backstage/backend-openapi-utils@0.5.1-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.4.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.3.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-gcp@0.3.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-kubernetes-common@0.9.4-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-gerrit@0.2.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-github@0.7.11-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.31.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/plugin-catalog-backend-module-github-org@0.3.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0
  - @backstage/plugin-catalog-backend-module-github@0.7.11-next.0
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/plugin-catalog-backend-module-gitlab@0.6.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/plugin-catalog-backend-module-gitlab-org@0.2.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend-module-gitlab@0.6.4-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/plugin-catalog-backend-module-incremental-ingestion@0.6.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.0
  - @backstage/plugin-catalog-backend@1.31.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/plugin-catalog-backend-module-ldap@0.11.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-logs@0.1.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.31.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/plugin-catalog-backend-module-msgraph@0.6.8-next.0

### Patch Changes

- 612d1fd: Update `catalog.providers.microsoftGraphOrg.target` config def to be optional as this has a default value.
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-openapi@0.2.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-puppetdb@0.2.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0

## @backstage/plugin-catalog-backend-module-unprocessed@0.5.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0

## @backstage/plugin-catalog-graph@0.4.17-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/core-compat-api@0.3.7-next.0

## @backstage/plugin-catalog-import@0.12.11-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/core-compat-api@0.3.7-next.0
  - @backstage/integration-react@1.2.4

## @backstage/plugin-catalog-node@1.16.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.9-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-catalog-unprocessed-entities@0.2.15-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/core-compat-api@0.3.7-next.0

## @backstage/plugin-devtools@0.1.25-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/core-compat-api@0.3.7-next.0

## @backstage/plugin-devtools-backend@0.5.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.0
  - @backstage/plugin-permission-node@0.8.9-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/config-loader@1.9.6

## @backstage/plugin-events-backend@0.4.4-next.0

### Patch Changes

- 2f4d3bc: Allow webhook content to be 5mb instead the default 100kb
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0
  - @backstage/backend-openapi-utils@0.5.1-next.0

## @backstage/plugin-events-backend-module-aws-sqs@0.4.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/plugin-events-backend-module-azure@0.2.18-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/plugin-events-backend-module-bitbucket-cloud@0.2.18-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/plugin-events-backend-module-gerrit@0.2.18-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/plugin-events-backend-module-github@0.2.18-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/plugin-events-backend-module-gitlab@0.2.18-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/plugin-events-backend-test-utils@0.1.42-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/plugin-events-node@0.4.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-home@0.8.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/core-compat-api@0.3.7-next.0

## @backstage/plugin-kubernetes@0.12.5-next.0

### Patch Changes

- d517d13: refactor: use `KUBERNETES_ANNOTATION` and `KUBERNETES_LABEL_SELECTOR_QUERY_ANNOTATION` annotations from `kubernetes-common`
- ba9649a: Set deployment as the default group of Kubernetes entity content. It is just an example and shouldn't cause any visual difference since entity page tabs with just one entity content appear as normal tabs.
- Updated dependencies
  - @backstage/plugin-kubernetes-common@0.9.4-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/plugin-kubernetes-react@0.5.5-next.0
  - @backstage/core-compat-api@0.3.7-next.0

## @backstage/plugin-kubernetes-backend@0.19.4-next.0

### Patch Changes

- d517d13: refactor: use `KUBERNETES_ANNOTATION` and `KUBERNETES_LABEL_SELECTOR_QUERY_ANNOTATION` annotations from `kubernetes-common`
- Updated dependencies
  - @backstage/plugin-kubernetes-common@0.9.4-next.0
  - @backstage/plugin-permission-node@0.8.9-next.0
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/plugin-kubernetes-node@0.2.4-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0

## @backstage/plugin-kubernetes-cluster@0.0.23-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-kubernetes-common@0.9.4-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/plugin-kubernetes-react@0.5.5-next.0

## @backstage/plugin-kubernetes-common@0.9.4-next.0

### Patch Changes

- d517d13: Export `backstage.io/kubernetes-id` and `backstage.io/kubernetes-label-selector` annotations as constants

## @backstage/plugin-kubernetes-node@0.2.4-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-kubernetes-common@0.9.4-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-kubernetes-react@0.5.5-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-kubernetes-common@0.9.4-next.0

## @backstage/plugin-notifications@0.5.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/core-compat-api@0.3.7-next.0

## @backstage/plugin-notifications-backend@0.5.4-next.0

### Patch Changes

- 20e8d12: Fix null topics being returned from notification API
- 425a61d: Improved notifications router tests
- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0
  - @backstage/plugin-notifications-node@0.2.13-next.0
  - @backstage/plugin-signals-node@0.1.18-next.0

## @backstage/plugin-notifications-backend-module-email@0.3.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0
  - @backstage/plugin-notifications-node@0.2.13-next.0

## @backstage/plugin-notifications-node@0.2.13-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-signals-node@0.1.18-next.0

## @backstage/plugin-org@0.6.37-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/core-compat-api@0.3.7-next.0

## @backstage/plugin-org-react@0.1.36-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.16.0-next.0

## @backstage/plugin-permission-backend@0.5.55-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.9-next.0
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-permission-backend-module-allow-all-policy@0.2.6-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.9-next.0
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-permission-node@0.8.9-next.0

### Patch Changes

- 728e3e1: Improved type inference when passing a `PermissionResourceRef` to `createPermissionRule`.
- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-proxy-backend@0.5.12-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-proxy-node@0.1.2-next.0

## @backstage/plugin-proxy-node@0.1.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-scaffolder-backend@1.30.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.0
  - @backstage/plugin-permission-node@0.8.9-next.0
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.6-next.0
  - @backstage/plugin-events-node@0.4.9-next.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.8-next.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.7-next.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.7-next.0
  - @backstage/plugin-scaffolder-backend-module-gerrit@0.2.7-next.0
  - @backstage/plugin-scaffolder-backend-module-gitea@0.2.7-next.0
  - @backstage/plugin-scaffolder-backend-module-github@0.6.1-next.0
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.8.1-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0
  - @backstage/plugin-scaffolder-backend-module-azure@0.2.7-next.0

## @backstage/plugin-scaffolder-backend-module-azure@0.2.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0

## @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.7-next.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.7-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0

## @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0

## @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0

## @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.3.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0

## @backstage/plugin-scaffolder-backend-module-gcp@0.2.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0

## @backstage/plugin-scaffolder-backend-module-gerrit@0.2.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0

## @backstage/plugin-scaffolder-backend-module-gitea@0.2.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0

## @backstage/plugin-scaffolder-backend-module-github@0.6.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0

## @backstage/plugin-scaffolder-backend-module-gitlab@0.8.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0

## @backstage/plugin-scaffolder-backend-module-notifications@0.1.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-notifications-node@0.2.13-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0

## @backstage/plugin-scaffolder-backend-module-rails@0.5.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0

## @backstage/plugin-scaffolder-backend-module-sentry@0.2.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0

## @backstage/plugin-scaffolder-backend-module-yeoman@0.4.8-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0
  - @backstage/plugin-scaffolder-node-test-utils@0.1.20-next.0

## @backstage/plugin-scaffolder-node@0.7.1-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-scaffolder-node-test-utils@0.1.20-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-test-utils@1.3.1-next.0
  - @backstage/plugin-scaffolder-node@0.7.1-next.0

## @backstage/plugin-scaffolder-react@1.14.6-next.0

### Patch Changes

- b3b7c9c: Deprecated the alpha `ScaffolderFormFieldsApi` and `formFieldsApiRef` as these are being replaced with a different solution.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/frontend-plugin-api@0.9.6-next.0

## @backstage/plugin-search@1.4.24-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-react@1.8.7-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/core-compat-api@0.3.7-next.0

## @backstage/plugin-search-backend@1.8.3-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.0
  - @backstage/plugin-permission-node@0.8.9-next.0
  - @backstage/plugin-search-backend-node@1.3.9-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/backend-openapi-utils@0.5.1-next.0

## @backstage/plugin-search-backend-module-catalog@0.3.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-backend-node@1.3.9-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0

## @backstage/plugin-search-backend-module-elasticsearch@1.6.6-next.0

### Patch Changes

- 37e5386: Allow `indexPrefix` configuration through the `app-config.yaml`
- Updated dependencies
  - @backstage/plugin-search-backend-node@1.3.9-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-search-backend-module-explore@0.2.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-backend-node@1.3.9-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-search-backend-module-pg@0.5.42-next.0

### Patch Changes

- 8155b04: Enable normalization in postgres query to change the behavior of the search.
- Updated dependencies
  - @backstage/plugin-search-backend-node@1.3.9-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-search-backend-module-stack-overflow-collator@0.3.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-backend-node@1.3.9-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-search-backend-module-techdocs@0.3.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs-node@1.13.1-next.0
  - @backstage/plugin-search-backend-node@1.3.9-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0

## @backstage/plugin-search-backend-node@1.3.9-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-search-react@1.8.7-next.0

### Patch Changes

- c37e480: Capture the number of search results in the search analytics event that correspond to the term entered.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.6-next.0

## @backstage/plugin-signals@0.0.17-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.6-next.0

## @backstage/plugin-signals-backend@0.3.2-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0
  - @backstage/plugin-signals-node@0.1.18-next.0

## @backstage/plugin-signals-node@0.1.18-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-events-node@0.4.9-next.0

## @backstage/plugin-techdocs@1.12.4-next.0

### Patch Changes

- 065e6b9: Excludes svg styling from sanitization
- b5a8208: Added `TechDocsAddonsBlueprint` extension to allow adding of techdocs addons.
- ed1cb3e: Adds the page name of techdoc to the document's title.
- fe4abb8: Updates logic to check for SVG sources when inlining svgs.
- Updated dependencies
  - @backstage/plugin-search-react@1.8.7-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/plugin-techdocs-react@1.2.15-next.0
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/core-compat-api@0.3.7-next.0
  - @backstage/integration-react@1.2.4

## @backstage/plugin-techdocs-addons-test-utils@1.0.46-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-react@1.8.7-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/plugin-catalog@1.28.0-next.0
  - @backstage/plugin-techdocs@1.12.4-next.0
  - @backstage/plugin-techdocs-react@1.2.15-next.0
  - @backstage/integration-react@1.2.4

## @backstage/plugin-techdocs-backend@1.11.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs-node@1.13.1-next.0
  - @backstage/plugin-search-backend-module-techdocs@0.3.7-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0

## @backstage/plugin-techdocs-module-addons-contrib@1.1.22-next.0

### Patch Changes

- b5a8208: Added `TechDocsAddonsBlueprint` extension to allow adding of techdocs addons.
- Updated dependencies
  - @backstage/plugin-techdocs-react@1.2.15-next.0
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/integration-react@1.2.4

## @backstage/plugin-techdocs-node@1.13.1-next.0

### Patch Changes

- 052a10a: Bumps the version of the techdocs generator container used.
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0

## @backstage/plugin-techdocs-react@1.2.15-next.0

### Patch Changes

- b5a8208: Added `TechDocsAddonsBlueprint` extension to allow adding of techdocs addons.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.6-next.0

## @backstage/plugin-user-settings@0.8.20-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/core-compat-api@0.3.7-next.0

## @backstage/plugin-user-settings-backend@0.2.31-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.0
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-signals-node@0.1.18-next.0

## example-app@0.2.107-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-react@1.8.7-next.0
  - @backstage/plugin-kubernetes@0.12.5-next.0
  - @backstage/plugin-scaffolder@1.29.0-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/plugin-scaffolder-react@1.14.6-next.0
  - @backstage/plugin-catalog@1.28.0-next.0
  - @backstage/plugin-techdocs@1.12.4-next.0
  - @backstage/plugin-techdocs-react@1.2.15-next.0
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.22-next.0
  - @backstage/plugin-search@1.4.24-next.0
  - @backstage/plugin-kubernetes-cluster@0.0.23-next.0
  - @backstage/plugin-api-docs@0.12.5-next.0
  - @backstage/plugin-catalog-graph@0.4.17-next.0
  - @backstage/plugin-catalog-import@0.12.11-next.0
  - @backstage/plugin-home@0.8.6-next.0
  - @backstage/plugin-org@0.6.37-next.0
  - @backstage/plugin-user-settings@0.8.20-next.0
  - @backstage/frontend-app-api@0.10.6-next.0
  - @backstage/plugin-catalog-unprocessed-entities@0.2.15-next.0
  - @backstage/plugin-devtools@0.1.25-next.0
  - @backstage/plugin-notifications@0.5.3-next.0
  - @backstage/plugin-signals@0.0.17-next.0
  - @backstage/cli@0.30.0
  - @backstage/integration-react@1.2.4

## example-app-next@0.0.21-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-react@1.8.7-next.0
  - @backstage/plugin-kubernetes@0.12.5-next.0
  - @backstage/plugin-scaffolder@1.29.0-next.0
  - @backstage/plugin-catalog-react@1.16.0-next.0
  - @backstage/plugin-scaffolder-react@1.14.6-next.0
  - @backstage/plugin-catalog@1.28.0-next.0
  - @backstage/plugin-techdocs@1.12.4-next.0
  - @backstage/plugin-techdocs-react@1.2.15-next.0
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.22-next.0
  - @backstage/frontend-plugin-api@0.9.6-next.0
  - @backstage/plugin-search@1.4.24-next.0
  - @backstage/plugin-kubernetes-cluster@0.0.23-next.0
  - @backstage/plugin-api-docs@0.12.5-next.0
  - @backstage/plugin-catalog-graph@0.4.17-next.0
  - @backstage/plugin-catalog-import@0.12.11-next.0
  - @backstage/plugin-home@0.8.6-next.0
  - @backstage/plugin-org@0.6.37-next.0
  - @backstage/plugin-user-settings@0.8.20-next.0
  - @backstage/core-compat-api@0.3.7-next.0
  - @backstage/frontend-app-api@0.10.6-next.0
  - @backstage/frontend-defaults@0.1.7-next.0
  - @backstage/plugin-app@0.1.7-next.0
  - @backstage/plugin-app-visualizer@0.1.17-next.0
  - @backstage/plugin-catalog-unprocessed-entities@0.2.15-next.0
  - @backstage/plugin-notifications@0.5.3-next.0
  - @backstage/plugin-signals@0.0.17-next.0
  - @backstage/cli@0.30.0
  - @backstage/integration-react@1.2.4

## app-next-example-plugin@0.0.21-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.6-next.0

## example-backend@0.0.36-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.2-next.0
  - @backstage/plugin-kubernetes-backend@0.19.4-next.0
  - @backstage/plugin-notifications-backend@0.5.4-next.0
  - @backstage/plugin-events-backend@0.4.4-next.0
  - @backstage/plugin-permission-node@0.8.9-next.0
  - @backstage/plugin-app-backend@0.4.6-next.0
  - @backstage/plugin-auth-backend@0.24.4-next.0
  - @backstage/plugin-auth-backend-module-github-provider@0.3.1-next.0
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/plugin-catalog-backend@1.31.1-next.0
  - @backstage/plugin-devtools-backend@0.5.3-next.0
  - @backstage/plugin-permission-backend@0.5.55-next.0
  - @backstage/plugin-proxy-backend@0.5.12-next.0
  - @backstage/plugin-scaffolder-backend@1.30.1-next.0
  - @backstage/plugin-search-backend@1.8.3-next.0
  - @backstage/plugin-search-backend-node@1.3.9-next.0
  - @backstage/plugin-signals-backend@0.3.2-next.0
  - @backstage/plugin-techdocs-backend@1.11.7-next.0
  - @backstage/plugin-search-backend-module-techdocs@0.3.7-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-permission-backend-module-allow-all-policy@0.2.6-next.0
  - @backstage/plugin-auth-backend-module-guest-provider@0.2.6-next.0
  - @backstage/plugin-catalog-backend-module-openapi@0.2.8-next.0
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.6-next.0
  - @backstage/plugin-scaffolder-backend-module-github@0.6.1-next.0
  - @backstage/plugin-search-backend-module-catalog@0.3.2-next.0
  - @backstage/plugin-search-backend-module-explore@0.2.9-next.0
  - @backstage/plugin-catalog-backend-module-backstage-openapi@0.4.6-next.0
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.6-next.0
  - @backstage/plugin-scaffolder-backend-module-notifications@0.1.8-next.0

## example-backend-legacy@0.2.108-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-backend-module-elasticsearch@1.6.6-next.0
  - @backstage/backend-defaults@0.8.2-next.0
  - @backstage/plugin-kubernetes-backend@0.19.4-next.0
  - @backstage/plugin-search-backend-module-pg@0.5.42-next.0
  - @backstage/plugin-events-backend@0.4.4-next.0
  - @backstage/plugin-permission-node@0.8.9-next.0
  - @backstage/plugin-app-backend@0.4.6-next.0
  - @backstage/plugin-auth-backend@0.24.4-next.0
  - @backstage/plugin-auth-node@0.6.1-next.0
  - @backstage/plugin-catalog-backend@1.31.1-next.0
  - @backstage/plugin-permission-backend@0.5.55-next.0
  - @backstage/plugin-proxy-backend@0.5.12-next.0
  - @backstage/plugin-scaffolder-backend@1.30.1-next.0
  - @backstage/plugin-search-backend@1.8.3-next.0
  - @backstage/plugin-search-backend-node@1.3.9-next.0
  - @backstage/plugin-signals-backend@0.3.2-next.0
  - @backstage/plugin-techdocs-backend@1.11.7-next.0
  - @backstage/plugin-search-backend-module-techdocs@0.3.7-next.0
  - @backstage/backend-plugin-api@1.2.1-next.0
  - @backstage/plugin-catalog-node@1.16.1-next.0
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.6-next.0
  - @backstage/plugin-events-node@0.4.9-next.0
  - @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.7-next.0
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.8.1-next.0
  - @backstage/plugin-scaffolder-backend-module-rails@0.5.7-next.0
  - @backstage/plugin-search-backend-module-catalog@0.3.2-next.0
  - @backstage/plugin-search-backend-module-explore@0.2.9-next.0
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.6-next.0
  - @backstage/plugin-signals-node@0.1.18-next.0

## e2e-test@0.2.26-next.0

### Patch Changes

- Updated dependencies
  - @backstage/create-app@0.5.26-next.0

## @internal/frontend@0.0.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.6-next.0

## @internal/scaffolder@0.0.7-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-react@1.14.6-next.0
  - @backstage/frontend-plugin-api@0.9.6-next.0

## techdocs-cli-embedded-app@0.2.106-next.0

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.28.0-next.0
  - @backstage/plugin-techdocs@1.12.4-next.0
  - @backstage/plugin-techdocs-react@1.2.15-next.0
  - @backstage/cli@0.30.0
  - @backstage/integration-react@1.2.4

## @internal/plugin-todo-list-backend@1.0.37-next.0

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.1-next.0
