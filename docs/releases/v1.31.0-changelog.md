# Release v1.31.0

Upgrade Helper: [https://backstage.github.io/upgrade-helper/?to=1.31.0](https://backstage.github.io/upgrade-helper/?to=1.31.0)

## @backstage/backend-app-api@1.0.0

### Major Changes

- ec1b4be: Release 1.0 of the new backend system! :tada:

  The backend system is finally getting promoted to 1.0.0. This means that the API is now stable and breaking changes should not occur until version 2.0.0, see our [package versioning policy](https://backstage.io/docs/overview/versioning-policy/#package-versioning-policy) for more information what this means.

  This release also marks the end of the old backend system based on `createRouter` exports. Going forward backend plugins packages will start to deprecate and later this year remove exports supporting the old backend system. If you would like to help out with this transition, see <https://github.com/backstage/backstage/issues/26353> or consult the [migration guide](https://backstage.io/docs/backend-system/building-plugins-and-modules/migrating/#remove-support-for-the-old-backend-system).

### Minor Changes

- 19ff127: **BREAKING**: The deprecated `identityServiceFactory` and `tokenManagerServiceFactory` have been removed.
- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- cd38da8: Deprecate the `featureDiscoveryServiceFactory` in favor of using `@backstage/backend-defaults#discoveryFeatureLoader` instead.
- 8ccf784: All created backend instances now share a the same `process` exit listeners, and on exit the process will wait for all backend instances to shut down before exiting. This fixes the `EventEmitter` leak warnings in tests.
- 6ed9264: chore(deps): bump `path-to-regexp` from 6.2.2 to 8.0.0
- c246372: Updated the error message for missing service dependencies to include the plugin and module IDs.
- 7f779c7: `auth.externalAccess` should be optional in the config schema
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- 51a69b5: Fix feature loaders in CJS double-default nested builds
- 0b2a402: Updates to the config schema to match reality
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/plugin-permission-node@0.8.3
  - @backstage/config-loader@1.9.1
  - @backstage/cli-common@0.1.14
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1

## @backstage/backend-plugin-api@1.0.0

### Major Changes

- ec1b4be: Release 1.0 of the new backend system! :tada:

  The backend system is finally getting promoted to 1.0.0. This means that the API is now stable and breaking changes should not occur until version 2.0.0, see our [package versioning policy](https://backstage.io/docs/overview/versioning-policy/#package-versioning-policy) for more information what this means.

  This release also marks the end of the old backend system based on `createRouter` exports. Going forward backend plugins packages will start to deprecate and later this year remove exports supporting the old backend system. If you would like to help out with this transition, see <https://github.com/backstage/backstage/issues/26353> or consult the [migration guide](https://backstage.io/docs/backend-system/building-plugins-and-modules/migrating/#remove-support-for-the-old-backend-system).

### Minor Changes

- 19ff127: **BREAKING**: The deprecated identity and token manager services have been removed. This means that `coreServices.identity` and `coreServices.tokenManager` are gone, along with related types and utilities in other packages.

- f687050: Removed the following deprecated exports

  - `BackendPluginConfig` use `CreateBackendPluginOptions`
  - `BackendModuleConfig` use `CreateBackendModuleOptions`
  - `ExtensionPointConfig` use `CreateExtensionPointOptions`

- 4d82481: Removed deprecated `ServiceFactoryOrFunction` type.

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- cd38da8: Deprecate the `featureDiscoveryServiceRef` in favor of using the new `discoveryFeatureLoader` instead.
- 8052b9b: Add a `toJSON` on refs so that they can appear in expectations in jest tests
- 66dbf0a: Allow the cache service to accept the human duration format for TTL
- 0b2a402: Updates to the config schema to match reality
- Updated dependencies
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/cli-common@0.1.14
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-permission-common@0.8.1

## @backstage/backend-test-utils@1.0.0

### Major Changes

- ec1b4be: Release 1.0 of the new backend system! :tada:

  The backend system is finally getting promoted to 1.0.0. This means that the API is now stable and breaking changes should not occur until version 2.0.0, see our [package versioning policy](https://backstage.io/docs/overview/versioning-policy/#package-versioning-policy) for more information what this means.

  This release also marks the end of the old backend system based on `createRouter` exports. Going forward backend plugins packages will start to deprecate and later this year remove exports supporting the old backend system. If you would like to help out with this transition, see <https://github.com/backstage/backstage/issues/26353> or consult the [migration guide](https://backstage.io/docs/backend-system/building-plugins-and-modules/migrating/#remove-support-for-the-old-backend-system).

### Minor Changes

- 19ff127: **BREAKING**: Removed service mocks for the identity and token manager services, which have been removed from `@backstage/backend-plugin-api`.
- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 710f621: Added missing service mock for `mockServices.rootConfig.mock`, and fixed the definition of `mockServices.rootHttpRouter.factory` to not have a duplicate callback.
- f421d2a: Make MySQL pool settings a bit more lax
- 0363bf1: There is a new `mockErrorHandler` utility to help in mocking the error middleware in tests.
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-defaults@0.5.0
  - @backstage/backend-app-api@1.0.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/plugin-events-node@0.4.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1

## @backstage/backend-common@0.25.0

### Minor Changes

- a4bac3c: **BREAKING**: You can no longer supply a `basePath` option to the host discovery implementation. In the new backend system, the ability to choose this path has been removed anyway at the plugin router level.

- 988c145: **BREAKING**: Simplifications and cleanup as part of the Backend System 1.0 work.

  - The deprecated `dropDatabase` function has now been removed, without replacement.

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 6ed9264: chore(deps): bump `path-to-regexp` from 6.2.2 to 8.0.0
- 8ba77ed: The `legacyPlugin` and `makeLegacyPlugin` helpers now provide their own shim implementation of the identity and token manager services, as these services are being removed from the new backend system.
- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- 2e9ec14: Add `pg-format` as a dependency
- 82539fe: Updated dependency `archiver` to `^7.0.0`.
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- 3606843: Internal fixes to match `testcontainers` update
- 19ff127: Internal refactor to re-declare the token manager service which was removed from `@backstage/backend-plugin-api`, but is still supported in this package for backwards compatibility.
- 66dbf0a: Allow the cache service to accept the human duration format for TTL
- 0b2a402: Updates to the config schema to match reality
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/integration@1.15.0
  - @backstage/config-loader@1.9.1
  - @backstage/backend-dev-utils@0.1.5
  - @backstage/cli-common@0.1.14
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/integration-aws-node@0.1.12
  - @backstage/types@1.1.1

## @backstage/backend-defaults@0.5.0

### Minor Changes

- a4bac3c: **BREAKING**: You can no longer supply a `basePath` option to the host discovery implementation. In the new backend system, the ability to choose this path has been removed anyway at the plugin router level.

- 359fcd7: **BREAKING**: The backwards compatibility with plugins using legacy auth through the token manager service has been removed. This means that instead of falling back to using the old token manager, requests towards plugins that don't support the new auth system will simply fail. Please make sure that all plugins in your deployment are hosted within a backend instance from the new backend system.

- baeef13: **BREAKING** Removed `createLifecycleMiddleware` and `LifecycleMiddlewareOptions` to clean up API surface. These exports have no external usage and do not provide value in its current form. If you were using these exports, please reach out to the maintainers to discuss your use case.

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

- 19ff127: **BREAKING**: The default backend instance no longer provides implementations for the identity and token manager services, which have been removed from `@backstage/backend-plugin-api`.

  If you rely on plugins that still require these services, you can add them to your own backend by re-creating the service reference and factory.

  The following can be used to implement the identity service:

  ```ts
  import {
    coreServices,
    createServiceFactory,
    createServiceRef,
  } from '@backstage/backend-plugin-api';
  import {
    DefaultIdentityClient,
    IdentityApi,
  } from '@backstage/plugin-auth-node';

  backend.add(
    createServiceFactory({
      service: createServiceRef<IdentityApi>({ id: 'core.identity' }),
      deps: {
        discovery: coreServices.discovery,
      },
      async factory({ discovery }) {
        return DefaultIdentityClient.create({ discovery });
      },
    }),
  );
  ```

  The following can be used to implement the token manager service:

  ```ts
  import { ServerTokenManager, TokenManager } from '@backstage/backend-common';
  import { createBackend } from '@backstage/backend-defaults';
  import {
    coreServices,
    createServiceFactory,
    createServiceRef,
  } from '@backstage/backend-plugin-api';

  backend.add(
    createServiceFactory({
      service: createServiceRef<TokenManager>({ id: 'core.tokenManager' }),
      deps: {
        config: coreServices.rootConfig,
        logger: coreServices.rootLogger,
      },
      createRootContext({ config, logger }) {
        return ServerTokenManager.fromConfig(config, {
          logger,
          allowDisabledTokenManager: true,
        });
      },
      async factory(_deps, tokenManager) {
        return tokenManager;
      },
    }),
  );
  ```

- 055b75b: **BREAKING**: Simplifications and cleanup as part of the Backend System 1.0 work.

  For the `/database` subpath exports:

  - The deprecated `dropDatabase` function has now been removed, without replacement.
  - The deprecated `LegacyRootDatabaseService` type has now been removed.
  - The return type from `DatabaseManager.forPlugin` is now directly a `DatabaseService`, as arguably expected.
  - `DatabaseManager.forPlugin` now requires the `deps` argument, with the logger and lifecycle services.

  For the `/cache` subpath exports:

  - The `PluginCacheManager` type has been removed. You can still import it from `@backstage/backend-common`, but it's deprecated there, and you should move off of that package by migrating fully to the new backend system.
  - Accordingly, `CacheManager.forPlugin` immediately returns a `CacheService` instead of a `PluginCacheManager`. The outcome of this is that you no longer need to make the extra `.getClient()` call. The old `CacheManager` with the old behavior still exists on `@backstage/backend-common`, but the above recommendations apply.

### Patch Changes

- 213664e: Fixed an issue where the `useRedisSets` configuration for the cache service would have no effect.

- 6ed9264: chore(deps): bump `path-to-regexp` from 6.2.2 to 8.0.0

- 622360e: Move down the discovery config to be in the root

- 7f779c7: `auth.externalAccess` should be optional in the config schema

- fe6fd8c: Accept `ConfigService` instead of `Config` in constructors/factories

- 82539fe: Updated dependency `archiver` to `^7.0.0`.

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.

- 5705424: Wrap scheduled tasks from the scheduler core service now in OpenTelemetry spans

- 7a72ec8: Exports the `discoveryFeatureLoader` as a replacement for the deprecated `featureDiscoveryService`.
  The `discoveryFeatureLoader` is a new backend system [feature loader](https://backstage.io/docs/backend-system/architecture/feature-loaders/) that discovers backend features from the current `package.json` and its dependencies.
  Here is an example using the `discoveryFeatureLoader` loader in a new backend instance:

  ```ts
  import { createBackend } from '@backstage/backend-defaults';
  import { discoveryFeatureLoader } from '@backstage/backend-defaults';
  //...

  const backend = createBackend();
  //...
  backend.add(discoveryFeatureLoader);
  //...
  backend.start();
  ```

- b2a329d: Properly indent the config schema

- 66dbf0a: Allow the cache service to accept the human duration format for TTL

- 5a8fcb4: Added the option to skip database migrations by setting `skipMigrations: true` in config. This can be done globally in the database config or by plugin id.

- 0b2a402: Updates to the config schema to match reality

- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-app-api@1.0.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/cli-node@0.2.8
  - @backstage/plugin-permission-node@0.8.3
  - @backstage/integration@1.15.0
  - @backstage/config-loader@1.9.1
  - @backstage/plugin-events-node@0.4.0
  - @backstage/backend-dev-utils@0.1.5
  - @backstage/cli-common@0.1.14
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/integration-aws-node@0.1.12
  - @backstage/types@1.1.1

## @backstage/backend-dynamic-feature-service@0.4.0

### Minor Changes

- 9080f57: **BREAKING**: `dynamicPluginsServiceFactory` is no longer callable as a function. If you need to provide options to make a custom factory, use `dynamicPluginsSchemasServiceFactoryWithOptions` instead.

### Patch Changes

- cd38da8: Deprecate the `dynamicPluginsServiceRef`, `dynamicPluginsServiceFactory` and `dynamicPluginsServiceFactoryWithOptions` in favor of using the `dynamicPluginsFeatureDiscoveryLoader` to discover dynamic features in a new backend system.

  See usage examples below:

  Example using the `dynamicPluginsFeatureDiscoveryLoader` loader in a backend instance:

  ```ts
  import { createBackend } from '@backstage/backend-defaults';
  import { dynamicPluginsFeatureDiscoveryLoader } from '@backstage/backend-dynamic-feature-service';
  //...

  const backend = createBackend();
  backend.add(dynamicPluginsFeatureDiscoveryLoader);
  //...
  backend.start();
  ```

  Passing options to the `dynamicPluginsFeatureDiscoveryLoader` loader in a backend instance:

  ```ts
  import { createBackend } from '@backstage/backend-defaults';
  import { dynamicPluginsFeatureDiscoveryLoader } from '@backstage/backend-dynamic-feature-service';
  import { myCustomModuleLoader } from './myCustomModuleLoader';
  //...

  const backend = createBackend();
  backend.add(
    dynamicPluginsFeatureDiscoveryLoader({
      moduleLoader: myCustomModuleLoader,
    }),
  );
  //...
  backend.start();
  ```

- e27f889: Relax type check for a plugin's default export to also accept a BackendFeature defined as a function instead of an object

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.

- Updated dependencies
  - @backstage/backend-defaults@0.5.0
  - @backstage/backend-common@0.25.0
  - @backstage/backend-app-api@1.0.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/plugin-catalog-backend@1.26.0
  - @backstage/plugin-app-node@0.1.25
  - @backstage/cli-node@0.2.8
  - @backstage/plugin-permission-node@0.8.3
  - @backstage/plugin-events-backend@0.3.12
  - @backstage/config-loader@1.9.1
  - @backstage/plugin-events-node@0.4.0
  - @backstage/cli-common@0.1.14
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-permission-common@0.8.1
  - @backstage/plugin-scaffolder-node@0.4.11
  - @backstage/plugin-search-backend-node@1.3.2
  - @backstage/plugin-search-common@1.2.14

## @backstage/catalog-client@1.7.0

### Minor Changes

- 78475c3: Allow offset mode paging in entity list provider
- 29e57c7: Add catalog service mocks under the `/testUtils` subpath export.

### Patch Changes

- 1882cfe: Moved `getEntities` ordering to utilize database instead of having it inside catalog client

  Please note that the latest version of `@backstage/catalog-client` will not order the entities in the same way as before. This is because the ordering is now done in the database query instead of in the client. If you rely on the ordering of the entities, you may need to update your backend plugin or code to handle this change.

- Updated dependencies
  - @backstage/catalog-model@1.7.0
  - @backstage/errors@1.2.4

## @backstage/catalog-model@1.7.0

### Minor Changes

- 74acf06: Add `dependencyOf` prop to catalog model for Component kind to enable building relationship graphs with both directions using `dependsOn` and `dependencyOf`.

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1

## @backstage/core-app-api@1.15.0

### Minor Changes

- ddbeace: Added the ability to explicitly disable routes through the `bindRoutes` option by passing `false` as the route target. This also fixes a bug where route bindings in config were incorrectly prioritized above the ones in code in certain situations.

### Patch Changes

- ea69e46: The `defaultConfigLoader` now also reads configuration from scripts tags with `type="backstage.io/config"`. The tag is expected to contain a JSON-serialized array of `AppConfig` objects. If any of these script tags are present, the injected runtime configuration in the static assets will no longer be used.
- b537bd7: Allow custom star icons to be provided via the `star` and `unstarred` icon overrides. See how to override existing icons in the [Backstage documentation](https://backstage.io/docs/getting-started/app-custom-theme/#custom-icons).
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.9.4
  - @backstage/version-bridge@1.0.9
  - @backstage/config@1.2.0
  - @backstage/types@1.1.1

## @backstage/core-compat-api@0.3.0

### Minor Changes

- 6db849e: **BREAKING**: The `namespace` parameter for API's is now defaulted to the `pluginId` which was discovered. This means that if you're overriding API's by using ID's directly, they might have changed to include the plugin ID too.

### Patch Changes

- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.
- c816e2d: Added support for new `FrontendPlugin` and `FrontendModule` types.
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/version-bridge@1.0.9

## @backstage/core-components@0.15.0

### Minor Changes

- 9adc552: Added missing items to `overridableComponents`

  **BREAKING** Overridable component name for styling `OAuthRequestDialog` changed to `BackstageOAuthRequestDialog`. Overridable component name `BackstageMissingAnnotationEmptyState` that was previously deprecated has been removed.

### Patch Changes

- c891b69: Add `FavoriteToggle` in `core-components` to standardise favorite marking
- 0944334: Removed default title set to Unknown page on `ContentHeaderTitle` component to support usage of component without title prop.
- f325258: Use getOptionalString for optional app.support.items\[].links\[].title
- af4c146: Support menu items should not be buttons in favour of links
- 107a147: Fixed `Table` width being overridden by custom `style` prop.
- b537bd7: Allow custom star icons to be provided via the `star` and `unstarred` icon overrides. See how to override existing icons in the [Backstage documentation](https://backstage.io/docs/getting-started/app-custom-theme/#custom-icons).
- 06b8206: Added `titleComponent` prop to `SignInPage` component to allow further customization of the title using `ReactNode`
- dbbd93e: Internal update to match recent React types
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.9.4
  - @backstage/theme@0.5.7
  - @backstage/version-bridge@1.0.9
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4

## @backstage/dev-utils@1.1.0

### Minor Changes

- 10b1452: Allow using translations in `createDevApp`

### Patch Changes

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/core-app-api@1.15.0
  - @backstage/integration-react@1.1.31
  - @backstage/catalog-model@1.7.0
  - @backstage/app-defaults@1.5.11
  - @backstage/core-plugin-api@1.9.4
  - @backstage/theme@0.5.7

## @backstage/frontend-app-api@0.9.0

### Minor Changes

- 7c80650: **BREAKING**: The `createSpecializedApp` function now creates a bare-bones app without any of the default app structure or APIs. To re-introduce this functionality if you need to use `createSpecializedApp` you can install the `app` plugin from `@backstage/plugin-app`.

  In addition, the `createApp` and `CreateAppFeatureLoader` exports are now deprecated as they are being moved to `@backstage/frontend-defaults`, which should be used instead.

- 62cce6c: Removed deprecated `icons` property passing to `createApp` and `createSpecializedApp`. Use `IconBundleBlueprint.make` to create extensions instead and include them in the app.

### Patch Changes

- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.

- 2bb9517: Introduce the `@backstage/plugin-app` package to hold all of the built-in extensions for easy consumption and overriding.

- c816e2d: Added support for new `FrontendPlugin` and `FrontendModule` types.

- f3a2b91: Moved several implementations of built-in APIs from being hardcoded in the app to instead be provided as API extensions. This moves all API-related inputs from the `app` extension to the respective API extensions. For example, extensions created with `ThemeBlueprint` are now attached to the `themes` input of `api:app-theme` rather than the `app` extension.

- 0c70949: Reverse the order of API factory initialization in order to make sure that overrides from modules take priority

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.

- 5446061: Internal refactor following removal of v1 extension support. The app implementation itself still supports v1 extensions at runtime.

- 948d431: Removing deprecated `namespace` parameter in favour of `pluginId` instead

- ddbeace: Added the ability to explicitly disable routes through the `bindRoutes` option by passing `false` as the route target. This also fixes a bug where route bindings in config were incorrectly prioritized above the ones in code in certain situations.

- 98850de: Added support for defining `replaces` in `createExtensionInput` which will allow extensions to redirect missing `attachTo` points to an input of the created extension.

  ```ts
  export const AppThemeApi = ApiBlueprint.makeWithOverrides({
    name: 'app-theme',
    inputs: {
      themes: createExtensionInput([ThemeBlueprint.dataRefs.theme], {
        // attachTo: { id: 'app', input: 'themes'} will be redirected to this input instead
        replaces: [{ id: 'app', input: 'themes' }],
      }),
    },
    factory: () {
      ...
    }
  });
  ```

- 4a66456: Added the `root` extension the replace the `app` extension as the root of the app.

- Updated dependencies
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-app-api@1.15.0
  - @backstage/frontend-defaults@0.1.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/version-bridge@1.0.9
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1

## @backstage/frontend-defaults@0.1.0

### Minor Changes

- 7c80650: Initial release of this package, which provides a default app setup through the `createApp` function. This replaces the existing `createApp` method from `@backstage/frontend-app-api`.

### Patch Changes

- 7d19cd5: Added a new `CreateAppOptions` type for the `createApp` options.
- 7d19cd5: Added `createPublicSignInApp`, used to creating apps for the public entry point.
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/frontend-app-api@0.9.0
  - @backstage/plugin-app@0.1.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4

## @backstage/frontend-plugin-api@0.8.0

### Minor Changes

- 5446061: **BREAKING**: Removed support for "v1" extensions. This means that it is no longer possible to declare inputs and outputs as objects when using `createExtension`. In addition, all extension creators except for `createComponentExtension` have been removed, use the equivalent blueprint instead. See the [1.30 migration documentation](https://backstage.io/docs/frontend-system/architecture/migrations/#130) for more information on this change.
- fec8b57: **BREAKING**: Updated the type parameters for `ExtensionDefinition` and `ExtensionBlueprint` to only have a single object parameter. The base type parameter is exported as `ExtensionDefinitionParameters` and `ExtensionBlueprintParameters` respectively. This is shipped as an immediate breaking change as we expect usage of these types to be rare, and it does not affect the runtime behavior of the API.

  This is a breaking change as it changes the type parameters. Existing usage can generally be updated as follows:

  - `ExtensionDefinition<any>` -> `ExtensionDefinition`
  - `ExtensionDefinition<any, any>` -> `ExtensionDefinition`
  - `ExtensionDefinition<TConfig>` -> `ExtensionDefinition<{ config: TConfig }>`
  - `ExtensionDefinition<TConfig, TConfigInput>` -> `ExtensionDefinition<{ config: TConfig, configInput: TConfigInput }>`

  If you need to infer the parameter you can use `ExtensionDefinitionParameters`, for example:

  ```ts
  import {
    ExtensionDefinition,
    ExtensionDefinitionParameters,
  } from '@backstage/frontend-plugin-api';

  function myUtility<T extends ExtensionDefinitionParameters>(
    ext: ExtensionDefinition<T>,
  ): T['config'] {
    // ...
  }
  ```

  The same patterns apply to `ExtensionBlueprint`.

  This change is made to improve the readability of API references and ability to evolve the type parameters in the future.

### Patch Changes

- 2bb9517: Introduce the `@backstage/plugin-app` package to hold all of the built-in extensions for easy consumption and overriding.

- c816e2d: Added `createFrontendModule` as a replacement for `createExtensionOverrides`, which is now deprecated.

  Deprecated the `BackstagePlugin` and `FrontendFeature` type in favor of `FrontendPlugin` and `FrontendFeature` from `@backstage/frontend-app-api` respectively.

- 52f9c5a: Deprecated the `namespace` option for `createExtensionBlueprint` and `createExtension`, these are no longer required and will default to the `pluginId` instead.

  You can migrate some of your extensions that use `createExtensionOverrides` to using `createFrontendModule` instead and providing a `pluginId` there.

  ```ts
  // Before
  createExtensionOverrides({
    extensions: [
      createExtension({
        name: 'my-extension',
        namespace: 'my-namespace',
        kind: 'test',
        ...
      })
    ],
  });

  // After
  createFrontendModule({
    pluginId: 'my-namespace',
    extensions: [
      createExtension({
        name: 'my-extension',
        kind: 'test',
        ...
      })
    ],
  });
  ```

- f3a2b91: Moved several implementations of built-in APIs from being hardcoded in the app to instead be provided as API extensions. This moves all API-related inputs from the `app` extension to the respective API extensions. For example, extensions created with `ThemeBlueprint` are now attached to the `themes` input of `api:app-theme` rather than the `app` extension.

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.

- 948d431: Removing deprecated `namespace` parameter in favour of `pluginId` instead

- 043d7cd: Internal refactor

- 220f4f7: Remove unnecessary config object on IconBundleBlueprint

- 2a61422: The `factory` option is no longer required when overriding an extension.

- 98850de: Added support for defining `replaces` in `createExtensionInput` which will allow extensions to redirect missing `attachTo` points to an input of the created extension.

  ```ts
  export const AppThemeApi = ApiBlueprint.makeWithOverrides({
    name: 'app-theme',
    inputs: {
      themes: createExtensionInput([ThemeBlueprint.dataRefs.theme], {
        // attachTo: { id: 'app', input: 'themes'} will be redirected to this input instead
        replaces: [{ id: 'app', input: 'themes' }],
      }),
    },
    factory: () {
      ...
    }
  });
  ```

- 4a66456: A new `apis` parameter has been added to `factory` for extensions. This is a way to access utility APIs without being coupled to the React context.

- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/version-bridge@1.0.9
  - @backstage/types@1.1.1

## @backstage/frontend-test-utils@0.2.0

### Minor Changes

- 5446061: Removed support for testing "v1" extensions, where outputs are defined as an object rather than an array.
- e6e488c: **BREAKING**: The deprecated `.render()` method has been removed from the extension tester.

### Patch Changes

- 2a61422: The extension tester will no longer unconditionally enable any additional extensions that have been added.
- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.
- 4a66456: Internal update to add support for passing an `ApiRegistry` when creating the node tree
- 2bb9517: Introduce the `@backstage/plugin-app` package to hold all of the built-in extensions for easy consumption and overriding.
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- 948d431: Removing deprecated `namespace` parameter in favour of `pluginId` instead
- 043d7cd: Internal refactor
- f6d1874: Added the ability to provide additional `extensions` and `features` to `renderInTestApp`
- Updated dependencies
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/frontend-app-api@0.9.0
  - @backstage/plugin-app@0.1.0
  - @backstage/test-utils@1.6.0
  - @backstage/version-bridge@1.0.9
  - @backstage/config@1.2.0
  - @backstage/types@1.1.1

## @backstage/integration@1.15.0

### Minor Changes

- 1573014: The Gerrit integration can now resolve Gitiles urls that point to the following
  refs:

  - HEAD
  - A SHA
  - Tag
  - Branch

### Patch Changes

- Updated dependencies
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4

## @backstage/test-utils@1.6.0

### Minor Changes

- d47be30: Added the icons option to the renderInTestApp function's TestAppOptions to be forwarded to the icons option of `createApp`.

### Patch Changes

- b537bd7: Allow custom star icons to be provided via the `star` and `unstarred` icon overrides. See how to override existing icons in the [Backstage documentation](https://backstage.io/docs/getting-started/app-custom-theme/#custom-icons).
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-app-api@1.15.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/theme@0.5.7
  - @backstage/plugin-permission-react@0.4.26
  - @backstage/config@1.2.0
  - @backstage/types@1.1.1
  - @backstage/plugin-permission-common@0.8.1

## @backstage/plugin-app@0.1.0

### Minor Changes

- 2bb9517: Introduce the `@backstage/plugin-app` package to hold all of the built-in extensions for easy consumption and overriding.

### Patch Changes

- 52f9c5a: Deprecated the `namespace` option for `createExtensionBlueprint` and `createExtension`, these are no longer required and will default to the `pluginId` instead.

  You can migrate some of your extensions that use `createExtensionOverrides` to using `createFrontendModule` instead and providing a `pluginId` there.

  ```ts
  // Before
  createExtensionOverrides({
    extensions: [
      createExtension({
        name: 'my-extension',
        namespace: 'my-namespace',
        kind: 'test',
        ...
      })
    ],
  });

  // After
  createFrontendModule({
    pluginId: 'my-namespace',
    extensions: [
      createExtension({
        name: 'my-extension',
        kind: 'test',
        ...
      })
    ],
  });
  ```

- 57bf6ae: Fix issue with `AlertDisplay` and other components defined with `AppRootElementBlueprint` not being rendered when at the `SignInWrapper`

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.

- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/theme@0.5.7
  - @backstage/plugin-permission-react@0.4.26

## @backstage/plugin-auth-backend@0.23.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 527d973: Migrated the `Bitbucket Server` auth provider to be implemented using the new `@backstage/plugin-auth-backend-module-bitbucket-server-provider` module.
- d908d8c: Migrated the `Auth0` auth provider to be implemented using the new `@backstage/plugin-auth-backend-module-auth0-provider` module.
- 19ff127: Internal refactor to remove dependencies on the identity and token manager services, which have been removed. Public APIs no longer require the identity service or token manager to be provided.
- 3c2d690: Allow users without defined email to be ingested by the `msgraph` catalog plugin and add `userIdMatchingUserEntityAnnotation` sign-in resolver for the Microsoft auth provider to support sign-in for users without defined email.
- 92118cd: Updated dependency `@node-saml/passport-saml` to `^5.0.0`.
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/plugin-auth-backend-module-aws-alb-provider@0.2.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/plugin-auth-backend-module-auth0-provider@0.1.0
  - @backstage/plugin-auth-backend-module-microsoft-provider@0.2.0
  - @backstage/plugin-auth-backend-module-atlassian-provider@0.3.0
  - @backstage/plugin-auth-backend-module-bitbucket-provider@0.2.0
  - @backstage/plugin-auth-backend-module-github-provider@0.2.0
  - @backstage/plugin-auth-backend-module-gitlab-provider@0.2.0
  - @backstage/plugin-auth-backend-module-google-provider@0.2.0
  - @backstage/plugin-auth-backend-module-oauth2-provider@0.3.0
  - @backstage/plugin-auth-backend-module-oidc-provider@0.3.0
  - @backstage/plugin-auth-backend-module-okta-provider@0.1.0
  - @backstage/plugin-auth-backend-module-onelogin-provider@0.2.0
  - @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.1.0
  - @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.0
  - @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.3.0
  - @backstage/plugin-auth-backend-module-gcp-iap-provider@0.3.0
  - @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1

## @backstage/plugin-auth-backend-module-atlassian-provider@0.3.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2

## @backstage/plugin-auth-backend-module-auth0-provider@0.1.0

### Minor Changes

- d908d8c: New module for `@backstage/plugin-auth-backend` that adds a Auth0 auth provider.

### Patch Changes

- 0a3a13e: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2

## @backstage/plugin-auth-backend-module-aws-alb-provider@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- ecbc47e: Fix a bug where the signer was checked from the payload instead of the header
- 8d1fb8d: Throw correct error when email is missing from the claims
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/plugin-auth-backend@0.23.0
  - @backstage/errors@1.2.4

## @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/catalog-model@1.7.0
  - @backstage/errors@1.2.4

## @backstage/plugin-auth-backend-module-bitbucket-provider@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2

## @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.1.0

### Minor Changes

- 527d973: New module for `@backstage/plugin-auth-backend` that adds a `Bitbucket Server` auth provider.

### Patch Changes

- 0a3a13e: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2

## @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.3.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4

## @backstage/plugin-auth-backend-module-gcp-iap-provider@0.3.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1

## @backstage/plugin-auth-backend-module-github-provider@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2

## @backstage/plugin-auth-backend-module-gitlab-provider@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2

## @backstage/plugin-auth-backend-module-google-provider@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2

## @backstage/plugin-auth-backend-module-guest-provider@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 339c67d: This provider will now reject authentication attempts rather than halt backend startup when `dangerouslyAllowOutsideDevelopment` is not set in production.
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/catalog-model@1.7.0
  - @backstage/errors@1.2.4

## @backstage/plugin-auth-backend-module-microsoft-provider@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 3c2d690: Allow users without defined email to be ingested by the `msgraph` catalog plugin and add `userIdMatchingUserEntityAnnotation` sign-in resolver for the Microsoft auth provider to support sign-in for users without defined email.
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2

## @backstage/plugin-auth-backend-module-oauth2-provider@0.3.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2

## @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/errors@1.2.4

## @backstage/plugin-auth-backend-module-oidc-provider@0.3.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/plugin-auth-backend@0.23.0

## @backstage/plugin-auth-backend-module-okta-provider@0.1.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2

## @backstage/plugin-auth-backend-module-onelogin-provider@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2

## @backstage/plugin-auth-backend-module-pinniped-provider@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/config@1.2.0

## @backstage/plugin-auth-backend-module-vmware-cloud-provider@0.3.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/catalog-model@1.7.0

## @backstage/plugin-catalog@1.23.0

### Minor Changes

- 78475c3: Allow offset mode paging in entity list provider

### Patch Changes

- c891b69: Add `FavoriteToggle` in `core-components` to standardise favorite marking
- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- 5446061: The `/alpha` export no longer export extension creators for the new frontend system, existing usage should be switched to use the equivalent extension blueprint instead. For more information see the [new frontend system 1.30 migration documentation](https://backstage.io/docs/frontend-system/architecture/migrations#130).
- 0ff53c8: Enable loading state for paginated catalog tables by passing `isLoading` prop
- 180a45f: Entity presentation api now only fetches fields that are required to display entity title
- a159180: Added missing items to `overridableComponents`
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-compat-api@0.3.0
  - @backstage/plugin-search-react@1.8.0
  - @backstage/integration-react@1.1.31
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/plugin-permission-react@0.4.26
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-scaffolder-common@1.5.6
  - @backstage/plugin-search-common@1.2.14

## @backstage/plugin-catalog-backend@1.26.0

### Minor Changes

- 74acf06: Add `dependencyOf` prop to catalog model for Component kind to enable building relationship graphs with both directions using `dependsOn` and `dependencyOf`.
- 78475c3: Allow offset mode paging in entity list provider
- bd35cdb: The `analyze-location` endpoint is now protected by the `catalog.location.analyze` permission.
  The `validate-entity` endpoint is now protected by the `catalog.entity.validate` permission.

### Patch Changes

- 1882cfe: Moved `getEntities` ordering to utilize database instead of having it inside catalog client

  Please note that the latest version of `@backstage/catalog-client` will not order the entities in the same way as before. This is because the ordering is now done in the database query instead of in the client. If you rely on the ordering of the entities, you may need to update your backend plugin or code to handle this change.

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.

- 53cce86: Fixed an issue with the by-query call, where ordering by a field that does not exist on all entities led to not all results being returned

- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-search-backend-module-catalog@0.2.2
  - @backstage/plugin-permission-node@0.8.3
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/integration@1.15.0
  - @backstage/backend-openapi-utils@0.1.18
  - @backstage/plugin-events-node@0.4.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-permission-common@0.8.1

## @backstage/plugin-catalog-backend-module-backstage-openapi@0.4.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/backend-openapi-utils@0.1.18
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4

## @backstage/plugin-catalog-backend-module-gcp@0.3.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/config@1.2.0
  - @backstage/plugin-kubernetes-common@0.8.3

## @backstage/plugin-catalog-backend-module-github-org@0.3.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/plugin-catalog-backend-module-github@0.7.3
  - @backstage/plugin-events-node@0.4.0
  - @backstage/config@1.2.0

## @backstage/plugin-catalog-backend-module-gitlab-org@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-catalog-backend-module-gitlab@0.4.2
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/plugin-events-node@0.4.0

## @backstage/plugin-catalog-backend-module-ldap@0.9.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- b50e4a8: Add support for optional configuration of `dnAttributeName` and `uuidAttributeName` in LDAP vendor
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1

## @backstage/plugin-catalog-backend-module-logs@0.1.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-catalog-backend@1.26.0
  - @backstage/plugin-events-node@0.4.0

## @backstage/plugin-catalog-backend-module-openapi@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-backend@1.26.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/types@1.1.1

## @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/plugin-scaffolder-common@1.5.6

## @backstage/plugin-catalog-backend-module-unprocessed@0.5.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-catalog-unprocessed-entities-common@0.0.4
  - @backstage/plugin-permission-common@0.8.1

## @backstage/plugin-catalog-common@1.1.0

### Minor Changes

- bd35cdb: The `analyze-location` endpoint is now protected by the `catalog.location.analyze` permission.
  The `validate-entity` endpoint is now protected by the `catalog.entity.validate` permission.

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-permission-common@0.8.1
  - @backstage/plugin-search-common@1.2.14

## @backstage/plugin-catalog-node@1.13.0

### Minor Changes

- bd35cdb: The `analyze-location` endpoint is now protected by the `catalog.location.analyze` permission.
  The `validate-entity` endpoint is now protected by the `catalog.entity.validate` permission.
- 29e57c7: Add catalog service mocks under the `/testUtils` subpath export.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-permission-node@0.8.3
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-permission-common@0.8.1

## @backstage/plugin-catalog-react@1.13.0

### Minor Changes

- 78475c3: Allow offset mode paging in entity list provider

### Patch Changes

- c891b69: Add `FavoriteToggle` in `core-components` to standardise favorite marking
- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.
- b537bd7: Allow custom star icons to be provided via the `star` and `unstarred` icon overrides. See how to override existing icons in the [Backstage documentation](https://backstage.io/docs/getting-started/app-custom-theme/#custom-icons).
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- 5446061: The `/alpha` export no longer export extension creators for the new frontend system, existing usage should be switched to use the equivalent extension blueprint instead. For more information see the [new frontend system 1.30 migration documentation](https://backstage.io/docs/frontend-system/architecture/migrations#130).
- a159180: Added missing items to `overridableComponents`
- ae9b6cb: Small internal fix to better work with recent `lodash` versions
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-compat-api@0.3.0
  - @backstage/integration-react@1.1.31
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/version-bridge@1.0.9
  - @backstage/plugin-permission-react@0.4.26
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-permission-common@0.8.1

## @backstage/plugin-devtools-backend@0.4.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 5d1670f: Update README installation instructions
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-permission-node@0.8.3
  - @backstage/config-loader@1.9.1
  - @backstage/cli-common@0.1.14
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-devtools-common@0.1.12
  - @backstage/plugin-permission-common@0.8.1

## @backstage/plugin-events-node@0.4.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0

## @backstage/plugin-notifications-backend@0.4.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 1c6f142: Internal refactor to avoid use of insecure coding patterns.
- 4a53dd0: Implement icon in backend and show icon in table if available.
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- f195972: Validate notification link when new notification is created
- 5edd344: Refactor to use injected catalog client in the new backend system
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/plugin-signals-node@0.1.11
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/plugin-events-node@0.4.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-notifications-common@0.0.5
  - @backstage/plugin-notifications-node@0.2.6

## @backstage/plugin-notifications-backend-module-email@0.3.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 5edd344: Refactor to use injected catalog client in the new backend system
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/config@1.2.0
  - @backstage/integration-aws-node@0.1.12
  - @backstage/types@1.1.1
  - @backstage/plugin-notifications-common@0.0.5
  - @backstage/plugin-notifications-node@0.2.6

## @backstage/plugin-permission-backend-module-allow-all-policy@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/plugin-permission-node@0.8.3
  - @backstage/plugin-permission-common@0.8.1

## @backstage/plugin-scaffolder@1.25.0

### Minor Changes

- 860ad3a: Expose styles for TemplateEditor, TemplateFormPreviewer and CustomFieldExplorer
- 4baad34: Added support for `omitExtraData` and `liveOmit` for rjsf in the scaffolder
- 5143616: Added EntityOwnerPicker component to the TemplateListPage to allow filtering on owner

### Patch Changes

- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.

- b0a5c9f: The `ui:options` for `OwnedEntityPicker` field are now passed to `EntityPicker`. This allows you to use any `ui:options` which `EntityPicker` accepts in the `OwnedEntityPicker` field including `allowArbitraryValues` and `defaultNamespace`.

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.

- 0a50d44: Updated dependency `@rjsf/utils` to `5.21.1`.
  Updated dependency `@rjsf/core` to `5.21.1`.
  Updated dependency `@rjsf/material-ui` to `5.21.1`.
  Updated dependency `@rjsf/validator-ajv8` to `5.21.1`.

- fa9d8da: Updated dependency `@rjsf/utils` to `5.20.1`.
  Updated dependency `@rjsf/core` to `5.20.1`.
  Updated dependency `@rjsf/material-ui` to `5.20.1`.
  Updated dependency `@rjsf/validator-ajv8` to `5.20.1`.

- 0944334: Removed duplicated titles on Scaffolder `TemplateListPage` component

- 7976081: Added support for all request parameters in the Github create/update environment API in the Github environment create scaffolder action.

  Disable MultiEntityPicker when `maxItems` limit is reached defined in `JSONSchema`

- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-compat-api@0.3.0
  - @backstage/integration-react@1.1.31
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-scaffolder-react@1.12.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/integration@1.15.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/plugin-permission-react@0.4.26
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-scaffolder-common@1.5.6

## @backstage/plugin-scaffolder-backend@1.25.0

### Minor Changes

- df9ae9e: Added scaffolder action publish:bitbucketCloud:pull-request
- 62898bd: `createRouter` and its related types has been marked as deprecared. This backend should instead be initialized using the new backend system.

### Patch Changes

- f0c6b25: Allow listing file contents with `debug:log` scaffolder action

- c160951: Found the issue during testing the clean up of the workspace for the database implementation.

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.

- f865103: Updated dependency `esbuild` to `^0.23.0`.

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.

- 7976081: Added support for all request parameters in the Github create/update environment API in the Github environment create scaffolder action.

  Disable MultiEntityPicker when `maxItems` limit is reached defined in `JSONSchema`

- Updated dependencies
  - @backstage/backend-defaults@0.5.0
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.5.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-permission-node@0.8.3
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/plugin-scaffolder-backend-module-github@0.5.0
  - @backstage/integration@1.15.0
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.0
  - @backstage/plugin-scaffolder-backend-module-azure@0.2.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.0
  - @backstage/plugin-scaffolder-backend-module-gerrit@0.2.0
  - @backstage/plugin-scaffolder-backend-module-gitea@0.2.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.23
  - @backstage/plugin-permission-common@0.8.1
  - @backstage/plugin-scaffolder-common@1.5.6
  - @backstage/plugin-scaffolder-node@0.4.11

## @backstage/plugin-scaffolder-backend-module-azure@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 59d8da4: Publish Azure action now uses basic authentication to authenticate to Git when Azure integration is configured to use App Registration/Service Account or Managed Identity.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-scaffolder-node@0.4.11

## @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.0
  - @backstage/integration@1.15.0
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-scaffolder-node@0.4.11

## @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- df9ae9e: Added scaffolder action publish:bitbucketCloud:pull-request
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-bitbucket-cloud-common@0.2.23
  - @backstage/plugin-scaffolder-node@0.4.11

## @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-scaffolder-node@0.4.11

## @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 5d1670f: Update README installation instructions
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-scaffolder-node@0.4.11

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.3.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 5d1670f: Update README installation instructions
- Updated dependencies
  - @backstage/backend-defaults@0.5.0
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-scaffolder-node@0.4.11

## @backstage/plugin-scaffolder-backend-module-gcp@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-scaffolder-node@0.4.11

## @backstage/plugin-scaffolder-backend-module-gerrit@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 9e5923d: Added test cases for publish:gerrit action examples
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-scaffolder-node@0.4.11

## @backstage/plugin-scaffolder-backend-module-gitea@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-scaffolder-node@0.4.11

## @backstage/plugin-scaffolder-backend-module-github@0.5.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 22a19e6: Added the ability for the actions `publish:github` and `github:repo:create` to take inputs for 'custom properties' for organization repositories.

- 162cdf4: Update dependency @octokit/webhooks to 10.9.2 due to SNYK-JS-OCTOKITWEBHOOKS-6129527

- 7976081: Added support for all request parameters in the Github create/update environment API in the Github environment create scaffolder action.

  Disable MultiEntityPicker when `maxItems` limit is reached defined in `JSONSchema`

- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-scaffolder-node@0.4.11

## @backstage/plugin-scaffolder-backend-module-gitlab@0.5.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 5d1670f: Update README installation instructions
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-scaffolder-node@0.4.11

## @backstage/plugin-scaffolder-backend-module-notifications@0.1.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-notifications-common@0.0.5
  - @backstage/plugin-notifications-node@0.2.6
  - @backstage/plugin-scaffolder-node@0.4.11

## @backstage/plugin-scaffolder-backend-module-rails@0.5.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 5d1670f: Update README installation instructions
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-scaffolder-node@0.4.11

## @backstage/plugin-scaffolder-backend-module-sentry@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 5d1670f: Update README installation instructions
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-scaffolder-node@0.4.11

## @backstage/plugin-scaffolder-backend-module-yeoman@0.4.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- 5d1670f: Update README installation instructions
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/types@1.1.1
  - @backstage/plugin-scaffolder-node@0.4.11
  - @backstage/plugin-scaffolder-node-test-utils@0.1.12

## @backstage/plugin-scaffolder-react@1.12.0

### Minor Changes

- 4512f71: Add `ui:backstage.review.name` option for custom item names on scaffolder review page, and also add support for rendering the `title` property instead of the key name.
- 4baad34: Added support for `omitExtraData` and `liveOmit` for rjsf in the scaffolder

### Patch Changes

- 1f3c5aa: Fix scaffolder review step issue where schema options are not handled for fields on multi-step templates.
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- 0a50d44: Updated dependency `@rjsf/utils` to `5.21.1`.
  Updated dependency `@rjsf/core` to `5.21.1`.
  Updated dependency `@rjsf/material-ui` to `5.21.1`.
  Updated dependency `@rjsf/validator-ajv8` to `5.21.1`.
- fa9d8da: Updated dependency `@rjsf/utils` to `5.20.1`.
  Updated dependency `@rjsf/core` to `5.20.1`.
  Updated dependency `@rjsf/material-ui` to `5.20.1`.
  Updated dependency `@rjsf/validator-ajv8` to `5.20.1`.
- c2cbe1e: Updated dependency `use-immer` to `^0.10.0`.
- b0f0118: Remove unnecessary singleton wrapping of `scaffolderApiRef`.
- 3ebb64f: - Fix secret widget field not displaying as required.
  - Fix secret widget not able to be required inside nested objects.
  - Fix secret widget not able to be disabled.
  - Support `minLength` and `maxLength` properties for secret widget.
- 8dd6ef6: Fix an issue where keys with duplicate final key parts are not all displayed in the `ReviewState`. Change the way the keys are formatted to include the full schema path, separated by `>`.
- 9a0672a: Scaffolder review page shows static amount of asterisks for secret fields.
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/theme@0.5.7
  - @backstage/version-bridge@1.0.9
  - @backstage/plugin-permission-react@0.4.26
  - @backstage/types@1.1.1
  - @backstage/plugin-scaffolder-common@1.5.6

## @backstage/plugin-search-backend-module-stack-overflow-collator@0.3.0

### Minor Changes

- 479808f: Always set default request parameters for requests to stackoverflow while allow to overwrite them. Remove site parameter as causing the request to fail.
- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/config@1.2.0
  - @backstage/plugin-search-backend-node@1.3.2
  - @backstage/plugin-search-common@1.2.14

## @backstage/plugin-search-react@1.8.0

### Minor Changes

- 9d66d8c: Make use of the `useApp` hook to retrieve the specified search icon in the SearchBar

### Patch Changes

- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.
- f26ff99: Slight type tweak to match newer React versions better
- dbbd93e: Internal update to match recent React types
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- 5446061: The `/alpha` export no longer export extension creators for the new frontend system, existing usage should be switched to use the equivalent extension blueprint instead. For more information see the [new frontend system 1.30 migration documentation](https://backstage.io/docs/frontend-system/architecture/migrations#130).
- a159180: Added missing items to `overridableComponents`
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/theme@0.5.7
  - @backstage/version-bridge@1.0.9
  - @backstage/types@1.1.1
  - @backstage/plugin-search-common@1.2.14

## @backstage/plugin-signals-backend@0.2.0

### Minor Changes

- d425fc4: **BREAKING**: The return values from `createBackendPlugin`, `createBackendModule`, and `createServiceFactory` are now simply `BackendFeature` and `ServiceFactory`, instead of the previously deprecated form of a function that returns them. For this reason, `createServiceFactory` also no longer accepts the callback form where you provide direct options to the service. This also affects all `coreServices.*` service refs.

  This may in particular affect tests; if you were effectively doing `createBackendModule({...})()` (note the parentheses), you can now remove those extra parentheses at the end. You may encounter cases of this in your `packages/backend/src/index.ts` too, where you add plugins, modules, and services. If you were using `createServiceFactory` with a function as its argument for the purpose of passing in options, this pattern has been deprecated for a while and is no longer supported. You may want to explore the new multiton patterns to achieve your goals, or moving settings to app-config.

  As part of this change, the `IdentityFactoryOptions` type was removed, and can no longer be used to tweak that service. The identity service was also deprecated some time ago, and you will want to [migrate to the new auth system](https://backstage.io/docs/tutorials/auth-service-migration) if you still rely on it.

### Patch Changes

- a0f1f0d: Bump the `ws` library
- 3ec5ccb: The `createRouter` and its related types has been marked as deprecared. This backend should instead be initialized using the new backend system.
- 19ff127: Internal refactor to remove dependencies on the identity and token manager services, which have been removed. Public APIs no longer require the identity service or token manager to be provided.
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/plugin-signals-node@0.1.11
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/plugin-events-node@0.4.0
  - @backstage/config@1.2.0
  - @backstage/types@1.1.1

## @backstage/app-defaults@1.5.11

### Patch Changes

- b537bd7: Allow custom star icons to be provided via the `star` and `unstarred` icon overrides. See how to override existing icons in the [Backstage documentation](https://backstage.io/docs/getting-started/app-custom-theme/#custom-icons).
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/core-app-api@1.15.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/theme@0.5.7
  - @backstage/plugin-permission-react@0.4.26

## @backstage/backend-openapi-utils@0.1.18

### Patch Changes

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/errors@1.2.4

## @backstage/cli@0.27.1

### Patch Changes

- d2d2313: Add `config.d.ts` files to the list of included file in `tsconfig.json`.

  This allows ESLint to detect issues or deprecations in those files.

- 16ffdd6: Remove direct `vite` dependency

- 8069f4a: Update Scaffolder module template to add itself to the backend

- 97422b0: Update templates to not refer to backend-common

- 0e1a817: The app build process now outputs an additional `index.html.tmpl` file. This is an non-templated version of the `index.html` file, which can be used to delay templating until runtime.

  The new `index.html.tmpl` file also sets a `backstage-public-path` meta tag to be templated at runtime. The meta tag is in turn picked up by the new `@backstage/cli/config/webpack-public-path.js` entry point script, which uses it to set the runtime public path of the Webpack bundle.

- 1b5c264: Add `checks: 'read'` for default GitHub app permissions

- b4685e7: Added `watchOptions` to frontend webpack config for compatibility with Yarn PnP

- d29fc1b: Updated dependency `@module-federation/enhanced` to `^0.6.0`.

- f865103: Updated dependency `esbuild` to `^0.23.0`.

- ab7713a: Updated dependency `eslint-plugin-jest` to `^28.0.0`.

- c78ff91: Updated dependency `@rollup/plugin-commonjs` to `^26.0.0`.

- 4ebf36f: Upgrade to `vite@v5`

- 2d3caaf: The build commands now support the new `backstage.inline` flag in `package.json`, which causes the contents of private packages to be inlined into the consuming package, rather than be treated as an external dependency.

- 569c3f0: Fixed an issue where published frontend packages would end up with an invalid import structure if a single module imported both `.css` and `.svg` files.

- 3d88455: Add support for `backstage:^` version ranges to versions:bump when using the experimental yarn plugin

- d10f6b6: Allow overriding minify flag with build repo command

- Updated dependencies
  - @backstage/catalog-model@1.7.0
  - @backstage/cli-node@0.2.8
  - @backstage/integration@1.15.0
  - @backstage/config-loader@1.9.1
  - @backstage/eslint-plugin@0.1.9
  - @backstage/cli-common@0.1.14
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/release-manifests@0.0.11
  - @backstage/types@1.1.1

## @backstage/cli-node@0.2.8

### Patch Changes

- 0c70f43: Add definition for the new `backstage.inline` field in `package.json`.
- Updated dependencies
  - @backstage/cli-common@0.1.14
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1

## @backstage/codemods@0.1.50

### Patch Changes

- 0894166: Updated dependency `jscodeshift` to `^0.16.0`.
- Updated dependencies
  - @backstage/cli-common@0.1.14

## @backstage/config-loader@1.9.1

### Patch Changes

- ef3c507: Updated dependency `typescript-json-schema` to `^0.65.0`.
- Updated dependencies
  - @backstage/cli-common@0.1.14
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1

## @backstage/core-plugin-api@1.9.4

### Patch Changes

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/version-bridge@1.0.9
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1

## @backstage/create-app@0.5.19

### Patch Changes

- d2d2313: Add `config.d.ts` files to the list of included file in `tsconfig.json`.

  This allows ESLint to detect issues or deprecations in those files.

- 4975e63: Yarn 4 is now the default for `create-app`. Also updated `yarn dev` script to use `yarn workspaces foreach` and removed unused Lerna and Concurrently dependencies.

- 4735881: Bumped create-app version.

- 97422b0: Update templates to not refer to backend-common

- 019d9ad: Minor dockerfile syntax update

- e03acd8: Updated Dockerfile base image to `node:20-bookworm-slim` from `node:18-bookworm-slim`

- bf370c2: Remove references to the `@backstage/backend-tasks` in versions of the `create-app` package, as it has been deprecated.

- Updated dependencies
  - @backstage/cli-common@0.1.14

## @backstage/eslint-plugin@0.1.9

### Patch Changes

- 08895e3: Added support for linting dependencies on workspace packages with the `backstage.inline` flag.

## @backstage/integration-react@1.1.31

### Patch Changes

- 8a9d797: Remove unnecessary broad permissions from Gitlab `SCMAuth`

  Newer versions of Gitlab (after 2019) do not require the broad api permissions to write to repos.

- Updated dependencies
  - @backstage/integration@1.15.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/config@1.2.0

## @backstage/repo-tools@0.9.7

### Patch Changes

- 5c4aa2f: Updated dependency `@useoptic/openapi-utilities` to `^0.55.0`.
- 1a8837e: Avoid generating API reports for packages with `backstage.inline` set.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/cli-node@0.2.8
  - @backstage/config-loader@1.9.1
  - @backstage/cli-common@0.1.14
  - @backstage/errors@1.2.4

## @techdocs/cli@1.8.19

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.5.0
  - @backstage/plugin-techdocs-node@1.12.11
  - @backstage/catalog-model@1.7.0
  - @backstage/cli-common@0.1.14
  - @backstage/config@1.2.0

## @backstage/theme@0.5.7

### Patch Changes

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.

## @backstage/version-bridge@1.0.9

### Patch Changes

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.

## @backstage/plugin-api-docs@0.11.9

### Patch Changes

- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- 8a474f2: Updating docs to use `createFrontendModule` instead
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/plugin-catalog@1.23.0
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-compat-api@0.3.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/plugin-permission-react@0.4.26

## @backstage/plugin-app-backend@0.3.74

### Patch Changes

- 72a8c7b: Return HTTP status 400 rather than 500 when receiving an unknown POST request.

- d3f79d1: Fixing dependency metadata with the new `@backstage/plugin-app` package

- 590fb2d: **BREAKING**: The app backend now supports the new `index.html.tmpl` output from `@backstage/cli`. If available, the `index.html` will be templated at runtime with the current configuration of the app backend.

  This is marked as a breaking change because you must now supply the app build-time configuration to the backend. This change also affects the public path behavior, where it is no longer necessary to build the app with the correct public path upfront. You now only need to supply a correct `app.baseUrl` to the app backend plugin at runtime.

  An effect that this change has is that the `index.html` will now contain and present the frontend configuration in an easily readable way, which can aid in debugging. This data was always available in the frontend, but it was injected and hidden in the static bundle.

  This templating behavior is enabled by default, but it can be disabled by setting the `app.disableConfigInjection` configuration option to `true`.

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.

- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/plugin-app-node@0.1.25
  - @backstage/config-loader@1.9.1
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1

## @backstage/plugin-app-node@0.1.25

### Patch Changes

- d3f79d1: Fixing dependency metadata with the new `@backstage/plugin-app` package
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/config-loader@1.9.1

## @backstage/plugin-app-visualizer@0.1.10

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-plugin-api@1.9.4

## @backstage/plugin-auth-node@0.5.2

### Patch Changes

- c46eb0f: Extend the "unable to resolve user identity" message
- d908d8c: Accepts an optional options object in the `PassportOAuthAuthenticatorHelper.authenticate` method.
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- 6f409b7: The `emailMatchingUserEntityProfileEmail` sign-in resolver will now also try matching emails with plus addressing removed.
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1

## @backstage/plugin-auth-react@0.1.6

### Patch Changes

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/errors@1.2.4

## @backstage/plugin-bitbucket-cloud-common@0.2.23

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.15.0

## @backstage/plugin-catalog-backend-module-aws@0.4.2

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- Updated dependencies
  - @backstage/backend-defaults@0.5.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/integration-aws-node@0.1.12
  - @backstage/plugin-kubernetes-common@0.8.3

## @backstage/plugin-catalog-backend-module-azure@0.2.2

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.3.2

### Patch Changes

- 19ff127: Internal refactor to remove dependencies on the identity and token manager services, which have been removed. Public APIs no longer require the identity service or token manager to be provided.
- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/integration@1.15.0
  - @backstage/plugin-events-node@0.4.0
  - @backstage/config@1.2.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.23

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.2.2

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4

## @backstage/plugin-catalog-backend-module-gerrit@0.2.2

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4

## @backstage/plugin-catalog-backend-module-github@0.7.3

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- 5edd344: Refactor to use injected catalog client in the new backend system
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-backend@1.26.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/integration@1.15.0
  - @backstage/plugin-events-node@0.4.0
  - @backstage/config@1.2.0

## @backstage/plugin-catalog-backend-module-gitlab@0.4.2

### Patch Changes

- 53b24d9: Internal update to use the new cache manager

- 0476be3: Add the `relations` array to allow Backstage to mirror GitLab's membership behavior, including descendant, inherited, and shared-from-group memberships.

  The previous `allowInherited` config option will be deprecated in future versions. Use the `relations` array with the `INHERITED` option instead.

  ```yaml
  catalog:
    providers:
      gitlab:
        development:
          relations:
            - INHERITED
  ```

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.

- b446954: Remove dependency on backend-common

- 06cc084: Added a `includeUsersWithoutSeat` config option that allow import of users without a paid seat, e.g. for Gitlab Free on SaaS. Defaults to false

- Updated dependencies
  - @backstage/backend-defaults@0.5.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/integration@1.15.0
  - @backstage/plugin-events-node@0.4.0
  - @backstage/config@1.2.0

## @backstage/plugin-catalog-backend-module-incremental-ingestion@0.5.3

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- 4b28e39: Updated the README to include documentation for the new backend support
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-backend@1.26.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/plugin-events-node@0.4.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-permission-common@0.8.1

## @backstage/plugin-catalog-backend-module-msgraph@0.6.2

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- 3c2d690: Allow users without defined email to be ingested by the `msgraph` catalog plugin and add `userIdMatchingUserEntityAnnotation` sign-in resolver for the Microsoft auth provider to support sign-in for users without defined email.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/config@1.2.0

## @backstage/plugin-catalog-backend-module-puppetdb@0.2.2

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1

## @backstage/plugin-catalog-graph@0.4.9

### Patch Changes

- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.
- da91078: Fixed a bug in the `CatalogGraphPage` component where, after clicking on some nodes, clicking the back button would break the navigation. This issue caused the entire navigation to fail and behaved differently across various browsers.
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- 8a474f2: Updating docs to use `createFrontendModule` instead
- a159180: Added missing items to `overridableComponents`
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-compat-api@0.3.0
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/types@1.1.1

## @backstage/plugin-catalog-import@0.12.3

### Patch Changes

- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.
- f3c90da: Support button title should be contained in a single menu item
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-compat-api@0.3.0
  - @backstage/integration-react@1.1.31
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/integration@1.15.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4

## @backstage/plugin-catalog-unprocessed-entities@0.2.8

### Patch Changes

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.

- 4f08c85: Show additional info on DevTools unprocessed entities table

  - Location path (so that it's easier to search the failed entity from the YAML URL)
  - Time info of last discovery and next refresh time so that users can be aware of it and can sort the errors based on the time.

- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/catalog-model@1.7.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/errors@1.2.4

## @backstage/plugin-config-schema@0.1.59

### Patch Changes

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1

## @backstage/plugin-devtools@0.1.18

### Patch Changes

- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.
- 019d9ad: Minor dockerfile syntax update
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-compat-api@0.3.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/plugin-permission-react@0.4.26
  - @backstage/errors@1.2.4
  - @backstage/plugin-devtools-common@0.1.12

## @backstage/plugin-events-backend@0.3.12

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-events-node@0.4.0
  - @backstage/config@1.2.0

## @backstage/plugin-events-backend-module-aws-sqs@0.4.2

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-events-node@0.4.0
  - @backstage/config@1.2.0
  - @backstage/types@1.1.1

## @backstage/plugin-events-backend-module-azure@0.2.11

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-events-node@0.4.0

## @backstage/plugin-events-backend-module-bitbucket-cloud@0.2.11

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-events-node@0.4.0

## @backstage/plugin-events-backend-module-gerrit@0.2.11

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-events-node@0.4.0

## @backstage/plugin-events-backend-module-github@0.2.11

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-events-node@0.4.0
  - @backstage/config@1.2.0

## @backstage/plugin-events-backend-module-gitlab@0.2.11

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-events-node@0.4.0
  - @backstage/config@1.2.0

## @backstage/plugin-events-backend-test-utils@0.1.35

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.0

## @backstage/plugin-home@0.7.10

### Patch Changes

- c891b69: Add `FavoriteToggle` in `core-components` to standardise favorite marking
- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- 0a50d44: Updated dependency `@rjsf/utils` to `5.21.1`.
  Updated dependency `@rjsf/core` to `5.21.1`.
  Updated dependency `@rjsf/material-ui` to `5.21.1`.
  Updated dependency `@rjsf/validator-ajv8` to `5.21.1`.
- fa9d8da: Updated dependency `@rjsf/utils` to `5.20.1`.
  Updated dependency `@rjsf/core` to `5.20.1`.
  Updated dependency `@rjsf/material-ui` to `5.20.1`.
  Updated dependency `@rjsf/validator-ajv8` to `5.20.1`.
- a159180: Added missing items to `overridableComponents`
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-app-api@1.15.0
  - @backstage/core-compat-api@0.3.0
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/theme@0.5.7
  - @backstage/plugin-home-react@0.1.17
  - @backstage/config@1.2.0

## @backstage/plugin-home-react@0.1.17

### Patch Changes

- 0a50d44: Updated dependency `@rjsf/utils` to `5.21.1`.
  Updated dependency `@rjsf/core` to `5.21.1`.
  Updated dependency `@rjsf/material-ui` to `5.21.1`.
  Updated dependency `@rjsf/validator-ajv8` to `5.21.1`.
- fa9d8da: Updated dependency `@rjsf/utils` to `5.20.1`.
  Updated dependency `@rjsf/core` to `5.20.1`.
  Updated dependency `@rjsf/material-ui` to `5.20.1`.
  Updated dependency `@rjsf/validator-ajv8` to `5.20.1`.
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/core-plugin-api@1.9.4

## @backstage/plugin-kubernetes@0.11.14

### Patch Changes

- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.
- bfc0f42: Make k8s entity content appear on components & resources only by default in new FE system
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-compat-api@0.3.0
  - @backstage/catalog-model@1.7.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/plugin-kubernetes-react@0.4.3
  - @backstage/plugin-kubernetes-common@0.8.3

## @backstage/plugin-kubernetes-backend@0.18.6

### Patch Changes

- a0f1f0d: Bump the `ws` library
- f55f8bf: The `KubernetesBuilder` and its related types has been marked as deprecared. This backend should instead be initialized using the new backend system.
- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- ca96b66: Skip start without proper config
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-permission-node@0.8.3
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/plugin-kubernetes-node@0.1.19
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/integration-aws-node@0.1.12
  - @backstage/types@1.1.1
  - @backstage/plugin-kubernetes-common@0.8.3
  - @backstage/plugin-permission-common@0.8.1

## @backstage/plugin-kubernetes-cluster@0.0.15

### Patch Changes

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/catalog-model@1.7.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/plugin-kubernetes-react@0.4.3
  - @backstage/plugin-kubernetes-common@0.8.3

## @backstage/plugin-kubernetes-common@0.8.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.0
  - @backstage/types@1.1.1
  - @backstage/plugin-permission-common@0.8.1

## @backstage/plugin-kubernetes-node@0.1.19

### Patch Changes

- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/types@1.1.1
  - @backstage/plugin-kubernetes-common@0.8.3

## @backstage/plugin-kubernetes-react@0.4.3

### Patch Changes

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/catalog-model@1.7.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-kubernetes-common@0.8.3

## @backstage/plugin-notifications@0.3.1

### Patch Changes

- 653f60b: Severity filter label newly contains "Min severity" to better describe range instead of exact value.
- 4a53dd0: Implement icon in backend and show icon in table if available.
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/theme@0.5.7
  - @backstage/plugin-signals-react@0.0.5
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-notifications-common@0.0.5

## @backstage/plugin-notifications-node@0.2.6

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/plugin-signals-node@0.1.11
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-notifications-common@0.0.5

## @backstage/plugin-org@0.6.29

### Patch Changes

- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- 8a474f2: Updating docs to use `createFrontendModule` instead
- a159180: Added missing items to `overridableComponents`
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-compat-api@0.3.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/core-plugin-api@1.9.4

## @backstage/plugin-org-react@0.1.28

### Patch Changes

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/core-plugin-api@1.9.4

## @backstage/plugin-permission-backend@0.5.49

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- fcb9356: Deprecated `createRouter` and its router options in favour of the new backend system.
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/plugin-permission-node@0.8.3
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-permission-common@0.8.1

## @backstage/plugin-permission-node@0.8.3

### Patch Changes

- 19ff127: Internal refactor to remove dependencies on the identity and token manager services, which have been removed. Public APIs no longer require the identity service or token manager to be provided.
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-permission-common@0.8.1

## @backstage/plugin-permission-react@0.4.26

### Patch Changes

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.9.4
  - @backstage/config@1.2.0
  - @backstage/plugin-permission-common@0.8.1

## @backstage/plugin-proxy-backend@0.5.6

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- d298e6e: Deprecated `createRouter` and its router options in favour of the new backend system.
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/config@1.2.0
  - @backstage/types@1.1.1

## @backstage/plugin-scaffolder-common@1.5.6

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.7.0
  - @backstage/types@1.1.1
  - @backstage/plugin-permission-common@0.8.1

## @backstage/plugin-scaffolder-node@0.4.11

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/integration@1.15.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-scaffolder-common@1.5.6

## @backstage/plugin-scaffolder-node-test-utils@0.1.12

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-test-utils@1.0.0
  - @backstage/types@1.1.1
  - @backstage/plugin-scaffolder-node@0.4.11

## @backstage/plugin-search@1.4.16

### Patch Changes

- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.
- eca03bd: Added ability to customize the search items within the SidebarSearchModal
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-compat-api@0.3.0
  - @backstage/plugin-search-react@1.8.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/version-bridge@1.0.9
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-search-common@1.2.14

## @backstage/plugin-search-backend@1.5.17

### Patch Changes

- 5726390: Deprecate create router as the legacy backend system will no longer be supported.
- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-defaults@0.5.0
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-permission-node@0.8.3
  - @backstage/backend-openapi-utils@0.1.18
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-permission-common@0.8.1
  - @backstage/plugin-search-backend-node@1.3.2
  - @backstage/plugin-search-common@1.2.14

## @backstage/plugin-search-backend-module-catalog@0.2.2

### Patch Changes

- 19ff127: Internal refactor to remove dependencies on the identity and token manager services, which have been removed. Public APIs no longer require the identity service or token manager to be provided.

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.

- 5726390: The following collator factories are deprecated, please [migrate](https://backstage.io/docs/backend-system/building-backends/migrating) to the new backend system and follow the instructions below to install collators via module:

  - `DefaultCatalogCollatorFactory`: <https://github.com/backstage/backstage/blob/nbs10/search-deprecate-create-router/plugins/search-backend-module-catalog/README.md#installation>;
  - `ToolDocumentCollatorFactory`: <https://github.com/backstage/backstage/blob/nbs10/search-deprecate-create-router/plugins/search-backend-module-explore/README.md#installation>;
  - `DefaultTechDocsCollatorFactory`: <https://github.com/backstage/backstage/blob/nbs10/search-deprecate-create-router/plugins/search-backend-module-techdocs/README.md#installation>.

- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-permission-common@0.8.1
  - @backstage/plugin-search-backend-node@1.3.2
  - @backstage/plugin-search-common@1.2.14

## @backstage/plugin-search-backend-module-elasticsearch@1.5.6

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- 5726390: Internal refactor to use `LoggerService` and `DatabaseService` instead of the legacy `Logger` and `PluginDatabaseManager` types.
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/config@1.2.0
  - @backstage/integration-aws-node@0.1.12
  - @backstage/plugin-search-backend-node@1.3.2
  - @backstage/plugin-search-common@1.2.14

## @backstage/plugin-search-backend-module-explore@0.2.2

### Patch Changes

- 19ff127: Internal refactor to remove dependencies on the identity and token manager services, which have been removed. Public APIs no longer require the identity service or token manager to be provided.

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.

- 5726390: The following collator factories are deprecated, please [migrate](https://backstage.io/docs/backend-system/building-backends/migrating) to the new backend system and follow the instructions below to install collators via module:

  - `DefaultCatalogCollatorFactory`: <https://github.com/backstage/backstage/blob/nbs10/search-deprecate-create-router/plugins/search-backend-module-catalog/README.md#installation>;
  - `ToolDocumentCollatorFactory`: <https://github.com/backstage/backstage/blob/nbs10/search-deprecate-create-router/plugins/search-backend-module-explore/README.md#installation>;
  - `DefaultTechDocsCollatorFactory`: <https://github.com/backstage/backstage/blob/nbs10/search-deprecate-create-router/plugins/search-backend-module-techdocs/README.md#installation>.

- 276f433: Updated dependency `@backstage-community/plugin-explore-common` to `^0.0.5`.

- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/config@1.2.0
  - @backstage/plugin-search-backend-node@1.3.2
  - @backstage/plugin-search-common@1.2.14

## @backstage/plugin-search-backend-module-pg@0.5.35

### Patch Changes

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- 5726390: Internal refactor to use `LoggerService` and `DatabaseService` instead of the legacy `Logger` and `PluginDatabaseManager` types.
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/config@1.2.0
  - @backstage/plugin-search-backend-node@1.3.2
  - @backstage/plugin-search-common@1.2.14

## @backstage/plugin-search-backend-module-techdocs@0.2.2

### Patch Changes

- 19ff127: Internal refactor to remove dependencies on the identity and token manager services, which have been removed. Public APIs no longer require the identity service or token manager to be provided.

- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.

- 5726390: The following collator factories are deprecated, please [migrate](https://backstage.io/docs/backend-system/building-backends/migrating) to the new backend system and follow the instructions below to install collators via module:

  - `DefaultCatalogCollatorFactory`: <https://github.com/backstage/backstage/blob/nbs10/search-deprecate-create-router/plugins/search-backend-module-catalog/README.md#installation>;
  - `ToolDocumentCollatorFactory`: <https://github.com/backstage/backstage/blob/nbs10/search-deprecate-create-router/plugins/search-backend-module-explore/README.md#installation>;
  - `DefaultTechDocsCollatorFactory`: <https://github.com/backstage/backstage/blob/nbs10/search-deprecate-create-router/plugins/search-backend-module-techdocs/README.md#installation>.

- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/plugin-techdocs-node@1.12.11
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/config@1.2.0
  - @backstage/plugin-permission-common@0.8.1
  - @backstage/plugin-search-backend-node@1.3.2
  - @backstage/plugin-search-common@1.2.14

## @backstage/plugin-search-backend-node@1.3.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.5.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-permission-common@0.8.1
  - @backstage/plugin-search-common@1.2.14

## @backstage/plugin-signals@0.0.10

### Patch Changes

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.

- 5add8e1: Added a `SignalsDisplay` extension to allows the signals plugin to be installed in an app as follows:

  ```tsx
  export default app.createRoot(
    <>
      <AlertDisplay transientTimeoutMs={2500} />
      <OAuthRequestDialog />
      <SignalsDisplay />
      <AppRouter>
        <VisitListener />
        <Root>{routes}</Root>
      </AppRouter>
    </>,
  );
  ```

  With this in place you can remove the explicit installation via the `plugins` option for `createApp`.

- 3e9b1a4: Put a name on the `SignalsDisplay` component extension

- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/theme@0.5.7
  - @backstage/plugin-signals-react@0.0.5
  - @backstage/types@1.1.1

## @backstage/plugin-signals-node@0.1.11

### Patch Changes

- a0f1f0d: Bump the `ws` library
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/plugin-events-node@0.4.0
  - @backstage/config@1.2.0
  - @backstage/types@1.1.1

## @backstage/plugin-signals-react@0.0.5

### Patch Changes

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- 0389801: Fix for `useSignal` returning the inverse value for `isSignalsAvailable`.
- Updated dependencies
  - @backstage/core-plugin-api@1.9.4
  - @backstage/types@1.1.1

## @backstage/plugin-techdocs@1.10.9

### Patch Changes

- c891b69: Add `FavoriteToggle` in `core-components` to standardise favorite marking

- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.

- fe94ad8: Fixes left navigation positioning when using mkdocs blog plugin

- b0206dc: Added support for setting page status with 'new' and 'deprecated' values, allowing visual indication of page status in TechDocs. To use include the following at the top of your markdown file:

  ```markdown
  ---
  status: new
  ---
  ```

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.

- c7cb4c0: Add `empty-state:techdocs/entity-content` extension to allow overriding the empty state for the entity page techdocs tab.

- 97db53e: Enhanced the table hover effect with a lighter color and updated the border radius to align with Backstage's theme styling

- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/plugin-techdocs-react@1.2.8
  - @backstage/core-compat-api@0.3.0
  - @backstage/plugin-search-react@1.8.0
  - @backstage/integration-react@1.1.31
  - @backstage/catalog-model@1.7.0
  - @backstage/integration@1.15.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/theme@0.5.7
  - @backstage/plugin-auth-react@0.1.6
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-search-common@1.2.14
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-techdocs-addons-test-utils@1.0.38

### Patch Changes

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/plugin-techdocs@1.10.9
  - @backstage/plugin-catalog@1.23.0
  - @backstage/plugin-techdocs-react@1.2.8
  - @backstage/core-app-api@1.15.0
  - @backstage/plugin-search-react@1.8.0
  - @backstage/integration-react@1.1.31
  - @backstage/test-utils@1.6.0
  - @backstage/core-plugin-api@1.9.4

## @backstage/plugin-techdocs-backend@1.10.13

### Patch Changes

- 086c32d: Dedicated token for techdocs cache sync
- 5b679ac: The `createRouter` and its related types has been marked as deprecared. This backend should instead be initialized using the new backend system.
- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- 5edd344: Refactor to use injected catalog client in the new backend system
- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/plugin-techdocs-node@1.12.11
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-search-backend-module-techdocs@0.2.2
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/plugin-permission-common@0.8.1
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-techdocs-module-addons-contrib@1.1.14

### Patch Changes

- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-techdocs-react@1.2.8
  - @backstage/integration-react@1.1.31
  - @backstage/integration@1.15.0
  - @backstage/core-plugin-api@1.9.4

## @backstage/plugin-techdocs-node@1.12.11

### Patch Changes

- f715f5c: Move `TechdocsContainerRunner` from `publish` to `generate`.
- 4417dd4: Fix typo and unify TechDocs casing in doc strings
- 57da51f: Add support for mapping custom tags in the techdocs yaml parser that validates the mkdocs.yaml file
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- 3606843: Internal fixes to match `testcontainers` update
- 33ebb28: As the `@backstage/backend-common` package is deprecated, we have updated the `techdocs-node` package to stop depending on it.
- Updated dependencies
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/catalog-model@1.7.0
  - @backstage/integration@1.15.0
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/integration-aws-node@0.1.12
  - @backstage/plugin-search-common@1.2.14
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-techdocs-react@1.2.8

### Patch Changes

- 5ee3d27: Fixed issue in useShadowRootElements which could lead to unlimited render loops
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/catalog-model@1.7.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/version-bridge@1.0.9
  - @backstage/config@1.2.0

## @backstage/plugin-user-settings@0.8.12

### Patch Changes

- fec8b57: Updated exports to use the new type parameters for extensions and extension blueprints.
- 836127c: Updated dependency `@testing-library/react` to `^16.0.0`.
- da86ac2: Update README to clarify location of additional tabs example
- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/core-app-api@1.15.0
  - @backstage/core-compat-api@0.3.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/theme@0.5.7
  - @backstage/plugin-signals-react@0.0.5
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-user-settings-common@0.0.1

## @backstage/plugin-user-settings-backend@0.2.24

### Patch Changes

- 5d1670f: Update README installation instructions
- 164ce3e: In preparation to stop supporting to the legacy backend system, the `createRouter` function is now deprecated and we strongly recommend you [migrate](https://backstage.io/docs/backend-system/building-backends/migrating) your backend to the new system.
- d425fc4: Modules, plugins, and services are now `BackendFeature`, not a function that returns a feature.
- 1b98099: Replaced usage of the deprecated identity service with the new HTTP auth service for the new backend system.
- c2b63ab: Updated dependency `supertest` to `^7.0.0`.
- Updated dependencies
  - @backstage/backend-defaults@0.5.0
  - @backstage/plugin-signals-node@0.1.11
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/config@1.2.0
  - @backstage/errors@1.2.4
  - @backstage/types@1.1.1
  - @backstage/plugin-user-settings-common@0.0.1

## example-app@0.2.101

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/plugin-techdocs@1.10.9
  - @backstage/plugin-catalog@1.23.0
  - @backstage/plugin-home@0.7.10
  - @backstage/cli@0.27.1
  - @backstage/plugin-techdocs-react@1.2.8
  - @backstage/core-app-api@1.15.0
  - @backstage/frontend-app-api@0.9.0
  - @backstage/plugin-catalog-import@0.12.3
  - @backstage/plugin-catalog-graph@0.4.9
  - @backstage/plugin-user-settings@0.8.12
  - @backstage/plugin-search-react@1.8.0
  - @backstage/plugin-kubernetes@0.11.14
  - @backstage/plugin-scaffolder@1.25.0
  - @backstage/plugin-api-docs@0.11.9
  - @backstage/plugin-devtools@0.1.18
  - @backstage/plugin-search@1.4.16
  - @backstage/plugin-org@0.6.29
  - @backstage/integration-react@1.1.31
  - @backstage/plugin-notifications@0.3.1
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-scaffolder-react@1.12.0
  - @backstage/app-defaults@1.5.11
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/theme@0.5.7
  - @backstage/plugin-auth-react@0.1.6
  - @backstage/plugin-catalog-unprocessed-entities@0.2.8
  - @backstage/plugin-kubernetes-cluster@0.0.15
  - @backstage/plugin-permission-react@0.4.26
  - @backstage/plugin-signals@0.0.10
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.14
  - @backstage/config@1.2.0
  - @backstage/plugin-search-common@1.2.14

## example-app-next@0.0.15

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-catalog-react@1.13.0
  - @backstage/plugin-techdocs@1.10.9
  - @backstage/plugin-catalog@1.23.0
  - @backstage/plugin-home@0.7.10
  - @backstage/cli@0.27.1
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/plugin-techdocs-react@1.2.8
  - @backstage/core-app-api@1.15.0
  - @backstage/frontend-app-api@0.9.0
  - @backstage/core-compat-api@0.3.0
  - @backstage/plugin-catalog-import@0.12.3
  - @backstage/plugin-catalog-graph@0.4.9
  - @backstage/plugin-user-settings@0.8.12
  - @backstage/plugin-search-react@1.8.0
  - @backstage/plugin-kubernetes@0.11.14
  - @backstage/plugin-scaffolder@1.25.0
  - @backstage/plugin-api-docs@0.11.9
  - @backstage/plugin-search@1.4.16
  - @backstage/plugin-org@0.6.29
  - @backstage/integration-react@1.1.31
  - @backstage/frontend-defaults@0.1.0
  - @backstage/plugin-notifications@0.3.1
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-scaffolder-react@1.12.0
  - @backstage/plugin-app@0.1.0
  - @backstage/app-defaults@1.5.11
  - @backstage/plugin-catalog-common@1.1.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/theme@0.5.7
  - @backstage/plugin-auth-react@0.1.6
  - @backstage/plugin-catalog-unprocessed-entities@0.2.8
  - @backstage/plugin-kubernetes-cluster@0.0.15
  - @backstage/plugin-permission-react@0.4.26
  - @backstage/plugin-signals@0.0.10
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.14
  - @backstage/plugin-app-visualizer@0.1.10
  - @backstage/config@1.2.0
  - @backstage/plugin-search-common@1.2.14

## app-next-example-plugin@0.0.15

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/frontend-plugin-api@0.8.0

## example-backend@0.0.30

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.5.0
  - @backstage/plugin-kubernetes-backend@0.18.6
  - @backstage/plugin-signals-backend@0.2.0
  - @backstage/plugin-techdocs-backend@1.10.13
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-search-backend@1.5.17
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/plugin-devtools-backend@0.4.0
  - @backstage/plugin-app-backend@0.3.74
  - @backstage/plugin-notifications-backend@0.4.0
  - @backstage/plugin-scaffolder-backend@1.25.0
  - @backstage/plugin-auth-backend@0.23.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-backend@1.26.0
  - @backstage/plugin-auth-backend-module-guest-provider@0.2.0
  - @backstage/plugin-search-backend-module-techdocs@0.2.2
  - @backstage/plugin-search-backend-module-catalog@0.2.2
  - @backstage/plugin-search-backend-module-explore@0.2.2
  - @backstage/plugin-permission-node@0.8.3
  - @backstage/plugin-permission-backend@0.5.49
  - @backstage/plugin-proxy-backend@0.5.6
  - @backstage/plugin-scaffolder-backend-module-github@0.5.0
  - @backstage/plugin-auth-backend-module-github-provider@0.2.0
  - @backstage/plugin-catalog-backend-module-backstage-openapi@0.4.0
  - @backstage/plugin-catalog-backend-module-openapi@0.2.0
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.0
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.0
  - @backstage/plugin-permission-backend-module-allow-all-policy@0.2.0
  - @backstage/plugin-permission-common@0.8.1
  - @backstage/plugin-search-backend-node@1.3.2

## example-backend-legacy@0.2.102

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.5.0
  - @backstage/backend-common@0.25.0
  - @backstage/plugin-kubernetes-backend@0.18.6
  - @backstage/plugin-signals-backend@0.2.0
  - @backstage/plugin-signals-node@0.1.11
  - @backstage/plugin-techdocs-backend@1.10.13
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-search-backend@1.5.17
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.0
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.5.0
  - @backstage/plugin-scaffolder-backend-module-rails@0.5.0
  - @backstage/plugin-devtools-backend@0.4.0
  - @backstage/plugin-app-backend@0.3.74
  - @backstage/plugin-scaffolder-backend@1.25.0
  - @backstage/plugin-auth-backend@0.23.0
  - @backstage/catalog-model@1.7.0
  - @backstage/plugin-catalog-backend@1.26.0
  - @backstage/catalog-client@1.7.0
  - @backstage/plugin-search-backend-module-techdocs@0.2.2
  - @backstage/plugin-search-backend-module-catalog@0.2.2
  - @backstage/plugin-search-backend-module-explore@0.2.2
  - @backstage/plugin-permission-node@0.8.3
  - @backstage/plugin-catalog-node@1.13.0
  - @backstage/plugin-events-backend@0.3.12
  - @backstage/plugin-permission-backend@0.5.49
  - @backstage/plugin-proxy-backend@0.5.6
  - @backstage/plugin-search-backend-module-elasticsearch@1.5.6
  - @backstage/plugin-search-backend-module-pg@0.5.35
  - @backstage/integration@1.15.0
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.0
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.0
  - @backstage/plugin-events-node@0.4.0
  - @backstage/config@1.2.0
  - @backstage/plugin-permission-common@0.8.1
  - @backstage/plugin-search-backend-node@1.3.2

## e2e-test@0.2.20

### Patch Changes

- Updated dependencies
  - @backstage/create-app@0.5.19
  - @backstage/cli-common@0.1.14
  - @backstage/errors@1.2.4

## @internal/frontend@0.0.1

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.8.0
  - @backstage/version-bridge@1.0.9
  - @backstage/types@1.1.1

## techdocs-cli-embedded-app@0.2.100

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/plugin-techdocs@1.10.9
  - @backstage/plugin-catalog@1.23.0
  - @backstage/cli@0.27.1
  - @backstage/plugin-techdocs-react@1.2.8
  - @backstage/core-app-api@1.15.0
  - @backstage/integration-react@1.1.31
  - @backstage/catalog-model@1.7.0
  - @backstage/app-defaults@1.5.11
  - @backstage/test-utils@1.6.0
  - @backstage/core-plugin-api@1.9.4
  - @backstage/theme@0.5.7
  - @backstage/config@1.2.0

## yarn-plugin-backstage@0.0.2

### Patch Changes

- bd32d5a: Add additional check before packing workspace to verify that all backstage:^ versions have been removed.
- Updated dependencies
  - @backstage/cli-common@0.1.14
  - @backstage/release-manifests@0.0.11

## @internal/plugin-todo-list@1.0.31

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.15.0
  - @backstage/core-plugin-api@1.9.4

## @internal/plugin-todo-list-backend@1.0.31

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.25.0
  - @backstage/backend-plugin-api@1.0.0
  - @backstage/plugin-auth-node@0.5.2
  - @backstage/errors@1.2.4
