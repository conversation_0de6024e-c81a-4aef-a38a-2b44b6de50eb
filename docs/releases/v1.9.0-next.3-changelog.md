# Release v1.9.0-next.3

## @backstage/app-defaults@1.0.9-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.2.1-next.3
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-permission-react@0.4.8-next.2

## @backstage/backend-test-utils@0.1.31-next.3

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.21.2-next.3
  - @backstage/backend-app-api@0.2.4-next.2
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/config@1.0.5-next.1

## @backstage/cli@0.21.2-next.3

### Patch Changes

- 43b2b9c791: Removed the unused dependency on `@sucrase/jest-plugin`.
- Updated dependencies
  - @backstage/cli-common@0.1.11-next.0
  - @backstage/config@1.0.5-next.1
  - @backstage/config-loader@1.1.7-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/release-manifests@0.0.8-next.0
  - @backstage/types@1.0.2-next.1

## @backstage/core-app-api@1.2.1-next.3

### Patch Changes

- 6870b43dd1: Fix for the automatic rewriting of base URLs.
- 653d7912ac: Made `WebStorage` notify its subscribers when `localStorage` values change in other tabs/windows
- Updated dependencies
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/types@1.0.2-next.1
  - @backstage/version-bridge@1.0.3-next.0

## @backstage/core-components@0.12.1-next.3

### Patch Changes

- 91bba69ef8: Internal refactor to remove deprecated symbols.
- Updated dependencies
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/version-bridge@1.0.3-next.0

## @backstage/create-app@0.4.35-next.3

### Patch Changes

- c4788dbb58: Fix dependency ordering in templated packages.
- af1358bb07: added default project name for CI job compatibility
- Updated dependencies
  - @backstage/cli-common@0.1.11-next.0

## @backstage/dev-utils@1.0.9-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.2.1-next.3
  - @backstage/core-components@0.12.1-next.3
  - @backstage/app-defaults@1.0.9-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration-react@1.1.7-next.3
  - @backstage/test-utils@1.2.3-next.3
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/integration-react@1.1.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @backstage/test-utils@1.2.3-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.2.1-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-permission-common@0.7.2-next.1
  - @backstage/plugin-permission-react@0.4.8-next.2

## @backstage/plugin-adr@0.2.4-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration-react@1.1.7-next.3
  - @backstage/theme@0.2.16
  - @backstage/plugin-adr-common@0.2.4-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.3
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/plugin-search-react@1.3.0-next.3

## @backstage/plugin-airbrake@0.3.12-next.3

### Patch Changes

- 151c0e1477: Remove the `object-hash` dependency
- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/dev-utils@1.0.9-next.3
  - @backstage/test-utils@1.2.3-next.3
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-allure@0.1.28-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-analytics-module-ga@0.1.23-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-apache-airflow@0.2.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2

## @backstage/plugin-api-docs@0.8.12-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog@1.7.0-next.3
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-apollo-explorer@0.1.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-azure-devops@0.2.3-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-devops-common@0.3.0
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-azure-sites@0.1.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-sites-common@0.1.0
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-badges@0.2.36-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-bazaar@0.2.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.21.2-next.3
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-catalog@1.7.0-next.3
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-bazaar-backend@0.2.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-test-utils@0.1.31-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-auth-node@0.2.8-next.2

## @backstage/plugin-bitrise@0.1.39-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-catalog@1.7.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration-react@1.1.7-next.3
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.3
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/plugin-search-react@1.3.0-next.3

## @backstage/plugin-catalog-backend-module-incremental-ingestion@0.1.0-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-plugin-api@0.2.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/backend-test-utils@0.1.31-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/plugin-catalog-node@1.3.0-next.2
  - @backstage/plugin-permission-common@0.7.2-next.1

## @backstage/plugin-catalog-graph@0.2.24-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-catalog-import@0.9.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/integration-react@1.1.7-next.3
  - @backstage/plugin-catalog-common@1.0.9-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-catalog-react@1.2.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/version-bridge@1.0.3-next.0
  - @backstage/plugin-catalog-common@1.0.9-next.2
  - @backstage/plugin-permission-common@0.7.2-next.1
  - @backstage/plugin-permission-react@0.4.8-next.2

## @backstage/plugin-cicd-statistics@0.1.14-next.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-cicd-statistics-module-gitlab@0.1.8-next.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/plugin-cicd-statistics@0.1.14-next.3

## @backstage/plugin-circleci@0.3.12-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-cloudbuild@0.3.12-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-code-climate@0.1.12-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-code-coverage@0.2.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-codescene@0.1.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-config-schema@0.1.35-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-cost-insights@0.12.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/test-utils@1.2.3-next.3
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3
  - @backstage/plugin-cost-insights-common@0.1.1

## @backstage/plugin-dynatrace@1.0.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-explore@0.3.43-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3
  - @backstage/plugin-explore-common@0.0.1-next.0
  - @backstage/plugin-explore-react@0.0.24-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/plugin-search-react@1.3.0-next.3

## @backstage/plugin-firehydrant@0.1.29-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-fossa@0.2.44-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-gcalendar@0.3.8-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-gcp-projects@0.3.31-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-git-release-manager@0.3.25-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-actions@0.5.12-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-github-deployments@0.1.43-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/integration-react@1.1.7-next.3
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-github-issues@0.2.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-github-pull-requests-board@0.1.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-gitops-profiles@0.3.30-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-gocd@0.1.18-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-graphiql@0.2.44-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-home@0.4.28-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3
  - @backstage/plugin-stack-overflow@0.1.8-next.3

## @backstage/plugin-ilert@0.2.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-jenkins@0.7.11-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3
  - @backstage/plugin-jenkins-common@0.1.11-next.2

## @backstage/plugin-kafka@0.3.12-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-kubernetes@0.7.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3
  - @backstage/plugin-kubernetes-common@0.4.5-next.1

## @backstage/plugin-kubernetes-backend@0.8.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-test-utils@0.1.31-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-auth-node@0.2.8-next.2
  - @backstage/plugin-kubernetes-common@0.4.5-next.1

## @backstage/plugin-lighthouse@0.3.12-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-newrelic@0.3.30-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic-dashboard@0.2.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-org@0.6.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-org-react@0.1.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-pagerduty@0.5.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-periskop@0.1.10-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-playlist@0.1.3-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.3
  - @backstage/plugin-permission-common@0.7.2-next.1
  - @backstage/plugin-permission-react@0.4.8-next.2
  - @backstage/plugin-playlist-common@0.1.3-next.1
  - @backstage/plugin-search-react@1.3.0-next.3

## @backstage/plugin-playlist-backend@0.2.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-test-utils@0.1.31-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-auth-node@0.2.8-next.2
  - @backstage/plugin-permission-common@0.7.2-next.1
  - @backstage/plugin-permission-node@0.7.2-next.2
  - @backstage/plugin-playlist-common@0.1.3-next.1

## @backstage/plugin-rollbar@0.4.12-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-scaffolder@1.9.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/integration-react@1.1.7-next.3
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.3
  - @backstage/plugin-permission-react@0.4.8-next.2
  - @backstage/plugin-scaffolder-common@1.2.3-next.1

## @backstage/plugin-search@1.0.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/version-bridge@1.0.3-next.0
  - @backstage/plugin-catalog-react@1.2.2-next.3
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/plugin-search-react@1.3.0-next.3

## @backstage/plugin-search-react@1.3.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/version-bridge@1.0.3-next.0
  - @backstage/plugin-search-common@1.2.0-next.2

## @backstage/plugin-sentry@0.4.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-shortcuts@0.3.4-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-sonarqube@0.5.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-splunk-on-call@0.4.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-stack-overflow@0.1.8-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-home@0.4.28-next.3
  - @backstage/plugin-search-common@1.2.0-next.2

## @backstage/plugin-stack-overflow-backend@0.1.8-next.3

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.21.2-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-search-common@1.2.0-next.2

## @backstage/plugin-tech-insights@0.3.4-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-react@1.2.2-next.3
  - @backstage/plugin-tech-insights-common@0.2.9-next.1

## @backstage/plugin-tech-radar@0.5.19-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs@1.4.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/integration-react@1.1.7-next.3
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/plugin-search-react@1.3.0-next.3
  - @backstage/plugin-techdocs-react@1.0.7-next.3

## @backstage/plugin-techdocs-addons-test-utils@1.0.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.2.1-next.3
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration-react@1.1.7-next.3
  - @backstage/test-utils@1.2.3-next.3
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog@1.7.0-next.3
  - @backstage/plugin-search-react@1.3.0-next.3
  - @backstage/plugin-techdocs@1.4.1-next.3
  - @backstage/plugin-techdocs-react@1.0.7-next.3

## @backstage/plugin-techdocs-module-addons-contrib@1.0.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration@1.4.1-next.1
  - @backstage/integration-react@1.1.7-next.3
  - @backstage/theme@0.2.16
  - @backstage/plugin-techdocs-react@1.0.7-next.3

## @backstage/plugin-techdocs-react@1.0.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/version-bridge@1.0.3-next.0

## @backstage/plugin-todo@0.2.14-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-user-settings@0.6.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.2.1-next.3
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-vault@0.1.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @backstage/plugin-vault-backend@0.2.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/backend-test-utils@0.1.31-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-xcmetrics@0.2.32-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## example-app@0.2.78-next.3

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.21.2-next.3
  - @backstage/core-app-api@1.2.1-next.3
  - @backstage/plugin-airbrake@0.3.12-next.3
  - @backstage/core-components@0.12.1-next.3
  - @backstage/app-defaults@1.0.9-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration-react@1.1.7-next.3
  - @backstage/theme@0.2.16
  - @backstage/plugin-apache-airflow@0.2.5-next.3
  - @backstage/plugin-api-docs@0.8.12-next.3
  - @backstage/plugin-azure-devops@0.2.3-next.3
  - @backstage/plugin-azure-sites@0.1.1-next.3
  - @backstage/plugin-badges@0.2.36-next.3
  - @backstage/plugin-catalog-common@1.0.9-next.2
  - @backstage/plugin-catalog-graph@0.2.24-next.3
  - @backstage/plugin-catalog-import@0.9.2-next.3
  - @backstage/plugin-catalog-react@1.2.2-next.3
  - @backstage/plugin-circleci@0.3.12-next.3
  - @backstage/plugin-cloudbuild@0.3.12-next.3
  - @backstage/plugin-code-coverage@0.2.5-next.3
  - @backstage/plugin-cost-insights@0.12.1-next.3
  - @backstage/plugin-dynatrace@1.0.2-next.3
  - @backstage/plugin-explore@0.3.43-next.3
  - @backstage/plugin-gcalendar@0.3.8-next.3
  - @backstage/plugin-gcp-projects@0.3.31-next.3
  - @backstage/plugin-github-actions@0.5.12-next.3
  - @backstage/plugin-gocd@0.1.18-next.3
  - @backstage/plugin-graphiql@0.2.44-next.3
  - @backstage/plugin-home@0.4.28-next.3
  - @backstage/plugin-jenkins@0.7.11-next.3
  - @backstage/plugin-kafka@0.3.12-next.3
  - @backstage/plugin-kubernetes@0.7.5-next.3
  - @backstage/plugin-lighthouse@0.3.12-next.3
  - @backstage/plugin-newrelic@0.3.30-next.3
  - @backstage/plugin-newrelic-dashboard@0.2.5-next.3
  - @backstage/plugin-org@0.6.2-next.3
  - @backstage/plugin-pagerduty@0.5.5-next.3
  - @backstage/plugin-permission-react@0.4.8-next.2
  - @backstage/plugin-playlist@0.1.3-next.3
  - @backstage/plugin-rollbar@0.4.12-next.3
  - @backstage/plugin-scaffolder@1.9.0-next.3
  - @backstage/plugin-search@1.0.5-next.3
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/plugin-search-react@1.3.0-next.3
  - @backstage/plugin-sentry@0.4.5-next.3
  - @backstage/plugin-shortcuts@0.3.4-next.3
  - @backstage/plugin-stack-overflow@0.1.8-next.3
  - @backstage/plugin-tech-insights@0.3.4-next.3
  - @backstage/plugin-tech-radar@0.5.19-next.3
  - @backstage/plugin-techdocs@1.4.1-next.3
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.7-next.3
  - @backstage/plugin-techdocs-react@1.0.7-next.3
  - @backstage/plugin-todo@0.2.14-next.3
  - @backstage/plugin-user-settings@0.6.0-next.3
  - @internal/plugin-catalog-customized@0.0.5-next.3

## example-backend@0.2.78-next.3

### Patch Changes

- Updated dependencies
  - example-app@0.2.78-next.3
  - @backstage/backend-common@0.17.0-next.2
  - @backstage/backend-tasks@0.4.0-next.2
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/plugin-app-backend@0.3.39-next.2
  - @backstage/plugin-auth-backend@0.17.2-next.2
  - @backstage/plugin-auth-node@0.2.8-next.2
  - @backstage/plugin-azure-devops-backend@0.3.18-next.2
  - @backstage/plugin-azure-sites-backend@0.1.1-next.2
  - @backstage/plugin-badges-backend@0.1.33-next.2
  - @backstage/plugin-catalog-backend@1.6.0-next.2
  - @backstage/plugin-catalog-node@1.3.0-next.2
  - @backstage/plugin-code-coverage-backend@0.2.5-next.2
  - @backstage/plugin-events-backend@0.2.0-next.2
  - @backstage/plugin-events-node@0.2.0-next.2
  - @backstage/plugin-explore-backend@0.0.1-next.1
  - @backstage/plugin-graphql-backend@0.1.29-next.2
  - @backstage/plugin-jenkins-backend@0.1.29-next.2
  - @backstage/plugin-kafka-backend@0.2.32-next.2
  - @backstage/plugin-kubernetes-backend@0.8.1-next.3
  - @backstage/plugin-permission-backend@0.5.14-next.2
  - @backstage/plugin-permission-common@0.7.2-next.1
  - @backstage/plugin-permission-node@0.7.2-next.2
  - @backstage/plugin-playlist-backend@0.2.2-next.3
  - @backstage/plugin-proxy-backend@0.2.33-next.2
  - @backstage/plugin-rollbar-backend@0.1.36-next.2
  - @backstage/plugin-scaffolder-backend@1.9.0-next.2
  - @backstage/plugin-scaffolder-backend-module-rails@0.4.7-next.2
  - @backstage/plugin-search-backend@1.2.0-next.2
  - @backstage/plugin-search-backend-module-elasticsearch@1.1.0-next.2
  - @backstage/plugin-search-backend-module-pg@0.4.3-next.2
  - @backstage/plugin-search-backend-node@1.1.0-next.2
  - @backstage/plugin-search-common@1.2.0-next.2
  - @backstage/plugin-tech-insights-backend@0.5.5-next.2
  - @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.23-next.2
  - @backstage/plugin-tech-insights-node@0.3.7-next.2
  - @backstage/plugin-techdocs-backend@1.4.2-next.2
  - @backstage/plugin-todo-backend@0.1.36-next.2

## techdocs-cli-embedded-app@0.2.77-next.3

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.21.2-next.3
  - @backstage/core-app-api@1.2.1-next.3
  - @backstage/core-components@0.12.1-next.3
  - @backstage/app-defaults@1.0.9-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration-react@1.1.7-next.3
  - @backstage/test-utils@1.2.3-next.3
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog@1.7.0-next.3
  - @backstage/plugin-techdocs@1.4.1-next.3
  - @backstage/plugin-techdocs-react@1.0.7-next.3

## @internal/plugin-catalog-customized@0.0.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.7.0-next.3
  - @backstage/plugin-catalog-react@1.2.2-next.3

## @internal/plugin-todo-list@1.0.8-next.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
