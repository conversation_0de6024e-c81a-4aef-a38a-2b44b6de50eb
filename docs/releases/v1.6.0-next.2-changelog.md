# Release v1.6.0-next.2

## @backstage/plugin-scaffolder-backend@1.6.0-next.2

### Minor Changes

- d1f7ba58e3: Added `repositoryId` output when create a repository in Azure

### Patch Changes

- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- 096631e571: Added support for handling broken symlinks within the scaffolder backend. This is intended for templates that may hold a symlink that is invalid at build time but valid within the destination repo.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- 6b9f6c0a4d: Added alpha `scaffolderPlugin` to be used with experimental backend system.
- 83c037cd46: Disable octokit throttling in publish:github:pull-request
- 2cbd533426: Uptake the `IdentityApi` change to use `getIdentity` instead of `authenticate` for retrieving the logged in users identity.
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.2-next.1
  - @backstage/plugin-catalog-node@1.0.2-next.1
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/integration@1.3.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.2
  - @backstage/plugin-auth-node@0.2.5-next.2
  - @backstage/catalog-client@1.0.5-next.1

## @backstage/plugin-search-react@1.1.0-next.2

### Minor Changes

- 18f60427f2: Provides search autocomplete functionality through a `SearchAutocomplete` component.
  A `SearchAutocompleteDefaultOption` can also be used to render options with icons, primary texts, and secondary texts.
  Example:

  ```jsx
  import React, { ChangeEvent, useState, useCallback } from 'react';
  import useAsync from 'react-use/lib/useAsync';

  import { Grid, Paper } from '@material-ui/core';

  import { Page, Content } from '@backstage/core-components';
  import { SearchAutocomplete, SearchAutocompleteDefaultOption} from '@backstage/plugin-search-react';

  const OptionsIcon = () => <svg />

  const SearchPage = () => {
    const [inputValue, setInputValue] = useState('');

    const options = useAsync(async () => {
      // Gets and returns autocomplete options
    }, [inputValue])

    const useCallback((_event: ChangeEvent<{}>, newInputValue: string) => {
      setInputValue(newInputValue);
    }, [setInputValue])

    return (
      <Page themeId="home">
        <Content>
          <Grid container direction="row">
            <Grid item xs={12}>
              <Paper>
                <SearchAutocomplete
                  options={options}
                  inputValue={inputValue}
                  inputDebounceTime={100}
                  onInputChange={handleInputChange}
                  getOptionLabel={option => option.title}
                  renderOption={option => (
                    <SearchAutocompleteDefaultOption
                      icon={<OptionIcon />}
                      primaryText={option.title}
                      secondaryText={option.text}
                    />
                  )}
                />
              </Paper>
            </Grid>
          </Grid>
          {'/* Filters and results are omitted */'}
        </Content>
      </Page>
    );
  };
  ```

- ca8d5a6eae: We noticed a repeated check for the existence of a parent context before creating a child search context in more the one component such as Search Modal and Search Bar and to remove code duplication we extract the conditional to the context provider, now you can use it passing an `inheritParentContextIfAvailable` prop to the `SearchContextProvider`.

  Note: This added property does not create a local context if there is a parent context and in this case, you cannot use it together with `initialState`, it will result in a type error because the parent context is already initialized.

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-techdocs-backend@1.3.0-next.1

### Minor Changes

- aa524a5377: Add `projectId` config option to GCP Cloud Storage techdocs publisher. This will allow users to override the project ID, instead of implicitly using the same one as found in a credentials bundle.

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/plugin-techdocs-node@1.4.0-next.1
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/integration@1.3.1-next.1
  - @backstage/catalog-client@1.0.5-next.1
  - @backstage/plugin-permission-common@0.6.4-next.1

## @backstage/plugin-techdocs-node@1.4.0-next.1

### Minor Changes

- aa524a5377: Add `projectId` config option to GCP Cloud Storage techdocs publisher. This will allow users to override the project ID, instead of implicitly using the same one as found in a credentials bundle.

### Patch Changes

- 33ac0c0546: Fix Techdocs S3 and GCS publisher to include bucketRootPath in requests
- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/integration@1.3.1-next.1

## @backstage/backend-app-api@0.2.1-next.1

### Patch Changes

- 2c57c0c499: Made `ApiRef.defaultFactory` internal.
- af6bb42c68: Updated `ServiceRegistry` to not initialize factories more than once.
- 1f384c5644: Improved error messaging when failing to instantiate services.
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.2-next.1
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/plugin-permission-node@0.6.5-next.2

## @backstage/backend-common@0.15.1-next.2

### Patch Changes

- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`

- 3d4f5daadf: Remove use of deprecated trimLeft/trimRight

- bf3cc134eb: Implemented KubernetesContainerRunner: a ContainerRunner implementation that leverages Jobs on a kubernetes cluster

  ```ts
  const kubeConfig = new KubeConfig();
  kubeConfig.loadFromDefault();

  const options: KubernetesContainerRunnerOptions = {
    kubeConfig,
    // namespace where Jobs will be created
    namespace: 'default',
    // Jobs name will be prefixed with this name
    name: 'my-runner',
    // An existing Kubernetes volume that will be used
    // as base for mounts
    mountBase: {
      volumeName: 'workdir',
      // Every mount must start with the base path
      // see example below
      basePath: '/workdir',
    },
    // Define a Pod template for the Jobs. It has to include
    // a volume definition named as the mountBase volumeName
    podTemplate: {
      spec: {
        containers: [],
        volumes: [
          {
            name: 'workdir',
            persistentVolumeClaim: {
              claimName: 'workdir-claim',
            },
          },
        ],
      },
    },
  };
  const containerRunner = new KubernetesContainerRunner(options);

  const runOptions: RunContainerOptions = {
    imageName: 'golang:1.17',
    args: ['echo', 'hello world'],
    mountDirs: {
      '/workdir/app': '/app',
    },
  };
  containerRunner.runContainer(runOptions);
  ```

- e3b1993788: Added port ranges in allowed hosts:

  ```yaml
  reading:
      allow:
        - host: *.examples.org:900-1000
  ```

- 2f52e74b49: Got rid of usages of the uppercase String type

- 667d917488: Updated dependency `msw` to `^0.47.0`.

- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.

- Updated dependencies
  - @backstage/integration@1.3.1-next.1
  - @backstage/config-loader@1.1.4-next.1

## @backstage/backend-plugin-api@0.1.2-next.1

### Patch Changes

- 2c57c0c499: Made `ApiRef.defaultFactory` internal.
- 91eed37a39: Updated `createBackendPlugin` and `createBackendModule` to properly forward lack of options.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/plugin-permission-common@0.6.4-next.1

## @backstage/backend-test-utils@0.1.28-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/cli@0.19.0-next.2
  - @backstage/backend-app-api@0.2.1-next.1
  - @backstage/backend-plugin-api@0.1.2-next.1
  - @backstage/backend-common@0.15.1-next.2

## @backstage/catalog-client@1.0.5-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.

## @backstage/cli@0.19.0-next.2

### Patch Changes

- 8d886dd33e: The `create-plugin` and `create` commands have both been deprecated in favor of a new `new` command. The `new` command is functionally identical to `create`, but the new naming makes it possible to use as yarn script, since `yarn create` is reserved.
- 548053614a: Deprecated the `plugin:diff` command. If you wish to keep running similar checks in your project we recommend using bespoke scripts. A useful utility for such scripts is `@manypkg/get-packages`, which helps you enumerate all packages in a monorepo.
- 513b4dd4ef: The `versions:bump` command will now update dependency ranges in `package.json`, even if the new version is within the current range.
- 221e951298: Added support for custom certificate for webpack dev server.
- 934cc34563: Avoid validating the backend configuration schema when loading static configuration for building the frontend.
- 3d4f5daadf: Remove use of deprecated trimLeft/trimRight
- 742cb4f3d7: Fix issue when using `.jsx` files inside tests
- e7600bdb04: Tweaked workspace packaging to not rewrite existing `package.json` files.
- 1cb078ad9f: Fixed a misconfiguration where all modules where treated as ESM by the React Refresh plugin for Webpack.
- 1fd4f2746f: Removed internal dependencies on Lerna. It is now no longer necessary to have Lerna installed in a project to use all features of the Backstage CLI.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- 68c2697077: Added a new `backstage-cli repo clean` command that cleans the repo root and runs the clean script in all packages.
- Updated dependencies
  - @backstage/config-loader@1.1.4-next.1
  - @backstage/release-manifests@0.0.6-next.1

## @backstage/config-loader@1.1.4-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.

## @backstage/core-app-api@1.1.0-next.2

### Patch Changes

- f9ec4e46e3: When using React Router v6 stable, it is now possible for components within the `Route` element tree to have `path` props, although they will be ignored.

- 667d917488: Updated dependency `msw` to `^0.47.0`.

- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.

- e9d40ebf54: If you'd like to send analytics events to multiple implementations, you may now
  do so using the `MultipleAnalyticsApi` implementation provided by this package.

  ```tsx
  import { MultipleAnalyticsApi } from '@backstage/core-app-api';
  import {
    analyticsApiRef,
    configApiRef,
    storageApiRef,
    identityApiRef,
  } from '@internal/backstage/core-plugin-api';
  import { CustomAnalyticsApi } from '@internal/analytics';
  import { VendorAnalyticsApi } from '@vendor/analytics';

  createApiFactory({
    api: analyticsApiRef,
    deps: { configApi: configApiRef, identityApi: identityApiRef, storageApi: storageApiRef },
    factory: ({ configApi, identityApi, storageApi }) =>
      MultipleAnalyticsApi.fromApis([
        VendorAnalyticsApi.fromConfig(configApi, { identityApi }),
        CustomAnalyticsApi.fromConfig(configApi, { identityApi, storageApi }),
      ]),
  }),
  ```

- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/core-components@0.11.1-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- 13f23f6510: Allow custom images in the empty state component
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/core-plugin-api@1.0.6-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.

## @backstage/create-app@0.4.31-next.2

### Patch Changes

- 6ff94d60d5: Removed usage of the deprecated `diff` command in the root `package.json`.

  To make this change in an existing app, make the following change in the root `package.json`:

  ```diff
  -    "diff": "lerna run diff --",
  ```

- 49416194e8: Adds `IdentityApi` configuration to `create-app` scaffolding templates.

  To migrate to the new `IdentityApi`, edit the `packages/backend/src/index.ts` adding the following import:

  ```typescript
  import { DefaultIdentityClient } from '@backstage/plugin-auth-node';
  ```

  Use the factory function to create an `IdentityApi` in the `makeCreateEnv` function and return it from the
  function as follows:

  ```typescript
  function makeCreateEnv(config: Config) {
  ...
    const identity = DefaultIdentityClient.create({
      discovery,
    });
  ...

    return {
      ...,
      identity
    }
  }
  ```

  Backend plugins can be upgraded to work with this new `IdentityApi`.

  Add `identity` to the `RouterOptions` type.

  ```typescript
  export interface RouterOptions {
    ...
    identity: IdentityApi;
  }
  ```

  Then you can use the `IdentityApi` from the plugin.

  ```typescript
  export async function createRouter(
    options: RouterOptions,
  ): Promise<express.Router> {
    const { identity } = options;

    router.get('/user', async (req, res) => {
      const user = await identity.getIdentity({ request: req });
      ...
  ```

- 8d886dd33e: Added `yarn new` as one of the scripts installed by default, which calls `backstage-cli new`. This script replaces `create-plugin`, which you can now remove if you want to. It is kept in the `create-app` template for backwards compatibility.

  The `remove-plugin` command has been removed, as it has been removed from the Backstage CLI.

  To apply these changes to an existing app, make the following change to the root `package.json`:

  ```diff
  -    "remove-plugin": "backstage-cli remove-plugin"
  +    "new": "backstage-cli new --scope internal"
  ```

- a578558180: Updated the root `package.json` to use the new `backstage-cli repo clean` command.

  To apply this change to an existing project, make the following change to the root `package.json`:

  ```diff
  -    "clean": "backstage-cli clean && lerna run clean",
  +    "clean": "backstage-cli repo clean",
  ```

## @backstage/integration@1.3.1-next.1

### Patch Changes

- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- 42918e085c: Fixed bug in the `bitbucketServer` integration where token did not take precedence over supplied username and password which is described in the documentation.

## @backstage/integration-react@1.1.4-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/integration@1.3.1-next.1
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/release-manifests@0.0.6-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.

## @techdocs/cli@1.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs-node@1.4.0-next.1
  - @backstage/backend-common@0.15.1-next.2

## @backstage/test-utils@1.2.0-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-app-api@1.1.0-next.2
  - @backstage/core-plugin-api@1.0.6-next.2
  - @backstage/plugin-permission-common@0.6.4-next.1

## @backstage/plugin-adr@0.2.1-next.2

### Patch Changes

- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2
  - @backstage/integration-react@1.1.4-next.1
  - @backstage/plugin-search-react@1.1.0-next.2

## @backstage/plugin-adr-backend@0.2.1-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/integration@1.3.1-next.1
  - @backstage/catalog-client@1.0.5-next.1

## @backstage/plugin-airbrake@0.3.9-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2
  - @backstage/test-utils@1.2.0-next.2

## @backstage/plugin-airbrake-backend@0.2.9-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2

## @backstage/plugin-allure@0.1.25-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-analytics-module-ga@0.1.20-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-apache-airflow@0.2.2-next.2

### Patch Changes

- 2f52e74b49: Got rid of usages of the uppercase String type
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-api-docs@0.8.9-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2
  - @backstage/plugin-catalog@1.5.1-next.2

## @backstage/plugin-apollo-explorer@0.1.2-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-app-backend@0.3.36-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/config-loader@1.1.4-next.1

## @backstage/plugin-auth-backend@0.16.0-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/plugin-auth-node@0.2.5-next.2
  - @backstage/catalog-client@1.0.5-next.1

## @backstage/plugin-auth-node@0.2.5-next.2

### Patch Changes

- 2cbd533426: `IdentityClient` is now deprecated. Please migrate to `IdentityApi` and `DefaultIdentityClient` instead. The authenticate function on `DefaultIdentityClient` is also deprecated. Please use `getIdentity` instead.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2

## @backstage/plugin-azure-devops@0.2.0-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-azure-devops-backend@0.3.15-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2

## @backstage/plugin-badges@0.2.33-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-bitbucket-cloud-common@0.1.3-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/integration@1.3.1-next.1

## @backstage/plugin-bitrise@0.1.36-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-catalog@1.5.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.0.5-next.1
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2
  - @backstage/integration-react@1.1.4-next.1
  - @backstage/plugin-search-react@1.1.0-next.2

## @backstage/plugin-catalog-backend@1.4.0-next.2

### Patch Changes

- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- 06e2b077a1: Limit the length of error messages that get written to the database and logs - to prevent performance issues
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.2-next.1
  - @backstage/plugin-catalog-node@1.0.2-next.1
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/integration@1.3.1-next.1
  - @backstage/catalog-client@1.0.5-next.1
  - @backstage/plugin-permission-common@0.6.4-next.1
  - @backstage/plugin-permission-node@0.6.5-next.2

## @backstage/plugin-catalog-backend-module-azure@0.1.7-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/integration@1.3.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.2

## @backstage/plugin-catalog-backend-module-bitbucket@0.2.3-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/integration@1.3.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.2
  - @backstage/plugin-bitbucket-cloud-common@0.1.3-next.1

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.1.3-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/integration@1.3.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.2
  - @backstage/plugin-bitbucket-cloud-common@0.1.3-next.1

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.1.1-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/integration@1.3.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.2

## @backstage/plugin-catalog-backend-module-gerrit@0.1.4-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/integration@1.3.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.2

## @backstage/plugin-catalog-backend-module-github@0.1.7-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- 3a62594a11: Add support for including (or excluding) Github repositories by topic
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.2-next.1
  - @backstage/plugin-catalog-node@1.0.2-next.1
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/integration@1.3.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.2

## @backstage/plugin-catalog-backend-module-gitlab@0.1.7-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/integration@1.3.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.2

## @backstage/plugin-catalog-backend-module-msgraph@0.4.2-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.4.0-next.2

## @backstage/plugin-catalog-graphql@0.3.13-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.

## @backstage/plugin-catalog-import@0.8.12-next.2

### Patch Changes

- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/integration@1.3.1-next.1
  - @backstage/catalog-client@1.0.5-next.1
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2
  - @backstage/integration-react@1.1.4-next.1

## @backstage/plugin-catalog-node@1.0.2-next.1

### Patch Changes

- 7d7d947352: Adds experimental `catalogServiceRef` for obtaining a `CatalogClient` in the new backend system.
- Updated dependencies
  - @backstage/backend-plugin-api@0.1.2-next.1
  - @backstage/catalog-client@1.0.5-next.1

## @backstage/plugin-circleci@0.3.9-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-cloudbuild@0.3.9-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-code-climate@0.1.9-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-code-coverage@0.2.2-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-code-coverage-backend@0.2.2-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/integration@1.3.1-next.1
  - @backstage/catalog-client@1.0.5-next.1

## @backstage/plugin-codescene@0.1.4-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-config-schema@0.1.32-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-cost-insights@0.11.31-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-dynatrace@0.2.0-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-explore@0.3.40-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2
  - @backstage/plugin-explore-react@0.0.21-next.2

## @backstage/plugin-explore-react@0.0.21-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-firehydrant@0.1.26-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-fossa@0.2.41-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-gcalendar@0.3.5-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-gcp-projects@0.3.28-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-git-release-manager@0.3.22-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/integration@1.3.1-next.1
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-github-actions@0.5.9-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/integration@1.3.1-next.1
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-github-deployments@0.1.40-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/integration@1.3.1-next.1
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2
  - @backstage/integration-react@1.1.4-next.1

## @backstage/plugin-github-issues@0.1.1-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/integration@1.3.1-next.1
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-github-pull-requests-board@0.1.3-next.2

### Patch Changes

- 2665ee4ed4: Clarified GitHub app permissions required by plugin
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/integration@1.3.1-next.1
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-gitops-profiles@0.3.27-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-gocd@0.1.15-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-graphiql@0.2.41-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-graphql-backend@0.1.26-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/plugin-catalog-graphql@0.3.13-next.2

## @backstage/plugin-home@0.4.25-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2
  - @backstage/plugin-stack-overflow@0.1.5-next.2

## @backstage/plugin-ilert@0.1.35-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-jenkins@0.7.8-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-jenkins-backend@0.1.26-next.2

### Patch Changes

- 47952621dc: Extend configuration so that extra headers can be sent to jenkins instance
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/plugin-auth-node@0.2.5-next.2
  - @backstage/catalog-client@1.0.5-next.1
  - @backstage/plugin-permission-common@0.6.4-next.1

## @backstage/plugin-kafka@0.3.9-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-kubernetes@0.7.2-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-kubernetes-backend@0.7.2-next.2

### Patch Changes

- 8902c2e39d: chore: Exporting KubernetesClientProvider and everything in kubernetes-auth-translator as requested in issue #10457
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/plugin-auth-node@0.2.5-next.2
  - @backstage/catalog-client@1.0.5-next.1

## @backstage/plugin-lighthouse@0.3.9-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-newrelic@0.3.27-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-org@0.5.9-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-pagerduty@0.5.2-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-periskop@0.1.7-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-periskop-backend@0.1.7-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2

## @backstage/plugin-permission-backend@0.5.11-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- 2cbd533426: Uptake the `IdentityApi` change to use `getIdentity` instead of `authenticate` for retrieving the logged in users identity.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/plugin-auth-node@0.2.5-next.2
  - @backstage/plugin-permission-common@0.6.4-next.1
  - @backstage/plugin-permission-node@0.6.5-next.2

## @backstage/plugin-permission-common@0.6.4-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.

## @backstage/plugin-permission-node@0.6.5-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/plugin-auth-node@0.2.5-next.2
  - @backstage/plugin-permission-common@0.6.4-next.1

## @backstage/plugin-proxy-backend@0.2.30-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2

## @backstage/plugin-rollbar@0.4.9-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-rollbar-backend@0.1.33-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2

## @backstage/plugin-scaffolder@1.6.0-next.2

### Patch Changes

- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- de336de9cd: fix uiSchema generation when using complex dependencies
- Updated dependencies
  - @backstage/integration@1.3.1-next.1
  - @backstage/catalog-client@1.0.5-next.1
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2
  - @backstage/integration-react@1.1.4-next.1

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.2.11-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/integration@1.3.1-next.1
  - @backstage/plugin-scaffolder-backend@1.6.0-next.2

## @backstage/plugin-search@1.0.2-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- 18f60427f2: Use the new `inheritParentContextIfAvailable` search context property in `SearchModal` instead of manually checking if a parent context exists, this conditional statement was previously duplicated in more than one component like in `SearchBar` as well and is now only done in ` SearchContextProvider`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2
  - @backstage/plugin-search-react@1.1.0-next.2

## @backstage/plugin-search-backend-module-elasticsearch@1.0.2-next.1

### Patch Changes

- 1bea488bd4: Updated dependency `aws-os-connection` to `^0.2.0`.

## @backstage/plugin-sentry@0.4.2-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-shortcuts@0.3.1-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-sonarqube@0.4.1-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-sonarqube-backend@0.1.1-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2

## @backstage/plugin-splunk-on-call@0.3.33-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-stack-overflow@0.1.5-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- b8190af939: Create a front end API.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2
  - @backstage/plugin-home@0.4.25-next.2

## @backstage/plugin-tech-insights@0.3.0-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-tech-radar@0.5.16-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-techdocs@1.3.2-next.2

### Patch Changes

- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- 7a95c705fa: Fixed a bug where addons wouldn't render on sub pages when using React Route v6 stable.
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- ca8d5a6eae: Use the new `SearchAutocomplete` component in the `TechDocsSearch` component to maintain consistency across search experiences and avoid code duplication.
- e97d616f08: Fixed a bug where scrolling for anchors where the id starts with number didn't work for the current page.
- Updated dependencies
  - @backstage/integration@1.3.1-next.1
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2
  - @backstage/integration-react@1.1.4-next.1
  - @backstage/plugin-search-react@1.1.0-next.2

## @backstage/plugin-techdocs-addons-test-utils@1.0.4-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-app-api@1.1.0-next.2
  - @backstage/plugin-techdocs@1.3.2-next.2
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2
  - @backstage/integration-react@1.1.4-next.1
  - @backstage/test-utils@1.2.0-next.2
  - @backstage/plugin-search-react@1.1.0-next.2
  - @backstage/plugin-catalog@1.5.1-next.2

## @backstage/plugin-techdocs-module-addons-contrib@1.0.4-next.1

### Patch Changes

- eadf56bbbf: Bump `git-url-parse` version to `^13.0.0`
- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/integration@1.3.1-next.1
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2
  - @backstage/integration-react@1.1.4-next.1

## @backstage/plugin-todo@0.2.11-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-todo-backend@0.1.33-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/integration@1.3.1-next.1
  - @backstage/catalog-client@1.0.5-next.1

## @backstage/plugin-user-settings@0.4.8-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-vault@0.1.3-next.1

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## @backstage/plugin-vault-backend@0.2.2-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/backend-test-utils@0.1.28-next.2

## @backstage/plugin-xcmetrics@0.2.29-next.2

### Patch Changes

- 667d917488: Updated dependency `msw` to `^0.47.0`.
- 87ec2ba4d6: Updated dependency `msw` to `^0.46.0`.
- Updated dependencies
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2

## example-app@0.2.75-next.2

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.19.0-next.2
  - @backstage/core-app-api@1.1.0-next.2
  - @backstage/plugin-catalog-import@0.8.12-next.2
  - @backstage/plugin-scaffolder@1.6.0-next.2
  - @backstage/plugin-techdocs@1.3.2-next.2
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.4-next.1
  - @backstage/plugin-apache-airflow@0.2.2-next.2
  - @backstage/core-components@0.11.1-next.2
  - @backstage/core-plugin-api@1.0.6-next.2
  - @backstage/integration-react@1.1.4-next.1
  - @backstage/plugin-airbrake@0.3.9-next.2
  - @backstage/plugin-api-docs@0.8.9-next.2
  - @backstage/plugin-azure-devops@0.2.0-next.2
  - @backstage/plugin-badges@0.2.33-next.2
  - @backstage/plugin-circleci@0.3.9-next.2
  - @backstage/plugin-cloudbuild@0.3.9-next.2
  - @backstage/plugin-code-coverage@0.2.2-next.2
  - @backstage/plugin-cost-insights@0.11.31-next.2
  - @backstage/plugin-dynatrace@0.2.0-next.2
  - @backstage/plugin-explore@0.3.40-next.2
  - @backstage/plugin-gcalendar@0.3.5-next.2
  - @backstage/plugin-gcp-projects@0.3.28-next.2
  - @backstage/plugin-github-actions@0.5.9-next.2
  - @backstage/plugin-gocd@0.1.15-next.1
  - @backstage/plugin-graphiql@0.2.41-next.2
  - @backstage/plugin-home@0.4.25-next.2
  - @backstage/plugin-jenkins@0.7.8-next.2
  - @backstage/plugin-kafka@0.3.9-next.2
  - @backstage/plugin-kubernetes@0.7.2-next.2
  - @backstage/plugin-lighthouse@0.3.9-next.2
  - @backstage/plugin-newrelic@0.3.27-next.2
  - @backstage/plugin-org@0.5.9-next.2
  - @backstage/plugin-pagerduty@0.5.2-next.2
  - @backstage/plugin-rollbar@0.4.9-next.2
  - @backstage/plugin-search@1.0.2-next.2
  - @backstage/plugin-sentry@0.4.2-next.2
  - @backstage/plugin-shortcuts@0.3.1-next.2
  - @backstage/plugin-stack-overflow@0.1.5-next.2
  - @backstage/plugin-tech-insights@0.3.0-next.2
  - @backstage/plugin-tech-radar@0.5.16-next.2
  - @backstage/plugin-todo@0.2.11-next.2
  - @backstage/plugin-user-settings@0.4.8-next.2
  - @backstage/plugin-search-react@1.1.0-next.2

## example-backend@0.2.75-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-kubernetes-backend@0.7.2-next.2
  - @backstage/backend-common@0.15.1-next.2
  - @backstage/integration@1.3.1-next.1
  - @backstage/plugin-catalog-backend@1.4.0-next.2
  - @backstage/plugin-scaffolder-backend@1.6.0-next.2
  - @backstage/plugin-auth-node@0.2.5-next.2
  - @backstage/plugin-techdocs-backend@1.3.0-next.1
  - @backstage/plugin-jenkins-backend@0.1.26-next.2
  - @backstage/catalog-client@1.0.5-next.1
  - @backstage/plugin-app-backend@0.3.36-next.2
  - @backstage/plugin-auth-backend@0.16.0-next.2
  - @backstage/plugin-azure-devops-backend@0.3.15-next.1
  - @backstage/plugin-code-coverage-backend@0.2.2-next.1
  - @backstage/plugin-graphql-backend@0.1.26-next.2
  - @backstage/plugin-permission-backend@0.5.11-next.1
  - @backstage/plugin-permission-common@0.6.4-next.1
  - @backstage/plugin-permission-node@0.6.5-next.2
  - @backstage/plugin-proxy-backend@0.2.30-next.1
  - @backstage/plugin-rollbar-backend@0.1.33-next.2
  - @backstage/plugin-todo-backend@0.1.33-next.1
  - @backstage/plugin-search-backend-module-elasticsearch@1.0.2-next.1
  - example-app@0.2.75-next.2
