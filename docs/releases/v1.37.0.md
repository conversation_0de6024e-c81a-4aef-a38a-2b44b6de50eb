---
id: v1.37.0
title: v1.37.0
description: Backstage Release v1.37.0
---

These are the release notes for the v1.37.0 release of [Backstage](https://backstage.io/).

A huge thanks to the whole team of maintainers and contributors as well as the amazing Backstage Community for the hard work in getting this release developed and done.

## Highlights

### BREAKING: Removals of Old Backend System support

We are gradually removing support for the old backend system from plugins and modules. In this release, the most significant such changes are the `app`, `events`, `proxy`, `search`, `techdocs`, and `user-settings` plugins and their modules. It also affects parts of the permissions framework.

If you have already [migrated to the new backend system](https://backstage.io/docs/backend-system/building-backends/migrating), you will not be required to do any code changes, except perhaps removing the `/alpha` suffix on your imports if you had that.

If you have not yet migrated, now would be a great time to do so.

Note that for the `@backstage/plugin-search-backend` and `@backstage/plugin-techdocs-backend` packages, this necessitated a major semantic version bump. The others are on version 0.x still and received a minor level bump. If you are using [the Backstage yarn plugin](https://backstage.io/docs/getting-started/keeping-backstage-updated#managing-package-versions-with-the-backstage-yarn-plugin), you will get on the new versions automatically. If you do not, you may want to bump your dependency ranges (to e.g. `^2.0.0` for the major bumps) for these to ensure that you keep getting the most recent updates.

Along with the major bump, most of the previously already deprecated exports were finally removed as well from these packages. There are a bit too many to list here, but as an example, some of the old deprecated exports from the Techdocs backend have been available from their new home in `@backstage/plugin-techdocs-node` instead.

An upshot of these changes is that we have nearly entirely eliminated the usage of the old `@backstage/backend-common` package. We hope to get rid of the very final remnants of it as soon as possible.

### BREAKING: Better Frontend Discovery

By default, your frontend will be using a more featureful discovery API implementation. The old one basically always pointed at your backend base URL. The new one lets you add a `discovery.endpoints` configuration section where you can easily point individual plugins to their own targets, simplifying the routing of split backend deployments without having to resort to implementing your own discovery. As the `discovery.endpoints` was already in use by the backend discovery service, this is marked as a breaking change, because it might cause any existing `discovery.endpoints` configuration to be picked up and used by the frontend discovery, which may break existing setups.

For example, consider the following configuration:

```yaml
app:
  baseUrl: https://backstage.acme.org

backend:
  baseUrl: https://backstage.internal.acme.org

discovery:
  endpoints:
    - target: https://catalog.internal.acme.org/api/{{pluginId}}
      plugins: [catalog]
```

This will now cause requests from the frontend towards the catalog plugin to be routed to `https://catalog.internal.acme.org/api/catalog`, but this might not be reachable from the frontend. To fix this, you should update the `discovery.endpoints` configuration to only override the internal URL of the plugin:

```
discovery:
  endpoints:
    - target:
        internal: https://catalog.internal.acme.org/api/{{pluginId}}
      plugins: [catalog]
```

### BREAKING: Linting Warnings

Our builtin CLI `lint` commands used to be unforgiving of warnings by default. This has now been relaxed, such that it instead behaves like `eslint` itself does: warnings are just warnings and do not trigger a failure. To restore the previous behavior, include the `--max-warnings 0` flag in your `backstage-cli <repo|package> lint` commands.

Contributed by [@schultzp2020](https://github.com/schultzp2020) in [#28699](https://github.com/backstage/backstage/pull/28699)

### BREAKING: Test Utilities

The `@backstage/backend-test-utils` database utilities now also cover Postgres major versions up to 17. If you were using the `TestDatabases` class without specifying what exact versions to test against, your tests will start covering Postgres 17 as well.

The deprecated `setupRequestMockHandlers` was also finally removed, please use `registerMswTestHooks` instead.

### Native Zod Schemas for `createTemplateAction`

We’ve revisited how to create Template Actions using the new `createTemplateAction` method. The new way has first class support for `zod` schemas, so you no longer need to think about `zodToJsonSchema`, or manually creating the JSON schema and the type parameters for typing the inputs and outputs for the actions.

The new API also drops support for `ctx.logStream` as this has been deprecated and will be removed in future versions.

Example usage:

```ts
createTemplateAction({
  id: 'test',
  schema: {
    input: {
      repoUrl: z => z.string(),
    },
    output: {
      test: z => z.string(),
    },
  },
  handler: async ctx => {
    // you can just use ctx.logger.log('...'), or if
    // you really need a log stream you can do this:
    const logStream = new PassThrough();
    logStream.on('data', chunk => {
      ctx.logger.info(chunk.toString());
    });
  },
});
```

There’s also some more information in the [changeset](https://github.com/backstage/backstage/blob/a90075af6605d452abce6561989a88784da9a942/.changeset/dull-olives-itch.md).

### New Frontend System improvements

There’s now [a `DialogApi`](https://backstage.io/docs/reference/frontend-plugin-api.dialogapi/), which greatly helps with placing dialogs in front of your users - in particular from code that lives outside the React tree, such as in callbacks.

At the same time, an `EntityHeaderBlueprint` lets you specifically replace the entity page headers. You can now also group multiple entity page contents into a single tab. A tab that groups more than one entity content is rendered as a dropdown menu. You can use the default list of tab groups or create your own. Groups are sorted in the same order as they are defined in the `app-config.yaml` file. The association between an entity content and a tab group can be optionally done when creating the content extension or via the `app-config.yaml` file. See usage examples in the [#28701](https://github.com/backstage/backstage/pull/28701) and [#29043](https://github.com/backstage/backstage/pull/29043) pull requests.

Another entity page feature introduced in this release is custom entity overview content layouts. It is now possible to have multiple overview content layouts. The layouts will be selected based on an entity filter defined when creating the layout extension or later via `app-config-file.` This change also allowed us to improve the default entity overview content layout. It now has 3 different types of areas that you can optionally assign to overview cards and have them rendered as a summary, information, or content card. Check out the following pull requests for more information: [#28758](https://github.com/backstage/backstage/pull/28758),[#28950](https://github.com/backstage/backstage/pull/28950), [#28928](https://github.com/backstage/backstage/pull/28928) and [#29145](https://github.com/backstage/backstage/pull/29145).

The concept of an `extensionFactoryMiddleware` was also added, which lets you add behaviors globally around any extension factory. This is useful for injecting debugging, analytics and much more. Contributed by [@iamEAP](https://github.com/iamEAP) in [#29068](https://github.com/backstage/backstage/pull/29068).

Another exciting development is that the catalog gradually is adopting a new query format. For now it’s only used in the new frontend system for specifying the conditions under which e.g. a card should appear on the entity page. Eventually, it may be extended to the server side of the catalog as well to allow for more expressive queries. Check out the [entity predicates documentation](https://backstage.io/docs/features/software-catalog/catalog-customization#entity-predicate-queries) if you are curious!

A bit of a lower level (but technically breaking) change is that the return type of `createSpecializedApp` was changed to allow for better iteration on it in the future. You are unlikely to have been using this internal function, but if you did, more information is [in the PR](https://github.com/backstage/backstage/pull/29125). The opaque old `ExtensionOverrides` and `BackstagePlugin` types were removed as well. This should not have an effect on end users, unless you are using a mixture of a new frontend framework and rather old plugins - if you see this causing any issues, please try updating the plugins as well.

### Version Bump of TypeScript

In accordance with our versioning policy, new Backstage installations will now be based on TypeScript version 5.8, while our internal development moves up to version 5.6.

We recommend that you upgrade the TypeScript version in your own repositories to at least version 5.6 so that you are guaranteed to be up to date with the features used by libraries that we publish.

### Delete “Provider Orphaned” Entities

It used to be that if you removed an entity provider, the entities it had put in the catalog remained and needed manual cleanup. There’s now a new `catalog.orphanProviderStrategy` configuration parameter that lets you opt in to having these be automatically deleted instead. Contributed by [@UsainBloot](https://github.com/UsainBloot) in [#28971](https://github.com/backstage/backstage/pull/28971)

## Security Fixes

This release does not contain any security fixes.

## Upgrade path

We recommend that you keep your Backstage project up to date with this latest release. For more guidance on how to upgrade, check out the documentation for [keeping Backstage updated](https://backstage.io/docs/getting-started/keeping-backstage-updated).

## Links and References

Below you can find a list of links and references to help you learn about and start using this new release.

- [Backstage official website](https://backstage.io/), [documentation](https://backstage.io/docs/), and [getting started guide](https://backstage.io/docs/getting-started/)
- [GitHub repository](https://github.com/backstage/backstage)
- Backstage's [versioning and support policy](https://backstage.io/docs/overview/versioning-policy)
- [Community Discord](https://discord.gg/backstage-687207715902193673) for discussions and support
- [Changelog](https://github.com/backstage/backstage/tree/master/docs/releases/v1.37.0-changelog.md)
- Backstage [Demos](https://backstage.io/demos), [Blog](https://backstage.io/blog), [Roadmap](https://backstage.io/docs/overview/roadmap) and [Plugins](https://backstage.io/plugins)

Sign up for our [newsletter](https://info.backstage.spotify.com/newsletter_subscribe) if you want to be informed about what is happening in the world of Backstage.
