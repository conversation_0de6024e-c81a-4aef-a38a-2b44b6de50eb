# Release v1.9.0

## @backstage/backend-common@0.17.0

### Minor Changes

- de8a975911: Changed to use native `AbortController` and `AbortSignal` from Node.js, instead
  of the one from `node-abort-controller`. This is possible now that the minimum
  supported Node.js version of the project is 16.

  Note that their interfaces are very slightly different, but typically not in a
  way that matters to consumers. If you see any typescript errors as a direct
  result from this, they are compatible with each other in the ways that we
  interact with them, and should be possible to type-cast across without ill
  effects.

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- 98776e638a: Fixed GitlabUrlReader to include api tokens in API calls
- 1f2b2de3fe: exported KubernetesContainerRunner, KubernetesContainerRunnerOptions, KubernetesContainerRunnerMountBase
- 840f2113c6: Fix `GitlabUrlReader.readTree` bug when there were no matching commits
- 20a5161f04: Adds MySQL support for the catalog-backend
- **********: Updated dependency `msw` to `^0.49.0`.
- 9ce7866ecd: Updated dependency `@kubernetes/client-node` to `0.18.0`.
- 3c1302c07d: Updated dependency `@types/http-errors` to `^2.0.0`.
- 6b82598bd8: Added the ability to understand Job Artifact URLs to the GitLab integration
- dfc8edf9c5: Internal refactor to avoid usage of deprecated symbols.
- 8015ff1258: Tweaked wording to use inclusive terminology
- 8646067e07: Fixed `SingleHostDiscovery` so that it properly handles single-string `backend.listen` configurations such as `:80`.
- Updated dependencies
  - @backstage/errors@1.1.4
  - @backstage/config-loader@1.1.7
  - @backstage/integration@1.4.1
  - @backstage/types@1.0.2
  - @backstage/cli-common@0.1.11
  - @backstage/config@1.0.5

## @backstage/backend-plugin-api@0.2.0

### Minor Changes

- 884d749b14: **BREAKING**: All core service references are now exported via a single `coreServices` object. For example, the `loggerServiceRef` is now accessed via `coreServices.logger` instead.
- a025190552: **BREAKING**: All service interfaces are now suffixed with `*Service`.

### Patch Changes

- cb1c2781c0: Updated `LoggerService` interface with more log methods and meta.
- d6dbf1792b: Added initial support for registering shutdown hooks via `lifecycleServiceRef`.
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/config@1.0.5

## @backstage/backend-tasks@0.4.0

### Minor Changes

- de8a975911: Changed to use native `AbortController` and `AbortSignal` from Node.js, instead
  of the one from `node-abort-controller`. This is possible now that the minimum
  supported Node.js version of the project is 16.

  Note that their interfaces are very slightly different, but typically not in a
  way that matters to consumers. If you see any typescript errors as a direct
  result from this, they are compatible with each other in the ways that we
  interact with them, and should be possible to type-cast across without ill
  effects.

### Patch Changes

- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/errors@1.1.4
  - @backstage/types@1.0.2
  - @backstage/config@1.0.5

## @backstage/catalog-client@1.2.0

### Minor Changes

- 00d90b520a: **BREAKING PRODUCERS**: Added a new `getEntitiesByRefs` endpoint to `CatalogApi`, for efficient batch fetching of entities by ref.

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/errors@1.1.4
  - @backstage/catalog-model@1.1.4

## @backstage/cli@0.22.0

### Minor Changes

- 736f893f72: The Jest configuration that was previously enabled with `BACKSTAGE_NEXT_TESTS` is now enabled by default. To revert to the old configuration you can now instead set `BACKSTAGE_OLD_TESTS`.

  This new configuration uses the `babel` coverage provider rather than `v8`. It used to be that `v8` worked better when using Sucrase for transpilation, but now that we have switched to SWC, `babel` seems to work better. In addition, the new configuration also enables source maps by default, as they no longer have a negative impact on code coverage accuracy, and it also enables a modified Jest runtime with additional caching of script objects.

### Patch Changes

- 91d050c140: changed tests created by create-plugin to follow eslint-rules best practices particularly testing-library/prefer-screen-queries and testing-library/render-result-naming-convention
- 43b2b9c791: Removed the unused dependency on `@sucrase/jest-plugin`.
- dd721148b5: Updated Jest coverage configuration to only apply either in the root project or package configuration, depending on whether repo or package tests are run.
- 5850ef9b84: Fix webpack dev server issue where it wasn't serving `index.html` from correct endpoint on subsequent requests.
- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- 459a3457e1: Bump `msw` version in default plugin/app templates
- c27eabef6b: Adds new web-library package option when generating a new plugin
- 8fffe42708: JSX and React Fast Refresh transforms are no longer enabled when bundling backend code.
- 309f2daca4: Updated dependency `esbuild` to `^0.16.0`.
- ee14bab716: Updated dependency `minimatch` to `5.1.1` and switch version range to `^`.
- **********: Updated dependency `msw` to `^0.49.0`.
- ed0cf59c59: Updated dependency `@rollup/plugin-commonjs` to `^23.0.0`.
- 16b7c2fccd: Updated dependency `@rollup/plugin-yaml` to `^4.0.0`.
- 086c0bbb45: Updated dependency `@rollup/plugin-json` to `^5.0.0`.
- 8015ff1258: Tweaked wording to use inclusive terminology
- d9d9a7a134: Removed all copyright notices from package templates.
- 8e0358e18d: Added `--skip-install` parameter to `backstage-cli versions:bump`
- Updated dependencies
  - @backstage/errors@1.1.4
  - @backstage/config-loader@1.1.7
  - @backstage/release-manifests@0.0.8
  - @backstage/types@1.0.2
  - @backstage/cli-common@0.1.11
  - @backstage/config@1.0.5

## @backstage/core-app-api@1.3.0

### Minor Changes

- e0d9c9559a: Added a new `AppRouter` component and `app.createRoot()` method that replaces `app.getRouter()` and `app.getProvider()`, which are now deprecated. The new `AppRouter` component is a drop-in replacement for the old router component, while the new `app.createRoot()` method is used instead of the old provider component.

  An old app setup might look like this:

  ```tsx
  const app = createApp(/* ... */);

  const AppProvider = app.getProvider();
  const AppRouter = app.getRouter();

  const routes = ...;

  const App = () => (
    <AppProvider>
      <AlertDisplay />
      <OAuthRequestDialog />
      <AppRouter>
        <Root>{routes}</Root>
      </AppRouter>
    </AppProvider>
  );

  export default App;
  ```

  With these new APIs, the setup now looks like this:

  ```tsx
  import { AppRouter } from '@backstage/core-app-api';

  const app = createApp(/* ... */);

  const routes = ...;

  export default app.createRoot(
    <>
      <AlertDisplay />
      <OAuthRequestDialog />
      <AppRouter>
        <Root>{routes}</Root>
      </AppRouter>
    </>,
  );
  ```

  Note that `app.createRoot()` accepts a React element, rather than a component.

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- b4b5b02315: Tweak feature flag registration so that it happens immediately before the first rendering of the app, rather than just after.
- 6870b43dd1: Fix for the automatic rewriting of base URLs.
- 203271b746: Prevent duplicate feature flag components from rendering in the settings when using <FeatureFlagged /> components
- **********: Updated dependency `msw` to `^0.49.0`.
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- 8015ff1258: Tweaked wording to use inclusive terminology
- 653d7912ac: Made `WebStorage` notify its subscribers when `localStorage` values change in other tabs/windows
- 63310e3987: Apps will now rewrite the `app.baseUrl` configuration to match the current `location.origin`. The `backend.baseUrl` will also be rewritten in the same way when the `app.baseUrl` and `backend.baseUrl` have matching origins. This will reduce the need for separate frontend builds for different environments.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/version-bridge@1.0.3
  - @backstage/types@1.0.2
  - @backstage/config@1.0.5

## @backstage/core-plugin-api@1.2.0

### Minor Changes

- 9a1864976a: Added a new `display` property to the `AlertMessage` which can accept the values `permanent` or `transient`.

  Here's a rough example of how to trigger an alert using the new `display` property:

  ```ts
  import { alertApiRef, useApi } from '@backstage/core-plugin-api';

  const ExampleTransient = () => {
    const alertApi = useApi(alertApiRef);
    alertApi.post({
      message: 'Example of Transient Alert',
      severity: 'success',
      display: 'transient',
    });
  };
  ```

### Patch Changes

- d56127c712: useRouteRef - Limit re-resolving to location pathname changes only
- **********: Updated dependency `msw` to `^0.49.0`.
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/version-bridge@1.0.3
  - @backstage/types@1.0.2
  - @backstage/config@1.0.5

## @backstage/integration-aws-node@0.1.0

### Minor Changes

- 13278732f6: New package for AWS integration node library

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.1.4
  - @backstage/config@1.0.5

## @backstage/repo-tools@0.1.0

### Minor Changes

- 99713fd671: Introducing repo-tools package
- 03843259b4: Api reference documentation improvements

  - breadcrumbs links semantics as code spans
  - new `@config` annotation to describe related config keys

### Patch Changes

- 9b1193f277: declare dependencies

- a8611bcac4: Add new command options to the `api-report`

  - added `--allow-warnings`, `-a` to continue processing packages if selected packages have warnings
  - added `--allow-all-warnings` to continue processing packages any packages have warnings
  - added `--omit-messages`, `-o` to pass some warnings messages code to be omitted from the api-report.md files
  - The `paths` argument for this command now takes as default the value on `workspaces.packages` inside the root package.json
  - change the path resolution to use the `@backstage/cli-common` packages instead

- 25ec5c0c3a: Include asset-types.d.ts while running the api report command

- 71f80eb354: add the command type-deps to the repo tool package.

- ac440299ef: Updated api docs generation to be compatible with Docusaurus 2-alpha and 2.x.

- Updated dependencies
  - @backstage/errors@1.1.4
  - @backstage/cli-common@0.1.11

## @backstage/plugin-catalog@1.7.0

### Minor Changes

- 6ffa47bb0a: Fixes in kind selectors (now `OwnershipCard` works again). `EntityKindPicker` now accepts an optional `allowedKinds` prop, just like `CatalogKindHeader`.
- 462c1d012e: Removed `CatalogKindHeader` from `DefaultCatalogPage`. Deprecated `CatalogKindHeader` in favour of `EntityKindPicker`.

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- ca04d97b09: Handle refresh entity error in `AboutCard`.
- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- a19cffbeed: Update search links to only have header as linkable text
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- 387d1d5218: Fixed Entity kind pluralisation in the `CatalogKindHeader` component.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/catalog-client@1.2.0
  - @backstage/plugin-search-react@1.3.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/integration-react@1.1.7
  - @backstage/types@1.0.2
  - @backstage/plugin-search-common@1.2.0
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9

## @backstage/plugin-catalog-backend@1.6.0

### Minor Changes

- 16891a212c: Added new `POST /entities/by-refs` endpoint, which allows you to efficiently
  batch-fetch entities by their entity ref. This can be useful e.g. in graphql
  resolvers or similar contexts where you need to fetch many entities at the same
  time.

- 273ba3a77f: Deprecated Prometheus metrics in favour of OpenTelemtry metrics.

- c395abb5b2: The catalog no longer stops after the first processor `validateEntityKind`
  method returns `true` when validating entity kind shapes. Instead, it continues
  through all registered processors that have this method, and requires that _at
  least one_ of them returned true.

  The old behavior of stopping early made it harder to extend existing core kinds
  with additional fields, since the `BuiltinKindsEntityProcessor` is always
  present at the top of the processing chain and ensures that your additional
  validation code would never be run.

  This is technically a breaking change, although it should not affect anybody
  under normal circumstances, except if you had problematic validation code that
  you were unaware that it was not being run. That code may now start to exhibit
  those problems.

  If you need to disable this new behavior, `CatalogBuilder` as used in your
  `packages/backend/src/plugins/catalog.ts` file now has a
  `useLegacySingleProcessorValidation()` method to go back to the old behavior.

  ```diff
   const builder = await CatalogBuilder.create(env);
  +builder.useLegacySingleProcessorValidation();
  ```

- 3072ebfdd7: The search table also holds the original entity value now and the facets endpoint fetches the filtered entity data from the search table.

### Patch Changes

- ba13ff663c: Added a new `catalog.rules[].location` configuration that makes it possible to configure catalog rules to only apply to specific locations, either via exact match or a glob pattern.
- d8593ce0e6: Do not use deprecated `LocationSpec` from the `@backstage/plugin-catalog-node` package
- c507aee8a2: Ensured typescript type checks in migration files.
- 2a8e3cc0b5: Optimize `Stitcher` process to be more memory efficient
- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- eacc8e2b55: Make it possible for entity providers to supply only entity refs, instead of full entities, in `delta` mutation deletions.
- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- 5b3e2afa45: Fixed deprecated use of `substr` into `substring`.
- 71147d5c16: Internal code reorganization.
- 93870e4df1: Track the last time the final entity changed with new timestamp "last updated at" data in final entities database, which gets updated with the time when final entity is updated.
- 20a5161f04: Adds MySQL support for the catalog-backend
- **********: Updated dependency `msw` to `^0.49.0`.
- e982f77fe3: Registered shutdown hook in experimental catalog plugin.
- b3fac9c107: Ignore attempts at emitting the current entity as a child of itself.
- Updated dependencies
  - @backstage/catalog-client@1.2.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-catalog-node@1.3.0
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/plugin-permission-node@0.7.2
  - @backstage/errors@1.1.4
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/integration@1.4.1
  - @backstage/types@1.0.2
  - @backstage/plugin-search-common@1.2.0
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/plugin-catalog-common@1.0.9
  - @backstage/plugin-scaffolder-common@1.2.3

## @backstage/plugin-catalog-backend-module-incremental-ingestion@0.1.0

### Minor Changes

- 98c643a1a2: Introduces incremental entity providers, which are used for streaming very large data sources into the catalog.

### Patch Changes

- c507aee8a2: Ensured typescript type checks in migration files.

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.

- de8a975911: Changed to use native `AbortController` and `AbortSignal` from Node.js, instead
  of the one from `node-abort-controller`. This is possible now that the minimum
  supported Node.js version of the project is 16.

  Note that their interfaces are very slightly different, but typically not in a
  way that matters to consumers. If you see any typescript errors as a direct
  result from this, they are compatible with each other in the ways that we
  interact with them, and should be possible to type-cast across without ill
  effects.

- 05a928e296: Updated usages of types from `@backstage/backend-plugin-api`.

- 61d4efe978: Make incremental providers more resilient to failures

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-catalog-node@1.3.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/backend-test-utils@0.1.31
  - @backstage/errors@1.1.4
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-catalog-node@1.3.0

### Minor Changes

- eacc8e2b55: Make it possible for entity providers to supply only entity refs, instead of full entities, in `delta` mutation deletions.

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0
  - @backstage/errors@1.1.4
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4
  - @backstage/plugin-catalog-common@1.0.9

## @backstage/plugin-events-backend@0.2.0

### Minor Changes

- cf41eedf43: **BREAKING:** Remove required field `router` at `HttpPostIngressEventPublisher.fromConfig`
  and replace it with `bind(router: Router)`.
  Additionally, the path prefix `/http` will be added inside `HttpPostIngressEventPublisher`.

  ```diff
  // at packages/backend/src/plugins/events.ts
     const eventsRouter = Router();
  -  const httpRouter = Router();
  -  eventsRouter.use('/http', httpRouter);

     const http = HttpPostIngressEventPublisher.fromConfig({
       config: env.config,
       logger: env.logger,
  -    router: httpRouter,
     });
  +  http.bind(eventsRouter);
  ```

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.

- cf41eedf43: Introduce a new interface `RequestDetails` to abstract `Request`
  providing access to request body and headers.

  **BREAKING:** Replace `request: Request` with `request: RequestDetails` at `RequestValidator`.

- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/plugin-events-node@0.2.0
  - @backstage/config@1.0.5

## @backstage/plugin-events-node@0.2.0

### Minor Changes

- cf41eedf43: Introduce a new interface `RequestDetails` to abstract `Request`
  providing access to request body and headers.

  **BREAKING:** Replace `request: Request` with `request: RequestDetails` at `RequestValidator`.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0

## @backstage/plugin-kubernetes-backend@0.9.0

### Minor Changes

- 2db8acffe7: Kubernetes plugin now gracefully surfaces transport-level errors (like DNS or timeout, or other socket errors) occurring while fetching data. This will be merged into any data that is fetched successfully, fixing a bug where the whole page would be empty if any fetch operation encountered such an error.

### Patch Changes

- 22e20b3a59: Clusters declared in the app-config can now have their CA configured via a local filesystem path using the `caFile` property.
- 9ce7866ecd: Updated dependency `@kubernetes/client-node` to `0.18.0`.
- b585179770: Added Kubernetes proxy API route to backend Kubernetes plugin, allowing Backstage plugin developers to read/write new information from Kubernetes (if proper credentials are provided).
- Updated dependencies
  - @backstage/plugin-kubernetes-common@0.5.0
  - @backstage/catalog-client@1.2.0
  - @backstage/backend-common@0.17.0
  - @backstage/backend-test-utils@0.1.31
  - @backstage/errors@1.1.4
  - @backstage/plugin-auth-node@0.2.8
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-kubernetes-common@0.5.0

### Minor Changes

- 2db8acffe7: Kubernetes plugin now gracefully surfaces transport-level errors (like DNS or timeout, or other socket errors) occurring while fetching data. This will be merged into any data that is fetched successfully, fixing a bug where the whole page would be empty if any fetch operation encountered such an error.

### Patch Changes

- 9ce7866ecd: Updated dependency `@kubernetes/client-node` to `0.18.0`.
- b585179770: Added Kubernetes proxy API route to backend Kubernetes plugin, allowing Backstage plugin developers to read/write new information from Kubernetes (if proper credentials are provided).
- Updated dependencies
  - @backstage/catalog-model@1.1.4

## @backstage/plugin-scaffolder@1.9.0

### Minor Changes

- ddd1c3308d: Implement Custom Field Explorer to view and play around with available installed custom field extensions
- adb1b01e32: Adds the ability to supply a `transformErrors` function to the `Stepper` for `/next`
- 34a48cdc4f: The `RepoUrlPicker` field extension now has an `allowedProjects` option for narrowing the selection of Bitbucket URLs.

### Patch Changes

- d4d07cf55e: Enabling the customization of the last step in the scaffolder template.

  To override the content you have to do the next:

  ```typescript jsx
  <TemplatePage ReviewStepComponent={YourCustomComponent} />
  ```

- ef803022f1: Initialize all `formData` in the `Stepper` in `/next`

- 9b1fadf6d8: Added `noHtml5Validate` prop to `FormProps` on `NextScaffolderPage`

- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.

- 9000952e87: Form data is now passed to validator functions in 'next' scaffolder, so it's now possible to perform validation for fields that depend on other field values. This is something that we discourage due to the coupling that it creates, but is sometimes still the most sensible solution.

  ```typescript jsx
  export const myCustomValidation = (
    value: string,
    validation: FieldValidation,
    { apiHolder, formData }: { apiHolder: ApiHolder; formData: JsonObject },
  ) => {
    // validate
  };
  ```

- 5b10b2485a: Parse `formData` from `window.location.query` for `scaffolder/next`

- 57ad6553d0: Pass through `transformErrors` to `TemplateWizardPage`

- **********: Updated dependency `msw` to `^0.49.0`.

- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.

- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.

- 5fb6d5e92e: Updated dependency `@react-hookz/web` to `^19.0.0`.

- 146378c146: Updated dependency `@react-hookz/web` to `^20.0.0`.

- 380f549b75: bump `@rjsf/*-v5` dependencies

- a63e2df559: fixed `headerOptions` not passed to `TemplatePage` component

- 9b606366bf: Bump `json-schema-library` to version `^7.3.9` which does not pull in the `gson-pointer` library

- db6310b6a0: Show input type array correctly on installed actions page.

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/catalog-client@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/plugin-permission-react@0.4.8
  - @backstage/integration-react@1.1.7
  - @backstage/integration@1.4.1
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9
  - @backstage/plugin-scaffolder-common@1.2.3

## @backstage/plugin-scaffolder-backend@1.9.0

### Minor Changes

- a20a0ea698: Added `requiredConversationResolution` template option to `github:repo:create`, `github:repo:push` and `publish:github`

- b32005e98a: Deprecated the `taskWorkers` option in RouterOptions in favor of `concurrentTasksLimit` which sets the limit of concurrent tasks in a single TaskWorker

  TaskWorker can now run multiple (defaults to 10) tasks concurrently using the `concurrentTasksLimit` option available in both `RouterOptions` and `CreateWorkerOptions`.

  To use the option to create a TaskWorker:

  ```diff
  const worker = await TaskWorker.create({
      taskBroker,
      actionRegistry,
      integrations,
      logger,
      workingDirectory,
      additionalTemplateFilters,
  +   concurrentTasksLimit: 10 // (1 to Infinity)
  });
  ```

- fc51bd8aa0: Add support for disabling Github repository wiki, issues and projects

- 0053d07bee: Update the `github:publish` action to allow passing wether to dismiss stale reviews on the protected default branch.

### Patch Changes

- cb716004ef: Internal refactor to improve tests
- 935b66a646: Change step output template examples to use square bracket syntax.
- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- 26404430bc: Use Json types from @backstage/types
- b07ccffad0: Backend now returns 'ui:options' value from template metadata, it can be used by all your custom scaffolder components.
- 309f2daca4: Updated dependency `esbuild` to `^0.16.0`.
- **********: Updated dependency `msw` to `^0.49.0`.
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0
  - @backstage/catalog-client@1.2.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-catalog-node@1.3.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/errors@1.1.4
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/integration@1.4.1
  - @backstage/plugin-auth-node@0.2.8
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/plugin-scaffolder-common@1.2.3

## @backstage/plugin-search-backend@1.2.0

### Minor Changes

- 29ebc43a0b: numberOfResults is now provided alongside the query result

### Patch Changes

- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- Updated dependencies
  - @backstage/plugin-search-backend-node@1.1.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/plugin-permission-node@0.7.2
  - @backstage/errors@1.1.4
  - @backstage/plugin-auth-node@0.2.8
  - @backstage/types@1.0.2
  - @backstage/plugin-search-common@1.2.0
  - @backstage/config@1.0.5

## @backstage/plugin-search-backend-module-elasticsearch@1.1.0

### Minor Changes

- 29ebc43a0b: numberOfResults is now provided alongside the query result
- dff9843718: The search engine now better handles the case when it receives 0 documents at index-time. Prior to this change, the indexer would replace any existing index with an empty index, effectively deleting it. Now instead, a warning is logged, and any existing index is left alone (preserving the index from the last successful indexing attempt).
- d09485ea79: Added support for self hosted OpenSearch via new provider

### Patch Changes

- 45eb4d23cf: Fixed a bug that prevented indices from being cleaned up under some circumstances, which could have led to shard exhaustion.
- Updated dependencies
  - @backstage/plugin-search-backend-node@1.1.0
  - @backstage/plugin-search-common@1.2.0
  - @backstage/config@1.0.5

## @backstage/plugin-search-backend-module-pg@0.5.0

### Minor Changes

- e48fc1f1ae: Added the option to pass a logger to `PgSearchEngine` during instantiation. You may do so as follows:

  ```diff
  const searchEngine = await PgSearchEngine.fromConfig(env.config, {
    database: env.database,
  + logger: env.logger,
  });
  ```

- dff9843718: The search engine now better handles the case when it receives 0 documents at index-time. Prior to this change, the indexer would replace any existing index with an empty index, effectively deleting it. Now instead, a warning is logged, and any existing index is left alone (preserving the index from the last successful indexing attempt).

### Patch Changes

- c507aee8a2: Ensured typescript type checks in migration files.
- Updated dependencies
  - @backstage/plugin-search-backend-node@1.1.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-search-common@1.2.0
  - @backstage/config@1.0.5

## @backstage/plugin-search-backend-node@1.1.0

### Minor Changes

- 29ebc43a0b: numberOfResults is now provided alongside the query result
- dff9843718: The search engine now better handles the case when it receives 0 documents at index-time. Prior to this change, the indexer would replace any existing index with an empty index, effectively deleting it. Now instead, a warning is logged, and any existing index is left alone (preserving the index from the last successful indexing attempt).

### Patch Changes

- a962ce0551: Wait for indexer initialization before finalizing indexing.

- de8a975911: Changed to use native `AbortController` and `AbortSignal` from Node.js, instead
  of the one from `node-abort-controller`. This is possible now that the minimum
  supported Node.js version of the project is 16.

  Note that their interfaces are very slightly different, but typically not in a
  way that matters to consumers. If you see any typescript errors as a direct
  result from this, they are compatible with each other in the ways that we
  interact with them, and should be possible to type-cast across without ill
  effects.

- 683ced83f6: Fixed a bug that could cause a `max listeners exceeded warning` to be logged when more than 10 collators were running simultaneously.

- 81b1e7b0fe: Updated indexer and decorator base classes to take advantage of features introduced in Node.js v16; be sure you are running a [supported version of Node.js](https://backstage.io/docs/releases/v1.8.0#node-16-and-18).

- 54c5836f7a: Use of `TestPipeline.withSubject()` is now deprecated. Instead, use the `fromCollator`, `fromDecorator`, or `fromIndexer` static methods to instantiate a test pipeline. You may also use the class' `withCollator`, `withDecorator`, and `withIndexer` instance methods to build test pipelines that consist of multiple test subjects.

- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/errors@1.1.4
  - @backstage/plugin-search-common@1.2.0
  - @backstage/config@1.0.5

## @backstage/plugin-search-common@1.2.0

### Minor Changes

- 29ebc43a0b: numberOfResults (total number of results for a given query) can now be provided by each search engine and consumed as part of the search results response

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/types@1.0.2

## @backstage/plugin-search-react@1.3.0

### Minor Changes

- 29ebc43a0b: The `value` of a search analytics event is now set as the total number of search results (when available)

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- a19cffbeed: Update search links to only have header as linkable text
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/version-bridge@1.0.3
  - @backstage/types@1.0.2
  - @backstage/plugin-search-common@1.2.0
  - @backstage/theme@0.2.16

## @backstage/plugin-sonarqube@0.6.0

### Minor Changes

- 6b59903bfa: Parts of plugin-sonarqube have been moved into a new plugin-sonarqube-react package. Additionally some types that were
  previously internal to plugin-sonarqube have been made public and will allow access for third-parties. As the sonarqube
  plugin has not yet reached 1.0 breaking changes are expected in the future. As such exports of plugin-sonarqube-react
  require importing via the `/alpha` entrypoint:

  ```ts
  import { sonarQubeApiRef } from '@backstage/plugin-sonarqube-react/alpha';

  const sonarQubeApi = useApi(sonarQubeApiRef);
  ```

  Moved from plugin-sonarqube to plugin-sonarqube-react:

  - isSonarQubeAvailable
  - SONARQUBE_PROJECT_KEY_ANNOTATION

  Exports that been introduced to plugin-sonarqube-react are documented in the [API report](https://github.com/backstage/backstage/blob/v1.9.0/plugins/sonarqube-react/api-report.md).

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 17a8e32f39: Updated dependency `rc-progress` to `3.4.1`.
- 3dee2f5ad0: Added links to the frontend and backend plugins in the readme.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/plugin-sonarqube-react@0.1.0
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-sonarqube-react@0.1.0

### Minor Changes

- 6b59903bfa: Parts of plugin-sonarqube have been moved into a new plugin-sonarqube-react package. Additionally some types that were
  previously internal to plugin-sonarqube have been made public and will allow access for third-parties. As the sonarqube
  plugin has not yet reached 1.0 breaking changes are expected in the future. As such exports of plugin-sonarqube-react
  require importing via the `/alpha` entrypoint:

  ```ts
  import { sonarQubeApiRef } from '@backstage/plugin-sonarqube-react/alpha';

  const sonarQubeApi = useApi(sonarQubeApiRef);
  ```

  Moved from plugin-sonarqube to plugin-sonarqube-react:

  - isSonarQubeAvailable
  - SONARQUBE_PROJECT_KEY_ANNOTATION

  Exports that been introduced to plugin-sonarqube-react are documented in the [API report](https://github.com/backstage/backstage/blob/v1.9.0/plugins/sonarqube-react/api-report.md).

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/catalog-model@1.1.4

## @backstage/plugin-techdocs-backend@1.5.0

### Minor Changes

- dfbdae092e: Added a new optional `accountId` to the configuration options of the AWS S3 publisher. Configuring this option will source credentials for the `accountId` in the `aws` app config section. See <https://github.com/backstage/backstage/blob/master/packages/integration-aws-node/README.md> for more details.

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/plugin-techdocs-node@1.4.3
  - @backstage/errors@1.1.4
  - @backstage/integration@1.4.1
  - @backstage/plugin-search-common@1.2.0
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/plugin-catalog-common@1.0.9

## @backstage/plugin-techdocs-react@1.1.0

### Minor Changes

- 786f1b1419: Support older versions of react-router

### Patch Changes

- cb716004ef: Internal refactor to improve tests
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/version-bridge@1.0.3
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-user-settings@0.6.0

### Minor Changes

- 29bdda5442: Added the ability to fully customize settings page. Deprecated UserSettingsTab in favour of SettingsLayout.Route

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/core-app-api@1.3.0
  - @backstage/errors@1.1.4
  - @backstage/types@1.0.2
  - @backstage/theme@0.2.16

## @backstage/app-defaults@1.0.9

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/core-app-api@1.3.0
  - @backstage/plugin-permission-react@0.4.8
  - @backstage/theme@0.2.16

## @backstage/backend-app-api@0.2.4

### Patch Changes

- cb1c2781c0: Updated logger implementations to match interface changes.
- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- afa3bf5657: Added `.stop()` method to `Backend`.
- d6dbf1792b: Added `lifecycleFactory` implementation.
- 05a928e296: Updated usages of types from `@backstage/backend-plugin-api`.
- 5260d8fc7d: Root scoped services are now always initialized, regardless of whether they're used by any features.
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/plugin-permission-node@0.7.2
  - @backstage/errors@1.1.4
  - @backstage/backend-plugin-api@0.2.0

## @backstage/backend-defaults@0.1.4

### Patch Changes

- d6dbf1792b: Added `lifecycleFactory` to default service factories.
- Updated dependencies
  - @backstage/backend-app-api@0.2.4
  - @backstage/backend-plugin-api@0.2.0

## @backstage/backend-test-utils@0.1.31

### Patch Changes

- afa3bf5657: Backends started with `startTestBackend` are now automatically stopped after all tests have run.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-app-api@0.2.4
  - @backstage/cli@0.22.0
  - @backstage/backend-common@0.17.0
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/config@1.0.5

## @backstage/catalog-model@1.1.4

### Patch Changes

- Updated dependencies
  - @backstage/errors@1.1.4
  - @backstage/types@1.0.2
  - @backstage/config@1.0.5

## @backstage/cli-common@0.1.11

### Patch Changes

- 8015ff1258: Tweaked wording to use inclusive terminology

## @backstage/codemods@0.1.42

### Patch Changes

- Updated dependencies
  - @backstage/cli-common@0.1.11

## @backstage/config@1.0.5

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2

## @backstage/config-loader@1.1.7

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 40e7e6e1a2: Updated dependency `typescript-json-schema` to `^0.55.0`.
- Updated dependencies
  - @backstage/errors@1.1.4
  - @backstage/types@1.0.2
  - @backstage/cli-common@0.1.11
  - @backstage/config@1.0.5

## @backstage/core-components@0.12.1

### Patch Changes

- a236a8830d: Update sidebar icon alignment

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals

- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries

- ea4a5be8f3: Create a variable for minimum height and add a prop named 'fit' for determining if the graph height should grow or be contained.

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.

- d2e3bf6737: Made AlertDisplay not crash on undefined messages

- 64a579a998: Add items prop to SupportButton. This prop can be used to override the items that would otherwise be grabbed from the config.

- 5d3058355d: Add `react/forbid-elements` linter rule for button, suggest Material UI `Button`

- **********: Updated dependency `msw` to `^0.49.0`.

- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.

- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.

- 5fb6d5e92e: Updated dependency `@react-hookz/web` to `^19.0.0`.

- 17a8e32f39: Updated dependency `rc-progress` to `3.4.1`.

- 146378c146: Updated dependency `@react-hookz/web` to `^20.0.0`.

- dfc8edf9c5: Internal refactor to avoid usage of deprecated symbols.

- 8015ff1258: Tweaked wording to use inclusive terminology

- 830687539f: Sync components in @backstage/core-components with the Component Design Guidelines

- 1ae86ab5fb: Added an option to allow the `AlertMessage` to be self-closing. This is done with a new `display` property that is set to `transient` on the `AlertMessage` when triggering a message to the `AlertApi`. The length of time that these transient messages stay open for can be set using the `transientTimeoutMs` prop on the `AlertDisplay` in the `App.tsx`. Here is an example:

  ```diff
    const App = () => (
      <AppProvider>
  +     <AlertDisplay transientTimeoutMs={2500} />
        <OAuthRequestDialog />
        <AppRouter>
          <Root>{routes}</Root>
        </AppRouter>
      </AppProvider>
    );
  ```

  The above example will set the transient timeout to 2500ms from the default of 5000ms

- 16e31e690f: InfoCard - Remove subheader container when there is not a subheader or icon

- a5a2d12298: Added option to pass additional headers to `<ProxiedSignInPage />`, which are passed along with the request to the underlying provider

- 91bba69ef8: Internal refactor to remove deprecated symbols.

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/version-bridge@1.0.3
  - @backstage/errors@1.1.4
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16

## @backstage/create-app@0.4.35

### Patch Changes

- c4788dbb58: Fix dependency ordering in templated packages.

- 83d3167594: Bumped create-app version.

- 2cb6963f9b: Bumped create-app version.

- 6465ab3686: Bumped create-app version.

- af1358bb07: added default project name for CI job compatibility

- 935b66a646: Change step output template examples to use square bracket syntax.

- dfb269fab2: Updated the template to have the `'/test'` proxy endpoint in `app-config.yaml` be commented out by default.

- d9b3753f87: Updated the app template to use the new `AppRouter` component instead of `app.getRouter()`, as well as `app.createRoot()` instead of `app.getProvider()`.

  To apply this change to an existing app, make the following change to `packages/app/src/App.tsx`:

  ```diff
  -import { FlatRoutes } from '@backstage/core-app-api';
  +import { AppRouter, FlatRoutes } from '@backstage/core-app-api';

   ...

  -const AppProvider = app.getProvider();
  -const AppRouter = app.getRouter();

   ...

  -const App = () => (
  +export default app.createRoot(
  -  <AppProvider>
  +  <>
       <AlertDisplay />
       <OAuthRequestDialog />
       <AppRouter>
         <Root>{routes}</Root>
       </AppRouter>
  -  </AppProvider>
  +  </>,
   );
  ```

  The final export step should end up looking something like this:

  ```tsx
  export default app.createRoot(
    <>
      <AlertDisplay />
      <OAuthRequestDialog />
      <AppRouter>
        <Root>{routes}</Root>
      </AppRouter>
    </>,
  );
  ```

  Note that `app.createRoot()` accepts a React element, rather than a component.

- 71e75c0b70: Removed the `react-router` dependency from the app package, using only `react-router-dom` instead.

  This change is just a bit of cleanup and is optional. If you want to apply it to your app, remove the `react-router` dependency from `packages/app/package.json`, and replace any imports from `react-router` with `react-router-dom` instead.

- Updated dependencies
  - @backstage/cli-common@0.1.11

## @backstage/dev-utils@1.0.9

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- 8015ff1258: Tweaked wording to use inclusive terminology
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/core-app-api@1.3.0
  - @backstage/test-utils@1.2.3
  - @backstage/app-defaults@1.0.9
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/integration-react@1.1.7
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/errors@1.1.4

### Patch Changes

- ac6cc9f7bd: Removed a circular import
- Updated dependencies
  - @backstage/types@1.0.2

## @backstage/integration@1.4.1

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 34b039ca9f: Added `integrations.github.apps.allowedInstallationOwners` to the configuration schema.
- Updated dependencies
  - @backstage/errors@1.1.4
  - @backstage/config@1.0.5

## @backstage/integration-react@1.1.7

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/integration@1.4.1
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16

## @backstage/release-manifests@0.0.8

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.

## @techdocs/cli@1.2.4

### Patch Changes

- 8015ff1258: Tweaked wording to use inclusive terminology
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-techdocs-node@1.4.3
  - @backstage/cli-common@0.1.11
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/test-utils@1.2.3

### Patch Changes

- 5e238ed56a: The test utility for the plugin context called `MockPluginProvider` has been created. It will be handy in the cases when you use
  `__experimentalConfigure` in your plugin. It is experimental and exported through `@backstage/test-utils/alpha`.
- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- 830687539f: Sync components in @backstage/core-components with the Component Design Guidelines
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-app-api@1.3.0
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/plugin-permission-react@0.4.8
  - @backstage/types@1.0.2
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16

## @backstage/types@1.0.2

### Patch Changes

- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.

## @backstage/version-bridge@1.0.3

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals

## @backstage/plugin-adr@0.2.4

### Patch Changes

- a19cffbeed: Update search links to only have header as linkable text
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/plugin-search-react@1.3.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/integration-react@1.1.7
  - @backstage/plugin-search-common@1.2.0
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16
  - @backstage/plugin-adr-common@0.2.4

## @backstage/plugin-adr-backend@0.2.4

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0
  - @backstage/backend-common@0.17.0
  - @backstage/errors@1.1.4
  - @backstage/integration@1.4.1
  - @backstage/plugin-search-common@1.2.0
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/plugin-adr-common@0.2.4

## @backstage/plugin-adr-common@0.2.4

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.4.1
  - @backstage/plugin-search-common@1.2.0
  - @backstage/catalog-model@1.1.4

## @backstage/plugin-airbrake@0.3.12

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- 151c0e1477: Remove the `object-hash` dependency
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/test-utils@1.2.3
  - @backstage/dev-utils@1.0.9
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-airbrake-backend@0.2.12

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/config@1.0.5

## @backstage/plugin-allure@0.1.28

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-analytics-module-ga@0.1.23

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- **********: Updated dependency `msw` to `^0.49.0`.
- 9516b0c355: Added support for sending virtual pageviews on `search` events in order to enable
  Site Search functionality in GA. For more information consult [README](https://github.com/backstage/backstage/blob/v1.9.0/plugins/analytics-module-ga/README.md#enabling-site-search)
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16

## @backstage/plugin-apache-airflow@0.2.5

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1

## @backstage/plugin-api-docs@0.8.12

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-catalog@1.7.0
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-apollo-explorer@0.1.5

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/theme@0.2.16

## @backstage/plugin-app-backend@0.3.39

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/config-loader@1.1.7
  - @backstage/types@1.0.2
  - @backstage/config@1.0.5

## @backstage/plugin-auth-backend@0.17.2

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0
  - @backstage/backend-common@0.17.0
  - @backstage/errors@1.1.4
  - @backstage/plugin-auth-node@0.2.8
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-auth-node@0.2.8

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/errors@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-azure-devops@0.2.3

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-azure-devops-backend@0.3.18

### Patch Changes

- eaccf6d628: Updated installation documentation
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/config@1.0.5
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-azure-sites@0.1.1

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-sites-common@0.1.0

## @backstage/plugin-azure-sites-backend@0.1.1

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/config@1.0.5
  - @backstage/plugin-azure-sites-common@0.1.0

## @backstage/plugin-badges@0.2.36

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-badges-backend@0.1.33

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.2.0
  - @backstage/backend-common@0.17.0
  - @backstage/errors@1.1.4
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-bazaar@0.2.1

### Patch Changes

- 9b1891061c: `HomePageBazaarInfoCard` is now displaying `title` instead of `name`. Title is a string that doesn't have to be URL friendly.
  The BazaarOverviewCard have the new property `fullHeight`. Link in `BazaarOverviewCard`is moved to header in card.
- 312962da30: Add `title` as optional parameter to `BazaarOverviewCard`
- Updated dependencies
  - @backstage/plugin-catalog@1.7.0
  - @backstage/core-plugin-api@1.2.0
  - @backstage/catalog-client@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/cli@0.22.0
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4

## @backstage/plugin-bazaar-backend@0.2.2

### Patch Changes

- c507aee8a2: Ensured typescript type checks in migration files.
- 9b1891061c: Column `title` has replaced column `name` for `BazaarProject` in database
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/backend-test-utils@0.1.31
  - @backstage/errors@1.1.4
  - @backstage/plugin-auth-node@0.2.8
  - @backstage/config@1.0.5

## @backstage/plugin-bitbucket-cloud-common@0.2.2

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/integration@1.4.1

## @backstage/plugin-bitrise@0.1.39

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog-backend-module-aws@0.1.12

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-catalog-node@1.3.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/errors@1.1.4
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/integration@1.4.1
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-catalog-backend-module-azure@0.1.10

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-catalog-node@1.3.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/errors@1.1.4
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/integration@1.4.1
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-catalog-backend-module-bitbucket@0.2.6

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0
  - @backstage/backend-common@0.17.0
  - @backstage/errors@1.1.4
  - @backstage/integration@1.4.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.2
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.1.6

### Patch Changes

- ce08e1798e: Refresh (potentially) updated catalog files on `repo:push` more efficiently.
- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- **********: Updated dependency `msw` to `^0.49.0`.
- 4eb0bce299: Fix repo:push topic not matching `BitbucketCloudEventRouter`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0
  - @backstage/catalog-client@1.2.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-catalog-node@1.3.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/plugin-events-node@0.2.0
  - @backstage/integration@1.4.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/plugin-catalog-common@1.0.9

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.1.4

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-catalog-node@1.3.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/errors@1.1.4
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/integration@1.4.1
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-catalog-backend-module-gerrit@0.1.7

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-catalog-node@1.3.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/errors@1.1.4
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/integration@1.4.1
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-catalog-backend-module-github@0.2.2

### Patch Changes

- 70fa5ec3ec: Fixes the assignment of group member references in `GithubMultiOrgProcessor` so membership relations are resolved correctly.

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.

- fe93cce743: Added the githubCredentialsProvider property to the GithubLocationAnalyzerOptions to be able to override the GithubCredentialsProvider.

- a0fd4af94a: Handle GitHub `push` events at the `GithubEntityProvider` by subscribing to the topic `github.push.`

  Implements `EventSubscriber` to receive events for the topic `github.push`.

  On `github.push`, the affected repository will be refreshed.
  This includes adding new Location entities, refreshing existing ones,
  and removing obsolete ones.

  Please find more information at
  <https://backstage.io/docs/integrations/github/discovery#installation-with-events-support>

- **********: Updated dependency `msw` to `^0.49.0`.

- 754b5854df: Fix incorrectly exported GithubOrgEntityProvider as a type

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0
  - @backstage/catalog-client@1.2.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-catalog-node@1.3.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/errors@1.1.4
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/plugin-events-node@0.2.0
  - @backstage/integration@1.4.1
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/plugin-catalog-common@1.0.9

## @backstage/plugin-catalog-backend-module-gitlab@0.1.10

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-catalog-node@1.3.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/errors@1.1.4
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/integration@1.4.1
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-catalog-backend-module-ldap@0.5.6

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/errors@1.1.4
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-catalog-backend-module-msgraph@0.4.5

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0
  - @backstage/plugin-catalog-node@1.3.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-catalog-backend-module-openapi@0.1.5

### Patch Changes

- dd995cdc72: Enabled support of resolving `$refs` in all kind of yaml documents, not only OpenAPI. This implicitly adds `$ref` resolving support for AsyncAPI specs. Thus, the `openApiPlaceholderResolver` has been renamed to `jsonSchemaRefPlaceholderResolver`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-catalog-node@1.3.0
  - @backstage/integration@1.4.1
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-catalog-common@1.0.9

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/plugin-search-common@1.2.0
  - @backstage/catalog-model@1.1.4

## @backstage/plugin-catalog-graph@0.2.24

### Patch Changes

- cb716004ef: Internal refactor to improve tests
- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- 454f2e90db: Set the default `maxDepth` prop for `EntityRelationsGraph` to a smaller value to provide better readability.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/catalog-client@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog-graphql@0.3.16

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-catalog-import@0.9.2

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/catalog-client@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/integration-react@1.1.7
  - @backstage/integration@1.4.1
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/plugin-catalog-common@1.0.9

## @backstage/plugin-catalog-react@1.2.2

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- 6ffa47bb0a: Cleanup and small fixes for the kind selector
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/catalog-client@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/version-bridge@1.0.3
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/errors@1.1.4
  - @backstage/plugin-permission-react@0.4.8
  - @backstage/integration@1.4.1
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9

## @backstage/plugin-cicd-statistics@0.1.14

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4

## @backstage/plugin-cicd-statistics-module-gitlab@0.1.8

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/plugin-cicd-statistics@0.1.14
  - @backstage/catalog-model@1.1.4

## @backstage/plugin-circleci@0.3.12

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-cloudbuild@0.3.12

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-code-climate@0.1.12

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage@0.2.5

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage-backend@0.2.5

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0
  - @backstage/backend-common@0.17.0
  - @backstage/errors@1.1.4
  - @backstage/integration@1.4.1
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-codescene@0.1.7

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 17a8e32f39: Updated dependency `rc-progress` to `3.4.1`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16

## @backstage/plugin-config-schema@0.1.35

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/types@1.0.2
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16

## @backstage/plugin-cost-insights@0.12.1

### Patch Changes

- 593c22253a: Added Y axis for metric data, with relevant formatting and data domain
- f9bbb3be37: Provide the ability to change the base currency from USD to any other currency in cost insights plugin
- d379b6f070: Internal refactor to avoid usage of deprecated symbols
- 5960d0902e: Internal refactor to avoid usage of deprecated symbols
- **********: Updated dependency `msw` to `^0.49.0`.
- f97c7fd1f7: added an optional config entry `costInsights.engineerThreshold` to allow users to control the threshold value for the 'negligible' change in costs.
- a5ecdb99f0: Making a possibility to hide a trending line in a cost insights plugin
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/test-utils@1.2.3
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16
  - @backstage/plugin-cost-insights-common@0.1.1

## @backstage/plugin-dynatrace@1.0.2

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-events-backend-module-aws-sqs@0.1.1

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.
- dd008a10c1: Upgrade to AWS SDK for Javascript v3
- Updated dependencies
  - @backstage/backend-tasks@0.4.0
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/plugin-events-node@0.2.0
  - @backstage/types@1.0.2
  - @backstage/config@1.0.5

## @backstage/plugin-events-backend-module-azure@0.1.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/plugin-events-node@0.2.0

## @backstage/plugin-events-backend-module-bitbucket-cloud@0.1.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/plugin-events-node@0.2.0

## @backstage/plugin-events-backend-module-gerrit@0.1.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/plugin-events-node@0.2.0

## @backstage/plugin-events-backend-module-github@0.1.1

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.

- 0f46ec304c: Add `createGithubSignatureValidator(config)` which can be used
  to create a validator used at an ingress for topic `github`.

  On top, there is a new `githubWebhookEventsModule` for the new backend plugin API
  which auto-registers the `HttpPostIngress` for topic `github` incl. the validator.

  Please find more information at
  <https://github.com/backstage/backstage/tree/master/plugins/events-backend-module-github/README.md>.

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/plugin-events-node@0.2.0
  - @backstage/config@1.0.5

## @backstage/plugin-events-backend-module-gitlab@0.1.1

### Patch Changes

- 884d749b14: Refactored to use `coreServices` from `@backstage/backend-plugin-api`.

- 31fe8f256a: Add `createGitlabTokenValidator(config)` which can be used
  to create a validator used at an ingress for topic `gitlab`.

  On top, there is a new `gitlabWebhookEventsModule` for the new backend plugin API
  which auto-registers the `HttpPostIngress` for topic `gitlab` incl. the validator.

  Please find more information at
  <https://github.com/backstage/backstage/tree/master/plugins/events-backend-module-gitlab/README.md>.

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0
  - @backstage/plugin-events-node@0.2.0
  - @backstage/config@1.0.5

## @backstage/plugin-events-backend-test-utils@0.1.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.2.0

## @backstage/plugin-explore@0.3.43

### Patch Changes

- c8f49ed4d0: Update search links to only have header as linkable text

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.

- ea4a5be8f3: Adds styling to graph forcing it to always fill out the available space.

- **********: Updated dependency `msw` to `^0.49.0`.

- 4dec6f16be: Added new `@backstage/plugin-explore-backend` & `@backstage/plugin-explore-common` packages.

  This deprecates the `ExploreToolsConfig` API (in `@backstage/plugin-explore-react`) which is replaced by the `ExploreApi` & `ExploreClient`. The list of `ExploreTool` data can now be provided on the backend by either using the supplied `StaticExploreToolProvider` or by implementing a custom `ExploreToolProvider`. See the [explore-backend README](https://github.com/backstage/backstage/blob/v1.9.0/plugins/explore-backend/README.md) for full details.

  NOTE: Existing installations that have customized the `ExploreToolConfig` will continue to work through the new `ExploreClient`. However, existing data should be migrated over to new `explore-backend` plugin as `ExploreToolConfig` will be removed in the future.

  BREAKING CHANGE: If you have previously installed the `explore` plugin, but not yet customized the `ExploreToolConfig` API in your `packages/app`, this will result in an empty list of tools.

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/plugin-search-react@1.3.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/plugin-explore-react@0.0.24
  - @backstage/plugin-explore-common@0.0.1
  - @backstage/plugin-search-common@1.2.0
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-explore-backend@0.0.1

### Patch Changes

- 4dec6f16be: Added new `@backstage/plugin-explore-backend` & `@backstage/plugin-explore-common` packages.

  This deprecates the `ExploreToolsConfig` API (in `@backstage/plugin-explore-react`) which is replaced by the `ExploreApi` & `ExploreClient`. The list of `ExploreTool` data can now be provided on the backend by either using the supplied `StaticExploreToolProvider` or by implementing a custom `ExploreToolProvider`. See the [explore-backend README](https://github.com/backstage/backstage/blob/v1.9.0/plugins/explore-backend/README.md) for full details.

  NOTE: Existing installations that have customized the `ExploreToolConfig` will continue to work through the new `ExploreClient`. However, existing data should be migrated over to new `explore-backend` plugin as `ExploreToolConfig` will be removed in the future.

  BREAKING CHANGE: If you have previously installed the `explore` plugin, but not yet customized the `ExploreToolConfig` API in your `packages/app`, this will result in an empty list of tools.

- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-explore-common@0.0.1
  - @backstage/plugin-search-common@1.2.0
  - @backstage/config@1.0.5

## @backstage/plugin-explore-common@0.0.1

### Patch Changes

- 4dec6f16be: Added new `@backstage/plugin-explore-backend` & `@backstage/plugin-explore-common` packages.

  This deprecates the `ExploreToolsConfig` API (in `@backstage/plugin-explore-react`) which is replaced by the `ExploreApi` & `ExploreClient`. The list of `ExploreTool` data can now be provided on the backend by either using the supplied `StaticExploreToolProvider` or by implementing a custom `ExploreToolProvider`. See the [explore-backend README](https://github.com/backstage/backstage/blob/v1.9.0/plugins/explore-backend/README.md) for full details.

  NOTE: Existing installations that have customized the `ExploreToolConfig` will continue to work through the new `ExploreClient`. However, existing data should be migrated over to new `explore-backend` plugin as `ExploreToolConfig` will be removed in the future.

  BREAKING CHANGE: If you have previously installed the `explore` plugin, but not yet customized the `ExploreToolConfig` API in your `packages/app`, this will result in an empty list of tools.

## @backstage/plugin-explore-react@0.0.24

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.

- 4dec6f16be: Added new `@backstage/plugin-explore-backend` & `@backstage/plugin-explore-common` packages.

  This deprecates the `ExploreToolsConfig` API (in `@backstage/plugin-explore-react`) which is replaced by the `ExploreApi` & `ExploreClient`. The list of `ExploreTool` data can now be provided on the backend by either using the supplied `StaticExploreToolProvider` or by implementing a custom `ExploreToolProvider`. See the [explore-backend README](https://github.com/backstage/backstage/blob/v1.9.0/plugins/explore-backend/README.md) for full details.

  NOTE: Existing installations that have customized the `ExploreToolConfig` will continue to work through the new `ExploreClient`. However, existing data should be migrated over to new `explore-backend` plugin as `ExploreToolConfig` will be removed in the future.

  BREAKING CHANGE: If you have previously installed the `explore` plugin, but not yet customized the `ExploreToolConfig` API in your `packages/app`, this will result in an empty list of tools.

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/plugin-explore-common@0.0.1

## @backstage/plugin-firehydrant@0.1.29

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/theme@0.2.16

## @backstage/plugin-fossa@0.2.44

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-gcalendar@0.3.8

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-gcp-projects@0.3.31

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- **********: Updated dependency `msw` to `^0.49.0`.
- 5fb6d5e92e: Updated dependency `@react-hookz/web` to `^19.0.0`.
- 146378c146: Updated dependency `@react-hookz/web` to `^20.0.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/theme@0.2.16

## @backstage/plugin-git-release-manager@0.3.25

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/integration@1.4.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-actions@0.5.12

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/integration@1.4.1
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-github-deployments@0.1.43

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/integration-react@1.1.7
  - @backstage/integration@1.4.1
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-github-issues@0.2.1

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/integration@1.4.1
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-github-pull-requests-board@0.1.6

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/integration@1.4.1
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-gitops-profiles@0.3.30

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/theme@0.2.16

## @backstage/plugin-gocd@0.1.18

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-graphiql@0.2.44

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/theme@0.2.16

## @backstage/plugin-graphql-backend@0.1.29

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-catalog-graphql@0.3.16
  - @backstage/config@1.0.5

## @backstage/plugin-home@0.4.28

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- edf2404e9f: Adjusted the description's empty state on the starred entities table,
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/plugin-stack-overflow@0.1.8
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16

## @backstage/plugin-ilert@0.2.1

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-jenkins@0.7.11

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16
  - @backstage/plugin-jenkins-common@0.1.11

## @backstage/plugin-jenkins-backend@0.1.29

### Patch Changes

- cfe2b98fbe: Remove unnecessary dependency `promise-any-polyfill`.
- **********: Updated dependency `msw` to `^0.49.0`.
- 9447b0fb46: added support for standalone jenkins projects
- Updated dependencies
  - @backstage/catalog-client@1.2.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/errors@1.1.4
  - @backstage/plugin-auth-node@0.2.8
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/plugin-jenkins-common@0.1.11

## @backstage/plugin-jenkins-common@0.1.11

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/plugin-catalog-common@1.0.9

## @backstage/plugin-kafka@0.3.12

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16

## @backstage/plugin-kafka-backend@0.2.32

### Patch Changes

- 0b54dbaf7d: Internal tweak to adapt to `kafkajs` 2.2.3
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/errors@1.1.4
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-kubernetes@0.7.5

### Patch Changes

- 2db8acffe7: Kubernetes plugin now gracefully surfaces transport-level errors (like DNS or timeout, or other socket errors) occurring while fetching data. This will be merged into any data that is fetched successfully, fixing a bug where the whole page would be empty if any fetch operation encountered such an error.
- 365f887717: Removed rendering for ErrorEmptyState in ErrorReporting component, so nothing is rendered when there are no errors. Also removed Divider on Kubernetes page.
- **********: Updated dependency `msw` to `^0.49.0`.
- 9ce7866ecd: Updated dependency `@kubernetes/client-node` to `0.18.0`.
- Updated dependencies
  - @backstage/plugin-kubernetes-common@0.5.0
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16

## @backstage/plugin-lighthouse@0.3.12

### Patch Changes

- e3dfef3f63: Fixed "Emulated Form Factor" field in the audit creation form not working with the latest version (1.0.2) of `lighthouse-audit-service`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic@0.3.30

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic-dashboard@0.2.5

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4

## @backstage/plugin-org@0.6.2

### Patch Changes

- 4395eac4d8: Made all the ownership boxes the same size
- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- 67cedfe42e: Update `UserProfileCard` and `GroupProfileCard` to not render links unless the `showLinks` prop is set. The primary component for rendering links are the `EntityLinksCard` from plugin-catalog.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-org-react@0.1.1

### Patch Changes

- 4cb5066828: Bug fixes and adding the possibility to add a default value for the `GroupListPicker`. Fixes: Vertical size jump on text entry, left align for text, selecting a value closes the popup, auto focus on the popup when opening
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/catalog-client@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-pagerduty@0.5.5

### Patch Changes

- cb716004ef: Internal refactor to improve tests
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop@0.1.10

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop-backend@0.1.10

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/config@1.0.5

## @backstage/plugin-permission-backend@0.5.14

### Patch Changes

- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/plugin-permission-node@0.7.2
  - @backstage/errors@1.1.4
  - @backstage/plugin-auth-node@0.2.8
  - @backstage/config@1.0.5

## @backstage/plugin-permission-common@0.7.2

### Patch Changes

- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/errors@1.1.4
  - @backstage/types@1.0.2
  - @backstage/config@1.0.5

## @backstage/plugin-permission-node@0.7.2

### Patch Changes

- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/errors@1.1.4
  - @backstage/plugin-auth-node@0.2.8
  - @backstage/config@1.0.5

## @backstage/plugin-permission-react@0.4.8

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/config@1.0.5

## @backstage/plugin-playlist@0.1.3

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/plugin-search-react@1.3.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/plugin-permission-react@0.4.8
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9
  - @backstage/plugin-playlist-common@0.1.3

## @backstage/plugin-playlist-backend@0.2.2

### Patch Changes

- c507aee8a2: Ensured typescript type checks in migration files.
- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/plugin-permission-node@0.7.2
  - @backstage/backend-test-utils@0.1.31
  - @backstage/errors@1.1.4
  - @backstage/plugin-auth-node@0.2.8
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/plugin-playlist-common@0.1.3

## @backstage/plugin-playlist-common@0.1.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2

## @backstage/plugin-proxy-backend@0.2.33

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 03843259b4: Documented the `createRouter` method.
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/config@1.0.5

## @backstage/plugin-rollbar@0.4.12

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-rollbar-backend@0.1.36

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/config@1.0.5

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.2.14

### Patch Changes

- 935b66a646: Change step output template examples to use square bracket syntax.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.9.0
  - @backstage/backend-common@0.17.0
  - @backstage/errors@1.1.4
  - @backstage/integration@1.4.1
  - @backstage/types@1.0.2
  - @backstage/config@1.0.5

## @backstage/plugin-scaffolder-backend-module-rails@0.4.7

### Patch Changes

- 935b66a646: Change step output template examples to use square bracket syntax.
- 27b23a86ad: Added more (optional) arguments to the `createFetchRailsAction` to be passed to `rails new`
- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.9.0
  - @backstage/backend-common@0.17.0
  - @backstage/errors@1.1.4
  - @backstage/integration@1.4.1
  - @backstage/types@1.0.2
  - @backstage/config@1.0.5

## @backstage/plugin-scaffolder-backend-module-yeoman@0.2.12

### Patch Changes

- 935b66a646: Change step output template examples to use square bracket syntax.
- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.9.0
  - @backstage/types@1.0.2
  - @backstage/config@1.0.5

## @backstage/plugin-scaffolder-common@1.2.3

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4

## @backstage/plugin-search@1.0.5

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/plugin-search-react@1.3.0
  - @backstage/core-components@0.12.1
  - @backstage/version-bridge@1.0.3
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/types@1.0.2
  - @backstage/plugin-search-common@1.2.0
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16

## @backstage/plugin-sentry@0.4.5

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-shortcuts@0.3.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- 19356df560: Updated dependency `zen-observable` to `^0.9.0`.
- c3fa90e184: Updated dependency `zen-observable` to `^0.10.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/types@1.0.2
  - @backstage/theme@0.2.16

## @backstage/plugin-sonarqube-backend@0.1.4

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 3dee2f5ad0: Added links to the frontend and backend plugins in the readme.
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/errors@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-splunk-on-call@0.4.1

### Patch Changes

- cb716004ef: Internal refactor to improve tests
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-stack-overflow@0.1.8

### Patch Changes

- a19cffbeed: Update search links to only have header as linkable text
- **********: Updated dependency `msw` to `^0.49.0`.
- c981e83612: The `<StackOverflowSearchResultListItem />` component is now able to highlight the result title and/or text when provided. To take advantage of this, pass in the `highlight` prop, similar to how it is done on other result list item components.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/plugin-search-react@1.3.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-home@0.4.28
  - @backstage/plugin-search-common@1.2.0
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16

## @backstage/plugin-stack-overflow-backend@0.1.8

### Patch Changes

- fd0ca6f447: Added option to supply API Access Token. This is required in addition to an API key when trying to access the data for a private Stack Overflow Team.
- Updated dependencies
  - @backstage/cli@0.22.0
  - @backstage/plugin-search-common@1.2.0
  - @backstage/config@1.0.5

## @backstage/plugin-tech-insights@0.3.4

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16
  - @backstage/plugin-tech-insights-common@0.2.9

## @backstage/plugin-tech-insights-backend@0.5.5

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.2.0
  - @backstage/backend-common@0.17.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/errors@1.1.4
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/plugin-tech-insights-common@0.2.9
  - @backstage/plugin-tech-insights-node@0.3.7

## @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.23

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/errors@1.1.4
  - @backstage/config@1.0.5
  - @backstage/plugin-tech-insights-common@0.2.9
  - @backstage/plugin-tech-insights-node@0.3.7

## @backstage/plugin-tech-insights-common@0.2.9

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.2

## @backstage/plugin-tech-insights-node@0.3.7

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/types@1.0.2
  - @backstage/config@1.0.5
  - @backstage/plugin-tech-insights-common@0.2.9

## @backstage/plugin-tech-radar@0.5.19

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs@1.4.1

### Patch Changes

- d3fea4ae0a: Internal fixes to avoid implicit usage of globals
- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- a19cffbeed: Update search links to only have header as linkable text
- 5d3058355d: Add `react/forbid-elements` linter rule for button, suggest Material UI `Button`
- **********: Updated dependency `msw` to `^0.49.0`.
- 786f1b1419: Support older versions of react-router
- Updated dependencies
  - @backstage/plugin-techdocs-react@1.1.0
  - @backstage/core-plugin-api@1.2.0
  - @backstage/plugin-search-react@1.3.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/integration-react@1.1.7
  - @backstage/integration@1.4.1
  - @backstage/plugin-search-common@1.2.0
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-addons-test-utils@1.0.7

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/plugin-techdocs-react@1.1.0
  - @backstage/plugin-catalog@1.7.0
  - @backstage/core-plugin-api@1.2.0
  - @backstage/plugin-search-react@1.3.0
  - @backstage/core-components@0.12.1
  - @backstage/core-app-api@1.3.0
  - @backstage/plugin-techdocs@1.4.1
  - @backstage/test-utils@1.2.3
  - @backstage/integration-react@1.1.7
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-module-addons-contrib@1.0.7

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- 5fb6d5e92e: Updated dependency `@react-hookz/web` to `^19.0.0`.
- 146378c146: Updated dependency `@react-hookz/web` to `^20.0.0`.
- 8536e7c281: Use `app.title` from `app-config.yaml` when creating new Documentation Feedback issue. `Backstage` is the default value.
- Updated dependencies
  - @backstage/plugin-techdocs-react@1.1.0
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/integration-react@1.1.7
  - @backstage/integration@1.4.1
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-node@1.4.3

### Patch Changes

- 0a61aab172: Bump dependency @azure/identity to next minor

- e40790d0c2: Add support for specifying an S3 bucket's account ID and retrieving the credentials from the `aws` app config section. This is now the preferred way to configure AWS credentials for Techdocs.

- 37931c33ce: Upgrade to AWS SDK for Javascript v3

  Techdocs support for AWS S3 now requires defining the AWS region to connect to.
  If `techdocs.publisher.awsS3.region` is missing from the config, the AWS environment variable `AWS_REGION` will be used.

- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/integration-aws-node@0.1.0
  - @backstage/errors@1.1.4
  - @backstage/integration@1.4.1
  - @backstage/plugin-search-common@1.2.0
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-todo@0.2.14

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-todo-backend@0.1.36

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/catalog-client@1.2.0
  - @backstage/backend-common@0.17.0
  - @backstage/errors@1.1.4
  - @backstage/integration@1.4.1
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-user-settings-backend@0.1.3

### Patch Changes

- c507aee8a2: Ensured typescript type checks in migration files.
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/errors@1.1.4
  - @backstage/plugin-auth-node@0.2.8
  - @backstage/types@1.0.2
  - @backstage/catalog-model@1.1.4

## @backstage/plugin-vault@0.1.6

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/catalog-model@1.1.4
  - @backstage/theme@0.2.16

## @backstage/plugin-vault-backend@0.2.5

### Patch Changes

- 568ae02463: Added (optional) config `vault.publicUrl` as alternative to `vault.baseUrl` for `editUrl` and `showUrl` in case `vault.baseUrl` is internal
- **********: Updated dependency `msw` to `^0.49.0`.
- 7a3d2688ed: Use `express-promise-router` to catch errors properly.
  Add `403` error as a known one. It will now return a `NotAllowed` error.
- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/backend-test-utils@0.1.31
  - @backstage/errors@1.1.4
  - @backstage/config@1.0.5

## @backstage/plugin-xcmetrics@0.2.32

### Patch Changes

- **********: Updated dependency `msw` to `^0.49.0`.
- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/errors@1.1.4
  - @backstage/theme@0.2.16

## example-app@0.2.78

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-graph@0.2.24
  - @backstage/plugin-pagerduty@0.5.5
  - @backstage/plugin-techdocs-react@1.1.0
  - @backstage/plugin-scaffolder@1.9.0
  - @backstage/plugin-cost-insights@0.12.1
  - @backstage/plugin-kubernetes@0.7.5
  - @backstage/core-plugin-api@1.2.0
  - @backstage/plugin-search-react@1.3.0
  - @backstage/core-components@0.12.1
  - @backstage/plugin-explore@0.3.43
  - @backstage/cli@0.22.0
  - @backstage/core-app-api@1.3.0
  - @backstage/plugin-cloudbuild@0.3.12
  - @backstage/plugin-gcp-projects@0.3.31
  - @backstage/plugin-github-actions@0.5.12
  - @backstage/plugin-graphiql@0.2.44
  - @backstage/plugin-techdocs@1.4.1
  - @backstage/plugin-org@0.6.2
  - @backstage/plugin-user-settings@0.6.0
  - @backstage/app-defaults@1.0.9
  - @backstage/plugin-airbrake@0.3.12
  - @backstage/plugin-api-docs@0.8.12
  - @backstage/plugin-azure-devops@0.2.3
  - @backstage/plugin-badges@0.2.36
  - @backstage/plugin-catalog-import@0.9.2
  - @backstage/plugin-catalog-react@1.2.2
  - @backstage/plugin-circleci@0.3.12
  - @backstage/plugin-code-coverage@0.2.5
  - @backstage/plugin-home@0.4.28
  - @backstage/plugin-jenkins@0.7.11
  - @backstage/plugin-kafka@0.3.12
  - @backstage/plugin-permission-react@0.4.8
  - @backstage/plugin-playlist@0.1.3
  - @backstage/plugin-rollbar@0.4.12
  - @backstage/plugin-search@1.0.5
  - @backstage/plugin-sentry@0.4.5
  - @backstage/plugin-shortcuts@0.3.4
  - @backstage/plugin-todo@0.2.14
  - @backstage/plugin-lighthouse@0.3.12
  - @backstage/plugin-stack-overflow@0.1.8
  - @backstage/integration-react@1.1.7
  - @backstage/plugin-apache-airflow@0.2.5
  - @backstage/plugin-azure-sites@0.1.1
  - @backstage/plugin-dynatrace@1.0.2
  - @backstage/plugin-gcalendar@0.3.8
  - @backstage/plugin-gocd@0.1.18
  - @backstage/plugin-newrelic@0.3.30
  - @backstage/plugin-tech-insights@0.3.4
  - @backstage/plugin-tech-radar@0.5.19
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.7
  - @backstage/plugin-search-common@1.2.0
  - @internal/plugin-catalog-customized@0.0.5
  - @backstage/plugin-newrelic-dashboard@0.2.5
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9

## example-backend@0.2.78

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.9.0
  - @backstage/plugin-azure-devops-backend@0.3.18
  - @backstage/plugin-kubernetes-backend@0.9.0
  - @backstage/plugin-catalog-backend@1.6.0
  - @backstage/catalog-client@1.2.0
  - @backstage/plugin-scaffolder-backend-module-rails@0.4.7
  - @backstage/plugin-search-backend@1.2.0
  - @backstage/plugin-search-backend-module-elasticsearch@1.1.0
  - @backstage/plugin-search-backend-node@1.1.0
  - @backstage/plugin-playlist-backend@0.2.2
  - @backstage/plugin-search-backend-module-pg@0.5.0
  - @backstage/backend-common@0.17.0
  - @backstage/plugin-app-backend@0.3.39
  - @backstage/plugin-catalog-node@1.3.0
  - @backstage/plugin-events-backend@0.2.0
  - @backstage/backend-tasks@0.4.0
  - @backstage/plugin-permission-backend@0.5.14
  - @backstage/plugin-permission-common@0.7.2
  - @backstage/plugin-permission-node@0.7.2
  - @backstage/plugin-kafka-backend@0.2.32
  - @backstage/plugin-jenkins-backend@0.1.29
  - @backstage/plugin-events-node@0.2.0
  - @backstage/integration@1.4.1
  - @backstage/plugin-auth-backend@0.17.2
  - @backstage/plugin-auth-node@0.2.8
  - @backstage/plugin-azure-sites-backend@0.1.1
  - @backstage/plugin-code-coverage-backend@0.2.5
  - @backstage/plugin-graphql-backend@0.1.29
  - @backstage/plugin-proxy-backend@0.2.33
  - @backstage/plugin-rollbar-backend@0.1.36
  - @backstage/plugin-techdocs-backend@1.5.0
  - @backstage/plugin-todo-backend@0.1.36
  - @backstage/plugin-explore-backend@0.0.1
  - @backstage/plugin-search-common@1.2.0
  - example-app@0.2.78
  - @backstage/plugin-badges-backend@0.1.33
  - @backstage/plugin-tech-insights-backend@0.5.5
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.23
  - @backstage/plugin-tech-insights-node@0.3.7

## example-backend-next@0.0.6

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.9.0
  - @backstage/plugin-catalog-backend@1.6.0
  - @backstage/plugin-app-backend@0.3.39
  - @backstage/backend-defaults@0.1.4

## techdocs-cli-embedded-app@0.2.77

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs-react@1.1.0
  - @backstage/plugin-catalog@1.7.0
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/cli@0.22.0
  - @backstage/core-app-api@1.3.0
  - @backstage/plugin-techdocs@1.4.1
  - @backstage/test-utils@1.2.3
  - @backstage/app-defaults@1.0.9
  - @backstage/integration-react@1.1.7
  - @backstage/catalog-model@1.1.4
  - @backstage/config@1.0.5
  - @backstage/theme@0.2.16

## @internal/plugin-catalog-customized@0.0.5

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.7.0
  - @backstage/plugin-catalog-react@1.2.2

## @internal/plugin-todo-list@1.0.8

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.2.0
  - @backstage/core-components@0.12.1
  - @backstage/theme@0.2.16

## @internal/plugin-todo-list-backend@1.0.8

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0
  - @backstage/errors@1.1.4
  - @backstage/plugin-auth-node@0.2.8
  - @backstage/config@1.0.5

## @internal/plugin-todo-list-common@1.0.7

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2
