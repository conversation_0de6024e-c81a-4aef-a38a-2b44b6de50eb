# Release v1.8.0-next.1

## @backstage/plugin-scaffolder-backend@1.8.0-next.1

### Minor Changes

- 5921b5ce49: - The GitLab Project ID for the `publish:gitlab:merge-request` action is now passed through the query parameter `project` in the `repoUrl`. It still allows people to not use the `projectid` and use the `repoUrl` with the `owner` and `repo` query parameters instead. This makes it easier to publish to repositories instead of writing the full path to the project.

## @backstage/create-app@0.4.33-next.1

### Patch Changes

- Bumped create-app version.

## @backstage/plugin-azure-devops-backend@0.3.17-next.1

### Patch Changes

- 62f284e394: - Adjusted the asset parser to accept case sensitive
  - Fixed fetching data that was using the deprecated function

## @backstage/plugin-playlist@0.1.2-next.1

### Patch Changes

- 605f269f0d: Updated Playlist plugin docs:

  - Updated `playlist` plugin README to include note about installing backend plugin and added images for the various features
  - Updated `playlist-backend` plugin README to remove `IdentityClient` import in example as it is not used and made minor change to headings

## @backstage/plugin-playlist-backend@0.2.1-next.1

### Patch Changes

- 605f269f0d: Updated Playlist plugin docs:

  - Updated `playlist` plugin README to include note about installing backend plugin and added images for the various features
  - Updated `playlist-backend` plugin README to remove `IdentityClient` import in example as it is not used and made minor change to headings

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.2.13-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.8.0-next.1

## @backstage/plugin-scaffolder-backend-module-rails@0.4.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.8.0-next.1

## @backstage/plugin-scaffolder-backend-module-yeoman@0.2.11-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.8.0-next.1

## @backstage/plugin-techdocs@1.4.0-next.1

### Patch Changes

- 9e4d8e6198: Fix logic bug that broke techdocs-cli-embedded-app

## @backstage/plugin-techdocs-addons-test-utils@1.0.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs@1.4.0-next.1

## example-app@0.2.77-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs@1.4.0-next.1
  - @backstage/plugin-playlist@0.1.2-next.1
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.6-next.0

## example-backend@0.2.77-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.8.0-next.1
  - @backstage/plugin-playlist-backend@0.2.1-next.1
  - @backstage/plugin-azure-devops-backend@0.3.17-next.1
  - example-app@0.2.77-next.1
  - @backstage/plugin-scaffolder-backend-module-rails@0.4.6-next.1

## example-backend-next@0.0.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.8.0-next.1

## techdocs-cli-embedded-app@0.2.76-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-techdocs@1.4.0-next.1
