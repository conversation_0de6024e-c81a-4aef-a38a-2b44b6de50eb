# Release v1.7.0-next.1

## @backstage/plugin-auth-backend@0.17.0-next.1

### Minor Changes

- e2dc42e9f0: Google OAuth refresh tokens will now be revoked on logout by calling Google's API

### Patch Changes

- b5c126010c: Auth0 provider now supports optional `connection` and `connectionScope` parameters to configure social identity providers.
- Updated dependencies
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/types@1.0.0
  - @backstage/plugin-auth-node@0.2.6-next.1

## @backstage/plugin-catalog@1.6.0-next.1

### Minor Changes

- d558f41d3a: Added new column `Label` to `CatalogTable.columns`, this new column allows you make use of labels from metadata.
  For example: category and visibility are type of labels associated with API entity illustrated below.

  YAML code snippet for API entity

  ```yaml
  apiVersion: backstage.io/v1alpha1
  kind: API
  metadata:
    name: sample-api
    description: API for sample
    links:
      - url: http://localhost:8080/swagger-ui.html
        title: Swagger UI
    tags:
      - http
    labels:
      category: legacy
      visibility: protected
  ```

  Consumers can customise columns to include label column and show in api-docs list

  ```typescript
  const columns = [
    CatalogTable.columns.createNameColumn({ defaultKind: 'API' }),
    CatalogTable.columns.createLabelColumn('category', { title: 'Category' }),
    CatalogTable.columns.createLabelColumn('visibility', {
      title: 'Visibility',
      defaultValue: 'public',
    }),
  ];
  ```

### Patch Changes

- 4efadb6968: Implemented the visual parts of `EntityKindPicker` so that it can be shown alongside the other filters on the left side of your catalog pages.
- e89e1f614d: Added support for copy entity URL in entity page context menu
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/plugin-search-react@1.2.0-next.1
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration-react@1.1.5-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/plugin-catalog-common@1.0.7-next.1

## @backstage/plugin-catalog-react@1.2.0-next.1

### Minor Changes

- 4efadb6968: Implemented the visual parts of `EntityKindPicker` so that it can be shown alongside the other filters on the left side of your catalog pages.

### Patch Changes

- 7939e743f5: Added two new `EntityRefLinks` props, the first being `getTitle` that allows for customization of the title used for each link. The second one is `fetchEntities`, which triggers a fetching of all entities so that the full entity definition is available in the `getTitle` callback.
- Updated dependencies
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1
  - @backstage/plugin-catalog-common@1.0.7-next.1
  - @backstage/plugin-permission-common@0.6.5-next.1
  - @backstage/plugin-permission-react@0.4.6-next.1

## @backstage/plugin-search-backend@1.1.0-next.1

### Minor Changes

- 16c853a6ed: Be less restrictive with unknown keys on query endpoint
- a799972bb1: The query received by search engines now contains a property called `pageLimit`, it specifies how many results to return per page when sending a query request to the search backend.

  Example:
  _Returns up to 30 results per page_

      GET /query?pageLimit=30

  The search backend validates the page limit and this value must not exceed 100, but it doesn't set a default value for the page limit parameter, it leaves it up to each search engine to set this, so Lunr, Postgres and Elastic Search set 25 results per page as a default value.

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/plugin-search-backend-node@1.0.3-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/types@1.0.0
  - @backstage/plugin-auth-node@0.2.6-next.1
  - @backstage/plugin-permission-common@0.6.5-next.1
  - @backstage/plugin-permission-node@0.6.6-next.1

## @backstage/plugin-search-common@1.1.0-next.1

### Minor Changes

- a799972bb1: There is a new property called `pageLimit` on the `SearchQuery` interface that specifies how many results should be returned per page.

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.0
  - @backstage/plugin-permission-common@0.6.5-next.1

## @backstage/plugin-search-react@1.2.0-next.1

### Minor Changes

- 4ed1fa2480: The search query state now has an optional `pageLimit` property that determines how many results will be requested per page, it defaults to 25.

  Examples:
  _Basic_

  ```jsx
  <SearchResults query={{ pageLimit: 30 }}>
    {results => {
      // Item rendering logic is omitted
    }}
  </SearchResults>
  ```

  _With context_

  ```jsx
  <SearchContextProvider initialState={{ pageLimit: 30 }}>
    <SearchResults>
      {results => {
        // Item rendering logic is omitted
      }}
    </SearchResults>
  </SearchContextProvider>
  ```

- bed5a1dc6e: The `<SearchResultList />` component now accepts an optional property `disableRenderingWithNoResults` to disable rendering when no results are returned.
  Possibility to provide a custom no results component if needed through the `noResultsComponent` property.

  Examples:

  _Rendering a custom no results component_

  ```jsx
  <SearchResultList
    query={query}
    noResultsComponent={<ListItemText primary="No results were found" />}
  />
  ```

  _Disable rendering when there are no results_

  ```jsx
  <SearchResultList query={query} disableRenderingWithNoResults />
  ```

- 6faaa05626: The `<SearchResultGroup />` component now accepts an optional property `disableRenderingWithNoResults` to disable rendering when no results are returned.
  Possibility to provide a custom no results component if needed through the `noResultsComponent` property.

  Examples:

  _Rendering a custom no results component_

  ```jsx
  <SearchResultGroup
    query={query}
    icon={<DocsIcon />}
    title="Documentation"
    noResultsComponent={<ListItemText primary="No results were found" />}
  />
  ```

  _Disable rendering when there are no results_

  ```jsx
  <SearchResultGroup
    query={query}
    icon={<DocsIcon />}
    title="Documentation"
    disableRenderingWithNoResults
  />
  ```

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1

## @backstage/app-defaults@1.0.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.1.1-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-permission-react@0.4.6-next.1

## @backstage/backend-app-api@0.2.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-plugin-api@0.1.3-next.1
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/plugin-permission-node@0.6.6-next.1

## @backstage/backend-common@0.15.2-next.1

### Patch Changes

- c31f7cdfbc: Fixed an issue where `getClient()` for a `pluginId` would return different clients and not share them
- Updated dependencies
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.3-next.1
  - @backstage/config-loader@1.1.5-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/types@1.0.0

## @backstage/backend-defaults@0.1.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.1.3-next.1
  - @backstage/backend-app-api@0.2.2-next.1

## @backstage/backend-plugin-api@0.1.3-next.1

### Patch Changes

- a35a27df70: Added documentation for `createBackendModule`, with guidelines for choosing a module ID.
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/plugin-permission-common@0.6.5-next.1

## @backstage/backend-tasks@0.3.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/types@1.0.0

## @backstage/backend-test-utils@0.1.29-next.1

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.20.0-next.1
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-plugin-api@0.1.3-next.1
  - @backstage/backend-app-api@0.2.2-next.1
  - @backstage/config@1.0.3-next.1

## @backstage/catalog-client@1.1.1-next.1

### Patch Changes

- 4f2ac624b4: Renamed argument in `validateEntity` from `location` to `locationRef`
- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1

## @backstage/catalog-model@1.1.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/types@1.0.0

## @backstage/cli@0.20.0-next.1

### Patch Changes

- 78d5eb299e: Tweak the Jest Caching loader to only operate when in `watch` mode
- Updated dependencies
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.3-next.1
  - @backstage/config-loader@1.1.5-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/release-manifests@0.0.6
  - @backstage/types@1.0.0

## @backstage/codemods@0.1.40-next.1

### Patch Changes

- Updated dependencies
  - @backstage/cli-common@0.1.10

## @backstage/config@1.0.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.0

## @backstage/config-loader@1.1.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/types@1.0.0

## @backstage/core-app-api@1.1.1-next.1

### Patch Changes

- 27e6404aba: Fixed a bug where gathered index routes would fail to bind routable extensions. This would typically show up when placing a routable extension in the entity page overview tab.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1

## @backstage/core-components@0.11.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16
  - @backstage/version-bridge@1.0.1

## @backstage/core-plugin-api@1.0.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.1
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1

## @backstage/create-app@0.4.32-next.1

### Patch Changes

- 7c6306fc8a: Initializes a git repository when creating an app using @packages/create-app
- Updated dependencies
  - @backstage/cli-common@0.1.10

## @backstage/dev-utils@1.0.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-app-api@1.1.1-next.1
  - @backstage/app-defaults@1.0.7-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/test-utils@1.2.1-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/integration-react@1.1.5-next.1
  - @backstage/theme@0.2.16

## @backstage/errors@1.1.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.0

## @backstage/integration@1.3.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1

## @backstage/integration-react@1.1.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/theme@0.2.16

## @techdocs/cli@1.2.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.3-next.1
  - @backstage/plugin-techdocs-node@1.4.1-next.1

## @backstage/test-utils@1.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.1.1-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/plugin-permission-common@0.6.5-next.1
  - @backstage/plugin-permission-react@0.4.6-next.1

## @backstage/plugin-adr@0.2.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/plugin-search-react@1.2.0-next.1
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/integration-react@1.1.5-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-adr-common@0.2.2-next.1

## @backstage/plugin-adr-backend@0.2.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/plugin-adr-common@0.2.2-next.1

## @backstage/plugin-adr-common@0.2.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1

## @backstage/plugin-airbrake@0.3.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/dev-utils@1.0.7-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/test-utils@1.2.1-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-airbrake-backend@0.2.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/config@1.0.3-next.1

## @backstage/plugin-allure@0.1.26-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-analytics-module-ga@0.1.21-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-apache-airflow@0.2.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1

## @backstage/plugin-api-docs@0.8.10-next.1

### Patch Changes

- 50c6e14aee: Updated dependency `@asyncapi/react-component` to `1.0.0-next.43`.
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/plugin-catalog@1.6.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-apollo-explorer@0.1.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-app-backend@0.3.37-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-plugin-api@0.1.3-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/config-loader@1.1.5-next.1
  - @backstage/types@1.0.0

## @backstage/plugin-auth-node@0.2.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1

## @backstage/plugin-azure-devops@0.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-azure-devops-backend@0.3.16-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-badges@0.2.34-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-badges-backend@0.1.31-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1

## @backstage/plugin-bazaar@0.1.25-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/plugin-catalog@1.6.0-next.1
  - @backstage/cli@0.20.0-next.1
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1

## @backstage/plugin-bazaar-backend@0.1.21-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-test-utils@0.1.29-next.1
  - @backstage/config@1.0.3-next.1

## @backstage/plugin-bitbucket-cloud-common@0.2.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.3.2-next.1

## @backstage/plugin-bitrise@0.1.37-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog-backend@1.4.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-plugin-api@0.1.3-next.1
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/types@1.0.0
  - @backstage/plugin-catalog-common@1.0.7-next.1
  - @backstage/plugin-catalog-node@1.1.1-next.1
  - @backstage/plugin-permission-common@0.6.5-next.1
  - @backstage/plugin-permission-node@0.6.6-next.1
  - @backstage/plugin-scaffolder-common@1.2.1-next.1

## @backstage/plugin-catalog-backend-module-aws@0.1.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/types@1.0.0
  - @backstage/plugin-catalog-backend@1.4.1-next.1

## @backstage/plugin-catalog-backend-module-azure@0.1.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/types@1.0.0
  - @backstage/plugin-catalog-backend@1.4.1-next.1

## @backstage/plugin-catalog-backend-module-bitbucket@0.2.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/types@1.0.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.0-next.1
  - @backstage/plugin-catalog-backend@1.4.1-next.1

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.1.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.0-next.1
  - @backstage/plugin-catalog-backend@1.4.1-next.1

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.1.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/plugin-catalog-backend@1.4.1-next.1

## @backstage/plugin-catalog-backend-module-gerrit@0.1.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/plugin-catalog-backend@1.4.1-next.1

## @backstage/plugin-catalog-backend-module-github@0.1.8-next.1

### Patch Changes

- a35a27df70: Updated the `moduleId` of the experimental module export.
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-plugin-api@0.1.3-next.1
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/types@1.0.0
  - @backstage/plugin-catalog-backend@1.4.1-next.1
  - @backstage/plugin-catalog-node@1.1.1-next.1

## @backstage/plugin-catalog-backend-module-gitlab@0.1.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/types@1.0.0
  - @backstage/plugin-catalog-backend@1.4.1-next.1

## @backstage/plugin-catalog-backend-module-ldap@0.5.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/types@1.0.0
  - @backstage/plugin-catalog-backend@1.4.1-next.1

## @backstage/plugin-catalog-backend-module-msgraph@0.4.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/plugin-catalog-backend@1.4.1-next.1

## @backstage/plugin-catalog-backend-module-openapi@0.1.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/types@1.0.0
  - @backstage/plugin-catalog-backend@1.4.1-next.1
  - @backstage/plugin-catalog-node@1.1.1-next.1

## @backstage/plugin-catalog-common@1.0.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/plugin-permission-common@0.6.5-next.1

## @backstage/plugin-catalog-graph@0.2.22-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog-graphql@0.3.14-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-import@0.8.13-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/integration-react@1.1.5-next.1

## @backstage/plugin-catalog-node@1.1.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/backend-plugin-api@0.1.3-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/types@1.0.0

## @backstage/plugin-cicd-statistics@0.1.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1

## @backstage/plugin-cicd-statistics-module-gitlab@0.1.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-cicd-statistics@0.1.12-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1

## @backstage/plugin-circleci@0.3.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-cloudbuild@0.3.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-code-climate@0.1.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage@0.2.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage-backend@0.2.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1

## @backstage/plugin-codescene@0.1.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-config-schema@0.1.33-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0

## @backstage/plugin-cost-insights@0.11.32-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-cost-insights-common@0.1.1

## @backstage/plugin-dynatrace@1.0.0-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-explore@0.3.41-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-explore-react@0.0.22-next.1

## @backstage/plugin-explore-react@0.0.22-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.0.7-next.1

## @backstage/plugin-firehydrant@0.1.27-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-fossa@0.2.42-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-gcalendar@0.3.6-next.1

### Patch Changes

- 4c2ed7ecf1: Fixed loader showing when user not signed in
- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-gcp-projects@0.3.29-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-git-release-manager@0.3.23-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-actions@0.5.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-deployments@0.1.41-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/integration-react@1.1.5-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-issues@0.1.2-next.1

### Patch Changes

- 8c7bff2bb4: Updated the `luxon` dependency to 3.x
- 719ccbb963: Properly filter on relations instead of the spec, when finding by owner
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-pull-requests-board@0.1.4-next.1

### Patch Changes

- 719ccbb963: Properly filter on relations instead of the spec, when finding by owner
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-gitops-profiles@0.3.28-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-gocd@0.1.16-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-graphiql@0.2.42-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-graphql-backend@0.1.27-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/plugin-catalog-graphql@0.3.14-next.1

## @backstage/plugin-home@0.4.26-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/plugin-stack-overflow@0.1.6-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-ilert@0.1.36-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-jenkins@0.7.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-jenkins-common@0.1.9-next.1

## @backstage/plugin-jenkins-backend@0.1.27-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/plugin-auth-node@0.2.6-next.1
  - @backstage/plugin-jenkins-common@0.1.9-next.1
  - @backstage/plugin-permission-common@0.6.5-next.1

## @backstage/plugin-jenkins-common@0.1.9-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-common@1.0.7-next.1
  - @backstage/plugin-permission-common@0.6.5-next.1

## @backstage/plugin-kafka@0.3.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-kafka-backend@0.2.30-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1

## @backstage/plugin-kubernetes@0.7.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-kubernetes-common@0.4.3-next.1

## @backstage/plugin-kubernetes-backend@0.7.3-next.1

### Patch Changes

- d4a8c683be: kubernetes service locator now take request context parameters
- Updated dependencies
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/plugin-auth-node@0.2.6-next.1
  - @backstage/plugin-kubernetes-common@0.4.3-next.1

## @backstage/plugin-kubernetes-common@0.4.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.1

## @backstage/plugin-lighthouse@0.3.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic@0.3.28-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic-dashboard@0.2.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1

## @backstage/plugin-org@0.5.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-pagerduty@0.5.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop@0.1.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop-backend@0.1.8-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/config@1.0.3-next.1

## @backstage/plugin-permission-backend@0.5.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/plugin-auth-node@0.2.6-next.1
  - @backstage/plugin-permission-common@0.6.5-next.1
  - @backstage/plugin-permission-node@0.6.6-next.1

## @backstage/plugin-permission-common@0.6.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1

## @backstage/plugin-permission-node@0.6.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/plugin-auth-node@0.2.6-next.1
  - @backstage/plugin-permission-common@0.6.5-next.1

## @backstage/plugin-permission-react@0.4.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/plugin-permission-common@0.6.5-next.1

## @backstage/plugin-playlist@0.1.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/plugin-search-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.7-next.1
  - @backstage/plugin-permission-common@0.6.5-next.1
  - @backstage/plugin-permission-react@0.4.6-next.1
  - @backstage/plugin-playlist-common@0.1.1-next.1

## @backstage/plugin-playlist-backend@0.1.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-test-utils@0.1.29-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/plugin-auth-node@0.2.6-next.1
  - @backstage/plugin-permission-common@0.6.5-next.1
  - @backstage/plugin-permission-node@0.6.6-next.1
  - @backstage/plugin-playlist-common@0.1.1-next.1

## @backstage/plugin-playlist-common@0.1.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.6.5-next.1

## @backstage/plugin-proxy-backend@0.2.31-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/config@1.0.3-next.1

## @backstage/plugin-rollbar@0.4.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-rollbar-backend@0.1.34-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/config@1.0.3-next.1

## @backstage/plugin-scaffolder@1.7.0-next.1

### Patch Changes

- e4f0a96424: Making the description of the GitLab repoUrl owner field more clearer by focusing it refers to the GitLab namespace.
- 8220f2fd83: Support custom layouts in dry run editor
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/integration-react@1.1.5-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/plugin-catalog-common@1.0.7-next.1
  - @backstage/plugin-permission-react@0.4.6-next.1
  - @backstage/plugin-scaffolder-common@1.2.1-next.1

## @backstage/plugin-scaffolder-backend@1.7.0-next.1

### Patch Changes

- 489621f613: Switching off duplicated timestamp in case of logging via task logger in a custom action
- a35a27df70: Updated the `moduleId` of the experimental module export.
- Updated dependencies
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-plugin-api@0.1.3-next.1
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/types@1.0.0
  - @backstage/plugin-auth-node@0.2.6-next.1
  - @backstage/plugin-catalog-backend@1.4.1-next.1
  - @backstage/plugin-catalog-node@1.1.1-next.1
  - @backstage/plugin-scaffolder-common@1.2.1-next.1

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.2.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/plugin-scaffolder-backend@1.7.0-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/types@1.0.0

## @backstage/plugin-scaffolder-backend-module-rails@0.4.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/plugin-scaffolder-backend@1.7.0-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/types@1.0.0

## @backstage/plugin-scaffolder-backend-module-yeoman@0.2.10-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.7.0-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/types@1.0.0

## @backstage/plugin-scaffolder-common@1.2.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/types@1.0.0

## @backstage/plugin-search@1.0.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/plugin-search-react@1.2.0-next.1
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1

## @backstage/plugin-search-backend-module-elasticsearch@1.0.3-next.1

### Patch Changes

- a799972bb1: The search engine has been updated to take advantage of the `pageLimit` property on search queries. If none is provided, the search engine will continue to use its default value of 25 results per page.
- Updated dependencies
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/plugin-search-backend-node@1.0.3-next.1
  - @backstage/config@1.0.3-next.1

## @backstage/plugin-search-backend-module-pg@0.4.1-next.1

### Patch Changes

- a799972bb1: The search engine has been updated to take advantage of the `pageLimit` property on search queries. If none is provided, the search engine will continue to use its default value of 25 results per page.
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/plugin-search-backend-node@1.0.3-next.1
  - @backstage/config@1.0.3-next.1

## @backstage/plugin-search-backend-node@1.0.3-next.1

### Patch Changes

- a799972bb1: The search engine has been updated to take advantage of the `pageLimit` property on search queries. If none is provided, the search engine will continue to use its default value of 25 results per page.
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/plugin-permission-common@0.6.5-next.1

## @backstage/plugin-sentry@0.4.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-shortcuts@0.3.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0

## @backstage/plugin-sonarqube@0.4.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-sonarqube-backend@0.1.2-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1

## @backstage/plugin-splunk-on-call@0.3.34-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-stack-overflow@0.1.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/plugin-home@0.4.26-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-stack-overflow-backend@0.1.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.20.0-next.1
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/config@1.0.3-next.1

## @backstage/plugin-tech-insights@0.3.1-next.1

### Patch Changes

- a60a6807bd: making available the search for the last FACTS executed
- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/plugin-tech-insights-common@0.2.7-next.1

## @backstage/plugin-tech-insights-backend@0.5.3-next.1

### Patch Changes

- f7cbfb97ed: Modify router endpoint to handle singular and collections of request parameters similarly.
- Updated dependencies
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/plugin-tech-insights-common@0.2.7-next.1
  - @backstage/plugin-tech-insights-node@0.3.5-next.1

## @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.21-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/plugin-tech-insights-common@0.2.7-next.1
  - @backstage/plugin-tech-insights-node@0.3.5-next.1

## @backstage/plugin-tech-insights-common@0.2.7-next.1

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.0

## @backstage/plugin-tech-insights-node@0.3.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/types@1.0.0
  - @backstage/plugin-tech-insights-common@0.2.7-next.1

## @backstage/plugin-tech-radar@0.5.17-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs@1.3.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/plugin-search-react@1.2.0-next.1
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/integration-react@1.1.5-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-techdocs-react@1.0.5-next.1

## @backstage/plugin-techdocs-addons-test-utils@1.0.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.6.0-next.1
  - @backstage/core-app-api@1.1.1-next.1
  - @backstage/plugin-search-react@1.2.0-next.1
  - @backstage/plugin-techdocs@1.3.3-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/test-utils@1.2.1-next.1
  - @backstage/integration-react@1.1.5-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-techdocs-react@1.0.5-next.1

## @backstage/plugin-techdocs-backend@1.3.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/plugin-catalog-common@1.0.7-next.1
  - @backstage/plugin-permission-common@0.6.5-next.1
  - @backstage/plugin-techdocs-node@1.4.1-next.1

## @backstage/plugin-techdocs-module-addons-contrib@1.0.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/integration-react@1.1.5-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-techdocs-react@1.0.5-next.1

## @backstage/plugin-techdocs-node@1.4.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1

## @backstage/plugin-techdocs-react@1.0.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/version-bridge@1.0.1

## @backstage/plugin-todo@0.2.12-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-todo-backend@0.1.34-next.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/integration@1.3.2-next.1

## @backstage/plugin-user-settings@0.5.0-next.1

### Patch Changes

- 06d61d1266: Handle errors that may occur when the user logs out
- 44c9a95dcf: Prevent `.set()` to execute a request to the StorageClient if the user is `guest`
- 174f02a00a: Update installation instructions
- Updated dependencies
  - @backstage/core-app-api@1.1.1-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0

## @backstage/plugin-user-settings-backend@0.1.1-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/types@1.0.0
  - @backstage/plugin-auth-node@0.2.6-next.1

## @backstage/plugin-vault@0.1.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-vault-backend@0.2.3-next.1

### Patch Changes

- dae0bbe522: VaultBuilder.tsx renamed to VaultBuilder in order for module to be correctly loaded.
- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/backend-test-utils@0.1.29-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1

## @backstage/plugin-xcmetrics@0.2.30-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/theme@0.2.16

## example-app@0.2.76-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-user-settings@0.5.0-next.1
  - @backstage/plugin-scaffolder@1.7.0-next.1
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/core-app-api@1.1.1-next.1
  - @backstage/cli@0.20.0-next.1
  - @backstage/plugin-tech-insights@0.3.1-next.1
  - @backstage/plugin-gcalendar@0.3.6-next.1
  - @backstage/plugin-api-docs@0.8.10-next.1
  - @backstage/plugin-search-react@1.2.0-next.1
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/plugin-airbrake@0.3.10-next.1
  - @backstage/plugin-azure-devops@0.2.1-next.1
  - @backstage/plugin-badges@0.2.34-next.1
  - @internal/plugin-catalog-customized@0.0.3-next.1
  - @backstage/plugin-catalog-graph@0.2.22-next.1
  - @backstage/plugin-catalog-import@0.8.13-next.1
  - @backstage/plugin-circleci@0.3.10-next.1
  - @backstage/plugin-cloudbuild@0.3.10-next.1
  - @backstage/plugin-code-coverage@0.2.3-next.1
  - @backstage/plugin-dynatrace@1.0.0-next.1
  - @backstage/plugin-explore@0.3.41-next.1
  - @backstage/plugin-github-actions@0.5.10-next.1
  - @backstage/plugin-gocd@0.1.16-next.1
  - @backstage/plugin-home@0.4.26-next.1
  - @backstage/plugin-jenkins@0.7.9-next.1
  - @backstage/plugin-kafka@0.3.10-next.1
  - @backstage/plugin-kubernetes@0.7.3-next.1
  - @backstage/plugin-lighthouse@0.3.10-next.1
  - @backstage/plugin-newrelic-dashboard@0.2.3-next.1
  - @backstage/plugin-org@0.5.10-next.1
  - @backstage/plugin-pagerduty@0.5.3-next.1
  - @backstage/plugin-playlist@0.1.1-next.1
  - @backstage/plugin-rollbar@0.4.10-next.1
  - @backstage/plugin-search@1.0.3-next.1
  - @backstage/plugin-sentry@0.4.3-next.1
  - @backstage/plugin-techdocs@1.3.3-next.1
  - @backstage/plugin-todo@0.2.12-next.1
  - @backstage/app-defaults@1.0.7-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/plugin-apache-airflow@0.2.3-next.1
  - @backstage/plugin-cost-insights@0.11.32-next.1
  - @backstage/plugin-gcp-projects@0.3.29-next.1
  - @backstage/plugin-graphiql@0.2.42-next.1
  - @backstage/plugin-newrelic@0.3.28-next.1
  - @backstage/plugin-shortcuts@0.3.2-next.1
  - @backstage/plugin-stack-overflow@0.1.6-next.1
  - @backstage/plugin-tech-radar@0.5.17-next.1
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.5-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/integration-react@1.1.5-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.7-next.1
  - @backstage/plugin-permission-react@0.4.6-next.1
  - @backstage/plugin-techdocs-react@1.0.5-next.1

## example-backend@0.2.76-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-backend@0.17.0-next.1
  - @backstage/plugin-search-backend@1.1.0-next.1
  - @backstage/catalog-client@1.1.1-next.1
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/plugin-scaffolder-backend@1.7.0-next.1
  - @backstage/plugin-search-common@1.1.0-next.1
  - @backstage/plugin-search-backend-node@1.0.3-next.1
  - @backstage/plugin-search-backend-module-pg@0.4.1-next.1
  - @backstage/plugin-search-backend-module-elasticsearch@1.0.3-next.1
  - @backstage/plugin-kubernetes-backend@0.7.3-next.1
  - @backstage/plugin-tech-insights-backend@0.5.3-next.1
  - example-app@0.2.76-next.1
  - @backstage/backend-tasks@0.3.6-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/integration@1.3.2-next.1
  - @backstage/plugin-app-backend@0.3.37-next.1
  - @backstage/plugin-auth-node@0.2.6-next.1
  - @backstage/plugin-azure-devops-backend@0.3.16-next.1
  - @backstage/plugin-badges-backend@0.1.31-next.1
  - @backstage/plugin-catalog-backend@1.4.1-next.1
  - @backstage/plugin-code-coverage-backend@0.2.3-next.1
  - @backstage/plugin-graphql-backend@0.1.27-next.1
  - @backstage/plugin-jenkins-backend@0.1.27-next.1
  - @backstage/plugin-kafka-backend@0.2.30-next.1
  - @backstage/plugin-permission-backend@0.5.12-next.1
  - @backstage/plugin-permission-common@0.6.5-next.1
  - @backstage/plugin-permission-node@0.6.6-next.1
  - @backstage/plugin-playlist-backend@0.1.1-next.1
  - @backstage/plugin-proxy-backend@0.2.31-next.1
  - @backstage/plugin-rollbar-backend@0.1.34-next.1
  - @backstage/plugin-scaffolder-backend-module-rails@0.4.5-next.1
  - @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.21-next.1
  - @backstage/plugin-tech-insights-node@0.3.5-next.1
  - @backstage/plugin-techdocs-backend@1.3.1-next.1
  - @backstage/plugin-todo-backend@0.1.34-next.1

## example-backend-next@0.0.4-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.7.0-next.1
  - @backstage/backend-defaults@0.1.2-next.1
  - @backstage/plugin-app-backend@0.3.37-next.1
  - @backstage/plugin-catalog-backend@1.4.1-next.1

## techdocs-cli-embedded-app@0.2.75-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.6.0-next.1
  - @backstage/core-app-api@1.1.1-next.1
  - @backstage/cli@0.20.0-next.1
  - @backstage/plugin-techdocs@1.3.3-next.1
  - @backstage/app-defaults@1.0.7-next.1
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/test-utils@1.2.1-next.1
  - @backstage/catalog-model@1.1.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/integration-react@1.1.5-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-techdocs-react@1.0.5-next.1

## @internal/plugin-catalog-customized@0.0.3-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0-next.1
  - @backstage/plugin-catalog@1.6.0-next.1

## @internal/plugin-todo-list@1.0.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2-next.1
  - @backstage/core-plugin-api@1.0.7-next.1
  - @backstage/theme@0.2.16

## @internal/plugin-todo-list-backend@1.0.6-next.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2-next.1
  - @backstage/config@1.0.3-next.1
  - @backstage/errors@1.1.2-next.1
  - @backstage/plugin-auth-node@0.2.6-next.1

## @internal/plugin-todo-list-common@1.0.5-next.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.6.5-next.1
