# Release v1.9.0-next.4

## @backstage/cli@0.22.0-next.4

### Minor Changes

- 736f893f72: The Jest configuration that was previously enabled with `BACKSTAGE_NEXT_TESTS` is now enabled by default. To revert to the old configuration you can now instead set `BAC<PERSON><PERSON><PERSON>_OLD_TESTS`.

  This new configuration uses the `babel` coverage provider rather than `v8`. It used to be that `v8` worked better when using Sucrase for transpilation, but now that we have switched to SWC, `babel` seems to work better. In addition, the new configuration also enables source maps by default, as they no longer have a negative impact on code coverage accuracy, and it also enables a modified Jest runtime with additional caching of script objects.

### Patch Changes

- dd721148b5: Updated Jest coverage configuration to only apply either in the root project or package configuration, depending on whether repo or package tests are run.
- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- c27eabef6b: Adds new web-library package option when generating a new plugin
- 309f2daca4: Updated dependency `esbuild` to `^0.16.0`.
- d9d9a7a134: Removed all copyright notices from package templates.
- Updated dependencies
  - @backstage/cli-common@0.1.11-next.0
  - @backstage/config@1.0.5-next.1
  - @backstage/config-loader@1.1.7-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/release-manifests@0.0.8-next.0
  - @backstage/types@1.0.2-next.1

## @backstage/core-app-api@1.3.0-next.4

### Minor Changes

- e0d9c9559a: Added a new `AppRouter` component and `app.createRoot()` method that replaces `app.getRouter()` and `app.getProvider()`, which are now deprecated. The new `AppRouter` component is a drop-in replacement for the old router component, while the new `app.createRoot()` method is used instead of the old provider component.

  An old app setup might look like this:

  ```tsx
  const app = createApp(/* ... */);

  const AppProvider = app.getProvider();
  const AppRouter = app.getRouter();

  const routes = ...;

  const App = () => (
    <AppProvider>
      <AlertDisplay />
      <OAuthRequestDialog />
      <AppRouter>
        <Root>{routes}</Root>
      </AppRouter>
    </AppProvider>
  );

  export default App;
  ```

  With these new APIs, the setup now looks like this:

  ```tsx
  import { AppRouter } from '@backstage/core-app-api';

  const app = createApp(/* ... */);

  const routes = ...;

  export default app.createRoot(
    <>
      <AlertDisplay />
      <OAuthRequestDialog />
      <AppRouter>
        <Root>{routes}</Root>
      </AppRouter>
    </>,
  );
  ```

  Note that `app.createRoot()` accepts a React element, rather than a component.

### Patch Changes

- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- Updated dependencies
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/types@1.0.2-next.1
  - @backstage/version-bridge@1.0.3-next.0

## @backstage/integration-aws-node@0.1.0-next.0

### Minor Changes

- 13278732f6: New package for AWS integration node library

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-scaffolder@1.9.0-next.4

### Minor Changes

- 34a48cdc4f: The `RepoUrlPicker` field extension now has an `allowedProjects` option for narrowing the selection of Bitbucket URLs.

### Patch Changes

- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- db6310b6a0: Show input type array correctly on installed actions page.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/plugin-permission-react@0.4.8-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/integration-react@1.1.7-next.4
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.3
  - @backstage/plugin-scaffolder-common@1.2.3-next.1

## @backstage/plugin-scaffolder-backend@1.9.0-next.3

### Minor Changes

- 0053d07bee: Update the `github:publish` action to allow passing wether to dismiss stale reviews on the protected default branch.

### Patch Changes

- 935b66a646: Change step output template examples to use square bracket syntax.
- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- 309f2daca4: Updated dependency `esbuild` to `^0.16.0`.
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.3
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-auth-node@0.2.8-next.3
  - @backstage/plugin-catalog-node@1.3.0-next.3
  - @backstage/plugin-scaffolder-common@1.2.3-next.1

## @backstage/plugin-search-backend-module-elasticsearch@1.1.0-next.3

### Minor Changes

- d09485ea79: Added support for self hosted OpenSearch via new provider

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-search-backend-node@1.1.0-next.3
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-techdocs-backend@1.5.0-next.3

### Minor Changes

- dfbdae092e: Added a new optional `accountId` to the configuration options of the AWS S3 publisher. Configuring this option will source credentials for the `accountId` in the `aws` app config section. See <https://github.com/backstage/backstage/blob/master/packages/integration-aws-node/README.md> for more details.

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/plugin-techdocs-node@1.4.3-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.3
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/app-defaults@1.0.9-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-app-api@1.3.0-next.4
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-permission-react@0.4.8-next.3
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/backend-app-api@0.2.4-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/plugin-permission-node@0.7.2-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/errors@1.1.4-next.1

## @backstage/backend-common@0.17.0-next.3

### Patch Changes

- 840f2113c6: Fix `GitlabUrlReader.readTree` bug when there were no matching commits
- Updated dependencies
  - @backstage/cli-common@0.1.11-next.0
  - @backstage/config@1.0.5-next.1
  - @backstage/config-loader@1.1.7-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/backend-defaults@0.1.4-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-app-api@0.2.4-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3

## @backstage/backend-plugin-api@0.2.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1

## @backstage/backend-tasks@0.4.0-next.3

### Patch Changes

- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/backend-test-utils@0.1.31-next.4

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.22.0-next.4
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-app-api@0.2.4-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/config@1.0.5-next.1

## @backstage/core-components@0.12.1-next.4

### Patch Changes

- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- a5a2d12298: Added option to pass additional headers to `<ProxiedSignInPage />`, which are passed along with the request to the underlying provider
- Updated dependencies
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/version-bridge@1.0.3-next.0

## @backstage/create-app@0.4.35-next.4

### Patch Changes

- 935b66a646: Change step output template examples to use square bracket syntax.

- dfb269fab2: Updated the template to have the `'/test'` proxy endpoint in `app-config.yaml` be commented out by default.

- d9b3753f87: Updated the app template to use the new `AppRouter` component instead of `app.getRouter()`, as well as `app.createRoot()` instead of `app.getProvider()`.

  To apply this change to an existing app, make the following change to `packages/app/src/App.tsx`:

  ```diff
  -import { FlatRoutes } from '@backstage/core-app-api';
  +import { AppRouter, FlatRoutes } from '@backstage/core-app-api';

   ...

  -const AppProvider = app.getProvider();
  -const AppRouter = app.getRouter();

   ...

  -const App = () => (
  +export default app.createRoot(
  -  <AppProvider>
  +  <>
       <AlertDisplay />
       <OAuthRequestDialog />
       <AppRouter>
         <Root>{routes}</Root>
       </AppRouter>
  -  </AppProvider>
  +  </>,
   );
  ```

  The final export step should end up looking something like this:

  ```tsx
  export default app.createRoot(
    <>
      <AlertDisplay />
      <OAuthRequestDialog />
      <AppRouter>
        <Root>{routes}</Root>
      </AppRouter>
    </>,
  );
  ```

  Note that `app.createRoot()` accepts a React element, rather than a component.

- 71e75c0b70: Removed the `react-router` dependency from the app package, using only `react-router-dom` instead.

  This change is just a bit of cleanup and is optional. If you want to apply it to your app, remove the `react-router` dependency from `packages/app/package.json`, and replace any imports from `react-router` with `react-router-dom` instead.

- Updated dependencies
  - @backstage/cli-common@0.1.11-next.0

## @backstage/dev-utils@1.0.9-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-app-api@1.3.0-next.4
  - @backstage/core-components@0.12.1-next.4
  - @backstage/app-defaults@1.0.9-next.4
  - @backstage/test-utils@1.2.3-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration-react@1.1.7-next.4
  - @backstage/theme@0.2.16

## @backstage/integration-react@1.1.7-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @backstage/repo-tools@0.1.0-next.2

### Patch Changes

- a8611bcac4: Add new command options to the `api-report`

  - added `--allow-warnings`, `-a` to continue processing packages if selected packages have warnings
  - added `--allow-all-warnings` to continue processing packages any packages have warnings
  - added `--omit-messages`, `-o` to pass some warnings messages code to be omitted from the api-report.md files
  - The `paths` argument for this command now takes as default the value on `workspaces.packages` inside the root package.json
  - change the path resolution to use the `@backstage/cli-common` packages instead

- Updated dependencies
  - @backstage/cli-common@0.1.11-next.0
  - @backstage/errors@1.1.4-next.1

## @techdocs/cli@1.2.4-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/plugin-techdocs-node@1.4.3-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/cli-common@0.1.11-next.0
  - @backstage/config@1.0.5-next.1

## @backstage/test-utils@1.2.3-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-app-api@1.3.0-next.4
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/plugin-permission-react@0.4.8-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-adr@0.2.4-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/plugin-search-react@1.3.0-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration-react@1.1.7-next.4
  - @backstage/theme@0.2.16
  - @backstage/plugin-adr-common@0.2.4-next.3
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-adr-backend@0.2.4-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/plugin-adr-common@0.2.4-next.3
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-adr-common@0.2.4-next.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-airbrake@0.3.12-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/dev-utils@1.0.9-next.4
  - @backstage/test-utils@1.2.3-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-airbrake-backend@0.2.12-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-allure@0.1.28-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-analytics-module-ga@0.1.23-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-apache-airflow@0.2.5-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/core-plugin-api@1.2.0-next.2

## @backstage/plugin-api-docs@0.8.12-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog@1.7.0-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-apollo-explorer@0.1.5-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-app-backend@0.3.39-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/config-loader@1.1.7-next.2
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-auth-backend@0.17.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-auth-node@0.2.8-next.3

## @backstage/plugin-auth-node@0.2.8-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-azure-devops@0.2.3-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-azure-devops-backend@0.3.18-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-azure-sites@0.1.1-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-sites-common@0.1.0

## @backstage/plugin-azure-sites-backend@0.1.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-azure-sites-common@0.1.0

## @backstage/plugin-badges@0.2.36-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-badges-backend@0.1.33-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-bazaar@0.2.1-next.4

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.22.0-next.4
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog@1.7.0-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-bazaar-backend@0.2.2-next.4

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-test-utils@0.1.31-next.4
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-auth-node@0.2.8-next.3

## @backstage/plugin-bitrise@0.1.39-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog@1.7.0-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/plugin-search-react@1.3.0-next.4
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration-react@1.1.7-next.4
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.3
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-catalog-backend@1.6.0-next.3

### Patch Changes

- ba13ff663c: Added a new `catalog.rules[].location` configuration that makes it possible to configure catalog rules to only apply to specific locations, either via exact match or a glob pattern.
- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- 71147d5c16: Internal code reorganization.
- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/plugin-permission-node@0.7.2-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.3
  - @backstage/plugin-catalog-node@1.3.0-next.3
  - @backstage/plugin-scaffolder-common@1.2.3-next.1
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-catalog-backend-module-aws@0.1.12-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.3
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-node@1.3.0-next.3

## @backstage/plugin-catalog-backend-module-azure@0.1.10-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.3
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-node@1.3.0-next.3

## @backstage/plugin-catalog-backend-module-bitbucket@0.2.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.2-next.1

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.1.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.3
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.2-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.3
  - @backstage/plugin-catalog-node@1.3.0-next.3
  - @backstage/plugin-events-node@0.2.0-next.3

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.1.4-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.3
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/plugin-catalog-node@1.3.0-next.3

## @backstage/plugin-catalog-backend-module-gerrit@0.1.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.3
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/plugin-catalog-node@1.3.0-next.3

## @backstage/plugin-catalog-backend-module-github@0.2.2-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.3
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.3
  - @backstage/plugin-catalog-node@1.3.0-next.3
  - @backstage/plugin-events-node@0.2.0-next.3

## @backstage/plugin-catalog-backend-module-gitlab@0.1.10-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.3
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-node@1.3.0-next.3

## @backstage/plugin-catalog-backend-module-incremental-ingestion@0.1.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.3
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/backend-test-utils@0.1.31-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-catalog-node@1.3.0-next.3

## @backstage/plugin-catalog-backend-module-ldap@0.5.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.3
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-catalog-backend-module-msgraph@0.4.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.3
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-catalog-node@1.3.0-next.3

## @backstage/plugin-catalog-backend-module-openapi@0.1.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-node@1.3.0-next.3

## @backstage/plugin-catalog-common@1.0.9-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-catalog-graph@0.2.24-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog-import@0.9.2-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/integration-react@1.1.7-next.4
  - @backstage/plugin-catalog-common@1.0.9-next.3

## @backstage/plugin-catalog-node@1.3.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-catalog-common@1.0.9-next.3

## @backstage/plugin-catalog-react@1.2.2-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/plugin-permission-react@0.4.8-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/version-bridge@1.0.3-next.0
  - @backstage/plugin-catalog-common@1.0.9-next.3

## @backstage/plugin-cicd-statistics@0.1.14-next.4

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2

## @backstage/plugin-cicd-statistics-module-gitlab@0.1.8-next.4

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/plugin-cicd-statistics@0.1.14-next.4

## @backstage/plugin-circleci@0.3.12-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-cloudbuild@0.3.12-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-code-climate@0.1.12-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage@0.2.5-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage-backend@0.2.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1

## @backstage/plugin-codescene@0.1.7-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-config-schema@0.1.35-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-cost-insights@0.12.1-next.4

### Patch Changes

- 593c22253a: Added Y axis for metric data, with relevant formatting and data domain
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/test-utils@1.2.3-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-cost-insights-common@0.1.1

## @backstage/plugin-dynatrace@1.0.2-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-events-backend@0.2.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-events-node@0.2.0-next.3

## @backstage/plugin-events-backend-module-aws-sqs@0.1.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-events-node@0.2.0-next.3

## @backstage/plugin-events-backend-module-azure@0.1.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/plugin-events-node@0.2.0-next.3

## @backstage/plugin-events-backend-module-bitbucket-cloud@0.1.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/plugin-events-node@0.2.0-next.3

## @backstage/plugin-events-backend-module-gerrit@0.1.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/plugin-events-node@0.2.0-next.3

## @backstage/plugin-events-backend-module-github@0.1.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-events-node@0.2.0-next.3

## @backstage/plugin-events-backend-module-gitlab@0.1.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-events-node@0.2.0-next.3

## @backstage/plugin-events-backend-test-utils@0.1.1-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.2.0-next.3

## @backstage/plugin-events-node@0.2.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@0.2.0-next.3

## @backstage/plugin-explore@0.3.43-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/plugin-search-react@1.3.0-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-explore-common@0.0.1-next.0
  - @backstage/plugin-explore-react@0.0.24-next.2
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-explore-backend@0.0.1-next.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-explore-common@0.0.1-next.0
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-firehydrant@0.1.29-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-fossa@0.2.44-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-gcalendar@0.3.8-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-gcp-projects@0.3.31-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-git-release-manager@0.3.25-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-actions@0.5.12-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-deployments@0.1.43-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/integration-react@1.1.7-next.4
  - @backstage/theme@0.2.16

## @backstage/plugin-github-issues@0.2.1-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-github-pull-requests-board@0.1.6-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration@1.4.1-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-gitops-profiles@0.3.30-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-gocd@0.1.18-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-graphiql@0.2.44-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-graphql-backend@0.1.29-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-catalog-graphql@0.3.16-next.1

## @backstage/plugin-home@0.4.28-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/plugin-stack-overflow@0.1.8-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-ilert@0.2.1-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-jenkins@0.7.11-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-jenkins-common@0.1.11-next.3

## @backstage/plugin-jenkins-backend@0.1.29-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-auth-node@0.2.8-next.3
  - @backstage/plugin-jenkins-common@0.1.11-next.3

## @backstage/plugin-jenkins-common@0.1.11-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/plugin-catalog-common@1.0.9-next.3

## @backstage/plugin-kafka@0.3.12-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-kafka-backend@0.2.32-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-kubernetes@0.7.5-next.4

### Patch Changes

- 365f887717: Removed rendering for ErrorEmptyState in ErrorReporting component, so nothing is rendered when there are no errors. Also removed Divider on Kubernetes page.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-kubernetes-common@0.4.5-next.1

## @backstage/plugin-kubernetes-backend@0.8.1-next.4

### Patch Changes

- 22e20b3a59: Clusters declared in the app-config can now have their CA configured via a local filesystem path using the `caFile` property.
- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-test-utils@0.1.31-next.4
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-auth-node@0.2.8-next.3
  - @backstage/plugin-kubernetes-common@0.4.5-next.1

## @backstage/plugin-lighthouse@0.3.12-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic@0.3.30-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic-dashboard@0.2.5-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-org@0.6.2-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-org-react@0.1.1-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-pagerduty@0.5.5-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop@0.1.10-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop-backend@0.1.10-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-permission-backend@0.5.14-next.3

### Patch Changes

- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/plugin-permission-node@0.7.2-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-auth-node@0.2.8-next.3

## @backstage/plugin-permission-common@0.7.2-next.2

### Patch Changes

- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- Updated dependencies
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-permission-node@0.7.2-next.3

### Patch Changes

- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-auth-node@0.2.8-next.3

## @backstage/plugin-permission-react@0.4.8-next.3

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2

## @backstage/plugin-playlist@0.1.3-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/plugin-permission-react@0.4.8-next.3
  - @backstage/plugin-search-react@1.3.0-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/plugin-catalog-common@1.0.9-next.3
  - @backstage/plugin-playlist-common@0.1.3-next.2

## @backstage/plugin-playlist-backend@0.2.2-next.4

### Patch Changes

- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/plugin-permission-node@0.7.2-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-test-utils@0.1.31-next.4
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-auth-node@0.2.8-next.3
  - @backstage/plugin-playlist-common@0.1.3-next.2

## @backstage/plugin-playlist-common@0.1.3-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.2

## @backstage/plugin-proxy-backend@0.2.33-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-rollbar@0.4.12-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-rollbar-backend@0.1.36-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.2.14-next.3

### Patch Changes

- 935b66a646: Change step output template examples to use square bracket syntax.
- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.9.0-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-scaffolder-backend-module-rails@0.4.7-next.3

### Patch Changes

- 935b66a646: Change step output template examples to use square bracket syntax.
- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.9.0-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-scaffolder-backend-module-yeoman@0.2.12-next.3

### Patch Changes

- 935b66a646: Change step output template examples to use square bracket syntax.
- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.9.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-search@1.0.5-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/plugin-search-react@1.3.0-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/version-bridge@1.0.3-next.0
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-search-backend@1.2.0-next.3

### Patch Changes

- b05dcd5530: Move the `zod` dependency to a version that does not collide with other libraries
- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/plugin-permission-node@0.7.2-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-auth-node@0.2.8-next.3
  - @backstage/plugin-search-backend-node@1.1.0-next.3
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-search-backend-module-pg@0.4.3-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-search-backend-node@1.1.0-next.3
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-search-backend-node@1.1.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-search-common@1.2.0-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-search-react@1.3.0-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/version-bridge@1.0.3-next.0
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-sentry@0.4.5-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-shortcuts@0.3.4-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-sonarqube@0.5.1-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-sonarqube-backend@0.1.4-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-splunk-on-call@0.4.1-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-stack-overflow@0.1.8-next.4

### Patch Changes

- c981e83612: The `<StackOverflowSearchResultListItem />` component is now able to highlight the result title and/or text when provided. To take advantage of this, pass in the `highlight` prop, similar to how it is done on other result list item components.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-home@0.4.28-next.4
  - @backstage/plugin-search-react@1.3.0-next.4
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-stack-overflow-backend@0.1.8-next.4

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.22.0-next.4
  - @backstage/config@1.0.5-next.1
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-tech-insights@0.3.4-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-tech-insights-common@0.2.9-next.1

## @backstage/plugin-tech-insights-backend@0.5.5-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-tech-insights-common@0.2.9-next.1
  - @backstage/plugin-tech-insights-node@0.3.7-next.3

## @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.23-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-tech-insights-common@0.2.9-next.1
  - @backstage/plugin-tech-insights-node@0.3.7-next.3

## @backstage/plugin-tech-insights-node@0.3.7-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-tech-insights-common@0.2.9-next.1

## @backstage/plugin-tech-radar@0.5.19-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs@1.4.1-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/plugin-search-react@1.3.0-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/integration-react@1.1.7-next.4
  - @backstage/theme@0.2.16
  - @backstage/plugin-search-common@1.2.0-next.3
  - @backstage/plugin-techdocs-react@1.0.7-next.4

## @backstage/plugin-techdocs-addons-test-utils@1.0.7-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-app-api@1.3.0-next.4
  - @backstage/core-components@0.12.1-next.4
  - @backstage/test-utils@1.2.3-next.4
  - @backstage/plugin-catalog@1.7.0-next.4
  - @backstage/plugin-search-react@1.3.0-next.4
  - @backstage/plugin-techdocs@1.4.1-next.4
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration-react@1.1.7-next.4
  - @backstage/theme@0.2.16
  - @backstage/plugin-techdocs-react@1.0.7-next.4

## @backstage/plugin-techdocs-module-addons-contrib@1.0.7-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration@1.4.1-next.1
  - @backstage/integration-react@1.1.7-next.4
  - @backstage/theme@0.2.16
  - @backstage/plugin-techdocs-react@1.0.7-next.4

## @backstage/plugin-techdocs-node@1.4.3-next.3

### Patch Changes

- e40790d0c2: Add support for specifying an S3 bucket's account ID and retrieving the credentials from the `aws` app config section. This is now the preferred way to configure AWS credentials for Techdocs.
- Updated dependencies
  - @backstage/integration-aws-node@0.1.0-next.0
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/plugin-search-common@1.2.0-next.3

## @backstage/plugin-techdocs-react@1.0.7-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/version-bridge@1.0.3-next.0

## @backstage/plugin-todo@0.2.14-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-todo-backend@0.1.36-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/integration@1.4.1-next.1

## @backstage/plugin-user-settings@0.6.0-next.4

### Patch Changes

- 2e701b3796: Internal refactor to use `react-router-dom` rather than `react-router`.
- Updated dependencies
  - @backstage/core-app-api@1.3.0-next.4
  - @backstage/core-components@0.12.1-next.4
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.2-next.1

## @backstage/plugin-user-settings-backend@0.1.3-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/types@1.0.2-next.1
  - @backstage/plugin-auth-node@0.2.8-next.3

## @backstage/plugin-vault@0.1.6-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## @backstage/plugin-vault-backend@0.2.5-next.4

### Patch Changes

- Updated dependencies
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/backend-test-utils@0.1.31-next.4
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1

## @backstage/plugin-xcmetrics@0.2.32-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/errors@1.1.4-next.1
  - @backstage/theme@0.2.16

## example-app@0.2.78-next.4

### Patch Changes

- Updated dependencies
  - @backstage/plugin-cost-insights@0.12.1-next.4
  - @backstage/plugin-kubernetes@0.7.5-next.4
  - @backstage/cli@0.22.0-next.4
  - @backstage/core-app-api@1.3.0-next.4
  - @backstage/core-components@0.12.1-next.4
  - @backstage/plugin-scaffolder@1.9.0-next.4
  - @backstage/app-defaults@1.0.9-next.4
  - @backstage/plugin-airbrake@0.3.12-next.4
  - @backstage/plugin-api-docs@0.8.12-next.4
  - @backstage/plugin-azure-devops@0.2.3-next.4
  - @backstage/plugin-badges@0.2.36-next.4
  - @backstage/plugin-catalog-graph@0.2.24-next.4
  - @backstage/plugin-catalog-import@0.9.2-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4
  - @backstage/plugin-circleci@0.3.12-next.4
  - @backstage/plugin-cloudbuild@0.3.12-next.4
  - @backstage/plugin-code-coverage@0.2.5-next.4
  - @backstage/plugin-explore@0.3.43-next.4
  - @backstage/plugin-github-actions@0.5.12-next.4
  - @backstage/plugin-home@0.4.28-next.4
  - @backstage/plugin-jenkins@0.7.11-next.4
  - @backstage/plugin-kafka@0.3.12-next.4
  - @backstage/plugin-org@0.6.2-next.4
  - @backstage/plugin-permission-react@0.4.8-next.3
  - @backstage/plugin-playlist@0.1.3-next.4
  - @backstage/plugin-rollbar@0.4.12-next.4
  - @backstage/plugin-search@1.0.5-next.4
  - @backstage/plugin-search-react@1.3.0-next.4
  - @backstage/plugin-sentry@0.4.5-next.4
  - @backstage/plugin-shortcuts@0.3.4-next.4
  - @backstage/plugin-techdocs@1.4.1-next.4
  - @backstage/plugin-todo@0.2.14-next.4
  - @backstage/plugin-user-settings@0.6.0-next.4
  - @backstage/plugin-stack-overflow@0.1.8-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration-react@1.1.7-next.4
  - @backstage/theme@0.2.16
  - @backstage/plugin-apache-airflow@0.2.5-next.4
  - @backstage/plugin-azure-sites@0.1.1-next.4
  - @backstage/plugin-catalog-common@1.0.9-next.3
  - @backstage/plugin-dynatrace@1.0.2-next.4
  - @backstage/plugin-gcalendar@0.3.8-next.4
  - @backstage/plugin-gcp-projects@0.3.31-next.4
  - @backstage/plugin-gocd@0.1.18-next.4
  - @backstage/plugin-graphiql@0.2.44-next.4
  - @backstage/plugin-lighthouse@0.3.12-next.4
  - @backstage/plugin-newrelic@0.3.30-next.4
  - @backstage/plugin-newrelic-dashboard@0.2.5-next.4
  - @backstage/plugin-pagerduty@0.5.5-next.4
  - @backstage/plugin-search-common@1.2.0-next.3
  - @backstage/plugin-tech-insights@0.3.4-next.4
  - @backstage/plugin-tech-radar@0.5.19-next.4
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.7-next.4
  - @backstage/plugin-techdocs-react@1.0.7-next.4
  - @internal/plugin-catalog-customized@0.0.5-next.4

## example-backend@0.2.78-next.4

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.3
  - @backstage/plugin-scaffolder-backend-module-rails@0.4.7-next.3
  - @backstage/plugin-scaffolder-backend@1.9.0-next.3
  - @backstage/backend-tasks@0.4.0-next.3
  - @backstage/plugin-permission-backend@0.5.14-next.3
  - @backstage/plugin-permission-common@0.7.2-next.2
  - @backstage/plugin-permission-node@0.7.2-next.3
  - @backstage/plugin-playlist-backend@0.2.2-next.4
  - @backstage/plugin-search-backend@1.2.0-next.3
  - @backstage/plugin-kubernetes-backend@0.8.1-next.4
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/plugin-search-backend-module-elasticsearch@1.1.0-next.3
  - @backstage/plugin-techdocs-backend@1.5.0-next.3
  - example-app@0.2.78-next.4
  - @backstage/catalog-client@1.2.0-next.1
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/integration@1.4.1-next.1
  - @backstage/plugin-app-backend@0.3.39-next.3
  - @backstage/plugin-auth-backend@0.17.2-next.3
  - @backstage/plugin-auth-node@0.2.8-next.3
  - @backstage/plugin-azure-devops-backend@0.3.18-next.3
  - @backstage/plugin-azure-sites-backend@0.1.1-next.3
  - @backstage/plugin-badges-backend@0.1.33-next.3
  - @backstage/plugin-catalog-node@1.3.0-next.3
  - @backstage/plugin-code-coverage-backend@0.2.5-next.3
  - @backstage/plugin-events-backend@0.2.0-next.3
  - @backstage/plugin-events-node@0.2.0-next.3
  - @backstage/plugin-explore-backend@0.0.1-next.2
  - @backstage/plugin-graphql-backend@0.1.29-next.3
  - @backstage/plugin-jenkins-backend@0.1.29-next.3
  - @backstage/plugin-kafka-backend@0.2.32-next.3
  - @backstage/plugin-proxy-backend@0.2.33-next.3
  - @backstage/plugin-rollbar-backend@0.1.36-next.3
  - @backstage/plugin-search-backend-module-pg@0.4.3-next.3
  - @backstage/plugin-search-backend-node@1.1.0-next.3
  - @backstage/plugin-search-common@1.2.0-next.3
  - @backstage/plugin-tech-insights-backend@0.5.5-next.3
  - @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.23-next.3
  - @backstage/plugin-tech-insights-node@0.3.7-next.3
  - @backstage/plugin-todo-backend@0.1.36-next.3

## example-backend-next@0.0.6-next.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.6.0-next.3
  - @backstage/plugin-scaffolder-backend@1.9.0-next.3
  - @backstage/backend-defaults@0.1.4-next.3
  - @backstage/plugin-app-backend@0.3.39-next.3

## techdocs-cli-embedded-app@0.2.77-next.4

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.22.0-next.4
  - @backstage/core-app-api@1.3.0-next.4
  - @backstage/core-components@0.12.1-next.4
  - @backstage/app-defaults@1.0.9-next.4
  - @backstage/test-utils@1.2.3-next.4
  - @backstage/plugin-catalog@1.7.0-next.4
  - @backstage/plugin-techdocs@1.4.1-next.4
  - @backstage/catalog-model@1.1.4-next.1
  - @backstage/config@1.0.5-next.1
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/integration-react@1.1.7-next.4
  - @backstage/theme@0.2.16
  - @backstage/plugin-techdocs-react@1.0.7-next.4

## @internal/plugin-catalog-customized@0.0.5-next.4

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.7.0-next.4
  - @backstage/plugin-catalog-react@1.2.2-next.4

## @internal/plugin-todo-list@1.0.8-next.4

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.12.1-next.4
  - @backstage/core-plugin-api@1.2.0-next.2
  - @backstage/theme@0.2.16

## @internal/plugin-todo-list-backend@1.0.8-next.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.17.0-next.3
  - @backstage/config@1.0.5-next.1
  - @backstage/errors@1.1.4-next.1
  - @backstage/plugin-auth-node@0.2.8-next.3

## @internal/plugin-todo-list-common@1.0.7-next.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.2-next.2
