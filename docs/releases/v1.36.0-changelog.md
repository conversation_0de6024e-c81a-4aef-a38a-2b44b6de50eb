# Release v1.36.0

Upgrade Helper: [https://backstage.github.io/upgrade-helper/?to=1.36.0](https://backstage.github.io/upgrade-helper/?to=1.36.0)

## @backstage/backend-app-api@1.2.0

### Minor Changes

- 92a56f6: **BREAKING ALPHA**: Removed the deprecated `featureDiscoveryServiceFactory`. Existing usage can be replaced with `discoveryFeatureLoader` from `@backstage/backend-defaults`.

### Patch Changes

- ad9aba2: The log message written when plugins fail to initialize now includes the error that caused the plugin startup to fail.

- 5622362: Added a configuration to permit backend plugin failures on startup:

  ```yaml
  backend:
    ...
    startup:
      plugins:
        plugin-x:
          onPluginBootFailure: continue
  ```

  This configuration permits `plugin-x` to fail on startup. Omitting the
  `onPluginBootFailure` configuration matches the previous behavior, wherein any
  individual plugin failure aborts backend startup.

  The default can also be changed, so that continuing on failure is the default
  unless otherwise specified:

  ```yaml
  backend:
    startup:
      default:
        onPluginBootFailure: continue
      plugins:
        catalog:
          onPluginBootFailure: abort
  ```

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8
  - @backstage/config-loader@1.9.6
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/backend-defaults@0.8.0

### Minor Changes

- a4aa244: This change introduces the `auditor` service implementation details.

### Patch Changes

- f866b86: Internal refactor to use explicit `require` for lazy-loading dependency.
- 92a56f6: Internal refactor to stop importing the removed `FeatureDiscoveryService` from `@backstage/backend-plugin-api`.
- a19cb2b: Added default implementation for the new `PermissionsRegistryService`.
- 0d39029: Do not send `etag` or `If-Modified-Since` headers for gitlab artifact urls
- c7609de: Allow passing IP type to use with cloud-sql-connector
- 3740229: In the different `UrlReadersService`, the `search` method have been updated to use the `readUrl` if the given URL doesn't contain a pattern.
  For `UrlReaders` that didn't implement the `search` method, `readUrl` is now called internally and throws if the given URL doesn't contain a pattern.
- 72cddf2: Updated `PermissionsRegistryService` to use `PermissionResourceRef`s and added the `getPermissionRuleset` method.
- Updated dependencies
  - @backstage/cli-node@0.2.13
  - @backstage/plugin-permission-node@0.8.8
  - @backstage/config-loader@1.9.6
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/backend-app-api@1.2.0
  - @backstage/backend-dev-utils@0.1.5
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.8

## @backstage/backend-dynamic-feature-service@0.6.0

### Minor Changes

- 92a56f6: **BREAKING**: removed the deprecated `dynamicPluginsFeatureDiscoveryServiceFactory`.

### Patch Changes

- 96c20cd: Make sure changes are successfully tracked before starting up scanner.
- Updated dependencies
  - @backstage/cli-node@0.2.13
  - @backstage/plugin-permission-node@0.8.8
  - @backstage/backend-defaults@0.8.0
  - @backstage/config-loader@1.9.6
  - @backstage/plugin-catalog-backend@1.31.0
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-search-backend-node@1.3.8
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-app-node@0.1.30
  - @backstage/plugin-events-backend@0.4.2
  - @backstage/plugin-events-node@0.4.8
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-common@1.2.17

## @backstage/backend-openapi-utils@0.5.0

### Minor Changes

- 700d32b: **BREAKING**: The `wrapInOpenApiTestServer` and `wrapServer` functions are now exported via `/testUtils` subpath. If you were importing these functions directly from the root of the package, you will need to update your imports to use the `/testUtils` subpath:

  ```diff
  - import { wrapInOpenApiTestServer } from '@backstage/backend-openapi-utils';
  + import { wrapInOpenApiTestServer } from '@backstage/backend-openapi-utils/testUtils';
  ```

  or

  ```diff
  - import { wrapServer } from '@backstage/backend-openapi-utils';
  + import { wrapServer } from '@backstage/backend-openapi-utils/testUtils';
  ```

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/backend-plugin-api@1.2.0

### Minor Changes

- 92a56f6: **BREAKING ALPHA**: Removed the deprecated `featureDiscoveryServiceRef` and `FeatureDiscoveryService`.
- a4aa244: This change introduces the `auditor` service definition.

### Patch Changes

- 9ddfd94: Added new `PermissionsRegistryService` that is used by plugins to register permissions, resource types, and rules into the permission system. This replaces the existing `createPermissionIntegrationRouter` from `@backstage/plugin-permission-node`.
- 72cddf2: Updated `PermissionsRegistryService` to use `PermissionResourceRef`s and added the `getPermissionRuleset` method.
- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4

## @backstage/backend-test-utils@1.3.0

### Minor Changes

- dd05a97: Added mocks for the new `PermissionsRegistryService`.
- a4aa244: This change introduces mocks for the `auditor` service.

### Patch Changes

- f866b86: Internal refactor to use explicit `require` for lazy-loading dependency.
- fb051f2: Sync feature installation compatibility logic with `@backstage/backend-app-api`.
- 72cddf2: Added the new `getPermissionRuleset` method to `mockServices.permissionsRegistry`.
- Updated dependencies
  - @backstage/backend-defaults@0.8.0
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/backend-app-api@1.2.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.8

## @backstage/canon@0.1.0

### Minor Changes

- 72c9800: **BREAKING**: Merged the Stack and Inline component into a single component called Flex.
- 65f4acc: This is the first alpha release for Canon. As part of this release we are introducing 5 layout components and 7 components. All theming is done through CSS variables.
- 1e4ccce: **BREAKING**: Fixing css structure and making sure that props are applying the correct styles for all responsive values.
- 8309bdb: Updated core CSS tokens and fixing the Button component accordingly.

### Patch Changes

- 989af25: Removed client directive as they are not needed in React 18.
- f44e5cf: Fix spacing props not being applied for custom values.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.

## @backstage/cli@0.30.0

### Minor Changes

- cb76663: **BREAKING**: Add support for native ESM in Node.js code. This changes the behavior of dynamic import expressions in Node.js code. Typically this can be fixed by replacing `import(...)` with `require(...)`, with an `as typeof import(...)` cast if needed for types. This is because dynamic imports will no longer be transformed to `require(...)` calls, but instead be left as-is. This in turn allows you to load ESM modules from CommonJS code using `import(...)`.

  This change adds support for the following in Node.js packages, across type checking, package builds, runtime transforms and Jest tests:

  - Dynamic imports that load ESM modules from CommonJS code.
  - Both `.mjs` and `.mts` files as explicit ESM files, as well as `.cjs` and `.cts` as explicit CommonJS files.
  - Support for the `"type": "module"` field in `package.json` to indicate that the package is an ESM package.

  There are a few caveats to be aware of:

  - To enable support for native ESM in tests, you need to run the tests with the `--experimental-vm-modules` flag enabled, typically via `NODE_OPTIONS='--experimental-vm-modules'`.
  - Declaring a package as `"type": "module"` in `package.json` is supported, but in tests it will cause all local transitive dependencies to also be treated as ESM, regardless of whether they declare `"type": "module"` or not.
  - Node.js has an [ESM interoperability layer with CommonJS](https://nodejs.org/docs/latest-v22.x/api/esm.html#interoperability-with-commonjs) that allows for imports from ESM to identify named exports in CommonJS packages. This interoperability layer is **only** enabled when importing packages with a `.cts` or `.cjs` extension. This is because the interoperability layer is not fully compatible with the NPM ecosystem, and would break package if it was enabled for `.js` files.
  - Dynamic imports of CommonJS packages will vary in shape depending on the runtime, i.e. test vs local development, etc. It is therefore recommended to avoid dynamic imports of CommonJS packages and instead use `require`, or to use the explicit CommonJS extensions as mentioned above. If you do need to dynamically import CommonJS packages, avoid using `default` exports, as the shape of them vary across different environments and you would otherwise need to manually unwrap the import based on the shape of the module object.

- b30e788: The `new` command is now powered by a new template system that allows you to define your own templates in a declarative way, as well as import existing templates from external sources. See the [CLI templates documentation](https://backstage.io/docs/tooling/cli/templates) for more information.

  The following flags for the `new` command have been deprecated and will be removed in a future release:

  - `--license=<license>`: Configure the global `license` instead.
  - `--no-private`: Configure the global `private` instead.
  - `--baseVersion=<version>`: Configure the global `version` instead.
  - `--npmRegistry=<url>`: Configure the global `publishRegistry` instead.
  - `--scope=<scope>`: Configure the global `namePrefix` and/or `namePluginInfix` instead.

  As part of this change the template IDs and their options have changed. The following backwards compatibility mappings for the `--select` and `--option` flags are enabled when using the default set of templates, but they will also be removed in the future:

  - `--select=plugin` is mapped to `--select=frontend-plugin` instead.
  - `--option=id=<id>` is mapped to `--option=pluginId=<id>` instead.

### Patch Changes

- f17ef61: The `versions:bump` command will now reject `*` as a pattern.
- 86c72c1: The packing process when running `build-workspace` with the `--alwaysYarnPack` flag now respects the `BACKSTAGE_CLI_BUILD_PARALLEL` environment variable, defaulting parallel work limits based on CPU availability.
- 2167afc: Treat static file assets as always being free from side effects in package builds.
- f54eed0: Fixed an issue where default feature type information wasn't being added to package.json/exports before publishing if exports didn't exist beforehand
- 90a1edf: Add check to make sure that the `--link` option for the `start` command is a valid workspace.
- 207f88f: Fixed the file path pattern of many static assets output as part of the frontend build process, where there was an extra `.` before the extension, leading to names like `image-af7946b..png`.
- 9638f6d: Only allow pass through of `.mjs` in Jest transform if static ESM is supported.
- 9d49e04: Replaced dependency `node-libs-browser` with `node-stdlib-browser`
- 2c14147: Corrected path when trying to add dependency to respective `package.json` when using `yarn new` templates
- f21b125: Ensure that both global-agent and undici agents are enabled when proxying is enabled.
- Updated dependencies
  - @backstage/cli-node@0.2.13
  - @backstage/config-loader@1.9.6
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/eslint-plugin@0.1.10
  - @backstage/integration@1.16.1
  - @backstage/release-manifests@0.0.12
  - @backstage/types@1.2.1

## @backstage/repo-tools@0.13.0

### Minor Changes

- b78b2b0: Adds a new experimental hidden command `package-docs` for generating API documentation. This is currently only intended for use in the Backstage main repository.

### Patch Changes

- 474b748: Improve knip reports by using a single workspace-based config
- 98ddf05: The `api-reports` command is now also able to generate SQL reports, enabled by the `--sql-reports` flag.
- cb76663: Internal refactor to support native ESM.
- ecd01a9: Internal refactor of API report generation.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/cli-node@0.2.13
  - @backstage/config-loader@1.9.6
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/errors@1.2.7

## @techdocs/cli@1.9.0

### Minor Changes

- 8de3d2d: Allow configurable optional retries for publisher AWS S3 operations.

### Patch Changes

- 69f84ac: Internal update to work with dynamic imports.
- Updated dependencies
  - @backstage/backend-defaults@0.8.0
  - @backstage/plugin-techdocs-node@1.13.0
  - @backstage/catalog-model@1.7.3
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2

## @backstage/plugin-auth-backend-module-atlassian-provider@0.4.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0

## @backstage/plugin-auth-backend-module-auth0-provider@0.2.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0

## @backstage/plugin-auth-backend-module-aws-alb-provider@0.4.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/errors@1.2.7
  - @backstage/plugin-auth-backend@0.24.3

## @backstage/plugin-auth-backend-module-bitbucket-provider@0.3.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0

## @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.2.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0

## @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.4.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-auth-backend-module-gcp-iap-provider@0.4.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-auth-backend-module-github-provider@0.3.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- b40af03: Fixed a bug where the requested scope was ignored when refreshing sessions for a GitHub OAuth App. This would lead to access tokens being returned that didn't have the requested scope, and in turn errors when trying to use these tokens.

  As part of this fix all existing sessions are being revoked in order to ensure that they receive the correct scope.

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0

## @backstage/plugin-auth-backend-module-gitlab-provider@0.3.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0

## @backstage/plugin-auth-backend-module-google-provider@0.3.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0

## @backstage/plugin-auth-backend-module-microsoft-provider@0.3.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0

## @backstage/plugin-auth-backend-module-oauth2-provider@0.4.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0

## @backstage/plugin-auth-backend-module-oidc-provider@0.4.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- 0afc4f2: Simplify the `start` method in the `authenticator` to just return the helper promise
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/plugin-auth-backend@0.24.3

## @backstage/plugin-auth-backend-module-okta-provider@0.2.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0

## @backstage/plugin-auth-backend-module-onelogin-provider@0.3.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0

## @backstage/plugin-auth-backend-module-pinniped-provider@0.3.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1

## @backstage/plugin-auth-backend-module-vmware-cloud-provider@0.5.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/catalog-model@1.7.3

## @backstage/plugin-auth-node@0.6.0

### Minor Changes

- 61f464e: Added `auth.providers.<providerId>.sessionDuration` config for auth providers to allow the lifespan of user sessions to be configured.

### Patch Changes

- 8699b79: Removed unused dependencies
- cb316fc: Add an `error` handler to the `strategy` to reject the `executeRedirectStrategy`
- ab9a6fb: Added `scopeAlreadyGranted` property to `OAuthAuthenticatorRefreshInput`, signaling to the provider whether the requested scope has already been granted when persisting session scope.
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-catalog@1.27.0

### Minor Changes

- b07756e: The Entity Page now retains the visibility of the Inspect Dialog after a reload. This allows sharing the URL with the dialog open.

### Patch Changes

- 7a15cff: Revert client side paginated catalog table to using built in Material Table toolbar component
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/plugin-search-react@1.8.6
  - @backstage/core-components@0.16.4
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/integration-react@1.2.4
  - @backstage/core-compat-api@0.3.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-scaffolder-common@1.5.9
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-catalog-backend@1.31.0

### Minor Changes

- 9b9737c: Added support for emitting entities with an explicit location key during processing.

- 8805f93: The catalog backend now supports the new `PermissionsRegistryService`, which can be used to add custom permission rules.

- 5aebc13: The `UrlReaderProccessor` accepts a new config flag `catalog.useUrlReadersSearch` to always call the `search` method of `UrlReaders`.

  This flag currently defaults to `false`, but adopters are encouraged to enable it as this behavior will be the default in a future release.

  Previous behavior was to call the `search` method only if the parsed Git URL's filename contained a wildcard and use `readUrl` otherwise. `UrlReaderService` must implement this logic in the `search` method instead.

  This allows each `UrlReaderService` implementation to check whether it's a search URL (that contains a wildcard pattern) or not using logic that is specific to each provider.

- a4aa244: This change integrates the `auditor` service into the Catalog plugin.

### Patch Changes

- c9139e1: Ignore benign database conflict errors during stitching, now logged with debug level instead.
- f178b12: Cleanup `refresh_state_references` for entity processors and providers that are no longer in control of a `refresh_state` row for entity
- cd372e4: Improved performance when applying conditional decisions to entities within the permission framework.
- 4ae6884: Fixed an bug in the entity processing caching that would prevent entities that were emitted during processing to be restored after being overridden.
- ef73f97: Updated permission integration to use new permission resource ref.
- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/plugin-search-backend-module-catalog@0.3.1
  - @backstage/backend-openapi-utils@0.5.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-events-node@0.4.8
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-catalog-node@1.16.0

### Minor Changes

- b4183ad: Added new `catalogEntityPermissionResourceRef` export via the `/alpha` sub-path.
- 9b9737c: Added the `locationKey` option to `processingResult.entity(...)`.

### Patch Changes

- 4a941e7: Deprecated the alpha `catalogPermissionExtensionPoint` and related types, since the same functionality is now available via the new `PermissionsRegistryService`.
- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-scaffolder@1.28.0

### Minor Changes

- 17088d2: Updating the `TaskLogStream` to take up all space in a running task, and also show the last line of the log by default
- 5d469c9: Added support for autocompletion of GitHub branches in scaffolder
- 8e67e4a: Added support for autocompletion to GithubRepoPicker component

### Patch Changes

- 82300ea: Added support for experimental form decorators when dry-running templates in the template editor.
- eb3d91a: Use the custom error page if provided for displaying errors instead of the default error page
- 3107f1f: Fixed a bug in the BitbucketRepoBranchPicker component that crashed the scaffolder
- fe44946: Fixed bug of passing wrong value to `onChange` handler when using `GitLab` autocomplete
- 3edf7e7: Add schema output return type to the `makeFieldSchema` function return
- 1e935f0: Added conditional rendering of `oneOf` output schemas on the Installed Actions page for scaffolder actions
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- 65d7020: Use template title for ongoing task page header
- Updated dependencies
  - @backstage/plugin-scaffolder-react@1.14.5
  - @backstage/core-components@0.16.4
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/integration-react@1.2.4
  - @backstage/core-compat-api@0.3.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-scaffolder-backend@1.30.0

### Minor Changes

- dc8dd4b: Added new `createTemplateFilter`, `createTemplateGlobalFunction`, `createTemplateGlobalValue` for template extensions.
- a4aa244: This change integrates the `auditor` service into the Scaffolder plugin.
- 3d6ef79: Support file globbing in fs:delete
- 91f3313: Added experimental flag for scaffolder to wait for running tasks to complete on shutdown

  Enabling the `EXPERIMENTAL_gracefulShutdown` flag in the scaffolder config will make the
  scaffolder block the shutdown process until all running tasks have completed. This is useful
  when there is a need to ensure that all tasks have completed before the scaffolder is shut down.

  Please note, that the `TaskWorker` `stop` method is now asynchronous and awaited for the
  tasks to complete when the experimental flag is enabled.

### Patch Changes

- 37ab712: Fixed an issue where invalid expressions or non-object values in `step.each` caused an error.
- 1e935f0: Added conditional rendering of `oneOf` output schemas on the Installed Actions page for scaffolder actions
- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.8.0
  - @backstage/backend-defaults@0.8.0
  - @backstage/plugin-scaffolder-backend-module-github@0.6.0
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.27
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.5
  - @backstage/plugin-events-node@0.4.8
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-scaffolder-backend-module-azure@0.2.6
  - @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.7
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.6
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.6
  - @backstage/plugin-scaffolder-backend-module-gerrit@0.2.6
  - @backstage/plugin-scaffolder-backend-module-gitea@0.2.6
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-scaffolder-backend-module-github@0.6.0

### Minor Changes

- bb8302b: **BREAKING**: The `remoteUrl` output is no longer required, it can be empty only when using the new `createWhenEmpty` boolean flag.

### Patch Changes

- 5c187f9: **DEPRECATION**: The `getOctokitOptions` function signature with `repoUrl` option has been deprecated in favour of a function signature with individual `host`, `owner`, and `repo` parameters:

  ```diff
    const octokitOptions = await getOctokitOptions({
      integrations,
      credentialsProvider,
      token,
  -   repoUrl,
  +   host,
  +   owner,
  +   repo,
    });
  ```

- b98d511: clean up github action schemas

- 5d469c9: Added support for autocompletion of GitHub branches in scaffolder

- 8e67e4a: Added support for autocompletion to GithubRepoPicker component

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-gitlab@0.8.0

### Minor Changes

- 06eee14: Support empty repository creation in gitlab without workspace pushing and conditionally skip if the repository already exists.

### Patch Changes

- a0419a6: Made gitlab:group:ensureExists action idempotent
- 9d04e91: Fix automated assignment of reviewers for instances without premium/ultimate license (404). Introduce opt-in flag for automatic reviewer assignment based on approval rules
- 9545c5f: `createGitlabProjectMigrateAction` can now output the `migrationId`
- e52cee6: Made gitlab:issues:create action idempotent.
- fe44946: Fixed bug of passing wrong value to `onChange` handler when using `GitLab` autocomplete
- 66e392f: Making publish:gitlab:merge-request idempotent.
- a7b22eb: Fixed bug in gitlabRepoPush where it was looking in the wrong place in the exception response from gitbeaker when checking if the branch already exists
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-node@0.7.0

### Minor Changes

- dc8dd4b: Added new `createTemplateFilter`, `createTemplateGlobalFunction`, `createTemplateGlobalValue` for template extensions.
- a4aa244: This change introduces an optional `taskId` property to `TaskContext`.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-techdocs-node@1.13.0

### Minor Changes

- 8de3d2d: Allow configurable optional retries for publisher AWS S3 operations.

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-aws-node@0.1.15
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/app-defaults@1.5.17

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/core-app-api@1.15.5
  - @backstage/theme@0.6.4

## @backstage/cli-node@0.2.13

### Patch Changes

- cb76663: Added `type` field to `BackstagePackageJson` type.
- f54eed0: Fixed an issue where default feature type information wasn't being added to package.json/exports before publishing if exports didn't exist beforehand
- Updated dependencies
  - @backstage/cli-common@0.1.15
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/config-loader@1.9.6

### Patch Changes

- f866b86: Internal refactor to use explicit `require` for lazy-loading dependency.
- Updated dependencies
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/core-app-api@1.15.5

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-plugin-api@1.10.4
  - @backstage/version-bridge@1.0.11
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1

## @backstage/core-compat-api@0.3.6

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/core-plugin-api@1.10.4
  - @backstage/version-bridge@1.0.11

## @backstage/core-components@0.16.4

### Patch Changes

- 9c9f4ff: Added `nestedValuesAsYaml` option to `StructuredMetadataTable` to render data as yaml.
- 47c27c2: Upgrade `dagre` to `@dagrejs/dagre`
- 17088d2: Updating the `TaskLogStream` to take up all space in a running task, and also show the last line of the log by default
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-plugin-api@1.10.4
  - @backstage/version-bridge@1.0.11
  - @backstage/theme@0.6.4
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/core-plugin-api@1.10.4

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/version-bridge@1.0.11
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/create-app@0.5.25

### Patch Changes

- 132173b: Bumped create-app version.

- 84fdc40: Bumped create-app version.

- 5aa499b: Bumped create-app version.

- bfa4ea9: Enable `catalog.useUrlReadersSearch` configuration by default in newly created Backstage installations.

  This parameter makes `UrlReaderProcessor` always use the search method.
  New adopters are encouraged to enable it as this behavior will be the default in a future release.

- Updated dependencies
  - @backstage/cli-common@0.1.15

## @backstage/dev-utils@1.1.7

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/integration-react@1.2.4
  - @backstage/core-plugin-api@1.10.4
  - @backstage/app-defaults@1.5.17
  - @backstage/core-app-api@1.15.5
  - @backstage/theme@0.6.4
  - @backstage/catalog-model@1.7.3

## @backstage/frontend-app-api@0.10.5

### Patch Changes

- 3e21b8d: Added `getNodesByRoutePath` method to the `AppTreeApi`.
- f1efb47: Add support for defining multiple attachment points for extensions and blueprints.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/frontend-defaults@0.1.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/version-bridge@1.0.11
  - @backstage/core-app-api@1.15.5
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/frontend-defaults@0.1.6

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/frontend-app-api@0.10.5
  - @backstage/plugin-app@0.1.6
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/frontend-plugin-api@0.9.5

### Patch Changes

- 3e21b8d: Added `getNodesByRoutePath` method to the `AppTreeApi`.
- f1efb47: Add support for defining multiple attachment points for extensions and blueprints.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- 9ff3322: Allow route references to be installed in multiple app instances as long as their name is the same.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/core-plugin-api@1.10.4
  - @backstage/version-bridge@1.0.11
  - @backstage/types@1.2.1

## @backstage/frontend-test-utils@0.2.6

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/frontend-app-api@0.10.5
  - @backstage/version-bridge@1.0.11
  - @backstage/test-utils@1.7.5
  - @backstage/plugin-app@0.1.6
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1

## @backstage/integration-react@1.2.4

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-plugin-api@1.10.4
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1

## @backstage/test-utils@1.7.5

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/core-app-api@1.15.5
  - @backstage/theme@0.6.4
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4

## @backstage/theme@0.6.4

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.

## @backstage/version-bridge@1.0.11

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.

## @backstage/plugin-api-docs@0.12.4

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/plugin-catalog@1.27.0
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/core-compat-api@0.3.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-api-docs-module-protoc-gen-doc@0.1.9

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.

## @backstage/plugin-app@0.1.6

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/integration-react@1.2.4
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/theme@0.6.4

## @backstage/plugin-app-backend@0.4.5

### Patch Changes

- Updated dependencies
  - @backstage/config-loader@1.9.6
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-app-node@0.1.30

## @backstage/plugin-app-node@0.1.30

### Patch Changes

- Updated dependencies
  - @backstage/config-loader@1.9.6
  - @backstage/backend-plugin-api@1.2.0

## @backstage/plugin-app-visualizer@0.1.16

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/core-plugin-api@1.10.4

## @backstage/plugin-auth-backend@0.24.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-auth-backend-module-github-provider@0.3.0
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/plugin-auth-backend-module-oidc-provider@0.4.0
  - @backstage/plugin-auth-backend-module-cloudflare-access-provider@0.4.0
  - @backstage/plugin-auth-backend-module-bitbucket-server-provider@0.2.0
  - @backstage/plugin-auth-backend-module-atlassian-provider@0.4.0
  - @backstage/plugin-auth-backend-module-bitbucket-provider@0.3.0
  - @backstage/plugin-auth-backend-module-microsoft-provider@0.3.0
  - @backstage/plugin-auth-backend-module-onelogin-provider@0.3.0
  - @backstage/plugin-auth-backend-module-aws-alb-provider@0.4.0
  - @backstage/plugin-auth-backend-module-gcp-iap-provider@0.4.0
  - @backstage/plugin-auth-backend-module-gitlab-provider@0.3.0
  - @backstage/plugin-auth-backend-module-google-provider@0.3.0
  - @backstage/plugin-auth-backend-module-oauth2-provider@0.4.0
  - @backstage/plugin-auth-backend-module-auth0-provider@0.2.0
  - @backstage/plugin-auth-backend-module-okta-provider@0.2.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.5
  - @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.5

## @backstage/plugin-auth-backend-module-azure-easyauth-provider@0.2.5

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7

## @backstage/plugin-auth-backend-module-guest-provider@0.2.5

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7

## @backstage/plugin-auth-backend-module-oauth2-proxy-provider@0.2.5

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/errors@1.2.7

## @backstage/plugin-auth-react@0.1.12

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7

## @backstage/plugin-catalog-backend-module-aws@0.4.8

### Patch Changes

- eb80a1a: Minor doc string update
- Updated dependencies
  - @backstage/backend-defaults@0.8.0
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/plugin-kubernetes-common@0.9.3
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/integration-aws-node@0.1.15
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-azure@0.3.2

### Patch Changes

- eb80a1a: Minor doc string update
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-backstage-openapi@0.4.5

### Patch Changes

- eb80a1a: Minor doc string update
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/backend-openapi-utils@0.5.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.4.5

### Patch Changes

- eb80a1a: Minor doc string update
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.27
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-events-node@0.4.8

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.3.2

### Patch Changes

- eb80a1a: Minor doc string update
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-catalog-backend-module-gcp@0.3.5

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/plugin-kubernetes-common@0.9.3
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2

## @backstage/plugin-catalog-backend-module-gerrit@0.2.7

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-catalog-backend-module-github@0.7.10

### Patch Changes

- eb80a1a: Minor doc string update
- Updated dependencies
  - @backstage/plugin-catalog-backend@1.31.0
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-events-node@0.4.8

## @backstage/plugin-catalog-backend-module-github-org@0.3.7

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend-module-github@0.7.10
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/config@1.3.2
  - @backstage/plugin-events-node@0.4.8

## @backstage/plugin-catalog-backend-module-gitlab@0.6.3

### Patch Changes

- 4db039a: Fix `GitlabOrgDiscoveryEntityProvider` group path not being encoded causing 404 when using a subgroup in `config.group`
- Updated dependencies
  - @backstage/backend-defaults@0.8.0
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-events-node@0.4.8

## @backstage/plugin-catalog-backend-module-gitlab-org@0.2.6

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/plugin-catalog-backend-module-gitlab@0.6.3
  - @backstage/plugin-events-node@0.4.8

## @backstage/plugin-catalog-backend-module-incremental-ingestion@0.6.3

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.0
  - @backstage/plugin-catalog-backend@1.31.0
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.8
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-catalog-backend-module-ldap@0.11.2

### Patch Changes

- eb80a1a: Minor doc string update
- 636744a: Make `ldapOrg` config key optional
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-logs@0.1.7

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.31.0
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-events-node@0.4.8

## @backstage/plugin-catalog-backend-module-msgraph@0.6.7

### Patch Changes

- eb80a1a: Minor doc string update
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-openapi@0.2.7

### Patch Changes

- 7fc6f7c: Fix `resolveUrl` for split openapi definition relative path
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/catalog-model@1.7.3
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-backend-module-puppetdb@0.2.7

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.5

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-catalog-backend-module-unprocessed@0.5.5

### Patch Changes

- 4e073c7: Use new `PermissionsRegistryService` instead of the deprecated `catalogPermissionExtensionPoint`.
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/plugin-catalog-unprocessed-entities-common@0.0.7
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-catalog-graph@0.4.16

### Patch Changes

- 719e48d: Fixed an issue causing the `CatalogGraphCard` to redraw its content whenever the parent component re-renders, resulting in flickering.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/core-compat-api@0.3.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/types@1.2.1

## @backstage/plugin-catalog-import@0.12.10

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/integration-react@1.2.4
  - @backstage/core-compat-api@0.3.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-catalog-react@1.15.2

### Patch Changes

- b07756e: The Entity Page now retains the visibility of the Inspect Dialog after a reload. This allows sharing the URL with the dialog open.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/frontend-test-utils@0.2.6
  - @backstage/integration-react@1.2.4
  - @backstage/core-compat-api@0.3.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/version-bridge@1.0.11
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-catalog-unprocessed-entities@0.2.14

### Patch Changes

- e09d3e8: Added alpha support for the New Frontend System
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/core-compat-api@0.3.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7

## @backstage/plugin-config-schema@0.1.65

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/core-plugin-api@1.10.4
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-devtools@0.1.24

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/core-compat-api@0.3.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/errors@1.2.7
  - @backstage/plugin-devtools-common@0.1.15

## @backstage/plugin-devtools-backend@0.5.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8
  - @backstage/backend-defaults@0.8.0
  - @backstage/config-loader@1.9.6
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/cli-common@0.1.15
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-devtools-common@0.1.15
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-events-backend@0.4.2

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/backend-openapi-utils@0.5.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.8

## @backstage/plugin-events-backend-module-aws-sqs@0.4.8

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.8

## @backstage/plugin-events-backend-module-azure@0.2.17

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-events-node@0.4.8

## @backstage/plugin-events-backend-module-bitbucket-cloud@0.2.17

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-events-node@0.4.8

## @backstage/plugin-events-backend-module-gerrit@0.2.17

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-events-node@0.4.8

## @backstage/plugin-events-backend-module-github@0.2.17

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/config@1.3.2
  - @backstage/plugin-events-node@0.4.8

## @backstage/plugin-events-backend-module-gitlab@0.2.17

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/config@1.3.2
  - @backstage/plugin-events-node@0.4.8

## @backstage/plugin-events-backend-test-utils@0.1.41

### Patch Changes

- Updated dependencies
  - @backstage/plugin-events-node@0.4.8

## @backstage/plugin-events-node@0.4.8

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-home@0.8.5

### Patch Changes

- aff6b47: feat: add cancel button for WidgetSettingsOverlay

- 8e86f96: Enable collision prevention by default in custom home page.

  This change ensures that items in the home page will not collide with each other
  making the user experience better.

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.

- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/core-compat-api@0.3.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/core-app-api@1.15.5
  - @backstage/plugin-home-react@0.1.23
  - @backstage/theme@0.6.4
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2

## @backstage/plugin-home-react@0.1.23

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/core-plugin-api@1.10.4

## @backstage/plugin-kubernetes@0.12.4

### Patch Changes

- ca3da29: The `/clusters` endpoint is now protected by the `kubernetes.clusters.read` permission.
  The `/services/:serviceId` endpoint is now protected by the `kubernetes.resources.read` permission.
  The `/resources` endpoints are now protected by the `kubernetes.resources.read` permission.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/plugin-kubernetes-react@0.5.4
  - @backstage/core-components@0.16.4
  - @backstage/plugin-kubernetes-common@0.9.3
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/core-compat-api@0.3.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/catalog-model@1.7.3

## @backstage/plugin-kubernetes-backend@0.19.3

### Patch Changes

- ac0e1ac: Fixed the lack of `secrets` to fetch from the kubernetes api by adding option to specify additional Objects which are not part of Default Objects
- ca3da29: The `/clusters` endpoint is now protected by the `kubernetes.clusters.read` permission.
  The `/services/:serviceId` endpoint is now protected by the `kubernetes.resources.read` permission.
  The `/resources` endpoints are now protected by the `kubernetes.resources.read` permission.
- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/plugin-kubernetes-common@0.9.3
  - @backstage/plugin-kubernetes-node@0.2.3
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-kubernetes-cluster@0.0.22

### Patch Changes

- d80b08f: Improved rendering of Kubernetes resources' metadata.
- ca3da29: The `/clusters` endpoint is now protected by the `kubernetes.clusters.read` permission.
  The `/services/:serviceId` endpoint is now protected by the `kubernetes.resources.read` permission.
  The `/resources` endpoints are now protected by the `kubernetes.resources.read` permission.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/plugin-kubernetes-react@0.5.4
  - @backstage/core-components@0.16.4
  - @backstage/plugin-kubernetes-common@0.9.3
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/catalog-model@1.7.3

## @backstage/plugin-kubernetes-common@0.9.3

### Patch Changes

- ac0e1ac: Fixed the lack of `secrets` to fetch from the kubernetes api by adding option to specify additional Objects which are not part of Default Objects
- ca3da29: The `/clusters` endpoint is now protected by the `kubernetes.clusters.read` permission.
  The `/services/:serviceId` endpoint is now protected by the `kubernetes.resources.read` permission.
  The `/resources` endpoints are now protected by the `kubernetes.resources.read` permission.
- Updated dependencies
  - @backstage/catalog-model@1.7.3
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-kubernetes-node@0.2.3

### Patch Changes

- ac0e1ac: Fixed the lack of `secrets` to fetch from the kubernetes api by adding option to specify additional Objects which are not part of Default Objects
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-kubernetes-common@0.9.3
  - @backstage/catalog-model@1.7.3
  - @backstage/types@1.2.1

## @backstage/plugin-kubernetes-react@0.5.4

### Patch Changes

- f35a754: Fixed bug in string-to-integer conversion to properly handle decimal values with BigInt.
- d80b08f: Improved rendering of Kubernetes resources' metadata.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/plugin-kubernetes-common@0.9.3
  - @backstage/core-plugin-api@1.10.4
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1

## @backstage/plugin-notifications@0.5.2

### Patch Changes

- 438c36c: added topic filter for notifications
- 005d241: Added support for the new frontend system via `/alpha` export.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/core-compat-api@0.3.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-signals-react@0.0.10
  - @backstage/theme@0.6.4
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-notifications-common@0.0.8

## @backstage/plugin-notifications-backend@0.5.2

### Patch Changes

- 438c36c: added topic filter for notifications
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-events-node@0.4.8
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-notifications-node@0.2.12
  - @backstage/plugin-signals-node@0.1.17

## @backstage/plugin-notifications-backend-module-email@0.3.6

### Patch Changes

- 6259aa9: Add transport for Azure Communication Service
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration-aws-node@0.1.15
  - @backstage/types@1.2.1
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-notifications-node@0.2.12

## @backstage/plugin-notifications-node@0.2.12

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-signals-node@0.1.17

## @backstage/plugin-org@0.6.36

### Patch Changes

- 9cf1260: Added support for `spec.profile.displayName` to be used in the `MyGroupsSidebarItem` component via the `EntityDisplayName` component when you are a member of multiple Groups.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/core-compat-api@0.3.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-catalog-common@1.1.3

## @backstage/plugin-org-react@0.1.35

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/core-plugin-api@1.10.4
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3

## @backstage/plugin-permission-backend@0.5.54

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-permission-backend-module-allow-all-policy@0.2.5

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-permission-node@0.8.8

### Patch Changes

- 049d5d4: The returned router from `createPermissionIntegrationRouter` is now mutable, allowing for permissions and resources to be added after creation of the router.
- b71f634: Added a new `PermissionRuleset` type that encapsulates a lookup function for permission rules, which can be created by the new `PermissionsRegistryService` via the `getPermissionRuleset` method. The `createConditionTransformer` and `createConditionAuthorizer` functions have been adapted to receive these accessors as arguments, with their older counterparts being deprecated.
- a9621de: Added a new `createPermissionResourceRef` utility that encapsulates the constants and types related to a permission resource types. The `createConditionExports` and `createPermissionRule` functions have also been adapted to accept these references as arguments, deprecating their older counterparts.
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-permission-react@0.4.31

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-plugin-api@1.10.4
  - @backstage/config@1.3.2
  - @backstage/plugin-permission-common@0.8.4

## @backstage/plugin-proxy-backend@0.5.11

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/plugin-proxy-node@0.1.1

## @backstage/plugin-proxy-node@0.1.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0

## @backstage/plugin-scaffolder-backend-module-azure@0.2.6

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-bitbucket@0.3.7

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.6
  - @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.6

## @backstage/plugin-scaffolder-backend-module-bitbucket-cloud@0.2.6

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-bitbucket-cloud-common@0.2.27

## @backstage/plugin-scaffolder-backend-module-bitbucket-server@0.2.6

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.6

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.3.7

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.0
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-backend-module-gcp@0.2.6

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-gerrit@0.2.6

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-gitea@0.2.6

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1

## @backstage/plugin-scaffolder-backend-module-notifications@0.1.7

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/plugin-notifications-common@0.0.8
  - @backstage/plugin-notifications-node@0.2.12

## @backstage/plugin-scaffolder-backend-module-rails@0.5.6

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-backend-module-sentry@0.2.6

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7

## @backstage/plugin-scaffolder-backend-module-yeoman@0.4.7

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/plugin-scaffolder-node-test-utils@0.1.19
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-node-test-utils@0.1.19

### Patch Changes

- 7ec0a5a: Changing the default behaviour of the checkpoint in createMockActionContext. It will reduce the code boilerplate in the tests where checkpoint is used.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/backend-test-utils@1.3.0
  - @backstage/plugin-scaffolder-node@0.7.0
  - @backstage/types@1.2.1

## @backstage/plugin-scaffolder-react@1.14.5

### Patch Changes

- 656f67b: Reverted the validation in dependencies in scaffolder
- 2003fc2: Hide text output button if only one is present
- a35118f: build(deps): bump `immer` from 8.0.4 to 9.0.6
- 1283f06: Added missing `ajv` and `immer` dependencies to `@backstage/plugin-scaffolder-react`
- 3edf7e7: Add schema output return type to the `makeFieldSchema` function return
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/version-bridge@1.0.11
  - @backstage/theme@0.6.4
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/types@1.2.1
  - @backstage/plugin-scaffolder-common@1.5.9

## @backstage/plugin-search@1.4.23

### Patch Changes

- 63e1012: Added new extension points to extend search filters `SearchFilterBlueprint` and `SearchFilterResultTypeBlueprint`
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/plugin-search-react@1.8.6
  - @backstage/core-components@0.16.4
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/core-compat-api@0.3.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/version-bridge@1.0.11
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend@1.8.2

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8
  - @backstage/backend-defaults@0.8.0
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-search-backend-node@1.3.8
  - @backstage/backend-openapi-utils@0.5.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-catalog@0.3.1

### Patch Changes

- 8699b79: Removed unused dependencies
- eee8d76: Modified the logic for generating the location URL by encoding the entity property values with `encodeURIComponent`. This enhancement improves the safety and reliability of the URL.
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/plugin-search-backend-node@1.3.8
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-elasticsearch@1.6.5

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-search-backend-node@1.3.8
  - @backstage/config@1.3.2
  - @backstage/integration-aws-node@0.1.15
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-explore@0.2.8

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-search-backend-node@1.3.8
  - @backstage/config@1.3.2
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-pg@0.5.41

### Patch Changes

- 8699b79: Removed unused dependencies
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-search-backend-node@1.3.8
  - @backstage/config@1.3.2
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-stack-overflow-collator@0.3.6

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-search-backend-node@1.3.8
  - @backstage/config@1.3.2
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-module-techdocs@0.3.6

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/plugin-search-backend-node@1.3.8
  - @backstage/plugin-techdocs-node@1.13.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-backend-node@1.3.8

### Patch Changes

- 8699b79: Removed unused dependencies
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-search-react@1.8.6

### Patch Changes

- 63e1012: Added new extension points to extend search filters `SearchFilterBlueprint` and `SearchFilterResultTypeBlueprint`
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/core-plugin-api@1.10.4
  - @backstage/version-bridge@1.0.11
  - @backstage/theme@0.6.4
  - @backstage/types@1.2.1
  - @backstage/plugin-search-common@1.2.17

## @backstage/plugin-signals@0.0.16

### Patch Changes

- 005d241: Added support for the new frontend system via `/alpha` export.
- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-signals-react@0.0.10
  - @backstage/theme@0.6.4
  - @backstage/types@1.2.1

## @backstage/plugin-signals-backend@0.3.1

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.8
  - @backstage/plugin-signals-node@0.1.17

## @backstage/plugin-signals-node@0.1.17

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/config@1.3.2
  - @backstage/types@1.2.1
  - @backstage/plugin-events-node@0.4.8

## @backstage/plugin-signals-react@0.0.10

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-plugin-api@1.10.4
  - @backstage/types@1.2.1

## @backstage/plugin-techdocs@1.12.3

### Patch Changes

- eb3d91a: Use the custom error page if provided for displaying errors instead of the default error page

- 524f0af: Add missing route ref to the `/alpha` entity content extension.

- f4be934: Changed the base URL in addLinkClickListener from window.location.origin to app.baseUrl for improved path handling. This fixes an issue where Backstage, when running on a subpath, was unable to handle non-Backstage URLs of the same origin correctly.

- 1f40e6b: Add optional props to `TechDocCustomHome` to allow for more flexibility:

  ```tsx
  import { TechDocsCustomHome } from '@backstage/plugin-techdocs';
  //...

  const options = { emptyRowsWhenPaging: false };
  const linkDestination = (entity: Entity): string | undefined => {
    return entity.metadata.annotations?.['external-docs'];
  };
  const techDocsTabsConfig = [
    {
      label: 'Recommended Documentation',
      panels: [
        {
          title: 'Golden Path',
          description: 'Documentation about standards to follow',
          panelType: 'DocsCardGrid',
          panelProps: { CustomHeader: () => <ContentHeader title='Golden Path'/> },
          filterPredicate: entity =>
            entity?.metadata?.tags?.includes('golden-path') ?? false,
        },
        {
          title: 'Recommended',
          description: 'Useful documentation',
          panelType: 'InfoCardGrid',
          panelProps: {
            CustomHeader: () => <ContentHeader title='Recommended' />
            linkDestination: linkDestination,
          },
          filterPredicate: entity =>
            entity?.metadata?.tags?.includes('recommended') ?? false,
        },
      ],
    },
    {
      label: 'Browse All',
      panels: [
        {
          description: 'Browse all docs',
          filterPredicate: filterEntity,
          panelType: 'TechDocsIndexPage',
          title: 'All',
          panelProps: { PageWrapper: React.Fragment, CustomHeader: React.Fragment, options: options },
        },
      ],
    },
  ];

  const AppRoutes = () => {
    <FlatRoutes>
      <Route
        path="/docs"
        element={
          <TechDocsCustomHome
            tabsConfig={techDocsTabsConfig}
            filter={{
              kind: ['Location', 'Resource', 'Component'],
              'metadata.annotations.featured-docs': CATALOG_FILTER_EXISTS,
            }}
            CustomPageWrapper={({ children }: React.PropsWithChildren<{}>) => (<PageWithHeader title="Docs" themeId="documentation">{children}</PageWithHeader>)}
          />
        }
      />
    </FlatRoutes>;
  };
  ```

  Add new Grid option called `InfoCardGrid` which is a more customizable card option for the Docs grid.

  ```tsx
  <InfoCardGrid
    entities={entities}
    linkContent="Learn more"
    linkDestination={entity => entity.metadata['external-docs']}
  />
  ```

  Expose existing `CustomDocsPanel` so that it can be used independently if desired.

  ```tsx
  const panels: PanelConfig[] = [
    {
      description: '',
      filterPredicate: entity => {},
      panelType: 'InfoCardGrid',
      title: 'Standards',
      panelProps: {
            CustomHeader: () => <ContentHeader title='Recommended' />
            linkDestination: linkDestination,
          },
    },
    {
      description: '',
      filterPredicate: entity => {},
      panelType: 'DocsCardGrid',
      title: 'Contribute',
    },
  ];
  {
    panels.map((config, index) => (
      <CustomDocsPanel
        key={index}
        config={config}
        entities={!!entities ? entities : []}
        index={index}
      />
    ));
  }
  ```

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.

- Updated dependencies
  - @backstage/plugin-search-react@1.8.6
  - @backstage/core-components@0.16.4
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/integration-react@1.2.4
  - @backstage/core-compat-api@0.3.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-techdocs-react@1.2.14
  - @backstage/plugin-auth-react@0.1.12
  - @backstage/theme@0.6.4
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-search-common@1.2.17
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-techdocs-addons-test-utils@1.0.45

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/plugin-search-react@1.8.6
  - @backstage/plugin-techdocs@1.12.3
  - @backstage/plugin-catalog@1.27.0
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/integration-react@1.2.4
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-techdocs-react@1.2.14
  - @backstage/core-app-api@1.15.5
  - @backstage/test-utils@1.7.5

## @backstage/plugin-techdocs-backend@1.11.6

### Patch Changes

- b78e3fd: Fixed issue `syncEntityDocs` that would cause the `/sync` endpoint to be continuously called if the request fails
- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/plugin-techdocs-node@1.13.0
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/integration@1.16.1
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-search-backend-module-techdocs@0.3.6
  - @backstage/plugin-techdocs-common@0.1.0

## @backstage/plugin-techdocs-module-addons-contrib@1.1.21

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/integration-react@1.2.4
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-techdocs-react@1.2.14
  - @backstage/integration@1.16.1

## @backstage/plugin-techdocs-react@1.2.14

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/core-plugin-api@1.10.4
  - @backstage/version-bridge@1.0.11
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2

## @backstage/plugin-user-settings@0.8.19

### Patch Changes

- 58ec9e7: Removed older versions of React packages as a preparatory step for upgrading to React 19. This commit does not introduce any functional changes, but removes dependencies on previous React versions, allowing for a cleaner upgrade path in subsequent commits.
- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/core-compat-api@0.3.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/core-app-api@1.15.5
  - @backstage/plugin-signals-react@0.0.10
  - @backstage/theme@0.6.4
  - @backstage/catalog-model@1.7.3
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-user-settings-common@0.0.1

## @backstage/plugin-user-settings-backend@0.2.30

### Patch Changes

- Updated dependencies
  - @backstage/backend-defaults@0.8.0
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/config@1.3.2
  - @backstage/errors@1.2.7
  - @backstage/types@1.2.1
  - @backstage/plugin-signals-node@0.1.17
  - @backstage/plugin-user-settings-common@0.0.1

## example-app@0.2.106

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.30.0
  - @backstage/plugin-scaffolder-react@1.14.5
  - @backstage/plugin-notifications@0.5.2
  - @backstage/plugin-search-react@1.8.6
  - @backstage/plugin-search@1.4.23
  - @backstage/plugin-kubernetes-cluster@0.0.22
  - @backstage/core-components@0.16.4
  - @backstage/plugin-scaffolder@1.28.0
  - @backstage/plugin-techdocs@1.12.3
  - @backstage/plugin-home@0.8.5
  - @backstage/plugin-catalog@1.27.0
  - @backstage/plugin-org@0.6.36
  - @backstage/plugin-kubernetes@0.12.4
  - @backstage/plugin-signals@0.0.16
  - @backstage/plugin-catalog-graph@0.4.16
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/frontend-app-api@0.10.5
  - @backstage/plugin-catalog-unprocessed-entities@0.2.14
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.21
  - @backstage/integration-react@1.2.4
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/plugin-catalog-import@0.12.10
  - @backstage/plugin-techdocs-react@1.2.14
  - @backstage/app-defaults@1.5.17
  - @backstage/core-app-api@1.15.5
  - @backstage/plugin-user-settings@0.8.19
  - @backstage/plugin-auth-react@0.1.12
  - @backstage/plugin-api-docs@0.12.4
  - @backstage/plugin-devtools@0.1.24
  - @backstage/theme@0.6.4
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-search-common@1.2.17

## example-app-next@0.0.20

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.30.0
  - @backstage/plugin-scaffolder-react@1.14.5
  - @backstage/plugin-notifications@0.5.2
  - @backstage/plugin-search-react@1.8.6
  - @backstage/plugin-search@1.4.23
  - @backstage/plugin-kubernetes-cluster@0.0.22
  - @backstage/core-components@0.16.4
  - @backstage/plugin-scaffolder@1.28.0
  - @backstage/plugin-techdocs@1.12.3
  - @backstage/plugin-home@0.8.5
  - @backstage/plugin-catalog@1.27.0
  - @backstage/plugin-org@0.6.36
  - @backstage/plugin-kubernetes@0.12.4
  - @backstage/plugin-signals@0.0.16
  - @backstage/plugin-catalog-graph@0.4.16
  - @backstage/plugin-catalog-react@1.15.2
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/frontend-app-api@0.10.5
  - @backstage/plugin-catalog-unprocessed-entities@0.2.14
  - @backstage/plugin-techdocs-module-addons-contrib@1.1.21
  - @backstage/frontend-defaults@0.1.6
  - @backstage/integration-react@1.2.4
  - @backstage/core-compat-api@0.3.6
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-permission-react@0.4.31
  - @backstage/plugin-app-visualizer@0.1.16
  - @backstage/plugin-catalog-import@0.12.10
  - @backstage/plugin-techdocs-react@1.2.14
  - @backstage/app-defaults@1.5.17
  - @backstage/core-app-api@1.15.5
  - @backstage/plugin-user-settings@0.8.19
  - @backstage/plugin-auth-react@0.1.12
  - @backstage/plugin-api-docs@0.12.4
  - @backstage/theme@0.6.4
  - @backstage/plugin-app@0.1.6
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/plugin-catalog-common@1.1.3
  - @backstage/plugin-search-common@1.2.17

## app-next-example-plugin@0.0.20

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/frontend-plugin-api@0.9.5

## example-backend@0.0.35

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8
  - @backstage/plugin-auth-backend-module-github-provider@0.3.0
  - @backstage/plugin-notifications-backend@0.5.2
  - @backstage/backend-defaults@0.8.0
  - @backstage/plugin-catalog-backend@1.31.0
  - @backstage/plugin-scaffolder-backend@1.30.0
  - @backstage/plugin-catalog-backend-module-backstage-openapi@0.4.5
  - @backstage/plugin-scaffolder-backend-module-github@0.6.0
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-kubernetes-backend@0.19.3
  - @backstage/plugin-search-backend-module-catalog@0.3.1
  - @backstage/plugin-search-backend-node@1.3.8
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/plugin-techdocs-backend@1.11.6
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.5
  - @backstage/plugin-catalog-backend-module-openapi@0.2.7
  - @backstage/plugin-devtools-backend@0.5.2
  - @backstage/plugin-permission-backend@0.5.54
  - @backstage/plugin-permission-backend-module-allow-all-policy@0.2.5
  - @backstage/plugin-search-backend@1.8.2
  - @backstage/catalog-model@1.7.3
  - @backstage/plugin-app-backend@0.4.5
  - @backstage/plugin-auth-backend@0.24.3
  - @backstage/plugin-auth-backend-module-guest-provider@0.2.5
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.5
  - @backstage/plugin-events-backend@0.4.2
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-proxy-backend@0.5.11
  - @backstage/plugin-scaffolder-backend-module-notifications@0.1.7
  - @backstage/plugin-search-backend-module-explore@0.2.8
  - @backstage/plugin-search-backend-module-techdocs@0.3.6
  - @backstage/plugin-signals-backend@0.3.1

## example-backend-legacy@0.2.107

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-node@0.8.8
  - @backstage/plugin-scaffolder-backend-module-gitlab@0.8.0
  - @backstage/backend-defaults@0.8.0
  - @backstage/plugin-catalog-backend@1.31.0
  - @backstage/plugin-scaffolder-backend@1.30.0
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/plugin-catalog-node@1.16.0
  - @backstage/plugin-kubernetes-backend@0.19.3
  - @backstage/plugin-search-backend-module-catalog@0.3.1
  - @backstage/plugin-search-backend-module-pg@0.5.41
  - @backstage/plugin-search-backend-node@1.3.8
  - @backstage/plugin-auth-node@0.6.0
  - @backstage/plugin-techdocs-backend@1.11.6
  - @backstage/plugin-catalog-backend-module-unprocessed@0.5.5
  - @backstage/plugin-permission-backend@0.5.54
  - @backstage/plugin-search-backend@1.8.2
  - @backstage/catalog-client@1.9.1
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2
  - @backstage/integration@1.16.1
  - @backstage/plugin-app-backend@0.4.5
  - @backstage/plugin-auth-backend@0.24.3
  - @backstage/plugin-catalog-backend-module-scaffolder-entity-model@0.2.5
  - @backstage/plugin-events-backend@0.4.2
  - @backstage/plugin-events-node@0.4.8
  - @backstage/plugin-permission-common@0.8.4
  - @backstage/plugin-proxy-backend@0.5.11
  - @backstage/plugin-scaffolder-backend-module-confluence-to-markdown@0.3.6
  - @backstage/plugin-scaffolder-backend-module-rails@0.5.6
  - @backstage/plugin-search-backend-module-elasticsearch@1.6.5
  - @backstage/plugin-search-backend-module-explore@0.2.8
  - @backstage/plugin-search-backend-module-techdocs@0.3.6
  - @backstage/plugin-signals-backend@0.3.1
  - @backstage/plugin-signals-node@0.1.17

## e2e-test@0.2.25

### Patch Changes

- Updated dependencies
  - @backstage/create-app@0.5.25
  - @backstage/cli-common@0.1.15
  - @backstage/errors@1.2.7

## @internal/frontend@0.0.6

### Patch Changes

- Updated dependencies
  - @backstage/frontend-plugin-api@0.9.5
  - @backstage/version-bridge@1.0.11
  - @backstage/types@1.2.1

## @internal/scaffolder@0.0.6

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-react@1.14.5
  - @backstage/frontend-plugin-api@0.9.5

## techdocs-cli-embedded-app@0.2.105

### Patch Changes

- Updated dependencies
  - @backstage/cli@0.30.0
  - @backstage/core-components@0.16.4
  - @backstage/plugin-techdocs@1.12.3
  - @backstage/plugin-catalog@1.27.0
  - @backstage/integration-react@1.2.4
  - @backstage/core-plugin-api@1.10.4
  - @backstage/plugin-techdocs-react@1.2.14
  - @backstage/app-defaults@1.5.17
  - @backstage/core-app-api@1.15.5
  - @backstage/test-utils@1.7.5
  - @backstage/theme@0.6.4
  - @backstage/catalog-model@1.7.3
  - @backstage/config@1.3.2

## @internal/plugin-todo-list@1.0.36

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.16.4
  - @backstage/core-plugin-api@1.10.4

## @internal/plugin-todo-list-backend@1.0.36

### Patch Changes

- Updated dependencies
  - @backstage/backend-plugin-api@1.2.0
  - @backstage/errors@1.2.7
