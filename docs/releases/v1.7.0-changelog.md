# Release v1.7.0

## @backstage/cli@0.20.0

### Minor Changes

- f368ad7279: **BREAKING**: Bumped `jest`, `jest-runtime`, and `jest-environment-jsdom` to v29. This is up from v27, so check out both the [v28](https://jestjs.io/docs/28.x/upgrading-to-jest28) and [v29](https://jestjs.io/docs/upgrading-to-jest29) (later [here](https://jestjs.io/docs/29.x/upgrading-to-jest29)) migration guides.

  Particular changes that where encountered in the main Backstage repo are:

  - The updated snapshot format.
  - `jest.useFakeTimers('legacy')` is now `jest.useFakeTimers({ legacyFakeTimers: true })`.
  - Error objects collected by `withLogCollector` from `@backstage/test-utils` are now objects with a `detail` property rather than a string.

### Patch Changes

- 78d5eb299e: Tweak the Jest Caching loader to only operate when in `watch` mode

- 9c595302cb: Normalize on winston version ^3.2.1

- 24b40140c4: Treat files in `__testUtils__` and `__mocks__` directories as test files for linting
  purposes.

  Updates the parts of the eslint configuration generator that specify which files
  should be treated as test code to include any files under two additional
  directories:

  - `__mocks__`: this is the directory used by Jest[0] for mock code.
  - `__testUtils__`: a suggested location for utility code executed only when
    running tests.

  [0]: https://jestjs.io/docs/manual-mocks#mocking-user-modules

- 3e309107ca: Updated fallback versions of dependencies in all templates.

- 292a088807: Added a new `repo test` command.

- ba63cae41c: Updated lockfile parsing to have better support for Yarn 3.

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`

- 2dddb32fea: Switched the Jest transform for YAML files to use a custom one available at `@backstage/cli/config/jestYamlTransform.js`.

- a541a3a78a: Switch to upfront resolution of `swc-loader` in Webpack config.

- cfb3598410: Removed `tsx` and `jsx` as supported extensions in backend packages. For most
  repos, this will not have any effect. But if you inadvertently had added some
  `tsx`/`jsx` files to your backend package, you may now start to see `code: 'MODULE_NOT_FOUND'` errors when launching the backend locally. The reason for
  this is that the offending files get ignored during transpilation. Hence, the
  importing file can no longer find anything to import.

  The fix is to rename any `.tsx` files in your backend packages to `.ts` instead,
  or `.jsx` to `.js`.

- Updated dependencies
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.3
  - @backstage/config-loader@1.1.5
  - @backstage/errors@1.1.2
  - @backstage/release-manifests@0.0.6
  - @backstage/types@1.0.0

## @backstage/plugin-auth-backend@0.17.0

### Minor Changes

- e2dc42e9f0: Google OAuth refresh tokens will now be revoked on logout by calling Google's API
- 5fa831ce55: CookieConfigurer can optionally return the `SameSite` cookie attribute.
  CookieConfigurer now requires an additional argument `appOrigin` - the origin URL of the app - which is used to calculate the `SameSite` attribute.
  defaultCookieConfigurer returns the `SameSite` attribute which defaults to `Lax`. In cases where an auth-backend is running on a different domain than the App, `SameSite=None` is used - but only for secure contexts. This is so that cookies can be included in third-party requests.

  OAuthAdapterOptions has been modified to require additional arguments, `baseUrl`, and `cookieConfigurer`.
  OAuthAdapter now resolves cookie configuration using its supplied CookieConfigurer for each request to make sure that the proper attributes always are set.

### Patch Changes

- b5c126010c: Auth0 provider now supports optional `connection` and `connectionScope` parameters to configure social identity providers.
- 8c6ec175bf: Fix GitLab provider setup so that it supports GitLab installations with a path in the URL.
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-auth-node@0.2.6
  - @backstage/catalog-client@1.1.1
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/types@1.0.0

## @backstage/plugin-bazaar-backend@0.2.0

### Minor Changes

- **********: **BREAKING** The bazaar-backend `createRouter` now requires that the `identityApi` is passed to the router.

  These changes are **required** to `packages/backend/src/plugins/bazaar.ts`

  The user entity ref is now added to the members table and is taken from the requesting user using the `identityApi`.

  ```diff
  import { PluginEnvironment } from '../types';
  import { createRouter } from '@backstage/plugin-bazaar-backend';
  import { Router } from 'express';

  export default async function createPlugin(
    env: PluginEnvironment,
  ): Promise<Router> {
    return await createRouter({
      logger: env.logger,
      config: env.config,
      database: env.database,
  +   identity: env.identity,
    });
  }
  ```

### Patch Changes

- f7c2855d76: Router now also has endpoint `getLatestProjects` that takes a limit of projects as prop.
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/backend-test-utils@0.1.29
  - @backstage/plugin-auth-node@0.2.6
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2

## @backstage/plugin-bitbucket-cloud-common@0.2.0

### Minor Changes

- ad74723fbf: Update Bitbucket Cloud models to latest OAS version.

  The latest specification contained some BREAKING CHANGES
  due to removed fields.

  All of these fields are not used at other plugins, though.
  Therefore, this change has no impact on other modules here.

### Patch Changes

- Updated dependencies
  - @backstage/integration@1.3.2

## @backstage/plugin-catalog@1.6.0

### Minor Changes

- d558f41d3a: Added new column `Label` to `CatalogTable.columns`, this new column allows you make use of labels from metadata.
  For example: category and visibility are type of labels associated with API entity illustrated below.

  YAML code snippet for API entity

  ```yaml
  apiVersion: backstage.io/v1alpha1
  kind: API
  metadata:
    name: sample-api
    description: API for sample
    links:
      - url: http://localhost:8080/swagger-ui.html
        title: Swagger UI
    tags:
      - http
    labels:
      category: legacy
      visibility: protected
  ```

  Consumers can customise columns to include label column and show in api-docs list

  ```typescript
  const columns = [
    CatalogTable.columns.createNameColumn({ defaultKind: 'API' }),
    CatalogTable.columns.createLabelColumn('category', { title: 'Category' }),
    CatalogTable.columns.createLabelColumn('visibility', {
      title: 'Visibility',
      defaultValue: 'public',
    }),
  ];
  ```

### Patch Changes

- 4efadb6968: Implemented the visual parts of `EntityKindPicker` so that it can be shown alongside the other filters on the left side of your catalog pages.
- 182000c663: Added `emptyContent` property to CatalogTable and DefaultCatalogPage to support customization of the Catalog Table.
- e89e1f614d: Added support for copy entity URL in entity page context menu
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/plugin-catalog-common@1.0.7
  - @backstage/catalog-client@1.1.1
  - @backstage/plugin-search-react@1.2.0
  - @backstage/plugin-search-common@1.1.0
  - @backstage/integration-react@1.1.5
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend@1.5.0

### Minor Changes

- b2e6cb6acf: Added a new method `addLocationAnalyzers` to the `CatalogBuilder`. With this you can add location analyzers to your catalog. These analyzers will be used by the /analyze-location endpoint to decide if the provided URL contains any catalog-info.yaml files already or not.

  Moved the following types from this package to `@backstage/plugin-catalog-backend`.

  - AnalyzeLocationResponse
  - AnalyzeLocationRequest
  - AnalyzeLocationExistingEntity
  - AnalyzeLocationGenerateEntity
  - AnalyzeLocationEntityField

- eb25f7e12d: The exported permission rules and the API of `createCatalogConditionalDecision` have changed to reflect the breaking changes made to the `PermissionRule` type. Note that all involved types are exported from `@backstage/plugin-catalog-backend/alpha`

### Patch Changes

- 8cb6e10105: Fixed a bug where entities provided without a location key would always replace existing entities, rather than updating them.
- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- 63296ebcd4: Allow Placeholder value to be any value, not only string.
- 74022e0163: Make sure to stitch entities correctly after deletion, to ensure that their relations are updated.
- Updated dependencies
  - @backstage/plugin-catalog-node@1.2.0
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-catalog-common@1.0.7
  - @backstage/plugin-permission-node@0.7.0
  - @backstage/backend-plugin-api@0.1.3
  - @backstage/plugin-permission-common@0.7.0
  - @backstage/catalog-client@1.1.1
  - @backstage/plugin-search-common@1.1.0
  - @backstage/plugin-scaffolder-common@1.2.1
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-import@0.9.0

### Minor Changes

- b2e6cb6acf: **Breaking**
  Moved the code search for the existing catalog-info.yaml files to the backend from the frontend. It means it will use the configured GitHub integration's credentials.

  Add the following to your `CatalogBuilder` to have the repo URL ingestion working again.

  ```ts
  // catalog.ts
  import { GithubLocationAnalyzer } from '@backstage/plugin-catalog-backend-module-github';
  ...
    builder.addLocationAnalyzers(
      new GithubLocationAnalyzer({
        discovery: env.discovery,
        config: env.config,
      }),
    );
  ...
  ```

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/plugin-catalog-common@1.0.7
  - @backstage/catalog-client@1.1.1
  - @backstage/integration-react@1.1.5
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2

## @backstage/plugin-catalog-node@1.2.0

### Minor Changes

- 404366c853: Deprecated the `LocationSpec` type. It got moved from this package to the `@backstage/plugin-catalog-common` so make sure imports are updated.

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-common@1.0.7
  - @backstage/backend-plugin-api@0.1.3
  - @backstage/catalog-client@1.1.1
  - @backstage/errors@1.1.2
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-react@1.2.0

### Minor Changes

- 4efadb6968: Implemented the visual parts of `EntityKindPicker` so that it can be shown alongside the other filters on the left side of your catalog pages.

### Patch Changes

- 7939e743f5: Added two new `EntityRefLinks` props, the first being `getTitle` that allows for customization of the title used for each link. The second one is `fetchEntities`, which triggers a fetching of all entities so that the full entity definition is available in the `getTitle` callback.
- e9e532ebd8: Fixed issue where the query kind parameter is not honored
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/core-components@0.11.2
  - @backstage/plugin-catalog-common@1.0.7
  - @backstage/plugin-permission-common@0.7.0
  - @backstage/catalog-client@1.1.1
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1
  - @backstage/plugin-permission-react@0.4.6

## @backstage/plugin-permission-common@0.7.0

### Minor Changes

- 46b4a72cee: **BREAKING**: When defining permission rules, it's now necessary to provide a [ZodSchema](https://github.com/colinhacks/zod) that specifies the parameters the rule expects. This has been added to help better describe the parameters in the response of the metadata endpoint and to validate the parameters before a rule is executed.

  To help with this, we have also made a change to the API of permission rules. Before, the permission rules `toQuery` and `apply` signature expected parameters to be separate arguments, like so...

  ```ts
  createPermissionRule({
    apply: (resource, foo, bar) => true,
    toQuery: (foo, bar) => {},
  });
  ```

  The API has now changed to expect the parameters as a single object

  ```ts
  createPermissionRule({
    paramSchema: z.object({
      foo: z.string().describe('Foo value to match'),
      bar: z.string().describe('Bar value to match'),
    }),
    apply: (resource, { foo, bar }) => true,
    toQuery: ({ foo, bar }) => {},
  });
  ```

  One final change made is to limit the possible values for a parameter to primitives and arrays of primitives.

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/types@1.0.0

## @backstage/plugin-permission-node@0.7.0

### Minor Changes

- 46b4a72cee: **BREAKING**: When defining permission rules, it's now necessary to provide a [ZodSchema](https://github.com/colinhacks/zod) that specifies the parameters the rule expects. This has been added to help better describe the parameters in the response of the metadata endpoint and to validate the parameters before a rule is executed.

  To help with this, we have also made a change to the API of permission rules. Before, the permission rules `toQuery` and `apply` signature expected parameters to be separate arguments, like so...

  ```ts
  createPermissionRule({
    apply: (resource, foo, bar) => true,
    toQuery: (foo, bar) => {},
  });
  ```

  The API has now changed to expect the parameters as a single object

  ```ts
  createPermissionRule({
    paramSchema: z.object({
      foo: z.string().describe('Foo value to match'),
      bar: z.string().describe('Bar value to match'),
    }),
    apply: (resource, { foo, bar }) => true,
    toQuery: ({ foo, bar }) => {},
  });
  ```

  One final change made is to limit the possible values for a parameter to primitives and arrays of primitives.

### Patch Changes

- 9335ad115e: Exported types for the .metadata endpoint of the permission router
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-auth-node@0.2.6
  - @backstage/plugin-permission-common@0.7.0
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2

## @backstage/plugin-playlist-backend@0.2.0

### Minor Changes

- eb25f7e12d: **BREAKING** The exported permission rules have changed to reflect the breaking changes made to the PermissionRule type.

  For example, the `playlistConditions.isOwner` API has changed from:

  ```ts
  playlistConditions.isOwner(['user:default/me', 'group:default/owner']);
  ```

  to:

  ```ts
  playlistConditions.isOwner({
    owners: ['user:default/me', 'group:default/owner'],
  });
  ```

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/backend-test-utils@0.1.29
  - @backstage/plugin-auth-node@0.2.6
  - @backstage/plugin-permission-node@0.7.0
  - @backstage/plugin-permission-common@0.7.0
  - @backstage/catalog-client@1.1.1
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/plugin-playlist-common@0.1.1

## @backstage/plugin-scaffolder@1.7.0

### Minor Changes

- f13d5f3f06: Add support for link to TechDocs and other links defined in template entity specification metadata on TemplateCard
- 05f22193c5: EntityPickers now support flags to control when to include default namespace
  in result

### Patch Changes

- e4f0a96424: Making the description of the GitLab repoUrl owner field more clearer by focusing it refers to the GitLab namespace.
- 92e490d6b4: Make the `/next` scaffolder work end to end with the old `TaskPage` view
- 8220f2fd83: Support custom layouts in dry run editor
- 1047baa926: Bump to `react-jsonschema-form@v5-beta` for the `NextRouter` under `@alpha` exports
- 98ae18b68f: Fixed a bug where the `allowed*` values for the `RepoUrlPicker` would be reset on render.
- 8960d83013: Add support for `allowedOrganizations` and `allowedOwners` to the `AzureRepoPicker`.
- b681275e69: Ignore .git directories in Template Editor, increase upload limit for dry-runs to 10MB.
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/plugin-catalog-common@1.0.7
  - @backstage/catalog-client@1.1.1
  - @backstage/plugin-scaffolder-common@1.2.1
  - @backstage/integration-react@1.1.5
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/plugin-permission-react@0.4.6

## @backstage/plugin-scaffolder-backend@1.7.0

### Minor Changes

- 253453fa14: Added a new property called `additionalTemplateGlobals` which allows you to add global functions to the scaffolder nunjucks templates.
- 17ff77154c: Update the `github:publish` action to allow passing whether pull
  requests must be up to date with the default branch before merging.
- 304305dd20: Add `allowAutoMerge` option for `publish:github` action
- 694bfe2d61: Add functionality to shutdown scaffolder tasks if they are stale
- a8e9848479: Added optional `sourcePath` parameter to `publish:gitlab:merge-request` action, `targetPath` is now optional and falls back to current workspace path.

### Patch Changes

- 489621f613: Switching off duplicated timestamp in case of logging via task logger in a custom action
- 4880d43e25: Fixed setting default branch for Bitbucket Server
- b681275e69: Ignore .git directories in Template Editor, increase upload limit for dry-runs to 10MB.
- a35a27df70: Updated the `moduleId` of the experimental module export.
- Updated dependencies
  - @backstage/plugin-catalog-node@1.2.0
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-catalog-backend@1.5.0
  - @backstage/plugin-auth-node@0.2.6
  - @backstage/backend-tasks@0.3.6
  - @backstage/backend-plugin-api@0.1.3
  - @backstage/catalog-client@1.1.1
  - @backstage/plugin-scaffolder-common@1.2.1
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2
  - @backstage/types@1.0.0

## @backstage/plugin-search-backend@1.1.0

### Minor Changes

- 16c853a6ed: Be less restrictive with unknown keys on query endpoint
- a799972bb1: The query received by search engines now contains a property called `pageLimit`, it specifies how many results to return per page when sending a query request to the search backend.

  Example:
  _Returns up to 30 results per page_

      GET /query?pageLimit=30

  The search backend validates the page limit and this value must not exceed 100, but it doesn't set a default value for the page limit parameter, it leaves it up to each search engine to set this, so Lunr, Postgres and Elastic Search set 25 results per page as a default value.

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-auth-node@0.2.6
  - @backstage/plugin-permission-node@0.7.0
  - @backstage/plugin-permission-common@0.7.0
  - @backstage/plugin-search-common@1.1.0
  - @backstage/plugin-search-backend-node@1.0.3
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/types@1.0.0

## @backstage/plugin-search-common@1.1.0

### Minor Changes

- a799972bb1: There is a new property called `pageLimit` on the `SearchQuery` interface that specifies how many results should be returned per page.

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.0
  - @backstage/types@1.0.0

## @backstage/plugin-search-react@1.2.0

### Minor Changes

- 4ed1fa2480: The search query state now has an optional `pageLimit` property that determines how many results will be requested per page, it defaults to 25.

  Examples:
  _Basic_

  ```jsx
  <SearchResults query={{ pageLimit: 30 }}>
    {results => {
      // Item rendering logic is omitted
    }}
  </SearchResults>
  ```

  _With context_

  ```jsx
  <SearchContextProvider initialState={{ pageLimit: 30 }}>
    <SearchResults>
      {results => {
        // Item rendering logic is omitted
      }}
    </SearchResults>
  </SearchContextProvider>
  ```

- bed5a1dc6e: The `<SearchResultList />` component now accepts an optional property `disableRenderingWithNoResults` to disable rendering when no results are returned.
  Possibility to provide a custom no results component if needed through the `noResultsComponent` property.

  Examples:

  _Rendering a custom no results component_

  ```jsx
  <SearchResultList
    query={query}
    noResultsComponent={<ListItemText primary="No results were found" />}
  />
  ```

  _Disable rendering when there are no results_

  ```jsx
  <SearchResultList query={query} disableRenderingWithNoResults />
  ```

- 3de4bd4f19: A `<SearchPagination />` component was created for limiting the number of results shown per search page. Use this new component to give users options to select how many search results they want to display per page. The default options are 10, 25, 50, 100.

  See examples below:

  _Basic_

  ```jsx
  import React, { useState } from 'react';
  import { Grid } from '@material-ui/core';
  import { Page, Header, Content, Lifecycle } from '@backstage/core-components';
  import {
    SearchBarBase,
    SearchPaginationBase,
    SearchResultList,
  } from '@backstage/plugin-search-react';

  const SearchPage = () => {
    const [term, setTerm] = useState('');
    const [pageLimit, setPageLimit] = useState(25);
    const [pageCursor, setPageCursor] = useState<string>();

    return (
      <Page themeId="home">
        <Header title="Search" subtitle={<Lifecycle alpha />} />
        <Content>
          <Grid container direction="row">
            <Grid item xs={12}>
              <SearchBarBase value={term} onChange={setTerm} />
            </Grid>
            <Grid item xs={12}>
              <SearchPaginationBase
                limit={pageLimit}
                onLimitChange={setPageLimit}
                cursor={pageCursor}
                onCursorChange={setPageCursor}
              />
            </Grid>
            <Grid item xs={12}>
              <SearchResultList query={{ term, pageLimit }} />
            </Grid>
          </Grid>
        </Content>
      </Page>
    );
  };
  ```

  _With context_

  ```jsx
  import React from 'react';
  import { Grid } from '@material-ui/core';
  import { Page, Header, Content, Lifecycle } from '@backstage/core-components';
  import {
    SearchBar,
    SearchResult,
    SearchPagination,
    SearchResultListLayout,
    SearchContextProvider,
    DefaultResultListItem,
  } from '@backstage/plugin-search-react';

  const SearchPage = () => (
    <SearchContextProvider>
      <Page themeId="home">
        <Header title="Search" subtitle={<Lifecycle alpha />} />
        <Content>
          <Grid container direction="row">
            <Grid item xs={12}>
              <SearchBar />
            </Grid>
            <Grid item xs={12}>
              <SearchPagination />
            </Grid>
            <Grid item xs={12}>
              <SearchResult>
                {({ results }) => (
                  <SearchResultListLayout
                    resultItems={results}
                    renderResultItem={({ document }) => (
                      <DefaultResultListItem
                        key={document.location}
                        result={document}
                      />
                    )}
                  />
                )}
              </SearchResult>
            </Grid>
          </Grid>
        </Content>
      </Page>
    </SearchContextProvider>
  );
  ```

- 6faaa05626: The `<SearchResultGroup />` component now accepts an optional property `disableRenderingWithNoResults` to disable rendering when no results are returned.
  Possibility to provide a custom no results component if needed through the `noResultsComponent` property.

  Examples:

  _Rendering a custom no results component_

  ```jsx
  <SearchResultGroup
    query={query}
    icon={<DocsIcon />}
    title="Documentation"
    noResultsComponent={<ListItemText primary="No results were found" />}
  />
  ```

  _Disable rendering when there are no results_

  ```jsx
  <SearchResultGroup
    query={query}
    icon={<DocsIcon />}
    title="Documentation"
    disableRenderingWithNoResults
  />
  ```

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/plugin-search-common@1.1.0
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1

## @backstage/plugin-techdocs-backend@1.4.0

### Minor Changes

- 7ced1b4076: Add optional `catalogClient` argument to `createRoute` parameters

### Patch Changes

- 8006f8a602: In order to improve the debuggability of the search indexing process, messages logged during indexing are now tagged with a `documentType` whose value corresponds to the `type` being indexed.
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-catalog-common@1.0.7
  - @backstage/plugin-techdocs-node@1.4.1
  - @backstage/plugin-permission-common@0.7.0
  - @backstage/catalog-client@1.1.1
  - @backstage/plugin-search-common@1.1.0
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2

## @backstage/plugin-user-settings@0.5.0

### Minor Changes

- 5543e86660: **BREAKING**: The `apiRef` passed to `ProviderSettingsItem` now needs to
  implement `ProfileInfoApi & SessionApi`, rather than just the latter. This is
  unlikely to have an effect on most users though, since the builtin auth
  providers generally implement both.

  Fixed settings page showing providers as logged out when the user is using more
  than one provider, and displayed some additional login information.

### Patch Changes

- 06d61d1266: Handle errors that may occur when the user logs out
- 44c9a95dcf: Prevent `.set()` to execute a request to the StorageClient if the user is `guest`
- 174f02a00a: Update installation instructions
- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-app-api@1.1.1
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0

## @backstage/app-defaults@1.0.7

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-app-api@1.1.1
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16
  - @backstage/plugin-permission-react@0.4.6

## @backstage/backend-app-api@0.2.2

### Patch Changes

- 0027a749cd: Added possibility to configure index plugin of the HTTP router service.
- 45857bffae: Properly export `rootLoggerFactory`.
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/backend-tasks@0.3.6
  - @backstage/plugin-permission-node@0.7.0
  - @backstage/backend-plugin-api@0.1.3
  - @backstage/errors@1.1.2

## @backstage/backend-common@0.15.2

### Patch Changes

- e8d7976413: Added back support for when no branch is provided for the Bitbucket Server `UrlReader`
- c44cf412de: Fix BitBucket server integration
- c31f7cdfbc: Fixed an issue where `getClient()` for a `pluginId` would return different clients and not share them
- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.3
  - @backstage/config-loader@1.1.5
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2
  - @backstage/types@1.0.0

## @backstage/backend-defaults@0.1.2

### Patch Changes

- 96d288a02d: Added root logger service to the set of default services.
- Updated dependencies
  - @backstage/backend-app-api@0.2.2
  - @backstage/backend-plugin-api@0.1.3

## @backstage/backend-plugin-api@0.1.3

### Patch Changes

- 28377dc89f: Allow interfaces to be used for inferred option types.
- a35a27df70: Added documentation for `createBackendModule`, with guidelines for choosing a module ID.
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/backend-tasks@0.3.6
  - @backstage/plugin-permission-common@0.7.0
  - @backstage/config@1.0.3

## @backstage/backend-tasks@0.3.6

### Patch Changes

- d4fea86ea3: Added new function `readTaskScheduleDefinitionFromConfig` to read `TaskScheduleDefinition` (aka. schedule) from the `Config`.
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/types@1.0.0

## @backstage/backend-test-utils@0.1.29

### Patch Changes

- 72549952d1: Fixed handling of root scoped services in `startTestBackend`.
- e91e8e9c55: Increased test database max connection pool size to reduce the risk of resource exhaustion.
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/cli@0.20.0
  - @backstage/backend-app-api@0.2.2
  - @backstage/backend-plugin-api@0.1.3
  - @backstage/config@1.0.3

## @backstage/catalog-client@1.1.1

### Patch Changes

- 4f2ac624b4: Renamed argument in `validateEntity` from `location` to `locationRef`
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/errors@1.1.2

## @backstage/catalog-model@1.1.2

### Patch Changes

- 6f3b8d0962: Defer `ajv` compilation of schema validators to improve module-import performance
- Updated dependencies
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/types@1.0.0

## @backstage/codemods@0.1.40

### Patch Changes

- Updated dependencies
  - @backstage/cli-common@0.1.10

## @backstage/config@1.0.3

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.0

## @backstage/config-loader@1.1.5

### Patch Changes

- Updated dependencies
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/types@1.0.0

## @backstage/core-app-api@1.1.1

### Patch Changes

- 27e6404aba: Fixed a bug where gathered index routes would fail to bind routable extensions. This would typically show up when placing a routable extension in the entity page overview tab.
- Updated dependencies
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1

## @backstage/core-components@0.11.2

### Patch Changes

- bde1e8c8e2: Added `curve` prop to the `DependencyGraph` component to select the type of layout
- 882101cd9b: Deep-import LightAsync component to improve module-import speed
- 0d0f30d87e: Fixed layout for core-components docs where table was broken by splitting with code sample
- Updated dependencies
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16
  - @backstage/version-bridge@1.0.1

## @backstage/core-plugin-api@1.0.7

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1

## @backstage/create-app@0.4.32

### Patch Changes

- 58c2264325: Newly created Backstage repositories now use the stable version 6 of
  `react-router`, just like the main repo does. Please let us know if you find any
  issues with this.

  Migrating to the stable version of `react-router` is optional for the time
  being. But if you want to do the same for your existing repository, please
  follow [this guide](https://backstage.io/docs/tutorials/react-router-stable-migration).

- e05e0f021b: Update versions of packages used in the create-app template, to match those in the main repo

- 01dff06be4: Leverage cache mounts in Dockerfile during `yarn install ...` and `apt-get ...` commands to speed up repeated builds.

- 90616bcaa6: Add the new search pagination component to the search page template.

- 7c6306fc8a: Initializes a git repository when creating an app using @packages/create-app

- 52f25858a8: Added `*.session.sql` Visual Studio Code database functionality files to `.gitignore` in the default template. This is optional but potentially helpful if your developers use Visual Studio Code; you can add a line with that exact value to your own root `.gitignore` if you want the same.

- 6d00e80146: Updated the root `test` scripts to use `backstage-cli repo test`.

  To apply this change to an existing app, make the following change to the root `package.json`:

  ```diff
  -    "test": "backstage-cli test",
  -    "test:all": "lerna run test -- --coverage",
  +    "test": "backstage-cli repo test",
  +    "test:all": "backstage-cli repo test --coverage",
  ```

- Updated dependencies
  - @backstage/cli-common@0.1.10

## @backstage/dev-utils@1.0.7

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-app-api@1.1.1
  - @backstage/test-utils@1.2.1
  - @backstage/app-defaults@1.0.7
  - @backstage/integration-react@1.1.5
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/errors@1.1.2

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.0

## @backstage/integration@1.3.2

### Patch Changes

- Updated dependencies
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2

## @backstage/integration-react@1.1.5

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/integration@1.3.2
  - @backstage/theme@0.2.16

## @techdocs/cli@1.2.2

### Patch Changes

- 0b2a30dead: fixing techdocs-cli Docker client creation

  Docker client does not need to be created when --no-docker
  option is provided.

  If you had DOCKER_CERT_PATH environment variable defined
  the Docker client was looking for certificates
  and breaking techdocs-cli generate command even with --no-docker
  option.

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-techdocs-node@1.4.1
  - @backstage/cli-common@0.1.10
  - @backstage/config@1.0.3

## @backstage/test-utils@1.2.1

### Patch Changes

- e05e0f021b: Align on the version of `@material-ui/icons` used, to `^4.9.1` like other packages in the main repo
- Updated dependencies
  - @backstage/core-app-api@1.1.1
  - @backstage/plugin-permission-common@0.7.0
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/plugin-permission-react@0.4.6

## @backstage/plugin-adr@0.2.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/plugin-search-react@1.2.0
  - @backstage/plugin-search-common@1.1.0
  - @backstage/plugin-adr-common@0.2.2
  - @backstage/integration-react@1.1.5
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-adr-backend@0.2.2

### Patch Changes

- 8006f8a602: In order to improve the debuggability of the search indexing process, messages logged during indexing are now tagged with a `documentType` whose value corresponds to the `type` being indexed.
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/catalog-client@1.1.1
  - @backstage/plugin-search-common@1.1.0
  - @backstage/plugin-adr-common@0.2.2
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2

## @backstage/plugin-adr-common@0.2.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-search-common@1.1.0
  - @backstage/integration@1.3.2

## @backstage/plugin-airbrake@0.3.10

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/test-utils@1.2.1
  - @backstage/dev-utils@1.0.7
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-airbrake-backend@0.2.10

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/config@1.0.3

## @backstage/plugin-allure@0.1.26

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-analytics-module-ga@0.1.21

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/theme@0.2.16

## @backstage/plugin-apache-airflow@0.2.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7

## @backstage/plugin-api-docs@0.8.10

### Patch Changes

- 50c6e14aee: Updated dependency `@asyncapi/react-component` to `1.0.0-next.43`.
- 3d5bb521ee: Updated dependency `@asyncapi/react-component` to `1.0.0-next.42`.
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/plugin-catalog@1.6.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-apollo-explorer@0.1.3

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-app-backend@0.3.37

### Patch Changes

- 11c9e0ad33: Added alpha plugin implementation for the new backend system. Available at `@backstage/plugin-app-backend/alpha`.
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/backend-plugin-api@0.1.3
  - @backstage/config@1.0.3
  - @backstage/config-loader@1.1.5
  - @backstage/types@1.0.0

## @backstage/plugin-auth-node@0.2.6

### Patch Changes

- f3a3fefb96: Ensure `getIdentity` throws an `AuthenticationError` instead of a `NotAllowed` error when authentication fails
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2

## @backstage/plugin-azure-devops@0.2.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-azure-devops-backend@0.3.16

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/config@1.0.3
  - @backstage/plugin-azure-devops-common@0.3.0

## @backstage/plugin-badges@0.2.34

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16

## @backstage/plugin-badges-backend@0.1.31

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/catalog-client@1.1.1
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2

## @backstage/plugin-bazaar@0.1.25

### Patch Changes

- f7c2855d76: Added a `Overview Card` for either latest or random projects. Changed `ProjectPreview.tsx` so it take `gridSize` and `useTablePagination` as props.
- c0352bbc69: Link to the user catalog entity of a member
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/plugin-catalog@1.6.0
  - @backstage/core-components@0.11.2
  - @backstage/cli@0.20.0
  - @backstage/catalog-client@1.1.1
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2

## @backstage/plugin-bitrise@0.1.37

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog-backend-module-aws@0.1.10

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-catalog-backend@1.5.0
  - @backstage/backend-tasks@0.3.6
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-azure@0.1.8

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-catalog-backend@1.5.0
  - @backstage/backend-tasks@0.3.6
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-bitbucket@0.2.4

### Patch Changes

- 23f9199a0f: Deprecate `@backstage/plugin-catalog-backend-module-bitbucket`.

  Please migrate to `@backstage/plugin-catalog-backend-module-bitbucket-cloud`
  or `@backstage/plugin-catalog-backend-module-bitbucket-server` instead.

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-catalog-backend@1.5.0
  - @backstage/plugin-bitbucket-cloud-common@0.2.0
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-bitbucket-cloud@0.1.4

### Patch Changes

- f66e696e7b: Bitbucket Cloud provider: Add option to configure schedule via `app-config.yaml` instead of in code.

  Please find how to configure the schedule at the config at
  <https://backstage.io/docs/integrations/bitbucketCloud/discovery>

- a9b91d39bb: Add `bitbucketCloudCatalogModule` (new backend-plugin-api, alpha).

- Updated dependencies
  - @backstage/plugin-catalog-node@1.2.0
  - @backstage/plugin-catalog-backend@1.5.0
  - @backstage/backend-tasks@0.3.6
  - @backstage/backend-plugin-api@0.1.3
  - @backstage/plugin-bitbucket-cloud-common@0.2.0
  - @backstage/config@1.0.3
  - @backstage/integration@1.3.2

## @backstage/plugin-catalog-backend-module-bitbucket-server@0.1.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-catalog-backend@1.5.0
  - @backstage/backend-tasks@0.3.6
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2

## @backstage/plugin-catalog-backend-module-gerrit@0.1.5

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-catalog-backend@1.5.0
  - @backstage/backend-tasks@0.3.6
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2

## @backstage/plugin-catalog-backend-module-github@0.1.8

### Patch Changes

- 8749df3d02: `GitHubEntityProvider`: Add option to configure schedule via `app-config.yaml` instead of in code.

  Please find how to configure the schedule at the config at
  <https://backstage.io/docs/integrations/github/discovery>

- 7022aebf35: Added `GithubLocationAnalyzer`. This can be used to add to the `CatalogBuilder`. When added this will be used by `RepoLocationAnalyzer` to figure out if the given URL that you are trying to import from the /catalog-import page already contains catalog-info.yaml files.

- 51046b58b0: Use schedule from config at backend module.

  Also, it removes `GithubEntityProviderCatalogModuleOptions`
  in favor of config-only for the backend module setup
  like at other similar modules.

- 7edb5909e8: Add missing config schema for the `GitHubEntityProvider`.

- be9474b103: Replaces in-code uses of `GitHub` by `Github` and deprecates old versions.

  Deprecates

  - `GitHubEntityProvider` replaced by `GithubEntityProvider`
  - `GitHubLocationAnalyzer` replaced by `GithubLocationAnalyzer`
  - `GitHubLocationAnalyzerOptions` replaced by `GithubLocationAnalyzerOptions`
  - `GitHubOrgEntityProvider` replaced by `GithubOrgEntityProvider`
  - `GitHubOrgEntityProviderOptions` replaced by `GithubOrgEntityProviderOptions`

  Renames

  - `GitHubLocationAnalyzer` to `GithubLocationAnalyzer`
  - `GitHubLocationAnalyzerOptions` to `GithubLocationAnalyzerOptions`

- a35a27df70: Updated the `moduleId` of the experimental module export.

- Updated dependencies
  - @backstage/plugin-catalog-node@1.2.0
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-catalog-backend@1.5.0
  - @backstage/backend-tasks@0.3.6
  - @backstage/backend-plugin-api@0.1.3
  - @backstage/catalog-client@1.1.1
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-gitlab@0.1.8

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-catalog-backend@1.5.0
  - @backstage/backend-tasks@0.3.6
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-ldap@0.5.4

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-backend@1.5.0
  - @backstage/backend-tasks@0.3.6
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-backend-module-msgraph@0.4.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-backend@1.5.0
  - @backstage/backend-tasks@0.3.6
  - @backstage/config@1.0.3

## @backstage/plugin-catalog-backend-module-openapi@0.1.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-node@1.2.0
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-catalog-backend@1.5.0
  - @backstage/config@1.0.3
  - @backstage/integration@1.3.2
  - @backstage/types@1.0.0

## @backstage/plugin-catalog-common@1.0.7

### Patch Changes

- 823acaa88b: Moved the following types from `@backstage/plugin-catalog-backend` to this package.

  - AnalyzeLocationResponse
  - AnalyzeLocationRequest
  - AnalyzeLocationExistingEntity
  - AnalyzeLocationGenerateEntity
  - AnalyzeLocationEntityField

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-permission-common@0.7.0
  - @backstage/plugin-search-common@1.1.0

## @backstage/plugin-catalog-graph@0.2.22

### Patch Changes

- bde1e8c8e2: Added `curve` prop to the `DependencyGraph` component to select the type of layout
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/catalog-client@1.1.1
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-catalog-graphql@0.3.14

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/config@1.0.3
  - @backstage/types@1.0.0

## @backstage/plugin-cicd-statistics@0.1.12

### Patch Changes

- e05e0f021b: Align on the version of `@material-ui/icons` used, to `^4.9.1` like other packages in the main repo
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-plugin-api@1.0.7

## @backstage/plugin-cicd-statistics-module-gitlab@0.1.6

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-cicd-statistics@0.1.12
  - @backstage/core-plugin-api@1.0.7

## @backstage/plugin-circleci@0.3.10

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-cloudbuild@0.3.10

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-code-climate@0.1.10

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage@0.2.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16

## @backstage/plugin-code-coverage-backend@0.2.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/catalog-client@1.1.1
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2

## @backstage/plugin-codescene@0.1.5

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16

## @backstage/plugin-config-schema@0.1.33

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0

## @backstage/plugin-cost-insights@0.11.32

### Patch Changes

- a94c2ed1b7: Fixed bug in `CostOverviewBreakdownChart` component where some datasets caused the cost overview breakdown chart to tear.
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/theme@0.2.16
  - @backstage/plugin-cost-insights-common@0.1.1

## @backstage/plugin-dynatrace@1.0.0

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-explore@0.3.41

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16
  - @backstage/plugin-explore-react@0.0.22

## @backstage/plugin-explore-react@0.0.22

### Patch Changes

- Updated dependencies
  - @backstage/core-plugin-api@1.0.7

## @backstage/plugin-firehydrant@0.1.27

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-fossa@0.2.42

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16

## @backstage/plugin-gcalendar@0.3.6

### Patch Changes

- 4c2ed7ecf1: Fixed loader showing when user not signed in
- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16

## @backstage/plugin-gcp-projects@0.3.29

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-git-release-manager@0.3.23

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/integration@1.3.2
  - @backstage/theme@0.2.16

## @backstage/plugin-github-actions@0.5.10

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/integration@1.3.2
  - @backstage/theme@0.2.16

## @backstage/plugin-github-deployments@0.1.41

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/integration-react@1.1.5
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2
  - @backstage/theme@0.2.16

## @backstage/plugin-github-issues@0.1.2

### Patch Changes

- 8c7bff2bb4: Updated the `luxon` dependency to 3.x
- 719ccbb963: Properly filter on relations instead of the spec, when finding by owner
- df226e124c: Add filtering and ordering to the graphql query
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2
  - @backstage/theme@0.2.16

## @backstage/plugin-github-pull-requests-board@0.1.4

### Patch Changes

- 719ccbb963: Properly filter on relations instead of the spec, when finding by owner
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/integration@1.3.2
  - @backstage/theme@0.2.16

## @backstage/plugin-gitops-profiles@0.3.28

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-gocd@0.1.16

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16

## @backstage/plugin-graphiql@0.2.42

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-graphql-backend@0.1.27

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-catalog-graphql@0.3.14
  - @backstage/config@1.0.3

## @backstage/plugin-home@0.4.26

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/plugin-stack-overflow@0.1.6
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/theme@0.2.16

## @backstage/plugin-ilert@0.1.36

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16

## @backstage/plugin-jenkins@0.7.9

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-jenkins-common@0.1.9

## @backstage/plugin-jenkins-backend@0.1.27

### Patch Changes

- b19ea927af: Fixed a bug where `extraRequestHeaders` configuration was ignored.
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-auth-node@0.2.6
  - @backstage/plugin-permission-common@0.7.0
  - @backstage/catalog-client@1.1.1
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/plugin-jenkins-common@0.1.9

## @backstage/plugin-jenkins-common@0.1.9

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-common@1.0.7
  - @backstage/plugin-permission-common@0.7.0

## @backstage/plugin-kafka@0.3.10

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/theme@0.2.16

## @backstage/plugin-kafka-backend@0.2.30

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2

## @backstage/plugin-kubernetes@0.7.3

### Patch Changes

- 51af8361de: Add useCustomResources react hook for fetching Kubernetes Custom Resources
- 35a6cfe257: Fix infinite call bug in `useCustomResources` hook
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/plugin-kubernetes-common@0.4.3
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/theme@0.2.16

## @backstage/plugin-kubernetes-backend@0.7.3

### Patch Changes

- de676888bc: Added missing cluster locator configuration schema entries, for the catalog and local proxy types
- d4a8c683be: kubernetes service locator now take request context parameters
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-auth-node@0.2.6
  - @backstage/catalog-client@1.1.1
  - @backstage/plugin-kubernetes-common@0.4.3
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2

## @backstage/plugin-kubernetes-common@0.4.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2

## @backstage/plugin-lighthouse@0.3.10

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic@0.3.28

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-newrelic-dashboard@0.2.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2

## @backstage/plugin-org@0.5.10

### Patch Changes

- f2b4b55636: consistently show parent and child relations in group profile card
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-pagerduty@0.5.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop@0.1.8

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16

## @backstage/plugin-periskop-backend@0.1.8

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/config@1.0.3

## @backstage/plugin-permission-backend@0.5.12

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-auth-node@0.2.6
  - @backstage/plugin-permission-node@0.7.0
  - @backstage/plugin-permission-common@0.7.0
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2

## @backstage/plugin-permission-react@0.4.6

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.0
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3

## @backstage/plugin-playlist@0.1.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/plugin-catalog-common@1.0.7
  - @backstage/plugin-permission-common@0.7.0
  - @backstage/plugin-search-react@1.2.0
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16
  - @backstage/plugin-permission-react@0.4.6
  - @backstage/plugin-playlist-common@0.1.1

## @backstage/plugin-playlist-common@0.1.1

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.0

## @backstage/plugin-proxy-backend@0.2.31

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/config@1.0.3

## @backstage/plugin-rollbar@0.4.10

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-rollbar-backend@0.1.34

### Patch Changes

- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/config@1.0.3

## @backstage/plugin-scaffolder-backend-module-cookiecutter@0.2.12

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-scaffolder-backend@1.7.0
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2
  - @backstage/types@1.0.0

## @backstage/plugin-scaffolder-backend-module-rails@0.4.5

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-scaffolder-backend@1.7.0
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2
  - @backstage/types@1.0.0

## @backstage/plugin-scaffolder-backend-module-yeoman@0.2.10

### Patch Changes

- Updated dependencies
  - @backstage/plugin-scaffolder-backend@1.7.0
  - @backstage/config@1.0.3
  - @backstage/types@1.0.0

## @backstage/plugin-scaffolder-common@1.2.1

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/types@1.0.0

## @backstage/plugin-search@1.0.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/plugin-search-react@1.2.0
  - @backstage/plugin-search-common@1.1.0
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/version-bridge@1.0.1

## @backstage/plugin-search-backend-module-elasticsearch@1.0.3

### Patch Changes

- a799972bb1: The search engine has been updated to take advantage of the `pageLimit` property on search queries. If none is provided, the search engine will continue to use its default value of 25 results per page.
- 8006f8a602: In order to improve the debuggability of the search indexing process, messages logged during indexing are now tagged with a `documentType` whose value corresponds to the `type` being indexed.
- Updated dependencies
  - @backstage/plugin-search-common@1.1.0
  - @backstage/plugin-search-backend-node@1.0.3
  - @backstage/config@1.0.3

## @backstage/plugin-search-backend-module-pg@0.4.1

### Patch Changes

- a799972bb1: The search engine has been updated to take advantage of the `pageLimit` property on search queries. If none is provided, the search engine will continue to use its default value of 25 results per page.
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-search-common@1.1.0
  - @backstage/plugin-search-backend-node@1.0.3
  - @backstage/config@1.0.3

## @backstage/plugin-search-backend-node@1.0.3

### Patch Changes

- a799972bb1: The search engine has been updated to take advantage of the `pageLimit` property on search queries. If none is provided, the search engine will continue to use its default value of 25 results per page.
- 8006f8a602: In order to improve the debuggability of the search indexing process, messages logged during indexing are now tagged with a `documentType` whose value corresponds to the `type` being indexed.
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/backend-tasks@0.3.6
  - @backstage/plugin-permission-common@0.7.0
  - @backstage/plugin-search-common@1.1.0
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2

## @backstage/plugin-sentry@0.4.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-shortcuts@0.3.2

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0

## @backstage/plugin-sonarqube@0.4.2

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-sonarqube-backend@0.1.2

### Patch Changes

- 9c595302cb: Normalize on winston version ^3.2.1
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2

## @backstage/plugin-splunk-on-call@0.3.34

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-stack-overflow@0.1.6

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/plugin-search-common@1.1.0
  - @backstage/plugin-home@0.4.26
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/theme@0.2.16

## @backstage/plugin-stack-overflow-backend@0.1.6

### Patch Changes

- 8006f8a602: In order to improve the debuggability of the search indexing process, messages logged during indexing are now tagged with a `documentType` whose value corresponds to the `type` being indexed.
- Updated dependencies
  - @backstage/cli@0.20.0
  - @backstage/plugin-search-common@1.1.0
  - @backstage/config@1.0.3

## @backstage/plugin-tech-insights@0.3.1

### Patch Changes

- f3d272cf57: Make sure to reload score card contents when props change
- a60a6807bd: making available the search for the last FACTS executed
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16
  - @backstage/types@1.0.0
  - @backstage/plugin-tech-insights-common@0.2.7

## @backstage/plugin-tech-insights-backend@0.5.3

### Patch Changes

- 296aea34da: The Tech Insights plugin supports running fact retrievers across multiple instances. Update the README to remove the stale instructions.
- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- f7cbfb97ed: Modify router endpoint to handle singular and collections of request parameters similarly.
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/backend-tasks@0.3.6
  - @backstage/catalog-client@1.1.1
  - @backstage/plugin-tech-insights-node@0.3.5
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/plugin-tech-insights-common@0.2.7

## @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.21

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-tech-insights-node@0.3.5
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/plugin-tech-insights-common@0.2.7

## @backstage/plugin-tech-insights-common@0.2.7

### Patch Changes

- Updated dependencies
  - @backstage/types@1.0.0

## @backstage/plugin-tech-insights-node@0.3.5

### Patch Changes

- 0963b4d5fb: Updated package role to be `node-library`.
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/backend-tasks@0.3.6
  - @backstage/config@1.0.3
  - @backstage/types@1.0.0
  - @backstage/plugin-tech-insights-common@0.2.7

## @backstage/plugin-tech-radar@0.5.17

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs@1.3.3

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/plugin-search-react@1.2.0
  - @backstage/plugin-search-common@1.1.0
  - @backstage/plugin-techdocs-react@1.0.5
  - @backstage/integration-react@1.1.5
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-addons-test-utils@1.0.5

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog@1.6.0
  - @backstage/core-components@0.11.2
  - @backstage/core-app-api@1.1.1
  - @backstage/test-utils@1.2.1
  - @backstage/plugin-search-react@1.2.0
  - @backstage/plugin-techdocs@1.3.3
  - @backstage/plugin-techdocs-react@1.0.5
  - @backstage/integration-react@1.1.5
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-module-addons-contrib@1.0.5

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/plugin-techdocs-react@1.0.5
  - @backstage/integration-react@1.1.5
  - @backstage/core-plugin-api@1.0.7
  - @backstage/integration@1.3.2
  - @backstage/theme@0.2.16

## @backstage/plugin-techdocs-node@1.4.1

### Patch Changes

- 0b2a30dead: fixing techdocs-cli Docker client creation

  Docker client does not need to be created when --no-docker
  option is provided.

  If you had DOCKER_CERT_PATH environment variable defined
  the Docker client was looking for certificates
  and breaking techdocs-cli generate command even with --no-docker
  option.

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-search-common@1.1.0
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2

## @backstage/plugin-techdocs-react@1.0.5

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/version-bridge@1.0.1

## @backstage/plugin-todo@0.2.12

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16

## @backstage/plugin-todo-backend@0.1.34

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/catalog-client@1.1.1
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2
  - @backstage/integration@1.3.2

## @backstage/plugin-user-settings-backend@0.1.1

### Patch Changes

- f3463b176b: Use `Response.status` instead of `.send(number)`
- 2d3a5f09ab: Use `response.json` rather than `response.send` where appropriate, as outlined in `SECURITY.md`
- 82ac9bcfe5: Fix wrong import statement in `README.md`.
- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-auth-node@0.2.6
  - @backstage/errors@1.1.2
  - @backstage/types@1.0.0

## @backstage/plugin-vault@0.1.4

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16

## @backstage/plugin-vault-backend@0.2.3

### Patch Changes

- 9c595302cb: Normalize on winston version ^3.2.1
- dae0bbe522: VaultBuilder.tsx renamed to VaultBuilder in order for module to be correctly loaded.
- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/backend-test-utils@0.1.29
  - @backstage/backend-tasks@0.3.6
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2

## @backstage/plugin-xcmetrics@0.2.30

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/errors@1.1.2
  - @backstage/theme@0.2.16

## example-app@0.2.76

### Patch Changes

- Updated dependencies
  - @backstage/plugin-user-settings@0.5.0
  - @backstage/plugin-scaffolder@1.7.0
  - @backstage/plugin-org@0.5.10
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/core-components@0.11.2
  - @backstage/plugin-catalog-graph@0.2.22
  - @backstage/core-app-api@1.1.1
  - @backstage/cli@0.20.0
  - @backstage/plugin-catalog-common@1.0.7
  - @backstage/plugin-catalog-import@0.9.0
  - @backstage/plugin-tech-insights@0.3.1
  - @backstage/plugin-kubernetes@0.7.3
  - @backstage/plugin-gcalendar@0.3.6
  - @backstage/plugin-api-docs@0.8.10
  - @backstage/plugin-search-react@1.2.0
  - @backstage/plugin-search-common@1.1.0
  - @backstage/plugin-cost-insights@0.11.32
  - @backstage/plugin-airbrake@0.3.10
  - @backstage/plugin-azure-devops@0.2.1
  - @backstage/plugin-badges@0.2.34
  - @backstage/plugin-circleci@0.3.10
  - @backstage/plugin-cloudbuild@0.3.10
  - @backstage/plugin-code-coverage@0.2.3
  - @backstage/plugin-dynatrace@1.0.0
  - @backstage/plugin-explore@0.3.41
  - @backstage/plugin-github-actions@0.5.10
  - @backstage/plugin-gocd@0.1.16
  - @backstage/plugin-home@0.4.26
  - @backstage/plugin-jenkins@0.7.9
  - @backstage/plugin-kafka@0.3.10
  - @backstage/plugin-lighthouse@0.3.10
  - @backstage/plugin-newrelic-dashboard@0.2.3
  - @backstage/plugin-pagerduty@0.5.3
  - @backstage/plugin-playlist@0.1.1
  - @backstage/plugin-rollbar@0.4.10
  - @backstage/plugin-search@1.0.3
  - @backstage/plugin-sentry@0.4.3
  - @backstage/plugin-techdocs@1.3.3
  - @backstage/plugin-techdocs-react@1.0.5
  - @backstage/plugin-todo@0.2.12
  - @internal/plugin-catalog-customized@0.0.3
  - @backstage/app-defaults@1.0.7
  - @backstage/integration-react@1.1.5
  - @backstage/plugin-apache-airflow@0.2.3
  - @backstage/plugin-gcp-projects@0.3.29
  - @backstage/plugin-graphiql@0.2.42
  - @backstage/plugin-newrelic@0.3.28
  - @backstage/plugin-shortcuts@0.3.2
  - @backstage/plugin-stack-overflow@0.1.6
  - @backstage/plugin-tech-radar@0.5.17
  - @backstage/plugin-techdocs-module-addons-contrib@1.0.5
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/theme@0.2.16
  - @backstage/plugin-permission-react@0.4.6

## example-backend@0.2.76

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-catalog-backend@1.5.0
  - @backstage/plugin-scaffolder-backend@1.7.0
  - @backstage/plugin-auth-node@0.2.6
  - @backstage/backend-tasks@0.3.6
  - @backstage/plugin-permission-node@0.7.0
  - @backstage/plugin-auth-backend@0.17.0
  - @backstage/plugin-permission-common@0.7.0
  - @backstage/plugin-tech-insights-backend@0.5.3
  - @backstage/plugin-search-backend@1.1.0
  - @backstage/catalog-client@1.1.1
  - @backstage/plugin-playlist-backend@0.2.0
  - @backstage/plugin-jenkins-backend@0.1.27
  - @backstage/plugin-app-backend@0.3.37
  - @backstage/plugin-badges-backend@0.1.31
  - @backstage/plugin-graphql-backend@0.1.27
  - @backstage/plugin-permission-backend@0.5.12
  - @backstage/plugin-rollbar-backend@0.1.34
  - @backstage/plugin-kubernetes-backend@0.7.3
  - @backstage/plugin-search-common@1.1.0
  - @backstage/plugin-search-backend-node@1.0.3
  - @backstage/plugin-search-backend-module-pg@0.4.1
  - @backstage/plugin-search-backend-module-elasticsearch@1.0.3
  - @backstage/plugin-techdocs-backend@1.4.0
  - @backstage/plugin-tech-insights-node@0.3.5
  - example-app@0.2.76
  - @backstage/plugin-code-coverage-backend@0.2.3
  - @backstage/plugin-kafka-backend@0.2.30
  - @backstage/plugin-todo-backend@0.1.34
  - @backstage/plugin-azure-devops-backend@0.3.16
  - @backstage/plugin-proxy-backend@0.2.31
  - @backstage/plugin-scaffolder-backend-module-rails@0.4.5
  - @backstage/plugin-tech-insights-backend-module-jsonfc@0.1.21
  - @backstage/config@1.0.3
  - @backstage/integration@1.3.2

## example-backend-next@0.0.4

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-backend@1.5.0
  - @backstage/plugin-scaffolder-backend@1.7.0
  - @backstage/backend-defaults@0.1.2
  - @backstage/plugin-app-backend@0.3.37

## techdocs-cli-embedded-app@0.2.75

### Patch Changes

- Updated dependencies
  - @backstage/catalog-model@1.1.2
  - @backstage/plugin-catalog@1.6.0
  - @backstage/core-components@0.11.2
  - @backstage/core-app-api@1.1.1
  - @backstage/cli@0.20.0
  - @backstage/test-utils@1.2.1
  - @backstage/plugin-techdocs@1.3.3
  - @backstage/plugin-techdocs-react@1.0.5
  - @backstage/app-defaults@1.0.7
  - @backstage/integration-react@1.1.5
  - @backstage/core-plugin-api@1.0.7
  - @backstage/config@1.0.3
  - @backstage/theme@0.2.16

## @internal/plugin-catalog-customized@0.0.3

### Patch Changes

- Updated dependencies
  - @backstage/plugin-catalog-react@1.2.0
  - @backstage/plugin-catalog@1.6.0

## @internal/plugin-todo-list@1.0.6

### Patch Changes

- Updated dependencies
  - @backstage/core-components@0.11.2
  - @backstage/core-plugin-api@1.0.7
  - @backstage/theme@0.2.16

## @internal/plugin-todo-list-backend@1.0.6

### Patch Changes

- Updated dependencies
  - @backstage/backend-common@0.15.2
  - @backstage/plugin-auth-node@0.2.6
  - @backstage/config@1.0.3
  - @backstage/errors@1.1.2

## @internal/plugin-todo-list-common@1.0.5

### Patch Changes

- Updated dependencies
  - @backstage/plugin-permission-common@0.7.0
