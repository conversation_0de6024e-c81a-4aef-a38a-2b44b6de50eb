---
id: v1.8.0
title: v1.8.0
description: Backstage Release v1.8.0
---

These are the release notes for the v1.8.0 release of [Backstage](https://backstage.io/).

A huge thanks to the whole team of maintainers and contributors as well as the amazing Backstage Community for the hard work in getting this release developed and done.

## Highlights

### Node 16 and 18

The project was updated to support and build against Node versions 16 and 18, after 18 had been elevated to [active LTS](https://github.com/nodejs/release#release-schedule). This means that support for Node 14 has been dropped in accordance with [our versioning policy](https://backstage.io/docs/overview/versioning-policy#nodejs-releases).

We would like to call out one particular change in Node 18 that may affect adopters. The way that it binds interfaces internally when listening for incoming connections has changed such that it may prefer e.g. an IPv6 interface that you did not intend to use. This might make it seem like the backend is unreachable from the outside in some circumstances. Because of this, you may want to change your configuration to say something along the lines of `listen: ':7007'` under the `backend` section to ensure that it listens on all interfaces.

### Events

An early version of the `events` plugin ecosystem has been added. This allows events such as webhooks and similar to be received and propagated by a Backstage backend, and plugins can then react to those events. One early use case for this plugin is to start powering catalog updates.

This is early days, so bear in mind that interfaces and implementations may still be subject to change. But do check it out, and let us know what you think!

Contributed by [@pjungermann](https://github.com/pjungermann) in [#13931](https://github.com/backstage/backstage/pull/13931)

### Gitea

There is now support for [Gitea](https://gitea.io) as a general SCM integration. Please check out [the docs](https://backstage.io/docs/integrations/gitea/locations) for how to get started.

Contributed by [@atoko](https://github.com/atoko) in [#14056](https://github.com/backstage/backstage/pull/14056)

### Azure Sites

There's a new Azure Sites (Apps & Functions) plugin. It lets you view the current status of the site, and quickly jump to the site's Overview or Log Stream pages. Please check out [the plugin README](https://github.com/backstage/backstage/tree/v1.8.0/plugins/azure-sites) for more information.

Contributed by [@wesley-pattison](https://github.com/wesley-pattison) in [#13634](https://github.com/backstage/backstage/pull/13634)

### Scaffolder Metrics

The scaffolder backend now emits default Prometheus metrics that track job execution.

Contributed by [@spencerrichardhenry](https://github.com/spencerrichardhenry) in [#13815](https://github.com/backstage/backstage/pull/13815)

### `@backstage/backend-common`

The `UrlReader` interface has been updated to require that `readUrl` is implemented. The `readUrl` method has previously been optional to implement, but a warning used to be logged when calling its predecessor `read`.

The `read` method is now deprecated and will be removed in a future release, and if you made custom URL readers you will have to implement a `readUrl` method on them.

### Recharts

The `recharts` library that powers the graphing functionality in some plugins was upgraded across the repository to version 2. Please let us know if you run into any new issues with graphing in plugins such as Bitrise, CICD statistics, code coverage, cost insights, GIT release manager, or XCmetrics.

### `GitHub` to `Github`

We are pursuing an effort to standardize the naming of exported GitHub related symbols. After an upgrade of Backstage, you may therefore see errors related to not finding components and types whose names start with “GitHub”. As an example, if you are using `GitHubIssuesPage` from the GitHub Issues plugin, you now need to import and use it as `GithubIssuesPage` instead.

## Security Fixes

This release does not contain any security fixes.

## Upgrade path

We recommend that you keep your Backstage project up to date with this latest release. For more guidance on how to upgrade, check out the documentation for [keeping Backstage updated](https://backstage.io/docs/getting-started/keeping-backstage-updated).

## Links and References

Below you can find a list of links and references to help you learn about and start using this new release.

- [Backstage official website](https://backstage.io/), [documentation](https://backstage.io/docs/), and [getting started guide](https://backstage.io/docs/getting-started/)
- [GitHub repository](https://github.com/backstage/backstage)
- Backstage's [versioning and support policy](https://backstage.io/docs/overview/versioning-policy)
- [Community Discord](https://discord.gg/backstage-687207715902193673) for discussions and support
- [Changelog](https://github.com/backstage/backstage/tree/master/docs/releases/v1.8.0-changelog.md)
- Backstage [Demos](https://backstage.io/demos), [Blog](https://backstage.io/blog), [Roadmap](https://backstage.io/docs/overview/roadmap) and [Plugins](https://backstage.io/plugins)

Sign up for our [newsletter](https://mailchi.mp/spotify/backstage-community) if you want to be informed about what is happening in the world of Backstage.
