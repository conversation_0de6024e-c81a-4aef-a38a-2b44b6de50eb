---
id: concepts
title: Concepts
description: Documentation on concepts that are introduced with Spotify's docs-like-code solution in Backstage
---

This page describes concepts that are introduced with Spotify's docs-like-code
solution in Backstage.

## Generating TechDocs Steps

### TechDocs Preparer

Preparing is the first step of generating documentation for an entity. It
fetches the source markdown files from the source code hosting provider (GitHub,
GitLab, etc.) and passes the files to the generator for next steps.

There are two kinds of preparers available -

1. Common Git Preparer - Uses `git clone` on any repository URL.
2. URL Reader - Uses source code hosting provider's API to download files.
   (Faster and recommended)

### TechDocs Generator

Generating is the second step after preparing the markdown source files. This
step either runs the TechDocs container (defined below) or runs `mkdocs` CLI to
generate static HTML files and its assets.

### TechDocs Publisher

Publishing is the third and final step after preparing and generating docs.
TechDocs Publisher uploads the generated files to a storage.

The `techdocs-backend` plugin currently comes with two publishers - Google Cloud
Storage and Local Filesystem. You can configure them in your Backstage app.
[See here](./configuration.md).

A TechDocs publisher is responsible for two things (two-way communication
between `techdocs-backend` and the storage)

1. Publish generated static files to a storage (Configured by
   `techdocs.builder`)
2. Read files from the storage when users visit a TechDocs site

[TechDocs Backend](https://github.com/backstage/backstage/tree/master/plugins/techdocs-backend)

## TechDocs Build Strategy

To accommodate more complex logic surrounding whether or not to build TechDocs, the TechDocs backend
supports selecting a Build Strategy.
The Build Strategy is responsible for deciding whether the documentation requested should be built locally
by the TechDocs backend or not.
Customization of the Build Strategy allows for more complex behaviour regarding whether the TechDocs backend
is responsible for building TechDocs, whether an external process is responsible, or whether a combination
of local builds and an external process is responsible, on an entity-by-entity basis.

The default Build Strategy results in the TechDocs backend building documentation locally if the
`techdocs.builder` configuration option is set to `'local'`, and skipping any building otherwise.
However any logic that satisfies the Build Strategy interface can be implemented, using the Backstage
config as well as the entity being processed to make a decision.

For an example of how the Build Strategy can be used to implement a 'hybrid' build model, refer to
the [How to implement a hybrid build strategy](./how-to-guides.md#how-to-implement-a-hybrid-build-strategy) guide.

## TechDocs Container

The TechDocs container is a Docker container available at
[DockerHub](https://hub.docker.com/r/spotify/techdocs). It builds static HTML
pages, including stylesheets and scripts from Python flavored Markdown, through
MkDocs.

[TechDocs Container](https://github.com/backstage/techdocs-container)

## TechDocs Core Plugin

The TechDocs Core Plugin is an [MkDocs](https://www.mkdocs.org/) plugin created
as a wrapper around multiple MkDocs plugins and Python Markdown extensions to
standardize the configuration of MkDocs used for TechDocs.

[TechDocs Core](https://github.com/backstage/mkdocs-techdocs-core)

## TechDocs CLI

The TechDocs CLI was created to make it easy to write, generate and preview
documentation for publishing. Currently it mostly acts as a wrapper around the
TechDocs container and provides an easy-to-use interface for our docker
container.

[TechDocs CLI](https://github.com/backstage/techdocs-cli)

## TechDocs Reader

Documentation generated by TechDocs is generated as static HTML sites. The
TechDocs Reader was therefore created to be able to integrate pre-generated HTML
sites with the Backstage UI.

[TechDocs Reader](https://github.com/backstage/backstage/blob/master/plugins/techdocs/src/reader/README.md)

## Transformers

Transformers are different pieces of functionality used inside the TechDocs
Reader. The reason why transformers were introduced was to provide a way to
transform the HTML content on pre and post render (e.g. rewrite docs links or
modify css).

[Transformers API docs](https://github.com/backstage/backstage/blob/master/plugins/techdocs/src/reader/README.md)

## TechDocs Addons

Addons (introduced in Backstage v1.2) are client-side, React-based extensions
that can be used to augment the TechDocs experience at read-time. They offer a
mechanism for configuring the TechDocs Reader to better suit your
organization's needs.

Addons can dynamically load and display information anywhere in the TechDocs
Reader, including within the statically generated content itself.

Addons should not be confused with `mkdocs` plugins, which may be used to
customize a TechDocs site's content at build-time. While it's possible to take
advantage of some `mkdocs` plugins, not all such plugins play well with
TechDocs (primarily, but not exclusively, for security reasons). Addons offer
an alternative.
