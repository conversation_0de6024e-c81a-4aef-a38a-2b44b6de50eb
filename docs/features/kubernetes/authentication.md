---
id: authentication
title: Kubernetes Authentication
description: Authentication in Kubernetes plugin
---

The authentication process in Kubernetes relies on `KubernetesAuthProviders` --
which are not the same as the application's auth providers. the default
providers are defined in
`plugins/kubernetes-react/src/kubernetes-auth-provider/KubernetesAuthProviders.ts`;
you can add custom providers there if needed.

These providers are configured so your Kubernetes plugin can locate and access
the clusters you have access to, some of them have special requirements in the
third party in question, like Microsoft Entra ID (formerly Azure Active
Directory) subscription or Azure RBAC support active on the cluster.

The providers currently available are summarized below:

## Server Side Providers

These providers authenticate your _application_ with the cluster, meaning anyone
that is logged in into your Backstage app will be granted the same access to
Kubernetes objects, including guest users.

The server side providers are:

- `aws`
- `azure`
- `googleServiceAccount`
- `localKubectlProxy`
- `serviceAccount`

### AWS

For AWS, in addition to Kubernetes configuration, you will have to set up
AWS authentication. The AWS server-side authentication provider uses [AWS
Identity and Access Management (IAM)][3] to authenticate to the target
Account(s); you can read more about it on the page for the [Integration AWS
node][4].

Using the plugin, you can authenticate to several AWS accounts using either
[static AWS Access keys][5] or short-lived Access keys generated by [assuming a
role][6], for either case you will need to install the [AWS CLI utility][7] and
set it up following the steps in the linked documentation.

If you have generated static AWS security credentials, the configuration block
for AWS will look like this:

```yaml
aws:
  mainAccount:
  accounts:
    - accountId: '<account-number>'
      accessKeyId: ${AWS_ACCESS_KEY_ID}
      secretAccessKey: ${AWS_SECRET_ACCESS_KEY}
      region: <region>
  accountDefaults:
```

If your environment is set up to assume a role, the configuration would instead
look like this:

```yaml
aws:
  mainAccount:
    roleName: <name-of-the-role-to-assume>
    region: <region>
  accounts:
  accountDefaults:
```

Either of these sections needs to be present for the Kubernetes configuration to
use the `aws` `authProvider`. The Kubernetes configuration looks like this:

```yaml
kubernetes:
  serviceLocatorMethod:
    type: 'multiTenant'
  clusterLocatorMethods:
    - type: 'config'
      clusters:
        - url: https://<unique-identifier>.<region>.eks.amazonaws.com
          name: ${CLUSTER_NAME_TO_DISPLAY}
          authProvider: 'aws'
          caData: ${EKS_CA_DATA}
          authMetadata:
            kubernetes.io/aws-assume-role: ${ROLE_ARN_TO_ASSUME}
            kubernetes.io/aws-external-id: ${ID_FROM_AWS_ADMIN}
            kubernetes.io/x-k8s-aws-id: ${CLUSTER_NAME_IN_AWS_CONSOLE}
```

You get both the cluster URL and CA directly from the AWS console by going to
`EKS` > `Your cluster` > `Overview` > `Details`. You will find them under 'API
server endpoint' and 'Certificate authority' respectively.

If Backstage needs to assume a role when authenticating with EKS clusters, the
`kubernetes.io/aws-assume-role` parameter can be set to the ARN of the desired
role. the `kubernetes.io/aws-external-id` parameter in the config corresponds to
the `ExternalId` parameter of the [`AssumeRole` API in STS][8].

### Azure

The Azure provider works by authenticating on the server with the Azure CLI,
please note that [Microsoft Entra authentication][1] is a requirement and has to
be enabled in your AKS cluster, then follow these steps:

- [Install the Azure CLI][2] in the environment where the backstage application
  will run.
- Login with your Azure/Microsoft account with `az login` in the server's
  terminal.
- Go to your AKS cluster's resource page in Azure Console and follow the steps
  in the `Connect` tab to set the subscription and get your credentials for
  `kubectl` integration.
- Configure your cluster to use the `azure` auth provider like this:

```yaml
kubernetes:
  clusterLocatorMethods:
    - type: 'config'
      clusters:
        - name: My AKS cluster
          url: ${AZURE_CLUSTER_API_SERVER_ADDRESS}
          authProvider: azure
          skipTLSVerify: true
```

To get the API server address for your Azure cluster, go to the Azure console
page for the cluster resource, go to `Overview` > `Properties` tab >
`Networking` section and copy paste the API server address directly in that
`url` field.

## Client Side Providers

These providers authenticate a _user_ with the cluster. Each Backstage user will
be prompted for credentials and will have access to the clusters as long as the
user has been authorized to access said cluster. If Backstage is configured to
communicate with a cluster but the user isn't authorized to access it, they will
see the cluster listed but will not see any resources in the plugin page for
that cluster. The error will show as `401` or similar.

The providers available as client side are:

- `aks`
- `google`
- `oidc`

[1]: https://docs.microsoft.com/en-us/azure/aks/managed-aad
[2]: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli?view=azure-cli-latest
[3]: https://docs.aws.amazon.com/IAM/latest/UserGuide/when-to-use-iam.html
[4]: https://github.com/backstage/backstage/blob/master/packages/integration-aws-node/README.md
[5]: https://docs.aws.amazon.com/IAM/latest/UserGuide/id_credentials_access-keys.html
[6]: https://docs.aws.amazon.com/IAM/latest/UserGuide/id_roles_use.html
[7]: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html
[8]: https://docs.aws.amazon.com/STS/latest/APIReference/API_AssumeRole.html#API_AssumeRole_RequestParameters
