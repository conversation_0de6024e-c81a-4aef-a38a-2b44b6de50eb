<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="391px" height="261px" viewBox="-0.5 -0.5 391 261" content="&lt;mxfile&gt;&lt;diagram id=&quot;Wjgsm-kAhxxm43YMIDME&quot; name=&quot;Page-1&quot;&gt;5VrbcqM4EP0aP8YFyGD8OHbizEN2a2ozVbv7KIOwVZGRI+Qk3q/fFkjchG8xziQ1TNUUanSBc7qPuuUM0Gz9di/wZvUHjwkbeE78NkC3A88beS78rwy7wuAHQWFYChoXJrcyPNL/iDY62rqlMckaHSXnTNJN0xjxNCWRbNiwEPy12S3hrLnqBi/1ik5leIwwI1a3v2ksV/qzwlrv74QuV2ZlXz9Y4OhpKfg21csNPDTPr+LxGpupdP9shWP+WjOhuwGaCc5lcbd+mxGmkDWoFePme56Wry1IKk8ZgPxixAtmW/3pEedPlCjQGLxZ8ZZyZ4CBF96o2+2azQVew+30dUUledzgSNlfwR3AtpJrBi1XPdb4AXZgN6Ah1UooYzPOuMjnRkmSeFEE9kwK/kRqT+JgEfiBGsFTWbM7+QV2+7s1FC9ESPJWM2kc7glfEyl20EU/Ndxqj3UNRa8V/2iibas696Yj1j63LKeugIcbjX03D57Nw1R5MRGHGHigCWE0VSRsiKCwKlHAMG3+UdmOkQRxJDEMEWWbMbzJ6CJfVSEsSLQVGX0hf5GsCFdl5VupVpqVYdhBa4xJmHTSGkQhWSTXpdUNmrwGNq1hB6tlv0tYRSOLVYvOGgkbTlOZL+dPB/5ti1Uu5IoveYpZndd2CPnqn41ocXVykF+dWJdOeTLYRk401hMba7cD616gDo9DTWLQeN1MeR42NfQziYX8pvaO6vF+TDK+FRFp0gwTLEmrn1rzIHKCMCwhrJpb2CVIuBYSP0m0inmUDbyAwftNF6AqwVLdTWG/IrBZfX2R8UkYj7ocPPQWKLjy3oG8XygywZcWGXQ+2E2RcZ3TVGbUB9bjS1Vmr7P1JxT5UBAyvKt10LRXM/9QhgpUK/0JnBYwxYx7Rjt+5+hy+UIu9ah6PtqaCPlHJio01pooJ64E6yQuR7ZO3t/9HKhInpscuIPaB7yAUqfBKWZ0mcJ9BJzm8aJ8mUIx8U0/WNM4VnOAvoGs4Urv7GBUIaXLoVA353hNmcJjBiBSlRE6f5LX7mAy/tkOprJS0osP6uVGV5A5QxDHoMlF0TrPDy2Gb9xRY9Ybd9KcgidJRi4m17k0UAFTsftHNYYO8ozhX0XccDIZG8Ptm6ayaO3qrZqE5sYYZysSl/PHJtngG5IWljlVX3R7cFf6ZELhOecIRTu+zeizhQIUZ4i8pn9OJsMwvJpceJZH5W7qlPUyT3Pwsw1Ps8+gHbYHlVFxsTzcgD4Eo7AfRWi6xE27nu5HEM4rxdosLLiUfA0PmKJwWp7ymBxH60ctqCOGs4xGjbh22yJwlLHumG+WGmiPDtTzIbONXkgVhOsQoXDsTILxeOROINtrRHPY07YPEw1diOfyOimqL5ay8ICUvcfn7EOdhTnUAb8DIqIi8iTJX0uQ5y3Jijf+fNphAqiH1ML1fLeVBPSjJOOWkrRm6EdJ7Hqr3AIklD2pRV+uFHnMO8eL5BoR4yucpXXQZ+nNXubKDcQETKABP1Lnuj2UXiO79PpdYXfDD4TdPlfbFgJGY3h9Kne/If6jj8Pft6tUc4qnvhieJFifQwbPW/Wz0fQ7YS9E7QOVyRz34eh5SwWpZ62au/bB4AJnQJ1OajXje7oaC3xK8UrGfKDeqs6Z7ipr197GSKImy8BxaLp8yFu3XjPVWjAePaldruFwB0vntrvtcZ5aEejmJ1u1ItDzS8M5ReDhBHJPune6r6rEHDmjXrZTFLQOAManpXfn5mHlz2qtdfZmi+3+k7NK0CCAytHKLau5huZE9Ui6Wc3ca37h24WmFUqfpFJ5j59+QFXiNdPA8meo86uSydA/PNW1KpFDHv0en/o61e/X8Cn03nMr26esqa7kU+jQQd1xn4Jm9WcpRffqL3/Q3f8=&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs/>
    <g>
        <path d="M 0 60 L 80 60 L 80 75 L 70 90 L 0 90 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 80 60 L 390 60 L 390 180 L 0 180 L 0 90" fill="none" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 75px; margin-left: 1px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                cookie flow
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="40" y="79" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    cookie flow
                </text>
            </switch>
        </g>
        <rect x="80" y="0" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/>
        <path d="M 120 40 L 120 260" fill="none" stroke="#6c8ebf" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 20px; margin-left: 81px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Browser
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="120" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Browser
                </text>
            </switch>
        </g>
        <rect x="115" y="90" width="10" height="60" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <rect x="240" y="0" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/>
        <path d="M 280 40 L 280 260" fill="none" stroke="#82b366" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 20px; margin-left: 241px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Techdocs
                                <br/>
                                Backend
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="280" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Techdocs...
                </text>
            </switch>
        </g>
        <rect x="275" y="100" width="10" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 125 100 L 190 100 Q 200 100 210 100 L 268.63 100" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 273.88 100 L 266.88 103.5 L 268.63 100 L 266.88 96.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 80px; margin-left: 186px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 8px; font-family: &quot;Courier New&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                GET /cookie
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="186" y="83" fill="rgb(0, 0, 0)" font-family="Courier New" font-size="8px" text-anchor="middle">
                    GET /cookie
                </text>
            </switch>
        </g>
        <path d="M 275 140 L 210 140 Q 200 140 190 139.98 L 127.56 139.88" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 133.44 136.39 L 126.44 139.88 L 133.43 143.39" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 120px; margin-left: 200px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 8px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                set cookie on response
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="200" y="123" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="8px" text-anchor="middle">
                    set cookie on response
                </text>
            </switch>
        </g>
        <path d="M 120.34 220 L 190 220 Q 200 220 210 220 L 273.13 220" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 278.38 220 L 271.38 223.5 L 273.13 220 L 271.38 216.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 200px; margin-left: 203px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 8px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                browser static content requests
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="203" y="203" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="8px" text-anchor="middle">
                    browser static content requests
                </text>
            </switch>
        </g>
        <rect x="160" y="207" width="80" height="10" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 212px; margin-left: 161px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 7px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                cookie token
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="200" y="214" fill="#000000" font-family="Helvetica" font-size="7px" text-anchor="middle">
                    cookie token
                </text>
            </switch>
        </g>
        <rect x="160" y="127" width="80" height="10" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 132px; margin-left: 161px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 7px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                cookie token
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="200" y="134" fill="#000000" font-family="Helvetica" font-size="7px" text-anchor="middle">
                    cookie token
                </text>
            </switch>
        </g>
        <rect x="160" y="87" width="80" height="10" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 92px; margin-left: 161px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 7px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                user identity token
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="200" y="94" fill="#000000" font-family="Helvetica" font-size="7px" text-anchor="middle">
                    user identity token
                </text>
            </switch>
        </g>
        <path d="M 286 110 L 310 110 L 310 130.4 L 294.45 130.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 287.45 130.36 L 294.45 126.87 L 294.44 133.87 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 119px; margin-left: 314px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 7px; font-family: &quot;Courier New&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font face="Helvetica">
                                    acquire cookie token
                                    <br/>
                                    based on user token
                                    <br/>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="314" y="121" fill="#000000" font-family="Courier New" font-size="7px">
                    acquire cookie token...
                </text>
            </switch>
        </g>
        <path d="M 120.34 230 L 190 230 Q 200 230 210 230 L 273.13 230" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 278.38 230 L 271.38 233.5 L 273.13 230 L 271.38 226.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 120.34 240 L 190 240 Q 200 240 210 240 L 273.13 240" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 278.38 240 L 271.38 243.5 L 273.13 240 L 271.38 236.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
