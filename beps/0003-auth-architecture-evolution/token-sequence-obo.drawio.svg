<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="401px" height="251px" viewBox="-0.5 -0.5 401 251" content="&lt;mxfile&gt;&lt;diagram id=&quot;Wjgsm-kAhxxm43YMIDME&quot; name=&quot;Page-1&quot;&gt;7VrbcuI4EP0aHkP5jnkcIGQeMrupzVbt7qOwhVFFWIwsh7Bfvy1bvsgywSQwyWyNU5Wy2rrY3eecbikZufPtyx1Hu803FmM6cqz4ZeQuRo7jOTb8loZDafCDoDQknMSlyW4Mj+RfrIyWsuYkxpnWUTBGBdnpxoilKY6EZkOcs73ebc2ovuoOJWpFqzE8Rohio9tfJBYb9Vlhq/dXTJJNtbKvHqxQ9JRwlqdquZHjrourfLxF1VSqf7ZBMdu3TO7tyJ1zxkR5t32ZYyo9W3mtHLc88rR+bY5TMWSA45cjnhHN1afPpPcwV+8nDpVL4FV38jbf0nuyxpSk0JrtMCdbLKC/u6DK/NDYZvsNEfhxhyI5dA9YAdtGbCm0bLiF+AkEQ3jdphTtMrIqVrXAwnGU84w84z9wVsJEWlku5ErzOvzSuCaUzhllvHhhN0Y4XEdgzwRnT7j1JIhCvFrLEbB8y24VF9hNNyrPPmMu8EvLpNx6hxl8MT9Al+ppoEKsGFA19w2cKjRtWkhyKiQhheCknrkJI9yoSPZH1fWMqBrhbAVhx0gqiuX82chfdKLKuNiwhKWItuPa8fXalz+mR8urNwbF1evrGpSDne36mq+npq/tHl/b3iV8bRu+Bh1ZS8WRJAooLDZbybtE3s1AIDCow8/PLh+HsdcX2dBZuUVkr8gu1/lAdgU/Nbvc852ts8u2BtLLuYCzA7fH2SWppDNkbi/RD9bvuUybs6+YPmNBItSYKvah6HtOOJaxwvyZRPKOrVhRXQAtTbquUIZj2QmeWXmRFo91rSzwReWbVeYONHAMlYZqNtG/baxtBiNKklSSH6/lZBlQnaTJfdFaQMhmoCVfZL0DzRVl0ZPktCw/cFzRFt5libaEyujNWc5J8RG/4b16qIqvySuEBezww9/ylcZ2gbfC8I9cYez4tWHxotYsW4d2q4XuwkjRCtNZXS1Vq6as0L3SadJTegmIeIIrGAfHoH0UxzfW2HItlRs5pkiA8OmlYw9S1XQPksbNXF6gc8LuYD0DT0dYDWrgDrFCh1Y3JQ5Hl5lYvcssB3Z3te5wU67fPzjoH1y/a+l945OaeaqObL3OsBh1WV4HZJjKTk6rbJtKCjkt7hxNM2Vs9FKpD1od/Jm4Og9D58a+zmBVQIJXovkGD3tm5fLw++OfIynbS4Gyp6zX4feSuf0qFYGni/wlcwsIMP2iHmxJHBfSxmWZgZr6w0yOLUkKCzET4GKWDlGzHkGoQNTNdfXOUb3LqL396tMOkA7L6nD+IkJyY3varDf2VJ/iImyqKt23s6mVA6xuDgjDN+WAGGWbIk/ZeiJjO8ivhWVJ5BctXi0afxhLj6qu6+shrIl7IhmYjLf8savSbnF15p1Mx9PAfHxCoN8CF8eAS5ZHEc4+gySYSKjR/W6ayxIh8MILMVsn9lV4be5F5kggypJfW8/jW88enBjCchQiXvhxW09vQFH0ybaeJ3xdAnjw1nMybOt5iYOdSgXeW4Aau5gBULtuYel5OoKds7YJbuD3jj472/n2iYkumM+mRizvblWlC7EggmDw13I8Hn+C/Dagxh2c6DpkC18l2+Vr3LqmDa+Q+3yzSDmToFpN6+g17XQ6+UE17ScUCHc6DkKdnJNzNMK3PZigKVTt3rnOVgyQnrHVvnSwOsF0HPrX0hDfPIkUeLuDOMhTxEJFDp9APd4lECWl/jfVsW+mcHWGS+IqYNVxbidw+iHqiWK3e4x64b9DvqtcdTrJ3q62DCfq1e6B5ltKKN9Mu0fO3a/o/Y/dLFidUsueXMv70Gz+b6AkS/OvGe7tfw==&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs/>
    <g>
        <rect x="0" y="0" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/>
        <path d="M 40 40 L 40 250" fill="none" stroke="#6c8ebf" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 20px; margin-left: 1px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Browser
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="40" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Browser
                </text>
            </switch>
        </g>
        <rect x="35" y="90" width="10" height="140" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <rect x="160" y="0" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/>
        <path d="M 200 40 L 200 250" fill="none" stroke="#82b366" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 20px; margin-left: 161px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Scaffolder
                                <br/>
                                Backend
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="200" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Scaffolder...
                </text>
            </switch>
        </g>
        <rect x="195" y="100" width="10" height="120" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 206 110 L 230 110 L 230 130.4 L 214.45 130.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 207.45 130.36 L 214.45 126.87 L 214.44 133.87 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 119px; margin-left: 234px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 7px; font-family: &quot;Courier New&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font face="Helvetica">
                                    acquire service obo token
                                    <br/>
                                    based on user token
                                    <br/>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="234" y="121" fill="#000000" font-family="Courier New" font-size="7px">
                    acquire service obo token...
                </text>
            </switch>
        </g>
        <path d="M 45 100 L 110 100 Q 120 100 130 100 L 188.63 100" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 193.88 100 L 186.88 103.5 L 188.63 100 L 186.88 96.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 80px; margin-left: 106px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 8px; font-family: &quot;Courier New&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                POST /tasks
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="106" y="83" fill="rgb(0, 0, 0)" font-family="Courier New" font-size="8px" text-anchor="middle">
                    POST /tasks
                </text>
            </switch>
        </g>
        <path d="M 194 220 L 47.57 219.96" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 53.45 216.46 L 46.45 219.96 L 53.45 223.46" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 211px; margin-left: 124px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 8px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                success
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="124" y="214" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="8px" text-anchor="middle">
                    success
                </text>
            </switch>
        </g>
        <rect x="320" y="0" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/>
        <path d="M 360 40 L 360 250" fill="none" stroke="#82b366" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 20px; margin-left: 321px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Catalog
                                <br/>
                                Backend
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="360" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Catalog...
                </text>
            </switch>
        </g>
        <rect x="355" y="170" width="10" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <path d="M 205 170 L 270 170 Q 280 170 290 170 L 348.63 170" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 353.88 170 L 346.88 173.5 L 348.63 170 L 346.88 166.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 151px; margin-left: 280px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 8px; font-family: &quot;Courier New&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                GET /entities/...
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="280" y="154" fill="rgb(0, 0, 0)" font-family="Courier New" font-size="8px" text-anchor="middle">
                    GET /entities/...
                </text>
            </switch>
        </g>
        <path d="M 354.68 210 L 289.68 210 Q 279.68 210 269.68 209.98 L 207.24 209.85" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 213.13 206.37 L 206.12 209.85 L 213.11 213.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 201px; margin-left: 284px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 8px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                template entity
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="284" y="204" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="8px" text-anchor="middle">
                    template entity
                </text>
            </switch>
        </g>
        <rect x="80" y="86" width="80" height="10" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 91px; margin-left: 81px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 7px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                user identity token
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="120" y="93" fill="#000000" font-family="Helvetica" font-size="7px" text-anchor="middle">
                    user identity token
                </text>
            </switch>
        </g>
        <rect x="240" y="157" width="80" height="10" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 162px; margin-left: 241px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 7px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                service obo token
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="280" y="164" fill="#000000" font-family="Helvetica" font-size="7px" text-anchor="middle">
                    service obo token
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
