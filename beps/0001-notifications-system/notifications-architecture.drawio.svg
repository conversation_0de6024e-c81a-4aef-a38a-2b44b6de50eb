<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="671px" height="311px" viewBox="-0.5 -0.5 671 311" content="&lt;mxfile scale=&quot;1&quot; border=&quot;20&quot;&gt;&lt;diagram id=&quot;2fh7CcgDxUdRYRy3FD-Z&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs/>
    <g>
        <path d="M 230 120 L 230 80" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 290 120 L 290 110 Q 290 100 300 100 L 310 100 Q 320 100 320 94.06 L 320 88.12" fill="none" stroke="#666666" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 320 81.12 L 322.33 88.12 L 317.67 88.12 Z" fill="#666666" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="200" y="120" width="120" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 140px; margin-left: 201px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Notification Backend
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="260" y="144" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Notification Backend
                </text>
            </switch>
        </g>
        <path d="M 200 260 L 148.12 260" fill="none" stroke="#666666" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 141.12 260 L 148.12 257.67 L 148.12 262.33 Z" fill="#666666" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 260 240 L 260 168.12" fill="none" stroke="#666666" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 260 161.12 L 262.33 168.12 L 257.67 168.12 Z" fill="#666666" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="200" y="240" width="120" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 260px; margin-left: 201px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Notification Frontend
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="260" y="264" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Notification Frontend
                </text>
            </switch>
        </g>
        <rect x="430" y="120" width="100" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 140px; margin-left: 431px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Signal Backend
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="480" y="144" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Signal Backend
                </text>
            </switch>
        </g>
        <path d="M 480 231.88 L 480 160" fill="none" stroke="#666666" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 480 238.88 L 477.67 231.88 L 482.33 231.88 Z" fill="#666666" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 480 240 L 480 210 Q 480 200 490 200 L 560 200 Q 570 200 570 190 L 570 160" fill="none" stroke="#666666" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 480 240 L 480 210 Q 480 200 490 200 L 620 200 Q 630 200 630 190 L 630 160" fill="none" stroke="#666666" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 430 260 L 328.12 260" fill="none" stroke="#666666" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 321.12 260 L 328.12 257.67 L 328.12 262.33 Z" fill="#666666" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="430" y="240" width="100" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 260px; margin-left: 431px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Signal Frontend
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="480" y="264" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Signal Frontend
                </text>
            </switch>
        </g>
        <path d="M 140 140 L 191.88 140" fill="none" stroke="#666666" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 198.88 140 L 191.88 142.33 L 191.88 137.67 Z" fill="#666666" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="20" y="120" width="120" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 140px; margin-left: 21px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Plugin X Backend
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="144" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Plugin X Backend
                </text>
            </switch>
        </g>
        <rect x="20" y="240" width="120" height="40" fill="#f5f5f5" stroke="#666666" stroke-dasharray="3 3" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 260px; margin-left: 21px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Plugin X Frontend
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="264" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Plugin X Frontend
                </text>
            </switch>
        </g>
        <path d="M 320 140 L 390 140 Q 400 140 400 130 L 400 70 Q 400 60 410 60 L 431.88 60" fill="none" stroke="#666666" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 438.88 60 L 431.88 62.33 L 431.88 57.67 Z" fill="#666666" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 480 80 L 480 111.88" fill="none" stroke="#666666" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 480 118.88 L 477.67 111.88 L 482.33 111.88 Z" fill="#666666" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 480 80 L 480 90 Q 480 100 490 100 L 560 100 Q 570 100 570 105.94 L 570 111.88" fill="none" stroke="#666666" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 570 118.88 L 567.67 111.88 L 572.33 111.88 Z" fill="#666666" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 480 80 L 480 90 Q 480 100 490 100 L 620 100 Q 630 100 630 105.94 L 630 111.88" fill="none" stroke="#666666" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 630 118.88 L 627.67 111.88 L 632.33 111.88 Z" fill="#666666" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="440" y="40" width="80" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 60px; margin-left: 441px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                EventsService
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="480" y="64" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    EventsService
                </text>
            </switch>
        </g>
        <rect x="550" y="120" width="40" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <rect x="610" y="120" width="40" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <rect x="530" y="120" width="20" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 140px; margin-left: 531px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                ...
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="540" y="144" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ...
                </text>
            </switch>
        </g>
        <rect x="590" y="120" width="20" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 140px; margin-left: 591px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                ...
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="600" y="144" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ...
                </text>
            </switch>
        </g>
        <rect x="320" y="120" width="80" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 130px; margin-left: 321px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                publish event
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="360" y="133" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    publish event
                </text>
            </switch>
        </g>
        <rect x="520" y="80" width="80" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 90px; margin-left: 521px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                broadcast
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="560" y="93" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    broadcast
                </text>
            </switch>
        </g>
        <rect x="500" y="180" width="50" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 48px; height: 1px; padding-top: 190px; margin-left: 501px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                ???
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="525" y="193" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    ???
                </text>
            </switch>
        </g>
        <rect x="480" y="210" width="70" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 220px; margin-left: 481px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                server push
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="515" y="223" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    server push
                </text>
            </switch>
        </g>
        <rect x="140" y="120" width="60" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 130px; margin-left: 141px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                POST
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="170" y="133" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    POST
                </text>
            </switch>
        </g>
        <path d="M 190 60 L 158.12 60" fill="none" stroke="#666666" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 151.12 60 L 158.12 57.67 L 158.12 62.33 Z" fill="#666666" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="30" y="40" width="120" height="40" fill="#f5f5f5" stroke="#666666" stroke-dasharray="3 3" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 60px; margin-left: 31px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                External System
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="90" y="64" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    External System
                </text>
            </switch>
        </g>
        <rect x="30" y="20" width="120" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 30px; margin-left: 31px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                email/slack/teams, etc.
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="90" y="33" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    email/slack/teams, etc.
                </text>
            </switch>
        </g>
        <rect x="140" y="240" width="60" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 250px; margin-left: 141px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Render
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="170" y="253" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    Render
                </text>
            </switch>
        </g>
        <ellipse cx="230" cy="60" rx="40" ry="20" fill="#f5f5f5" stroke="#666666" stroke-dasharray="3 3" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 60px; margin-left: 191px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="color: rgb(51, 51, 51); font-size: 12px;">
                                    Processor
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="230" y="63" fill="#333333" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    Processor
                </text>
            </switch>
        </g>
        <rect x="260" y="190" width="30" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 28px; height: 1px; padding-top: 200px; margin-left: 261px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                GET
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="275" y="203" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    GET
                </text>
            </switch>
        </g>
        <ellipse cx="160" cy="160" rx="10" ry="10" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 160px; margin-left: 151px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="160" y="163" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    1
                </text>
            </switch>
        </g>
        <ellipse cx="340" cy="160" rx="10" ry="10" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 160px; margin-left: 331px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                3
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="340" y="163" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    3
                </text>
            </switch>
        </g>
        <ellipse cx="210" cy="100" rx="10" ry="10" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 100px; margin-left: 201px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                1b
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="210" y="103" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    1b
                </text>
            </switch>
        </g>
        <ellipse cx="460" cy="100" rx="10" ry="10" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 100px; margin-left: 451px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                4
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="460" y="103" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    4
                </text>
            </switch>
        </g>
        <ellipse cx="500" cy="180" rx="10" ry="10" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 180px; margin-left: 491px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                5
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="500" y="183" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    5
                </text>
            </switch>
        </g>
        <ellipse cx="410" cy="280" rx="10" ry="10" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 280px; margin-left: 401px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                6
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="410" y="283" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    6
                </text>
            </switch>
        </g>
        <ellipse cx="180" cy="280" rx="10" ry="10" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 280px; margin-left: 171px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                7b
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="283" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    7b
                </text>
            </switch>
        </g>
        <ellipse cx="240" cy="220" rx="10" ry="10" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 220px; margin-left: 231px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                7
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="240" y="223" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    7
                </text>
            </switch>
        </g>
        <path d="M 300 34.96 C 300 32.22 308.95 30 320 30 C 325.3 30 330.39 30.52 334.14 31.45 C 337.89 32.38 340 33.65 340 34.96 L 340 75.04 C 340 76.35 337.89 77.62 334.14 78.55 C 330.39 79.48 325.3 80 320 80 C 314.7 80 309.61 79.48 305.86 78.55 C 302.11 77.62 300 76.35 300 75.04 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 340 34.96 C 340 36.28 337.89 37.54 334.14 38.47 C 330.39 39.4 325.3 39.93 320 39.93 C 314.7 39.93 309.61 39.4 305.86 38.47 C 302.11 37.54 300 36.28 300 34.96" fill="none" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/>
        <ellipse cx="275" cy="100" rx="10" ry="10" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 18px; height: 1px; padding-top: 100px; margin-left: 266px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="275" y="103" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    2
                </text>
            </switch>
        </g>
        <rect x="320" y="240" width="110" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 250px; margin-left: 321px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Channel Callback
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="375" y="253" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    Channel Callback
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
